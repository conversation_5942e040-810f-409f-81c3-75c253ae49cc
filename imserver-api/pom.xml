<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.tig</groupId>
		<artifactId>imserver-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>imserver-api</artifactId>
	<packaging>war</packaging>
	<name>imserver-api</name>


	<properties>

		<!--maven.build.timestamp保存了maven编译时间戳-->
		<timestamp>${maven.build.timestamp}</timestamp>
		<!--指定时间格式-->
		<maven.build.timestamp.format>yyyy-MM-dd</maven.build.timestamp.format>

		<tomcat.version>8.5.13</tomcat.version>
		<jstl.version>1.2</jstl.version>
		<jackson-databind.version>2.9.5</jackson-databind.version>
		<java.version>1.8</java.version>
		<rocketmq.version>4.3.1</rocketmq.version>
		<start-class>com.tig.im.Application</start-class>
		<main.basedir>${basedir}/../..</main.basedir>
		<m2eclipse.wtp.contextRoot>/</m2eclipse.wtp.contextRoot>
	</properties>


	<dependencies>
		<dependency>
			<groupId>com.paypal.sdk</groupId>
			<artifactId>checkout-sdk</artifactId>
			<version>${paypal-sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
			<version>${spring-boot.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<version>${spring-boot.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.tig</groupId>
			<artifactId>imserver-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<!-- Temporarily commented out - missing dependency
		<dependency>
			<groupId>com.tig.commons</groupId>
			<artifactId>tig-utils</artifactId>
			<version>1.0</version>
		</dependency>
		-->

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson-databind.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
			<version>${tomcat.version}</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>${jstl.version}</version>
		</dependency>
		<!--quartz持久化到MongoDB - Temporarily commented out - missing dependency
		<dependency>
			<groupId>com.novemberain</groupId>
			<artifactId>quartz-mongodb</artifactId>
			<version>2.0.0-rc2</version>
			<exclusions>
				<exclusion>
					<artifactId>quartz</artifactId>
					<groupId>org.quartz-scheduler</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		-->
		<!--quartz-->
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.2.3</version>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz-jobs</artifactId>
			<version>2.2.3</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-quartz</artifactId>
			<version>${spring-boot.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>quartz</artifactId>
					<groupId>org.quartz-scheduler</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.16.22</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.github.penggle</groupId>
			<artifactId>kaptcha</artifactId>
			<version>2.3.2</version>
		</dependency>
		<!--<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
		</dependency>-->

	</dependencies>

	<!-- Package as an executable jar -->
	<build>
		<!--  <resources>
            <resource>
                <directory>src/main/resources</directory>
                 <excludes>
                    <exclude>**/*.properties</exclude>
                    <exclude>**/*.xml</exclude>
                 </excludes>
                <filtering>true</filtering>
            </resource>
        </resources> -->
		<!-- 指定打包的文件名 -->
		<finalName>imserver-api-${timestamp}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
					<fork>true</fork>
					<executable>javac</executable>
					<compilerArgs>
						<arg>-J-Dfile.encoding=UTF-8</arg>
						<arg>-J-Duser.language=en</arg>
						<arg>-J-Duser.country=US</arg>
					</compilerArgs>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>