<%--<!DOCTYPE html>--%>
<%--<html>--%>
<%--<head>--%>
<%--<meta charset="UTF-8">--%>
<%--<title>入驻公众号申请审核管理界面</title>--%>
<%--</head>--%>
<%--<link href="/pages/common/layui/css/layui.css" rel="stylesheet">--%>

<%--<body>--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
	<title>article</title>
</head>
<body>
	<div class="layui-tab layui-tab-brief" lay-filter="demo">
		<ul class="layui-tab-title">
			<li class="layui-this">待审核</li>
			<li>审核通过</li>
			<li>审核驳回</li>
		</ul>
		<div class="layui-tab-content" style="height: 100px;">
			<div class="layui-tab-item layui-show">
				<div class="layui-row">
					<div class="layui-col-md1">&nbsp;</div>
					<input id="pageCountHolding" type="" name="" style="display: none">
					<div id="examineListHolding" class="layui-col-md10">
						<div class="admin_btn_div" style="margin-top: 2%">
							<input type="text" name="" class="layui-input admin_keywordHolding" style="width: 15%;display: inline" placeholder="昵称/手机号">
							<button class="layui-btn  search_examineListHolding">搜索</button>

						</div>

						<div class="layui-card examine_table" style="margin-top: 1%">
							<div class="layui-card-header">申请审核列表</div>
							<div class="layui-card-body">
								<table id="examine_listHolding" lay-filter="examine_listHolding" style="table-layout:fixed;word-break:break-all;" >

								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-tab-item">
				<div class="layui-row">
					<div class="layui-col-md1">&nbsp;</div>
					<input id="pageCountPass" type="" name="" style="display: none">
					<div id="examineListPass" class="layui-col-md10">
						<div class="admin_btn_div" style="margin-top: 2%">
							<input type="text" name="" class="layui-input admin_keywordPass" style="width: 15%;display: inline" placeholder="昵称/手机号">
							<button class="layui-btn  search_examineListPass">搜索</button>

						</div>

						<div class="layui-card examine_table" style="margin-top: 1%">
							<div class="layui-card-header">申请通过列表</div>
							<div class="layui-card-body">
								<table id="examine_listPass" lay-filter="examine_listPass" style="table-layout:fixed;word-break:break-all;" >

								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-tab-item">
				<div class="layui-row">
					<div class="layui-col-md1">&nbsp;</div>
					<input id="pageCountReject" type="" name="" style="display: none">
					<div id="examineListReject" class="layui-col-md10">
						<div class="admin_btn_div" style="margin-top: 2%">
							<input type="text" name="" class="layui-input admin_keywordReject" style="width: 15%;display: inline" placeholder="昵称/手机号">
							<button class="layui-btn  search_examineListReject">搜索</button>

						</div>

						<div class="layui-card examine_table" style="margin-top: 1%">
							<div class="layui-card-header">申请驳回列表</div>
							<div class="layui-card-body">
								<table id="examine_listReject" lay-filter="examine_listReject" style="table-layout:fixed;word-break:break-all;" >

								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!--操作-->
	<script type="text/html" id="examineListBarHolding">
		<a class="layui-btn layui-btn-danger layui-btn-xs pass" lay-event="pass">通过</a>
		<a class="layui-btn layui-btn-primary layui-btn-xs reject" lay-event="reject">驳回</a>
	</script>
	<script type="text/html" id="examineListBarPass">
		{{#  if(d.accountStatus==0){ }}
			<a class="layui-btn layui-btn-danger layui-btn-xs locking" lay-event="locking">禁用</a>
		{{#  }else{ }}
			<a class="layui-btn layui-btn-danger layui-btn-xs unlocking" lay-event="unlocking">解锁</a>
		{{#  } }}
	</script>
	<script type="text/html" id="toolbarUsers">
		<div class="layui-btn-container">
			<button id="checkPassUsersId" class="layui-btn layui-btn-sm checkPassUsers" onclick="MP.checkPassUsers()" lay-event="passMore">多选通过</button>
			<button id="checkRejectUsersId" class="layui-btn layui-btn-sm checkRejectUsers" onclick="MP.checkRejectUsers()" lay-event="rejectMore">多选驳回</button>
		</div>
	</script>

</body>
</html>