var i=1;
var sum=0;
var pageIndex=0;
var currentPageIndex;// 当前页码数
var currentCount;// 当前总数
var currentPageIndex1;// 当前页码数
var currentCount1;// 当前总数
var currentPageIndex2;// 当前页码数
var currentCount2;// 当前总数
var form;
var layer;
var table;
layui.use(['form','layer','laydate','table','laytpl'],function() {
	form = layui.form,
		layer = parent.layer === undefined ? layui.layer : top.layer,
		$ = layui.jquery,
		table = layui.table;
})
$(function () {
      $("#project_article_path_update").attr("action",Config.getConfig().uploadUrl+"/upload/UploadServlet");
})
 function projectArticleClick(){
     $("#project_article_display_update").click();
}
function projectArticleDisplay(){
       var file=$("#project_article_display_update")[0].files[0];
       console.log(file)
       $("#project_article_path_update").ajaxSubmit(function(data){
           var jsonObj = eval('(' + data + ')');
           console.log(jsonObj.data.images[0]);
           $("#project_article_update").html(jsonObj.data.images[0].oUrl);
       });
 }
var MP={
	//把ueditor的config配置，外置（默认为本地测试用）,在init方法里面有赋值
	ueditorConfig : {
		"imagePathFormat":'/pages/common/ueditor/jsp/upload/image/{yyyy}{mm}{dd}/{time}{rand:6}',//图片上传路径
		"imageUrlPrefix": 'http://localhost:8092',
		"imageManagerUrlPrefix": 'http://localhost:8092'
	},
	init : function() {
		// var imageUrlPrefix = $("#ueditorImageUrlPrefix").val();
		// if(imageUrlPrefix) {
		// 	MP.ueditorConfig.imageUrlPrefix = imageUrlPrefix;
		// 	MP.ueditorConfig.imageManagerUrlPrefix = imageUrlPrefix;
		// }
	},
	// 首页
	index:function(){
		$("#li_one").css("background-color","#4E5465");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#393D49");
		$("#li_nine").css("background-color","#393D49");

		$("#index").show();
		$("#index_one").show();
		$("#update_menu").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#pushManyText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index_newMsg").hide();
		$("#index_add").hide();
		$("#index_userSum").hide();
		$("#pushMsg").hide();
		$("#pageIndex").hide();
		$("#newMsg_item").hide();
		$("#msg_item").hide();
		$("#welcomeText").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#accessVerify").hide();

		$("#commonArticleVerify").hide();
	},
	// 群发消息
	pushText:function(){
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#4E5465");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#393D49");
		$("#li_nine").css("background-color","#393D49");
		$("#index_one").hide();
		$("#update_menu").hide();
		$("#pushText").show();
		$("#pushOneText").hide();
		$("#pushManyText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#accessVerify").hide();
		$("#commonArticleVerify").hide();
		$("#welcomeText").hide();
	},
	// 欢迎语设置
	welcomeText:function(){
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#4E5465");
		$("#li_nine").css("background-color","#393D49");
		$("#index_one").hide();
		$("#update_menu").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#pushManyText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#accessVerify").hide();
		$("#commonArticleVerify").hide();
		$("#welcomeText").show();
		//
		$.ajax({
			type:'POST',
			url : '/mp/getWelcomeText',
			dataType:"json",
			async: false,
			success:function(result){
				$("#welecomeTextbody").val(result.data);
			}
		});
	},
	// 群发单条图文消息
	pushOneText:function(){
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#4E5465");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#393D49");
		$("#li_nine").css("background-color","#393D49");

		$("#pushOneText").show();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushManyText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#welcomeText").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#accessVerify").hide();
		$("#commonArticleVerify").hide();
	},
	// 群发多条图文消息
	pushManyText:function(){
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#4E5465");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#393D49");
		$("#li_nine").css("background-color","#393D49");

		$("#pushManyText").show();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#welcomeText").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#accessVerify").hide();
		$("#commonArticleVerify").hide();
	},
	//图文群发
	pushArticles : function() {
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#393D49");
		$("#li_nine").css("background-color","#4E5465");
		$("#li_eight").css("background-color","#393D49");
		$("#li_nine").css("background-color","#393D49");

		$("#editArticles").show();
		$("#pushManyText").hide();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#articleVerify").hide();
		$("#accessVerify").hide();
		$("#commonArticleVerify").hide();
		$("#welcomeText").hide();

		//富文本ueditor初始化
		//MP.editArticleUE = UE.getEditor('editor', {serverUrl:"/pages/common/ueditor/jsp/controller.jsp?json="+encodeURI(JSON.stringify(MP.ueditorConfig))});
		MP.editArticleUE = UE.getEditor('editor', {});
	},
	//文章审核(草稿箱)
	articleManager : function() {
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#4E5465");
		$("#li_nine").css("background-color","#393D49");

		$("#pushManyText").hide();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#editArticles").hide();
		$("#accessVerify").hide();
		$("#commonArticleVerify").hide();
		$("#articleVerify").show();

		$("#articleManager").show();
		$("#articleAuditing").hide();
		$("#updateArticleOuter").hide();
		$('#previewArticleOuter').hide();
		//隐藏回收站
		$("#articleRecycleBin").hide();
		$("#commonArticleRecycleBin").hide();
		$("#welcomeText").hide();
		//默认加载待审核的页面数据
		// MP.checkPending();
		MP.articleList(0, 0, '', 1);
	},
	//已经发送完成的文章
	articlePushOverList : function() {
		//加载回收站列表
		var page=0;
		var lock=0;
		var currentPageIndex;// 当前页码数
		var currentCount;// 当前总数
		layui.use(['form','layer','table'],function(){
			var form = layui.form,
				layer = parent.layer === undefined ? layui.layer : top.layer,
				$ = layui.jquery,
				laydate = layui.laydate,
				laytpl = layui.laytpl,
				table = layui.table;
			//发送完成列表
			var tableInArticle = table.render({
				elem: '#articlePushOverList'
				// ,toolbar: '#articleRecycleBinBar'
				,url:"/mp/articleList"+"?articleState=5&pageSize=15"//发送完成的状态为5
				,id: 'articlePushOverList'
				,page: true
				,curr: 0
				,limit:15
				,limits:[15,50,100,1000,10000]
				,groups: 7
				,cols: [[ //表头
					{type:'checkbox',fixed:'left'}// 多选
					,{field: 'userId', title: '用户Id',sort:'true', width:100}
					,{field: 'articleFirstName', title: '文章标题',sort:'true', width:235}
					,{field: 'articleSecondName', title: '文章副标题',sort:'true', width:200}
					,{fixed: 'right', width: 370,title:"操作", align:'left', toolbar: '#articlePushOverBar'}
				]]
				,done:function(res, curr, count){
					if(count==0&&lock==1){
						layer.msg("暂无数据",{"icon":2});
						renderTable();
					}
					lock=0;
					var pageIndex = tableInArticle.config.page.curr;//获取当前页码
					var resCount = res.count;// 获取table总条数
					currentCount = resCount;
					currentPageIndex = pageIndex;
				}
			});

			table.on('tool(articlePushOverList)', function(obj){
				var layEvent = obj.event,data = obj.data;
				var articleId = data._id;//文章ID
				if(layEvent === 'select') {//查看
					MP.previewArticle(null, articleId, 'articleManager')
				} else if(layEvent === 'edit') {//从回收站拉到草稿箱
					$.ajax({
						type:'POST',
						url : '/mp/updateArticleByOid',
						dataType:"json",
						//加上这句话
						xhrFields: {
							withCredentials: true
						},
						data:{
							objectId:articleId,
							articleState : 1
						},
						crossDomain: true,
						async: false,
						success:function(result){
							//恢复成功之后，再调用加载回收站列表的方法
							MP.articleRecycleBin();
						}
					});
				}
			});
		})
	},
	//文章审核
	articleVerify : function() {
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#4E5465");
		$("#li_nine").css("background-color","#393D49");

		$("#pushManyText").hide();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#editArticles").hide();
		$("#accessVerify").hide();
		$("#commonArticleVerify").hide();
		$("#articleVerify").show();

		//articleVerify内部显隐处理
		$("#articleAuditing").show();
		$("#updateArticleOuter").hide();
		$('#previewArticleOuter').hide();
		$('#articleRecycleBin').hide();
		$('#commonArticleRecycleBin').hide();
		//图文群发处的文章管理
		$("#articleManager").hide();
		$("#welcomeText").hide();
		//默认加载待审核的页面数据
		MP.checkPending();
	},
	accessVerify:function(){
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#4E5465");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#menu").hide();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#pushManyText").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#menuPage").hide();
		$("#msg_item").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#accessVerify").show();
		$("#commonArticleVerify").hide();

		MP.access();
		// MP.checkPending();
		//处理（逻辑内流程）显示问题
		$("#articleAuditing").show();
		$("#updateArticleOuter").hide();
		$('#previewArticleOuter').hide();
		$("#welcomeText").hide();
	},
	//普通用户的文章管理
	commonArticleVerify : function() {
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#393D49");
		$("#li_nine").css("background-color","#4E5465");

		$("#pushManyText").hide();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#commonArticleVerify").show();

		//在这里要调用列表未提交审核的列表
		MP.articleList(0, 0, "", 1);
		$("#commonArticleOuter").show();
		$("#updateCommonArticleOuter").hide();
		$('#previewCommonArticleOuter').hide();
		$("#welcomeText").hide();
	},
	//普通文章审核列表
	commonArticleExamineList : function() {
		//加载普通文章审核列表
		var page=0;
		var lock=0;
		var currentPageIndex;// 当前页码数
		var currentCount;// 当前总数
		layui.use(['form','layer','table'],function(){
			var form = layui.form,
				layer = parent.layer === undefined ? layui.layer : top.layer,
				$ = layui.jquery,
				laydate = layui.laydate,
				laytpl = layui.laytpl,
				table = layui.table;
			//普通文章审核列表
			var tableInCommonArticle = table.render({
				elem: '#commonArticleExamineList'
				,url:"/mp/articleList"+"?articleState=23456&pageSize=15"//普通文章审核的状态为23456
				,id: 'commonArticleExamineList'
				,page: true
				,curr: 0
				,limit:15
				,limits:[15,50,100,1000,10000]
				,groups: 7
				,cols: [[ //表头
					{type:'checkbox',fixed:'left'}// 多选
					,{field: 'userId', title: '用户Id',sort:'true', width:100}
					,{field: 'articleFirstName', title: '文章标题',sort:'true', width:235}
					,{field: 'articleSecondName', title: '文章副标题',sort:'true', width:200}
					,{field: 'articleState', title: '审核状态',sort:'true', width:200,templet(d){
							if(2 == d.articleState){
								return '待审核';
							} else if(3 == d.articleState) {
								return '审核通过';
							} else if(4 == d.articleState) {
								return '审核拒绝';
							} else if(5 == d.articleState) {
								return '发送完成';
							} else if(6 == d.articleState) {
								return '待审核';
							}
							return "审核出错";
						}}
					// ,{fixed: 'right', width: 370,title:"操作", align:'left', toolbar: '#commonArticleExamineBar'}
				]]
				,done:function(res, curr, count){
					if(count==0&&lock==1){
						layer.msg("暂无数据",{"icon":2});
						renderTable();
					}
					lock=0;
					var pageIndex = tableInCommonArticle.config.page.curr;//获取当前页码
					var resCount = res.count;// 获取table总条数
					currentCount = resCount;
					currentPageIndex = pageIndex;
				}
			});

			table.on('tool(commonArticleExamineList)', function(obj){
				var layEvent = obj.event,data = obj.data;
				var articleId = data._id;//文章ID
				if(layEvent === 'select') {//查看

				} else if(layEvent === 'edit') {
					$.ajax({
						type:'POST',
						url : '/mp/updateArticleByOid',
						dataType:"json",
						//加上这句话
						xhrFields: {
							withCredentials: true
						},
						data:{
							objectId:articleId,
							articleState : 1
						},
						crossDomain: true,
						async: false,
						success:function(result){
							//恢复成功之后，再调用加载回收站列表的方法
							MP.articleRecycleBin();
						}
					});
				}
			});
		})
	},
	//普通用户的文章管理
	commonArticle3Verify : function() {
		//在这里要调用列表未提交审核的列表
		MP.articleList(0, 0, "", 3);
		$("#commonArticleOuter").show();
		$("#updateCommonArticleOuter").hide();
		$('#previewCommonArticleOuter').hide();
	},
	updateAndPushNow : function(obj) {
		//先行表单验证
		if(!MP.article10000UpdateCheck()) {
			return ;
		}

		var _this = $(obj);
		var ttContent = MP.updateArticleUE.getContent();
		var aticleCover = $("#project_article_update").html();
		var articleFirstName = $("#articleFirstName_update").val();
		var articleSecondName = $("#articleSecondName_update").val();

		var userId = _this.attr("data-uid");
		var objectId = _this.attr("data-oid");
		$.ajax({
			url : '/mp/updateAndPushNow',
			method : 'POST',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data : {
				title:articleFirstName,
				sub:articleSecondName,
				img:aticleCover,
				userId : userId,
				objectId : objectId,
				articleState : 5,//发送成功就为5
				content:ttContent
			},
			success : function(result) {
				if (result.resultCode == 1) {
					layui.layer.alert("群发成功");
				} else {
					layui.layer.alert("群发失败");
				}
			},
			error : function() {
				layui.layer.alert("群发失败");
			}
		})
	},
	//回收站用户的文章
	articleRecycleBin : function() {
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#393D49");
		$("#li_nine").css("background-color","#4E5465");

		$("#pushManyText").hide();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#editArticles").hide();
		$("#accessVerify").hide();
		$("#commonArticleVerify").hide();
		$("#articleVerify").show();

		//平行分支的 单图文群发和文章管理隐藏
		$("#editArticles").hide();
		$("#articleManager").hide();
		$("#commonArticleRecycleBin").hide();
		$("#articleAuditing").hide();
		$("#updateArticleOuter").hide();
		//显示回收站DIV
		$("#articleRecycleBin").show();
		$("#welcomeText").hide();

		//加载回收站列表
		var page=0;
		var lock=0;
		var currentPageIndex;// 当前页码数
		var currentCount;// 当前总数
		layui.use(['form','layer','table'],function(){
			var form = layui.form,
				layer = parent.layer === undefined ? layui.layer : top.layer,
				$ = layui.jquery,
				laydate = layui.laydate,
				laytpl = layui.laytpl,
				table = layui.table;
			//待审核列表
			var tableInArticle = table.render({
				elem: '#articleRecycleBin_list'
				// ,toolbar: '#articleRecycleBinBar'
				,url:"/mp/articleList"+"?articleState=6&pageSize=15"//回收站的状态为6
				,id: 'articleRecycleBin_list'
				,page: true
				,curr: 0
				,limit:15
				,limits:[15,50,100,1000,10000]
				,groups: 7
				,cols: [[ //表头
					{type:'checkbox',fixed:'left'}// 多选
					,{field: 'userId', title: '用户Id',sort:'true', width:100}
					,{field: 'articleFirstName', title: '文章标题',sort:'true', width:235}
					,{field: 'articleSecondName', title: '文章副标题',sort:'true', width:200}
					,{fixed: 'right', width: 370,title:"操作", align:'left', toolbar: '#articleRecycleBinBar'}
				]]
				,done:function(res, curr, count){
					if(count==0&&lock==1){
						layer.msg("暂无数据",{"icon":2});
						renderTable();
					}
					lock=0;
					var pageIndex = tableInArticle.config.page.curr;//获取当前页码
					var resCount = res.count;// 获取table总条数
					currentCount = resCount;
					currentPageIndex = pageIndex;
				}
			});

			table.on('tool(articleRecycleBin_list)', function(obj){
				var layEvent = obj.event,data = obj.data;
				var articleId = data._id;//文章ID
				if(layEvent === 'select') {//查看
					MP.previewArticle(null, articleId, 'articleRecycleBin');
				} else if(layEvent === 'edit') {//从回收站拉到草稿箱
					$.ajax({
						type:'POST',
						url : '/mp/updateArticleByOid',
						dataType:"json",
						//加上这句话
						xhrFields: {
							withCredentials: true
						},
						data:{
							objectId:articleId,
							articleState : 1
						},
						crossDomain: true,
						async: false,
						success:function(result){
							//恢复成功之后，再调用加载回收站列表的方法
							MP.articleRecycleBin();
						}
					});
				}
			});
		})
	},
	//普通文章的回收站
	commonArticleRecycleBin : function() {
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#4E5465");
		$("#li_nine").css("background-color","#393D49");

		$("#pushManyText").hide();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msg_item").hide();
		$("#editArticles").hide();
		$("#accessVerify").hide();
		$("#commonArticleVerify").hide();
		$("#articleVerify").show();

		//平行分支的 单图文群发和文章管理隐藏
		$("#editArticles").hide();
		$("#articleManager").hide();
		$("#articleAuditing").hide();
		$('#articleRecycleBin').hide();
		$('#previewArticleOuter').hide();
		//显示回收站DIV
		$("#commonArticleRecycleBin").show();
		$("#welcomeText").hide();

		//加载回收站列表
		var page=0;
		var lock=0;
		var currentPageIndex;// 当前页码数
		var currentCount;// 当前总数
		layui.use(['form','layer','table'],function(){
			var form = layui.form,
				layer = parent.layer === undefined ? layui.layer : top.layer,
				$ = layui.jquery,
				laydate = layui.laydate,
				laytpl = layui.laytpl,
				table = layui.table;
			//待审核列表
			var tableInArticle = table.render({
				elem: '#commonArticleRecycleBin_list'
				,url:"/mp/articleList"+"?articleState=66&pageSize=15"//回收站的状态为6,66表示非10000公众号的用户文章
				,id: 'commonArticleRecycleBin_list'
				,page: true
				,curr: 0
				,limit:15
				,limits:[15,50,100,1000,10000]
				,groups: 7
				,cols: [[ //表头
					{type:'checkbox',fixed:'left'}// 多选
					,{field: 'userId', title: '用户Id',sort:'true', width:100}
					,{field: 'articleFirstName', title: '文章标题',sort:'true', width:235}
					,{field: 'articleSecondName', title: '文章副标题',sort:'true', width:200}
					,{fixed: 'right', width: 370,title:"操作", align:'left', toolbar: '#commonArticleRecycleBinBar'}
				]]
				,done:function(res, curr, count){
					if(count==0&&lock==1){
						layer.msg("暂无数据",{"icon":2});
						renderTable();
					}
					lock=0;
					var pageIndex = tableInArticle.config.page.curr;//获取当前页码
					var resCount = res.count;// 获取table总条数
					currentCount = resCount;
					currentPageIndex = pageIndex;
				}
			});

			table.on('tool(commonArticleRecycleBin_list)', function(obj){
				var layEvent = obj.event,data = obj.data;
				var articleId = data._id;//文章ID
				if(layEvent === 'select') {//查看
					MP.previewArticle(null, articleId, 'commonArticleRecycleBin');
				} else if(layEvent === 'edit') {//从回收站拉到草稿箱
					$.ajax({
						type:'POST',
						url : '/mp/updateArticleByOid',
						dataType:"json",
						//加上这句话
						xhrFields: {
							withCredentials: true
						},
						data:{
							objectId:articleId,
							articleState : 2//普通文章恢复到待审核状态
						},
						crossDomain: true,
						async: false,
						success:function(result){
							//恢复成功之后，再调用加载回收站列表的方法
							MP.commonArticleRecycleBin();
						}
					});
				}
			});
		})
	},
	updateArticleUE : '',
	editArticleUE : '',
	//文章列表
	articleList: function (pageIndex, flag, keyword, articleState) {
		var userId = $("#loginUserId").val();
		//处理（逻辑内流程）显示问题
		if(userId == 10000) {
			if(articleState == 1) {
				$("#articleManager").show();
				//初始把文章模板隐藏起来，在文章处理数据的时候显示
				$('#article10000ListUl li').hide();
			} else if(articleState == 2 || articleState == 33 || articleState == 55) {
				$("#articleAuditing").show();
				//初始把文章模板隐藏起来，在文章处理数据的时候显示
				$('#articleListUl li').hide();
			}
		} else {
			$("#commonArticleVerify").show();
			//初始把文章模板隐藏起来，在文章处理数据的时候显示
			$('#commonArticleListUl li').hide();
			$('#commonArticle3ListUl li').hide();
		}
		$("#updateArticleOuter").hide();
		$('#previewArticleOuter').hide();

		var pageCount = 6;
		if(!pageIndex) pageIndex = 0;

		//加载文章数据
		$.ajax({
			type:'POST',
			url : '/mp/articleList',
			dataType:"json",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			data:{
				pageIndex : pageIndex,
				//userId : userId,//这个后台也可以得到，不用传
				keyWord : keyword,
				articleState : articleState
			},
			crossDomain: true,
			async: false,
			success:function(result){
				pageCount = result.count;
				var articleList = result.data;
				//循环给文章列表处理数据
				if(pageCount == 0) {
					//显示暂时无文章发布
					if(userId == 10000) {
						if(articleState == 1) {
							$('#article10000ListUl').append('<li style="height: 31%;text-align: center;border: 1px solid #e6e6e6;padding-top: 15%;">暂无文章发布</li>');
						} else {
							$('#articleListUl').append('<li style="height: 31%;text-align: center;border: 1px solid #e6e6e6;padding-top: 15%;">暂无文章发布</li>');
						}
					} else {
						$('#commonArticleListUl').append('<li style="height: 31%;border: 1px solid #e6e6e6;padding-top: 15%;">暂无文章发布</li>');
					}
				} else {
					articleList.forEach(function (currentValue, index) {
						if(userId == 10000) {
							if(articleState == 1) {//10000公众号的草稿箱
								$('#article10000ListUl li .articleFirstName').get(index).innerText = currentValue.articleFirstName;
								$('#article10000ListUl li .articleSecondName').get(index).innerText = currentValue.articleSecondName;
								$('#article10000ListUl li .articleCover img').eq(index).attr("src", currentValue.articleCoverUrl);
								var createDate = new Date(currentValue.createTime);
								$('#article10000ListUl li .articleDesc .createDate').get(index).innerText = createDate.format("yyyy-MM-dd hh:mm");
								$('#article10000ListUl li .articleDesc .readCount').get(index).innerText = "阅读数：" + currentValue.pageView;
								$('#article10000ListUl li .articleBtn').eq(index).attr("data-id", currentValue._id);
								$('#article10000ListUl li').eq(index).show();
							} else if(articleState == 2 || articleState == 33 || articleState == 55) {//平台管理即普通用户的文章审核
								$('#articleListUl li .articleFirstName').get(index).innerText = currentValue.articleFirstName;
								$('#articleListUl li .articleSecondName').get(index).innerText = currentValue.articleSecondName;
								$('#articleListUl li .articleCover img').eq(index).attr("src", currentValue.articleCoverUrl);
								var createDate = new Date(currentValue.createTime);
								$('#articleListUl li .articleDesc .createDate').get(index).innerText = createDate.format("yyyy-MM-dd hh:mm");
								$('#articleListUl li .articleDesc .readCount').get(index).innerText = "阅读数：" + currentValue.pageView;
								$('#articleListUl li .articleBtn').eq(index).attr("data-id", currentValue._id);
								$('#articleListUl li .articleBtn').eq(index).attr("data-as", currentValue.articleSource);
								$('#articleListUl li').eq(index).show();
							}
						} else {//非10000的公众号文章处理
							if(articleState == 3) {
								$('#commonArticle3ListUl li .articleFirstName').get(index).innerText = currentValue.articleFirstName;
								$('#commonArticle3ListUl li .articleSecondName').get(index).innerText = currentValue.articleSecondName;
								$('#commonArticle3ListUl li .articleCover img').eq(index).attr("src", currentValue.articleCoverUrl);
								var createDate = new Date(currentValue.createTime);
								$('#commonArticle3ListUl li .articleDesc .createDate').get(index).innerText = createDate.format("yyyy-MM-dd hh:mm");
								$('#commonArticle3ListUl li .articleDesc .readCount').get(index).innerText = "阅读数：" + currentValue.pageView;
								$('#commonArticle3ListUl li .articleBtn').eq(index).attr("data-id", currentValue._id);
								$('#commonArticle3ListUl li').eq(index).show();
							} else {
								$('#commonArticleListUl li .articleFirstName').get(index).innerText = currentValue.articleFirstName;
								$('#commonArticleListUl li .articleSecondName').get(index).innerText = currentValue.articleSecondName;
								$('#commonArticleListUl li .articleCover img').eq(index).attr("src", currentValue.articleCoverUrl);
								var createDate = new Date(currentValue.createTime);
								$('#commonArticleListUl li .articleDesc .createDate').get(index).innerText = createDate.format("yyyy-MM-dd hh:mm");
								$('#commonArticleListUl li .articleDesc .readCount').get(index).innerText = "阅读数：" + currentValue.pageView;
								$('#commonArticleListUl li .articleBtn').eq(index).attr("data-id", currentValue._id);
								$('#commonArticleListUl li').eq(index).show();
							}
						}
					});
				}
			}
		});
		//判断第一次点击，进来显示分页模，再次jump不会再次加载
		if(flag != 1) {
			//文章审核处的分页插件使用
			var article_laypage = layui.laypage;
			if(userId == 10000) {//10000公众号的分页
				if(articleState == 1) {
					article_laypage.render({
						elem: 'article10000_page_container',
						count: pageCount,
						limit: 6,
						jump: function (obj, first) {
							if(!first) {
								MP.articleList(obj.curr - 1, 1, "", 1);
							}
						}
					});
				} else if(articleState == 2 || articleState == 33 || articleState == 55) {
					article_laypage.render({
						elem: 'article_page_container',
						count: pageCount,
						limit: 6,
						jump: function (obj, first) {
							if(!first) {
								MP.articleList(obj.curr - 1, 1, "", articleState);
							}
						}
					});
				}
			} else {//非10000公众号的分页
				if(articleState == 3) {
					article_laypage.render({
						elem: 'common_article3_page_container',
						count: pageCount,
						limit: 6,
						jump: function (obj, first) {
							if(!first) {
								MP.articleList(obj.curr - 1, 1, "", 3);
							}
						}
					});
				} else if(articleState == 1) {
					article_laypage.render({
						elem: 'common_article_page_container',
						count: pageCount,
						limit: 6,
						jump: function (obj, first) {
							if(!first) {
								MP.articleList(obj.curr - 1, 1, "", 1);
							}
						}
					});
				}
			}
		}

	},
	//进到编辑修改文章页面(10000公众号和普通公众号的页面不一样，走的是同一个方法)
	toUpdateArticle : function(obj) {
		var _this = $(obj);
		var objectId = _this.closest(".articleBtn").attr("data-id");
		var articleSource = _this.closest(".articleBtn").attr("data-as");
		var userId = $("#loginUserId").val();

		if(userId == 10000) {
			$('#articleAuditing').hide();
			$('#articleManager').hide();
			if(articleSource == 1) {
				$('#updateArticleOuter').show();
			} else if(articleSource == 2) {
				$('#articleVerify').hide();//把整个文章的审核DIV隐藏掉
				$('#pushOneText').show();//显示修改发单图文的地方
			}
			//MP.updateArticleUE = UE.getEditor('editorUpdate', {serverUrl:"/pages/common/ueditor/jsp/controller.jsp?json="+encodeURI(JSON.stringify(MP.ueditorConfig))});
			MP.updateArticleUE = UE.getEditor('editor', {});
		} else {
			$("#commonArticleOuter").hide();
			$("#updateCommonArticleOuter").show();
			//MP.updateArticleUE = UE.getEditor('commonEditorUpdate', {serverUrl:"/pages/common/ueditor/jsp/controller.jsp?json="+encodeURI(JSON.stringify(MP.ueditorConfig))});
			MP.updateArticleUE = UE.getEditor('editor', {});
		}

		$('.updateArticleInner .updateArticleHeader').empty().append('文章修改');

		//加载文章数据，成功处理回显
		$.ajax({
			type:'POST',
			url : '/mp/articleList',
			dataType:"json",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			data:{
				objectId:objectId
			},
			crossDomain: true,
			async: false,
			success:function(result){
				//这里处理查询出来的数据放到回显里面
				var articleObj = result.data[0];
				if(userId == 10000) {
					if(articleSource == 1) {
						$("#articlePushNowBtn_update").attr("data-oid", articleObj._id);
						$("#articlePushTimingBtn_update").attr("data-oid", articleObj._id);
						$("#articlePushNowBtn_update").attr("data-uid", articleObj.userId);
						$("#articlePushTimingBtn_update").attr("data-uid", articleObj.userId);

						$("#articleFirstName_update").val(articleObj.articleFirstName);
						$("#articleSecondName_update").val(articleObj.articleSecondName);
						$("#project_article_update").html(articleObj.articleCoverUrl);
					} else if(articleSource == 2) {
						$("#pushbody").val(articleObj.articleFirstName);
						$("#pushbodyTitle").val(articleObj.articleSecondName);
						$("#pushbodyImgUrl").val(articleObj.articleCoverUrl);
						$("#pushbodyHtmlUrl").val(articleObj.articleUrl);
						//给群发按钮一个标识，这里表示为修改后的文章群发为2，默认为1
						$("#pushbodySendBtn").attr("data-as", 2);
						$("#pushbodySendBtn").attr("data-oid", articleObj._id);
						$("#pushbodySendBtn").attr("data-uid", articleObj.userId);
					}
				} else {
					if(articleObj.articleState == 3) {
						$("#commonArticlePushNowBtn_update").empty().append("立即发送");
						$("#commonArticleCommitBtn_update").empty().append("定时发送");

						//修改触发的事件
						$("#commonArticlePushNowBtn_update").attr("onclick", "commonPushNow()");
						$("#commonArticleCommitBtn_update").attr("onclick", "schedule3PushClick()");
					} else {
						$("#commonArticlePushNowBtn_update").empty().append("修改文章");
						$("#commonArticleCommitBtn_update").empty().append("提交审核");
					}
					$("#commonArticlePushNowBtn_update").attr("data-oid", articleObj._id);//修改文章按钮
					$("#commonArticleCommitBtn_update").attr("data-oid", articleObj._id);//提交审核按钮
					$("#commonArticleFirstName_update").val(articleObj.articleFirstName);
					$("#commonArticleSecondName_update").val(articleObj.articleSecondName);
					$("#project_article_update").html(articleObj.articleCoverUrl);
				}

				// $("#editorUpdate").val(articleObj.articleContent);
				// ue.setContent(articleObj.articleContent);
				if(articleSource != 2) {
					MP.updateArticleUE.ready(function(){//用这种方式给ueditor赋值
						MP.updateArticleUE.setContent(articleObj.articleContent);
					})
				}
			}
		});
	},
	//10000的草稿箱处的修改验证
	article10000UpdateCheck: function() {
		var articleFirstName = $("#articleFirstName_update").val().trim();
		var articleSecondName = $("#articleSecondName_update").val().trim();
		var pushArticleImgUrl10 = $("#project_article_update").html();
		var ueditorContent = MP.updateArticleUE.getContent();
		if(!articleFirstName) {
			layui.layer.msg("文章标题不能为空！",{"icon":2});
			return false;
		}
		if(!articleSecondName) {
			layui.layer.msg("文章副标题不能为空！",{"icon":2});
			return false;
		}
		if(!pushArticleImgUrl10) {
			layui.layer.msg("文章封面图片不能为空！",{"icon":2});
			return false;
		}
		if(!ueditorContent) {
			layui.layer.msg("文章内容不能为空！",{"icon":2});
			return false;
		}
		return true;
	},
	//修改文章(10000公众号的)
	updateArticle : function(obj) {
		var _this = $(obj);
		var objectId = _this.attr("data-oid");

		var ttContent = MP.updateArticleUE.getContent();
		var aticleCover = $("#project_article_update").html();
		var articleFirstName = $("#articleFirstName_update").val();
		var articleSecondName = $("#articleSecondName_update").val();

		$.ajax({
			type:'POST',
			url : '/mp/updateArticleByOid',
			dataType:"json",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			data:{
				objectId:objectId,
				title:articleFirstName,
				sub:articleSecondName,
				img:aticleCover,
				content:ttContent
			},
			crossDomain: true,
			async: false,
			success:function(result){
				MP.updateArticleUE = '';
				//修改成功之后，再调用加载列表的方法
				MP.articleList();
			}
		});
	},
	//立即发送
	pushNow : function() {
		//先行表单验证
		if(!MP.checkArticlePush()) {
			return ;
		}
		var ttContent = MP.editArticleUE.getContent();
		var aticleCover = $("#project_article_update").html();
		var articleFirstName = $("#articleFirstName").val();
		var articleSecondName = $("#articleSecondName").val();
		$.ajax({
			url : '/mp/saveAndPushNow',
			method : 'POST',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data : {
				title:articleFirstName,
				sub:articleSecondName,
				img:aticleCover,
				content:ttContent
			},
			success : function(result) {
				if (result.resultCode == 1) {
					//清空表单
					MP.emptyArticleEdit();
					layui.layer.alert("群发成功");
				} else {
					layui.layer.alert("群发失败");
				}
			},
			error : function() {
				layui.layer.alert("群发失败");
			}
		})
	},
	//根据articleState处理文章
	saveArticle : function(articleState) {
		//先行表单验证
		if(!MP.checkArticlePush()) {
			return ;
		}
		var ttContent = MP.editArticleUE.getContent();
		var aticleCover = $("#project_article_update").html();
		var articleFirstName = $("#articleFirstName").val();
		var articleSecondName = $("#articleSecondName").val();
		$.ajax({
			url : '/mp/saveArticle',
			method : 'POST',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data : {
				title : articleFirstName,
				sub : articleSecondName,
				img : aticleCover,
				content : ttContent,
				articleState : articleState,
				articleSource : 1//1为内部自己编写的文章
			},
			success : function(result) {
				if (result.resultCode == 1) {
					if(articleState == 1) {
						//清空表单
						MP.emptyArticleEdit();
						layui.layer.alert("保存成功");
					}
					else
						layui.layer.alert("提交成功");

				} else {
					layui.layer.alert("群发失败");
				}
			},
			error : function() {
				layui.layer.alert("群发失败");
			}
		})
	},
	//点定时发送的操作(显示时间组件)
	schedulePushClick : function() {
		//先行表单验证
		if(!MP.checkArticlePush()) {
			return ;
		}
		$("#scheduleArticleTime").show();
	},
	//表单验证是否为空，下面的操作按钮都会调用进行验证
	checkArticlePush : function() {
		var articleFirstName = $("#articleFirstName").val().trim();
		var articleSecondName = $("#articleSecondName").val().trim();
		var pushArticleImgUrl10 = $("#project_article_update").html();
		var ueditorContent = MP.editArticleUE.getContent();
		if(!articleFirstName) {
			layui.layer.msg("文章标题不能为空！",{"icon":2});
			return false;
		}
		if(!articleSecondName) {
			layui.layer.msg("文章副标题不能为空！",{"icon":2});
			return false;
		}
		if(!pushArticleImgUrl10) {
			layui.layer.msg("文章封面图片不能为空！",{"icon":2});
			return false;
		}
		if(!ueditorContent) {
			layui.layer.msg("文章内容不能为空！",{"icon":2});
			return false;
		}
		return true;
	},
	//保存或发送成功清空表单
	emptyArticleEdit : function() {
		$("#articleFirstName").val("");
		$("#articleSecondName").val("");
		$("#project_article_update").html("");
		MP.editArticleUE.ready(function(){//用这种方式给ueditor赋值
			MP.editArticleUE.setContent("");
		})
	},
	//预览文章(参数：obj和articleId都是得到文章的ID作为查询条件，dataAttr作为点击时显隐的一个id属性)
	previewArticle : function(obj, articleId, dataAttr) {
		var _this = $(obj);
		var objectId = '';
		if(articleId) {
			objectId = articleId;
		} else {
			objectId = _this.closest(".articleBtn").attr("data-id");
		}

		var userId = $("#loginUserId").val();

		if(userId == 10000) {
			$('#articleAuditing').hide();
			$('#articleManager').hide();
			$('#articleRecycleBin').hide();
			$('#commonArticleRecycleBin').hide();
			$('#previewArticleOuter').show();
		} else {
			$('#commonArticleOuter').hide();
			$('#previewCommonArticleOuter').show();
		}
		//加载当前点击的文章数据，成功后处理回显
		$.ajax({
			type:'POST',
			url : '/mp/articleList',
			dataType:"json",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			data:{
				objectId:objectId
			},
			crossDomain: true,
			async: false,
			success:function(result){
				//这里处理查询出来的数据放到预览位置
				var articleObj = result.data[0];
				if(userId == 10000) {
					$("#previewArticleInner .articleFirstName").empty().append(articleObj.articleFirstName);
					$("#previewArticleInner .articleSecondName").empty().append(articleObj.articleSecondName);
					$("#previewArticleInner .articleContent").empty().append(articleObj.articleContent);
					$("#previewArticleInner .articleCoverUrl img").attr("src", articleObj.articleCoverUrl);
					$("#previewArticleInner .articleUrl").empty().append(articleObj.articleUrl);
					var createDate = new Date(articleObj.createTime);
					$("#previewArticleInner .articleCreatetime").empty().append("创建时间："+createDate.format("yyyy-MM-dd hh:mm"));
					$("#previewArticleInner .articleReadCount").empty().append("阅读数："+articleObj.pageView);

					$("#previewArticleOuter div .btnBack").attr("data-attr", dataAttr);
				} else {
					$("#previewCommonArticleInner .commonArticleFirstName").empty().append(articleObj.articleFirstName);
					$("#previewCommonArticleInner .commonArticleSecondName").empty().append(articleObj.articleSecondName);
					$("#previewCommonArticleInner .commonArticleContent").empty().append(articleObj.articleContent);
					$("#previewCommonArticleInner .articleCoverUrl img").attr("src", articleObj.articleCoverUrl);
					$("#previewCommonArticleInner .articleUrl").empty().append(articleObj.articleUrl);
					var createDate = new Date(articleObj.createTime);
					$("#previewArticleInner .articleCreatetime").empty().append("创建时间："+createDate.format("yyyy-MM-dd hh:mm"));
					$("#previewArticleInner .articleReadCount").empty().append("阅读数："+articleObj.pageView);

					$("#previewCommonArticleInner div .btnBack").attr("data-attr", dataAttr);
				}
			}
		});
	},
	//逻辑删除文章（这里是把文章的状态改为6，即回收站的状态）
	deleteArticle : function(obj, articleState) {
		var _this = $(obj);
		var objectId = _this.closest(".articleBtn").attr("data-id");

		$.ajax({
			type:'POST',
			url : '/mp/updateArticleByOid',
			dataType:"json",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			data:{
				objectId:objectId,
				articleState : 6
			},
			crossDomain: true,
			async: false,
			success:function(result){
				//删除成功之后，再调用加载列表的方法
				MP.articleList(0, 0, "", articleState);
			}
		});
	},
	//重新审核文章(非10000公众号的)
	pushArticleToExamine : function(obj, articleState) {
		var _this = $(obj);
		var objectId = _this.closest(".articleBtn").attr("data-id");
		var userId = $("#loginUserId").val();

		$.ajax({
			type:'POST',
			url : '/mp/updateArticleByOid',
			dataType:"json",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			data:{
				objectId : objectId,
				articleState : articleState
			},
			crossDomain: true,
			async: false,
			success:function(result){
				MP.updateArticleUE = '';
				//修改成功之后，再调用加载列表的方法
				if(userId == 10000)
					MP.articleVerify();
				else
					MP.commonArticleVerify();
			}
		});
	},
	//flag为1表示修改的返回操作，2表示预览的返回操作
	button_back : function(flag, obj) {
		var userId = $("#loginUserId").val();
		var _this = $(obj);
		var dataAttr = _this.attr("data-attr");
		if(flag == 1) {
			if(userId == 10000) {
				$('#articleManager').show();
				$('#updateArticleOuter').hide();
			} else {
				$("#commonArticleOuter").show();
				$("#updateCommonArticleOuter").hide();
			}
		} else if(flag == 2) {
			if(userId == 10000) {
				$('#previewArticleOuter').hide();
				$('#articleManager').show();
			} else {
				$('#previewCommonArticleOuter').hide();
				$('#commonArticleOuter').show();
			}
		} else if(flag == 3) {
			if(userId == 10000) {
				$('#articleAuditing').show();
				$('#updateArticleOuter').hide();
			}
		} else if(flag == 4) {
			if(userId == 10000) {
				$('#' + dataAttr).show();
				$('#previewArticleOuter').hide();
			}
		}
	},
	//待审核数据加载
	checkPending : function() {
		var page=0;
		var lock=0;
		var currentPageIndex;// 当前页码数
		var currentCount;// 当前总数
		layui.use(['form','layer','table'],function(){
			var form = layui.form,
				layer = parent.layer === undefined ? layui.layer : top.layer,
				$ = layui.jquery,
				laydate = layui.laydate,
				laytpl = layui.laytpl,
				table = layui.table;
			//待审核列表
			var tableInArticle = table.render({
				elem: '#article_list'
				,toolbar: '#toolbarArticles'
				,url:"/mp/articleList"+"?articleState=22&pageSize=15"//待审核的状态为2,传为22，到后台判断22时候，要处理userId为非10000的
				,id: 'article_list'
				,page: true
				,curr: 0
				,pageSize: 15
				,limit:15
				,limits:[15,50,100,1000,10000]
				,groups: 7
				,cols: [[ //表头
					{type:'checkbox',fixed:'left'}// 多选
					,{field: 'userId', title: '用户Id',sort:'true', width:100}
					,{field: 'articleFirstName', title: '文章标题',sort:'true', width:235}
					,{field: 'articleSecondName', title: '文章副标题',sort:'true', width:200}
					,{fixed: 'right', width: 370,title:"操作", align:'left', toolbar: '#articleListBar'}
				]]
				,done:function(res, curr, count){
					if(count==0&&lock==1){
						layer.msg("暂无数据",{"icon":2});
						renderTable();
					}
					lock=0;
					var pageIndex = tableInArticle.config.page.curr;//获取当前页码
					var resCount = res.count;// 获取table总条数
					currentCount = resCount;
					currentPageIndex = pageIndex;
				}
			});

			table.on('tool(article_list)', function(obj){
				var layEvent = obj.event,data = obj.data;
				var articleId = data._id;//文章ID
				if(layEvent === 'articleCheck') {//查看
					MP.previewArticle(null, articleId, 'articleAuditing');
				} else if(layEvent === 'articleRefuse') {//审核拒绝
					//把文章状态articleState修改为3
					$.ajax({
						url : '/mp/updateArticleByOid',
						method : 'POST',
						//加上这句话
						xhrFields: {
							withCredentials: true
						},
						crossDomain: true,
						data : {
							objectId : articleId,
							articleState : 4//4为审核拒绝
						},
						success : function(result) {
							layui.layer.alert("成功" + result);
							//审核通过，再次加载待审核列表
							MP.articleVerify();
						},
						error : function() {
							layui.layer.alert("群发失败");
						}
					})
				} else if(layEvent === 'articleUpdate') {//审核通过
					//把文章状态articleState修改为3
					$.ajax({
						url : '/mp/updateArticleByOid',
						method : 'POST',
						//加上这句话
						xhrFields: {
							withCredentials: true
						},
						crossDomain: true,
						data : {
							objectId : articleId,
							articleState : 3
						},
						success : function(result) {
							layui.layer.alert("成功" + result);
							//审核通过，再次加载待审核列表
							MP.articleVerify();
						},
						error : function() {
							layui.layer.alert("群发失败");
						}
					})
				} else if(layEvent === 'articlePushByTime') {
					layer.open({
						id:1,
						type: 1,
						title:'选择定时时间',
						skin:'layui-layer-rim',
						area:['450px', 'auto'],
						content: ' <div class="row" style="width: 420px;  margin-left:7px; margin-top:10px;">'
							+'<div class="col-sm-12" style="margin-top: 10px">'
							+'<div class="input-group">'
							+'<input id="assign_time" type="text" style="margin-left: 28%;width: 180px;" class="layui-input" placeholder="请指定一个时间">'
							+'</div>'
							+'</div>'
							+'</div>',
						success: function (layero, index) { // 弹窗成功
							laydate.render({
								elem: '#assign_time',//指定元素
								type: 'datetime',
								trigger: 'click',
								done: function (value, date) {
								}
							});
						},
						btn:['确定','取消'],
						btn1: function (index,layero) {
							var assign_time=$('#assign_time').val();
							console.log(assign_time);
							MP.schedulePushArticleByIdTime(assign_time, articleId);
							layer.close(index);
						},
						btn2:function (index,layero) {
							layer.close(index);
						}

					});
				} else if(layEvent === 'articlePushNow') {
					$.ajax({
						url : '/mp/pushNowByOid',
						method : 'POST',
						//加上这句话
						xhrFields: {
							withCredentials: true
						},
						crossDomain: true,
						data : {
							objectId : articleId
						},
						success : function(result) {
							layui.layer.alert("成功" + result);
							//审核通过，再次加载待审核列表
							MP.articleVerify();
						},
						error : function() {
							layui.layer.alert("群发失败");
						}
					})
				}
			});

		})
	},
	//待审核数据加载
	articleRefuse : function() {
		var page=0;
		var lock=0;
		var currentPageIndex;// 当前页码数
		var currentCount;// 当前总数
		layui.use(['form','layer','table'],function(){
			var form = layui.form,
				layer = parent.layer === undefined ? layui.layer : top.layer,
				$ = layui.jquery,
				laydate = layui.laydate,
				laytpl = layui.laytpl,
				table = layui.table;
			//待审核列表
			var tableInArticle = table.render({
				elem: '#articleRefuse_list'
				,toolbar: '#toolbarArticles'
				,url:"/mp/articleList"+"?articleState=44&pageSize=15"//待审核的状态为2,传为22，到后台判断22时候，要处理userId为非10000的
				,id: 'articleRefuse_list'
				,page: true
				,curr: 0
				,limit:15
				,pageSize:15
				,limits:[15,50,100,1000,10000]
				,groups: 7
				,cols: [[ //表头
					{type:'checkbox',fixed:'left'}// 多选
					,{field: 'userId', title: '用户Id',sort:'true', width:100}
					,{field: 'articleFirstName', title: '文章标题',sort:'true', width:235}
					,{field: 'articleSecondName', title: '文章副标题',sort:'true', width:200}
					,{fixed: 'right', width: 370,title:"操作", align:'left', toolbar: '#articleRefuseListBar'}
				]]
				,done:function(res, curr, count){
					if(count==0&&lock==1){
						layer.msg("暂无数据",{"icon":2});
						renderTable();
					}
					lock=0;
					var pageIndex = tableInArticle.config.page.curr;//获取当前页码
					var resCount = res.count;// 获取table总条数
					currentCount = resCount;
					currentPageIndex = pageIndex;
				}
			});

			table.on('tool(articleRefuse_list)', function(obj){
				var layEvent = obj.event,data = obj.data;
				var articleId = data._id;//文章ID
				if(layEvent === 'articleCheck') {//查看
					MP.previewArticle(null, articleId, 'articleAuditing')
				} else if(layEvent === 'articleRefuse') {//审核拒绝
					//把文章状态articleState修改为3
					$.ajax({
						url : '/mp/updateArticleByOid',
						method : 'POST',
						//加上这句话
						xhrFields: {
							withCredentials: true
						},
						crossDomain: true,
						data : {
							objectId : articleId,
							articleState : 4//4为审核拒绝
						},
						success : function(result) {
							layui.layer.alert("成功" + result);
							//审核通过，再次加载待审核列表
							MP.articleVerify();
						},
						error : function() {
							layui.layer.alert("群发失败");
						}
					})
				} else if(layEvent === 'articleUpdate') {//审核通过
					//把文章状态articleState修改为3
					$.ajax({
						url : '/mp/updateArticleByOid',
						method : 'POST',
						//加上这句话
						xhrFields: {
							withCredentials: true
						},
						crossDomain: true,
						data : {
							objectId : articleId,
							articleState : 3
						},
						success : function(result) {
							layui.layer.alert("成功" + result);
							//审核通过，再次加载待审核列表
							MP.articleVerify();
						},
						error : function() {
							layui.layer.alert("群发失败");
						}
					})
				}
			});

		})
	},
	// 自定义菜单
	menu:function(num){
		var html="";
		var body="";
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#4E5465");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#393D49");
		$("#menu").show();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#pushManyText").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#menuPage").show();
		$("#msg_item").hide();
		$("#welcomeText").hide();

		$("#accessVerify").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#commonArticleVerify").hide();

		if($("#parentId").val()==0){
			$("#menu_url").hide();
		}else{
			$("#menu_url").show();
		}

		$.ajax({
			type:'POST',
			url : '/mp/menuList',
			dataType:"json",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			async: false,
			success:function(result){
				if(result.data.length==0){
//					alert("暂无数据");
				}else{

					for(var i=0;i<result.data.length;i++){
						var url="";
						if(result.data[i].url!=undefined){
							url=result.data[i].url;
						}

						html+="<tr><td>"+result.data[i].id+"</td><td>"+result.data[i].name+"</td><td>"+result.data[i].index
							+"</td><td></td><td>"+url
							+"</td><td><button onclick='MP.deleteMenu(\""+result.data[i].id+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>删除</button><button onclick='MP.updateMenu(\""+result.data[i].id+"\",\""+result.data[i].name+"\",\""+result.data[i].index+"\",\""
							+result.data[i].url+"\",\""+result.data[i].desc+"\",\""+result.data[i].menuId+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>修改</button></td><tr>";

						body+="<option value='"+result.data[i].id+"'>"+"二级菜单 - "+result.data[i].name+"</option>";



					}
					if(result.data[0].menuList.length>0){
						for(var j=0;j<result.data[0].menuList.length;j++){
							html+="<tr><td>"+result.data[0].menuList[j].id+"</td><td></td><td>"+result.data[0].menuList[j].index
								+"</td><td>"+result.data[0].menuList[j].name+"</td><td>"+result.data[0].menuList[j].url
								+"</td><td><button onclick='MP.deleteMenu(\""+result.data[0].menuList[j].id+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>删除</button><button onclick='MP.updateMenu(\""+result.data[0].menuList[j].id+"\",\""+result.data[0].menuList[j].name+"\",\""+result.data[0].menuList[j].index+"\",\""
								+result.data[0].menuList[j].url+"\",\""+result.data[0].menuList[j].desc+"\",\""+result.data[0].menuList[j].menuId+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>修改</button></td><tr>";
						}
					}



				}
				$("#menu_td").empty();
				$("#menu_td").append(html);

				$("#parentId").empty();
				$("#parentId").append("<option value='0'>一级菜单</option>");
				$("#parentId").append(body);
				$("#update_parentId").empty();
				$("#update_parentId").append("<option value='0'>一级菜单</option>");
				$("#update_parentId").append(body);
				$("#menu_name").val("");
				$("#menu_num").val("");
				$("#menu_mark").val("");
				$("#menu_url").val("");
			}

		});
	},
	// 添加菜单
	addMenu:function(){
		var reg = new RegExp("^[0-9]*$");
		var obj = $("#menu_num").val();
		if($("#menu_name").val()==""){
			layui.layer.alert("请输入菜单名");
			return;
		}else if($("#menu_num").val()==""){
			layui.layer.alert("请输入排序");
			return;
		}else if(!reg.test(obj)){
			layui.layer.alert("排序必须为数字!");
			return;
		}
		var url="";
		if($("#menu_url").val()!=""){
			url=$("#menu_url").val();
		}

		$.ajax({
			type:'POST',
			url : '/mp/menu/save',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				parentId:$("#parentId").val(),
				menuId:$("#menu_menuId").val(),
				name:$("#menu_name").val(),
				index:$("#menu_num").val(),
				desc:$("#menu_mark").val(),
				urls:url
			},
			dataType:"json",
			async: false,
			success:function(data){
				MP.menu();
				$("#menu_url").hide();// 访问地址
				$("#menu_name").val("");
				$("#menu_num").val("");
				$("#menu_mark").val("");
				$("#menu_url").val("");
			}

		});
	},
	// 修改菜单
	updateMenu:function(id,name,index,url,desc,menuId){
		$("#index").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#pushManyText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index_newMsg").hide();
		$("#index_add").hide();
		$("#index_userSum").hide();
		$("#pushMsg").hide();
		$("#commonArticleVerify").hide();
		$("#update_menu").show();
		$("#update_id").val(id);
		$("#update_name").val(name);
		$("#update_index").val(index);
		$("#accessVerify").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#commonArticleVerify").hide();
		if(url=="undefined"){
			$("#update_urls").val("");
		}else{
			$("#update_urls").val(url);
		}
		if(menuId=="undefined"){
			$("#update_menu_id").val("");
		}else{
			$("#update_menu_id").val(menuId);
		}

		if(desc=="undefined"){
			$("#update_desc").val("");
		}else{
			$("#update_desc").val(desc);
		}

	},
	// 提交修改菜单
	submit_update:function(){
		var reg = new RegExp("^[0-9]*$");
		var obj = $("#update_index").val();
		if($("#update_name").val()==""){
			layui.layer.alert("请输入菜单名");
			return;
		}else if($("#update_index").val()==""){
			layui.layer.alert("请输入排序");
			return;
		}else if(!reg.test(obj)){
			layui.layer.alert("排序必须为数字!");
			return;
		}
		$.ajax({
			type:'POST',
			url:'/mp/menu/saveupdate',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				id:$("#update_id").val(),
				parentId:$("#update_parentId").val(),
				menuId:$("#update_menu_id").val(),
				name:$("#update_name").val(),
				url:$("#update_urls").val(),
				index:$("#update_index").val(),
				desc:$("#update_desc").val()
			},
			async: false,
			success:function(result){
				layui.layer.alert("修改成功");
				MP.menu(0);
			}
		});
	},
	// 消息管理
	msg:function(num){
		if(num==1){
			if(pageIndex>0){
				pageIndex--;
			}else{
				layui.layer.alert("已是第一页");
				return;
			}
		}else if(num==2){
			if(sum!=10){
				layui.layer.alert("已是最后一页");
				return;
			}else{
				pageIndex++;
			}
		}else {
			pageIndex=num;
		}

		var html="";
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#4E5465");
		$("#li_seven").css("background-color","#393D49");
		$("#li_eight").css("background-color","#393D49");

		$("#msg").show();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#pushManyText").hide();
		$("#menu").hide();
		$("#fan").hide();
		$("#index").hide();
		$("#pushMsg").hide();
		$("#msgPage").show();
		$("#msg_item").hide();
		$("#accessVerify").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#commonArticleVerify").hide();
		$("#welcomeText").hide();
		$.ajax({
			type:'POST',
			url : '/mp/msgs',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				pageIndex:pageIndex,
				pageSize:10
			},
			dataType:"json",
			async: false,
			success:function(result){
				if(result.data==null){
					html+="<tr><td>暂无数据</td><td></tr>";
				}else{
					sum=result.data.length;
					for(var i=0;i<result.data.length;i++){
						var msg = result.data[i];
						var content = msg.body;
						if(msg.isEncrypt == 1){
							content = msgCommon.decryptMsg(msg.body,msg.messageId,msg.timeSend);
						}
						var sender = result.data[i].sender;
						html+="<tr><td><img width='40px' onerror='this.src=\"/pages/img/ic_avatar.png\"' src='"+myFn.getImgUrl(sender)+"'>"
							+"</td><td>"+sender+"</td><td>"+msg.nickname+"</td><td><a onclick='MP.findMsgList(\""+sender+"\")'>"+msg.count+"条</a></td><td>"+content+"</td><td><button onclick='MP.openMsg(\""+sender+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>发消息</button></td></tr>";
					}
					$("#msg_td").empty();
					$("#msg_td").append(html);
				}
			}
		});

	},
	// 粉丝管理
	fan:function(num){
		if(num==1){
			if(pageIndex>0){
				pageIndex--;
			}else{
				layui.layer.alert("已是第一页");
				return;
			}
		}else if(num==2){
			if(sum!=10){
				layui.layer.alert("已是最后一页");
				return;
			}else{
				pageIndex++;
			}
		}else if(num==0){
		      pageIndex=num;
		}else{
            if(10*$("#fanInputPage").val()>$("#fanTotalHide").val()){
                layui.layer.alert("您填的页数太大了，没有那么多数据");
                 return;
            }
            pageIndex=$("#fanInputPage").val();
		}
		var html="";
		$("#li_one").css("background-color","#393D49");
		$("#li_two").css("background-color","#393D49");
		$("#li_three").css("background-color","#393D49");
		$("#li_four").css("background-color","#393D49");
		$("#li_five").css("background-color","#393D49");
		$("#li_six").css("background-color","#393D49");
		$("#li_seven").css("background-color","#4E5465");
		$("#li_eight").css("background-color","#393D49");

		$("#pageIndex").show();
		$("#fan").show();
		$("#update_menu").hide();
		$("#index_one").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#pushManyText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#index").hide();
		$("#msg").hide();
		$("#fanPage").show();
		$("#msg_item").hide();
		$("#accessVerify").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#commonArticleVerify").hide();
		$("#welcomeText").hide();
		$.ajax({
			type:'POST',
			url : '/mp/fans',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				pageIndex:pageIndex,
				pageSize:10
			},
			dataType:"json",
			async: false,
			success:function(result){

				if(result.data.pageData==null){
					html+="<tr><td>暂无数据</td><td></tr>";
				}else{
					// console.log("粉丝数据："+JSON.stringify(result.data));
					$("#fanTotal").empty();
					$("#fanTotal").append("共"+result.data.total+"条");
					$("#fanTotalHide").val(result.data.total);
					fanTotalAll = result.data.total;
					sum=result.data.pageData.length;
					for(var i=0;i<result.data.pageData.length;i++){
						var toUserId = result.data.pageData[i].toUserId;
						html+="<tr><td><img width='40px' onerror='this.src=\"/pages/img/ic_avatar.png\"'  src='"+myFn.getImgUrl(toUserId)+"'></td><td>"+toUserId
							+"</td><td>"+result.data.pageData[i].toNickname+"</td><td><button onclick='MP.deleteUser(\""+toUserId+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>删除</button></td></tr>";
					}
					$("#fan_td").empty();
					$("#fan_td").append(html);
				}
			}
		});


	},
	// 新消息
	newMsg:function(num){
		if(num==1){
			if(pageIndex>0){
				pageIndex--;
			}else{
				layui.layer.alert("已是第一页");
				return;
			}
		}else if(num==2){
			if(sum!=10){
				layui.layer.alert("已是最后一页");
				return;
			}else{
				pageIndex++;
			}
		}else {
			pageIndex=num;
		}
		var html="";
		$("#index_one").hide();
		$("#index_newMsg").show();
		$("#newMsgPage").show();
		$.ajax({
			type:"POST",
			url:"/mp/msgs",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				pageIndex:pageIndex,
				pageSize:10
			},
			dataType:"json",
			async: false,
			success:function(result){
				if(result.data.length==0){
//					html+="<tr><td>暂无数据</td><td></tr>";
				}else{
					sum=result.data.length;
					for(var i=0;i<result.data.length;i++){
						var msg = result.data[i];
						var content = msg.body;
						if(msg.isEncrypt == 1){
							content = msgCommon.decryptMsg(msg.body,msg.messageId,msg.timeSend);
						}
						html+="<tr><td>"+msg.sender+"</td><td>"+msg.nickname+"</td><td><a onclick='MP.findMsgList(\""+msg.sender+"\")'>"
							+msg.count+"条</a></td><td>"+content+"</td><td><button onclick='MP.openMsg(\""+msg.sender+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>发消息</button></td></tr>";
					}
					$("#newMsg_tb").empty();
					$("#newMsg_tb").append(html);
				}

			}
		});
	},
	// 打开发消息的面板
	openMsg:function(toUserId){
		$("#index").hide();
		$("#index_one").hide();
		$("#update_menu").hide();
		$("#pushText").hide();
		$("#pushOneText").hide();
		$("#pushManyText").hide();
		$("#menu").hide();
		$("#msg").hide();
		$("#fan").hide();
		$("#index_newMsg").hide();
		$("#index_add").hide();
		$("#index_userSum").hide();
		$("#pushMsg").show();
		$("#msg_item").hide();
		$("#accessVerify").hide();
		$("#articleVerify").hide();
		$("#editArticles").hide();
		$("#commonArticleVerify").hide();
//		alert(toUserId);

		$("#toUserId").html(toUserId);
	},
	// 新增用户
	newAddUser:function(num){


		var a="a";
		var html="";
		$("#addPage").show();
		$("#index_one").hide();
		$("#index_add").show();
		if(num==1){
			if(pageIndex>0){
				pageIndex--;
			}else{
//				layui.layer.open({
//					type:1,
//					title:'ssss',
//					offset:{
//						Math.random()*($(window).height()-300)
//						 ,Math.random()*($(window).width()-390)
//					}
//				});
				layui.layer.alert("已是第一页");
				return;
			}
		}else if(num==2){
			if(sum!=10){
				layui.layer.alert("已是最后一页");
				return;
			}else{
				pageIndex++;
			}
		}else {
			pageIndex=num;
		}
		$.ajax({
			type:"POST",
			url:"/mp/fans",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				pageIndex:pageIndex,
				pageSize:10
			},
			dataType:"json",
			async: false,
			success:function(result){
				if(result.data.pageData==null){
					html+="<tr><td>暂无数据</td><td></tr>";
				}else{
					$("#addTotal").empty();
					$("#addTotal").append("共"+result.data.total+"条");
					sum=result.data.pageData.length;
					for(var i=0;i<result.data.pageData.length;i++){
						html+="<tr><td>"+result.data.pageData[i].toUserId+"</td><td>"+result.data.pageData[i].toNickname+"</td><td><button onclick='MP.deleteUser(\""+result.data.pageData[i].toUserId+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>删除</button></td></tr>";
					}
					$("#add_tb").empty();
					$("#add_tb").append(html);
				}
			}
		});

	},
	// 用户总数
	userSum:function(num){
		if(num==1){
			if(pageIndex>0){
				pageIndex--;
			}else{
				layui.layer.alert("已是第一页");
				return;
			}
		}else if(num==2){
			if(sum!=10){
				layui.layer.alert("已是最后一页");
				return;
			}else{
				pageIndex++;
			}
		}else {
			pageIndex=num;
		}
		var html="";
		$("#index_one").hide();
		$("#userSumPage").show();
		$("#index_userSum").show();

		$.ajax({
			type:"POST",
			url:"/mp/fans",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				pageIndex:pageIndex,
				pageSize:10
			},
			dataType:"json",
			async: false,
			success:function(result){
				sum=result.data.pageData.length;
				if(result.data.pageData==null){
					html+="<tr><td>暂无数据</td><td></tr>";
				}else{
					$("#userTotal").empty();
					$("#userTotal").append("共"+result.data.total+"条");
					for(var i=0;i<result.data.pageData.length;i++){
						html+="<tr><td>"+result.data.pageData[i].toUserId+"</td><td>"+result.data.pageData[i].toNickname+"</td><td><button onclick='MP.deleteUser(\""+result.data.pageData[i].toUserId+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>删除</button></td></tr>";
					}
					$("#sum_tb").empty();
					$("#sum_tb").append(html);
				}
			}
		});
	},
	// 发送群发消息
	pushTextToAll:function() {
		var title =$("#textbody").val();
		if(title=='' ){
			layui.layer.alert("文本不能为空");
			return false;
		}
		uploading("文本发送中");
		/* alert(body); */
		$.ajax({
			url : '/mp/textToAll',
			method : 'POST',
			data : {
				title : title
			},
			success : function(result) {
				if (result.resultCode == 1) {
				    uploadClose();
					layui.layer.alert("群发成功");
					$("#textbody").val("");
				} else {
				    uploadClose();
					layui.layer.alert("群发失败");
				}
			}
		})
	},
	// 发送群发消息
	saveWelcomeText:function() {
		var body=$("#welecomeTextbody").val();
		/* alert(body); */
		$.ajax({
			url : '/mp/saveWelcomeText',
			method : 'POST',
			data : {
				title : body
			},
			success : function(result) {
				if (result.resultCode == 1) {
					layui.layer.alert("保存成功");
				} else {
					layui.layer.alert("保存失败");
				}
			}
		})
	},
	pushImgToAll:function() {
		var title = $("#img01")[0].src;
		var bodyImg = $("#pushBodyImgUrl10").val();
		if(bodyImg ==''){
			layui.layer.alert("请先上传图片");
			return false;
		}
		var sizeImg = $("#pushBodyImgUrl10")[0].files[0].size;
		var nameImg = bodyImg;
		var sfIndexImg=nameImg.lastIndexOf(".");//后缀位置的.的位置
		var extImg=nameImg.substring(sfIndexImg,nameImg.length).toUpperCase();//截取后缀
		if(extImg !='.PNG' && extImg !='.GIF' && extImg !='.JPG' && extImg !='.JPEG' && extImg !='.BMP'){
			layui.layer.alert("文件类型错误,请上传正确图片类型(png、gif、jpg、jpeg、bmp)");
			return false;
		}
		if(sizeImg/1024/1024>5){
			layui.layer.alert("图片大小不能大于5M");
			return false;
		}
		var img = $("#pushBodyImgUrl10")[0].files[0];
		var formData = new FormData();
		formData.append("file",img);
		uploading("图片发送中");
		$.ajax({
			url : '/mp/filesToAll',
			method : 'POST',
			processData: false,
			contentType:false,
			data : formData,
			success : function(result) {
				if (result.resultCode == 1) {
				    uploadClose();
					layui.layer.alert("群发成功");
					$("#pushBodyImgUrl10").val("");
					$("#img01")[0].src="";
					$("#img01").addClass("hide");
				} else {
				    uploadClose();
					layui.layer.alert("群发失败");
				}
			}
		})
	},
	pushVideoToAll:function() {
		var title = $("#img02")[0].src;
		var bodyVideo = $("#pushBodyVideoUrl10").val();
		if(bodyVideo ==''){
			layui.layer.alert("请先上传视频");
			return false;
		}
		var sizeVideo = $("#pushBodyVideoUrl10")[0].files[0].size;
		var nameVideo = bodyVideo;
		var sfIndexVideo=nameVideo.lastIndexOf(".");//后缀位置的.的位置
		var extVideo=nameVideo.substring(sfIndexVideo,nameVideo.length).toUpperCase();//截取后缀
		if(extVideo!='.MP4' ){
			layui.layer.alert("文件类型错误,请上传正确视频类型(.mp4)");
			return false;
		}
		if(sizeVideo/1024/1024>40){
			layui.layer.alert("视频大小不能大于40M");
			return false;
		}
		/* alert(body); */
		var video = $("#pushBodyVideoUrl10")[0].files[0];
		var formData = new FormData();
		formData.append("file",video);
		uploading("视频发送中");
		$.ajax({
			url : '/mp/filesToAll',
			method : 'POST',
			processData: false,
			contentType:false,
			data : formData,
			success : function(result) {
				if (result.resultCode == 1) {
				    uploadClose();
					layui.layer.alert("群发成功");
					$("#pushBodyVideoUrl10").val("");
					$("#img02")[0].src="";
					$("#img02").addClass("hide");
				} else {
				    uploadClose();
					layui.layer.alert("群发失败");
				}
			}
		})
	},

	// 消息管理中的发消息
	pusgMsg:function(){
		var userId=$("#toUserId").html();
		// alert(userId);
		var body=$("#Msgbody").val();
		$.ajax({
			url : '/mp/msg/send',
			method : 'POST',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data : {
				body :body,
				toUserId : userId,
				type:1
			},
			success : function(result) {
				if (result.resultCode == 1) {
					layui.layer.alert("发送成功");
					$("#Msgbody").val("");
				} else {
					layui.layer.alert("发送失败");
				}
			}
		})
	},
	// 发送单条图文消息
	pushOneToAll:function(flag, obj, userId) {
		var title=$("#pushbody").val()
		if(null == title  || "" == title || undefined == title){
			layui.layer.alert("请输入标题");
			return;
		}
		var sub=$("#pushbodyTitle").val();
		if(null == sub  || "" == sub || undefined == sub){
			layui.layer.alert("请输入小标题");
			return;
		}
		var img=$("#pushbodyImgUrl").val();
		if(null == img  || "" == img || undefined == img){
			layui.layer.alert("请输入图片Url");
			return;
		}
		var url=$("#pushbodyHtmlUrl").val();
		if(null == url  || "" == url || undefined == url){
			layui.layer.alert("请输入网页Url");
			return;
		}
		var articleSource = 1;
		if(obj) {
			var _this = $(obj);
			articleSource = _this.attr("data-as");
		}

		if(flag == 2) {
			$.ajax({
				url : '/mp/saveArticle',
				method : 'POST',
				//加上这句话
				xhrFields: {
					withCredentials: true
				},
				crossDomain: true,
				data : {
					title:title,
					sub:sub,
					img:img,
					articleUrl:url,
					articleState : 2,//2为待审核状态
					articleSource : 2//2为外部url的文章
				},
				success : function(result) {
					if (result.resultCode == 1) {
						layui.layer.alert("提交审核成功");
						$("#pushbody").val("");
						$("#pushbodyTitle").val("");
						$("#pushbodyImgUrl").val("");
						$("#pushbodyHtmlUrl").val("");
					} else {
						layui.layer.alert("提交审核失败");
					}
				},
				error : function() {
					layui.layer.alert("提交审核失败");
				}
			})
		} else if(flag == 1) {
			var push_userId = 0;
			if(userId) push_userId = userId;
			//要处理是直接发送，还是普通公众号的编辑后的发送
			if(articleSource == 1) {
				$.ajax({
					url : '/mp/pushToAll',
					method : 'POST',
					//加上这句话
					xhrFields: {
						withCredentials: true
					},
					crossDomain: true,
					data : {
						title:title,
						sub:sub,
						img:img,
						url:url,
						userId:push_userId
					},
					success : function(result) {
						if (result.resultCode == 1) {
							layui.layer.alert("群发成功");
							$("#pushbody").val("");
							$("#pushbodyTitle").val("");
							$("#pushbodyImgUrl").val("");
							$("#pushbodyHtmlUrl").val("");
						} else {
							layui.layer.alert("群发失败");
						}
					},
					error : function() {
						layui.layer.alert("群发失败");
					}
				})
			} else if(articleSource == 2) {
				var objectId = _this.attr("data-oid");
				var userId = _this.attr("data-uid");
				$.ajax({
					type:'POST',
					url : '/mp/updateArticleByOid',
					dataType:"json",
					//加上这句话
					xhrFields: {
						withCredentials: true
					},
					data:{
						objectId:objectId,
						title:title,
						sub:sub,
						img:img,
						articleUrl:url
					},
					crossDomain: true,
					async: false,
					success:function(result){
						//修改成功之后，群发
						MP.pushOneToAll(1, null, userId);
					}
				});
			}
		}
	},
	// 发送多条图文消息
	pushManyToAll:function () {

		var bodyVal = $("#body").val();
		var bodyHtmlUrlVal = $("#bodyHtmlUrl").val();
		var bodyImgUrlVal = $("#bodyImgUrl").val();
		if(null == bodyVal || "" == bodyVal || undefined == bodyVal){
			layui.layer.alert("请输入标题");
			return;
		}
		if(null == bodyImgUrlVal || "" == bodyImgUrlVal || undefined == bodyImgUrlVal){
			layui.layer.alert("请输入图片Url");
			return;
		}
		if(null == bodyHtmlUrlVal || "" == bodyHtmlUrlVal || undefined == bodyHtmlUrlVal){
			layui.layer.alert("请输入网页Url");
			return;
		}
		var title=new Array();
		title.push(bodyVal);
		var url=new Array();
		url.push(bodyHtmlUrlVal);
		var img=new Array();
		img.push(bodyImgUrlVal);

		for(var j=2;j<=i;j++){
			var fVal = $("#f"+j).val();
			var dVal = $("#d"+j).val();
			var cVal = $("#c"+j).val();
			if(null == fVal || "" == fVal || undefined == fVal){
				layui.layer.alert("请输入标题");
				return;
			}
			if(null == cVal || "" == cVal || undefined == cVal){
				layui.layer.alert("请输入图片Url");
				return;
			}
			if(null == dVal || "" == dVal || undefined == dVal){
				layui.layer.alert("请输入网页Url");
				return;
			}

			title.push($("#f"+j).val());
			url.push($("#d"+j).val());
			img.push($("#c"+j).val());
		}
		$.ajax({
			type : 'POST',
			url : '/mp/manyToAll',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			dataType : 'json',
			data : {
				title:title,
				url:url,
				img:img
			},
			async:false,
			traditional: true,
			success : function(result) {
				if (result.resultCode == 1) {

					$("#body").val("");
					$("#bodyImgUrl").val("");
					$("#bodyHtmlUrl").val("");
					for(var j=2;j<=i;j++){
						$("#f"+j).hide();
						$("#f"+j).val("");
						$("#d"+j).hide();
						$("#d"+j).val("");
						$("#c"+j).hide();
						$("#c"+j).val("");
					}
					title=[];
					url=[];
					img=[];
					i = 1;// 重置
					layui.layer.alert("群发成功");
				} else {
					alert("群发失败");
				}
			},
			error : function(result) {

			}
		})
	},
	// 新增
	add:function(){
		i++;
		var table="<div style='margin-top: 1%'><input id='f"+i+"' name='title' class='layui-input' style='width: 20%;display: inline;margin-right: 1.3%' placeholder='请输入标题'>"
			+"<input id='c"+i+"' name='img' class='layui-input' style='width: 20%;display: inline;margin-right: 1.3%' placeholder='请输入图片url'>"
			+"<input id='d"+i+"' name='url' class='layui-input' style='width: 20%;display: inline;margin-right: 1.3%' placeholder='请输入网页url'></div>";

		$("#tb").append(table);

	},
	// 删除用户
	deleteUser:function(userId){
		$.ajax({
			type:"POST",
			url:"/mp/fans/delete",
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				toUserId:userId
			},
			async: false,
			success:function(result){
				layui.layer.alert("删除成功");
				MP.fan(0);
			}

		});
	},
	findMsgList:function(toUserId){
		$("#newMsg_item").show();
		$("#msg_item").show();
		$("#index_newMsg").hide();
		$("#msg").hide();
		var html="";
		$.ajax({
			type:'POST',
			url:'/mp/msg/list',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				toUserId:toUserId
			},
			async:false,
			success:function(result){
				if(result.data==null){
					html+="<tr><td>暂无数据</td><td></tr>";
				}else{
					for(var i=0;i<result.data.length;i++){
						var msg = result.data[i];
						var sender = msg.sender;
						var plaintext = msg.content;
						// 消息解密处理
						if(1 == msg.isEncrypt){
							// 明文
							plaintext = msgCommon.decryptMsg(msg.content,msg.messageId,msg.timeSend);
							console.log(" msg : "+msg.content+"      role:   "+msg.isEncrypt+"       msgId:  "+msg.messageId+"    timeSend:  "+msg.timeSend +" plaintext:   "+plaintext);
						}

						html+="<tr><td><img width='40px' onerror='this.src=\"/pages/img/ic_avatar.png\"' src='"+myFn.getImgUrl(sender)+"'></td><td>"+sender+"</td><td>"
							+result.data[i].nickname+"</td><td>"+plaintext+"</td><td><button onclick='MP.openMsg(\""+sender+"\")' class='layui-btn' style='height:30px;line-height:30px;border-radius:3px'>发消息</button></td></tr>";
					}
					$("#newMsg_body").empty();
					$("#newMsg_body").append(html);
					$("#Msg_body").empty();
					$("#Msg_body").append(html);
				}
			}
		});
	},
	// select框变化
	change:function(){
		if($("#parentId").val()==0){
			$("#menu_url").hide();
			$("#menu_menuId").hide();
		}else{
			$("#menu_url").show();
			$("#menu_menuId").show();
		}
	},
	// 删除菜单
	deleteMenu:function(id){
		$.ajax({
			type:'POST',
			url:'/mp/menu/delete',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data:{
				id:id
			},
			async:false,
			success:function(result){
				layui.layer.alert("删除成功");
				MP.menu(0);
			}
		})
	},
	limit:function(index){
		layui.use('laypage', function(){
			var laypage = layui.laypage;
			console.log($("#pageCount").val());
			var count=$("#pageCount").val();
			//执行一个laypage实例
			laypage.render({
				elem: 'laypage'
				,count: count
				,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
				,jump: function(obj){
					console.log(obj)
					if(index==1){
						Key.keyword_list(1,obj.limit)
						index=0;
					}else{
						Key.keyword_list(obj.curr,obj.limit)
					}

				}
			})
		})
	},
	// 返回
	return_btn:function(){
		$("#menu").show();
		$("#update_menu").hide();
	},
	pushBodyImg:function(){
		var file = $("#pushBodyImgUrl10")[0].files[0];
		var objUrl = MP.getObjectURL(file);
		//var objUrl = $("#pushBodyImgUrl10")[0].files[0];
		var size = $("#pushBodyImgUrl10")[0].files[0].size;
		console.log("size = "+size) ;
		console.log("objUrl = "+objUrl) ;
		if (objUrl)
		{
			$("#img01").attr("src", objUrl);
			console.log("objUrl = "+objUrl) ;
			$("#img01").removeClass("hide");
		}
	},
	//上传封面图片
//	pushArticleImg : function() {
//		//准备数据，把图片上传到upload服务器
//		var img = $("#pushArticleImgUrl10")[0].files[0];
//		var formData = new FormData();
//		formData.append("file",img);
//		$.ajax({
//			url : '/mp/passArticleCoverImg',
//			method : 'POST',
//			processData: false,
//			contentType:false,
//			data : formData,
//			success : function(result) {
//				console.log(result);
//				$("#pushArticleImgUrl10").attr("attr", result.resultMsg);
//			}
//		})
//	},
	//上传封面图片
//	pushArticleImg_update : function() {
//		//准备数据，把图片上传到upload服务器
//		var img = $("#pushArticleImgUrl10_update")[0].files[0];
//		var formData = new FormData();
//		formData.append("file",img);
//		$.ajax({
//			url : '/mp/passArticleCoverImg',
//			method : 'POST',
//			processData: false,
//			contentType:false,
//			data : formData,
//			success : function(result) {
//				console.log(result);
//				$("#pushArticleImgUrl10_update").attr("attr", result.resultMsg);
//			}
//		})
//	},
	pushBodyVideo:function(){
		var file = $("#pushBodyVideoUrl10")[0].files[0];
		var objUrl = MP.getObjectURL(file);
		var size = $("#pushBodyVideoUrl10")[0].files[0].size;
		console.log("size = "+size) ;
		console.log("objUrl = "+objUrl) ;
		if (objUrl)
		{
			$("#img02").attr("src", objUrl);
			console.log("objUrl = "+objUrl) ;
			$("#img02").removeClass("hide");
		}
	},


	getObjectURL:function(file){
		var url = null ;
		if (window.createObjectURL!=undefined)
		{ // basic
			url = window.createObjectURL(file) ;
		}
		else if (window.URL!=undefined)
		{
			// mozilla(firefox)
			url = window.URL.createObjectURL(file) ;
		}
		else if (window.webkitURL!=undefined) {
			// webkit or chrome
			url = window.webkitURL.createObjectURL(file) ;
		}
		return url ;

	},
	clearVideo:function(){
		$("#pushBodyVideoUrl10").val("");
		$("#img02")[0].src="";
		$("#img02").addClass("hide");
	},
	clearImg:function(){
		$("#pushBodyImgUrl10").val("");
		$("#img01")[0].src="";
		$("#img01").addClass("hide");
	},
	getLocalTime:function(time){
		var date = new Date(time * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
		var Y = date.getFullYear() + '-';
		var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
		var D = date.getDate() + ' ';
		var h = date.getHours() + ':';
		var m = (date.getMinutes()<10?'0'+(date.getMinutes()):date.getMinutes()) + ':';
		var s = (date.getSeconds()<10?'0'+(date.getSeconds()):date.getSeconds());
		return Y+M+D+h+m+s;
	},
	access:function(){
		layui.use(['form','layer','laydate','table','laytpl'],function(){
			var form = layui.form,
				layer = parent.layer === undefined ? layui.layer : top.layer,
				$ = layui.jquery,
				table = layui.table;
			//管理员列表
			var tableInsHolding = table.render({
				elem: '#examine_listHolding'
				,toolbar: '#toolbarUsers'
				,url:"/mp/accessPnExamineHoldingList/page"
				,id: 'examine_listHolding'
				,page: true
				,curr: 0
				,limit:15
				,limits:[15,50,100,1000,10000]
				,groups: 6
				,cols: [[ //表头
					{type:'checkbox',fixed:'left'}
					,{field: 'nickName', title: '姓名', width:150}
					,{field: 'reason', title: '入驻理由', width:150}
					,{field: 'telephone', title: '手机号', width:150}
					,{field: 'raiseTime', title: '申请时间',sort:'true', width:200,templet: function(d){
							return UI.getLocalTime(d.raiseTime);
						}}
					,{fixed: 'right', width: 250,title:"操作", align:'left', toolbar: '#examineListBarHolding'}
				]]
				,done:function(res){
					var pageIndex = tableInsHolding.config.page.curr;//获取当前页码
					var resCount = res.count;// 获取table总条数
					currentCount = resCount;
					currentPageIndex = pageIndex;
				}
			});

			// 管理员列表
			var tableInsPass = table.render({
				elem: '#examine_listPass'
				,url:"/mp/accessPnExaminePassList/page"
				,id: 'examine_listPass'
				,page: true
				,curr: 0
				,limit:15
				,limits:[15,50,100,1000,10000]
				,groups: 6
				,cols: [[ //表头
					{field: 'nickName', title: '姓名', width:150}
					,{field: 'reason', title: '入驻理由', width:150}
					,{field: 'telephone', title: '手机号', width:150}
					,{field: 'examineTime', title: '通过时间',sort:'true', width:200,templet: function(d){
							return UI.getLocalTime(d.examineTime);
						}}
					,{fixed: 'right', width: 250,title:"操作", align:'left', toolbar: '#examineListBarPass'}
				]]
				,done:function(res){
					var pageIndex = tableInsPass.config.page.curr;//获取当前页码
					var resCount = res.count;// 获取table总条数
					currentCount1 = resCount;
					currentPageIndex1 = pageIndex;
				}
			});

			//管理员列表
			var tableInsReject = table.render({
				elem: '#examine_listReject'
				,url:"/mp/accessPnExamineRejectList/page"
				,id: 'examine_listReject'
				,page: true
				,curr: 0
				,limit:15
				,limits:[15,50,100,1000,10000]
				,groups: 6
				,cols: [[ //表头
					{field: 'nickName', title: '姓名', width:150}
					,{field: 'reason', title: '入驻理由', width:150}
					,{field: 'telephone', title: '手机号', width:150}
					,{field: 'rejectReason', title: '驳回理由', width:150}
					,{field: 'examineTime', title: '通过时间',sort:'true', width:200,templet: function(d){
							return UI.getLocalTime(d.examineTime);
						}}
					// ,{fixed: 'right', width: 250,title:"操作", align:'left', toolbar: '#examineListBarReject'}
				]]
				,done:function(res){
					var pageIndex = tableInsReject.config.page.curr;//获取当前页码
					var resCount = res.count;// 获取table总条数
					currentCount2 = resCount;
					currentPageIndex2 = pageIndex;
				}
			});

			//列表操作
			table.on('tool(examine_listHolding)', function(obj){
				var layEvent = obj.event, data = obj.data;
				console.log("pass:"+JSON.stringify(data));
				if(layEvent === 'pass'){ //移出
					MP.checkPassUsersImpl(data.id,data.userId,1);
				}else if(layEvent === 'reject'){
					MP.checkRejectUsersImpl(data.id,data.userId,1);
				}
			});

			//列表操作
			table.on('tool(examine_listPass)', function(obj){
				var layEvent = obj.event, data = obj.data;
				console.log("locking:"+JSON.stringify(data));
				if(layEvent === 'locking') {//锁定
					layer.confirm('确定禁用该用户？',{icon:3, title:'提示信息'},function(index) {
						MP.lockingImpl(data, 1,index);
					})
				}else if(layEvent === 'unlocking'){//解锁
					layer.confirm('确定解禁该用户？',{icon:3, title:'提示信息'},function(index) {
						MP.lockingImpl(data,0,index);
					})
				}
			});

			//搜索
			$(".search_examineListHolding").on("click",function(){
				// 关闭超出宽度的弹窗
				$(".layui-layer-content").remove();
				console.log(" ======>>>>>>> search Admin Test "+$(".admin_keywordHolding").val());

				table.reload("examine_listHolding",{
					page: {
						curr: 1 //重新从第 1 页开始
					},
					where: {
						keyWorld : $(".admin_keywordHolding").val()  //搜索的关键字
					}
				})
				$(".admin_keywordHolding").val("");
			});

			$(".search_examineListPass").on("click",function(){
				// 关闭超出宽度的弹窗
				$(".layui-layer-content").remove();
				console.log(" ======>>>>>>> search Admin Test "+$(".admin_keywordPass").val());

				table.reload("examine_listPass",{
					page: {
						curr: 1 //重新从第 1 页开始
					},
					where: {
						keyWorld : $(".admin_keywordPass").val()  //搜索的关键字
					}
				})
				$(".admin_keywordPass").val("");
			});

			$(".search_examineListReject").on("click",function(){
				// 关闭超出宽度的弹窗
				$(".layui-layer-content").remove();
				console.log(" ======>>>>>>> search Admin Test "+$(".admin_keywordReject").val());

				table.reload("examine_listReject",{
					page: {
						curr: 1 //重新从第 1 页开始
					},
					where: {
						keyWorld : $(".admin_keywordReject").val()  //搜索的关键字
					}
				})
				$(".admin_keywordReject").val("");
			});
		})
	},
	lockingImpl: function (data,status,index) {
		$.ajax({
			url : '/mp/pnExamineLocking',
			method : 'POST',
			crossDomain: true,
			data : {
				"accessId" :data.id,
				"accountStatus":status
			},
			success : function(result) {
				layer.close(index);//关闭弹框
				if (result.resultCode == 1) {
					// obj.del();
					Common.tableReload(currentCount,currentPageIndex,1,"examine_listHolding");
					Common.tableReload(currentCount1,currentPageIndex1,1,"examine_listPass");
					Common.tableReload(currentCount2,currentPageIndex2,1,"examine_listReject");
				} else {
					layui.layer.alert("失败");
				}
			},
			error : function() {
				layui.layer.alert("失败");
			}
		})
	},
	checkPassUsers:function(){
		// 多选操作通过
		var checkStatus = layui.table.checkStatus('examine_listHolding'); //idTest 即为基础参数 id 对应的值
		var accessIds = new Array();
		var userIds = new Array();
		console.log("新版："+checkStatus.data) //获取选中行的数据
		console.log("新版："+checkStatus.data.length) //获取选中行数量，可作为是否有选中行的条件
		console.log("新版："+checkStatus.isAll ) //表格是否全选
		for (var i = 0; i < checkStatus.data.length; i++){
			accessIds.push(checkStatus.data[i].id);
			userIds.push(checkStatus.data[i].userId)
		}
		console.log(accessIds);
		console.log(userIds);
		if(0 == checkStatus.data.length){
			layer.msg("请勾选要通过的行");
			return;
		}
		MP.checkPassUsersImpl(accessIds.join(","),userIds.join(","),checkStatus.data.length);
	},
	checkRejectUsers:function(){
		// 多选操作驳回
		var checkStatus = layui.table.checkStatus('examine_listHolding'); //idTest 即为基础参数 id 对应的值
		var accessIds = new Array();
		var userIds = new Array();
		console.log("新版："+checkStatus.data) //获取选中行的数据
		console.log("新版："+checkStatus.data.length) //获取选中行数量，可作为是否有选中行的条件
		console.log("新版："+checkStatus.isAll ) //表格是否全选
		for (var i = 0; i < checkStatus.data.length; i++){
			accessIds.push(checkStatus.data[i].id);
			userIds.push(checkStatus.data[i].userId)
		}
		console.log(accessIds);
		console.log(userIds);
		if(0 == checkStatus.data.length){
			layer.msg("请勾选要驳回的行");
			return;
		}
		MP.checkRejectUsersImpl(accessIds.join(","),userIds.join(","),checkStatus.data.length);
	},
	checkPassUsersImpl:function(accessId,userId,checkLength){
		layer.confirm('确定通过该申请？',{icon:3, title:'提示信息'},function(index){
			$.ajax({
				url : '/mp/accessPnExamineHoldingList/pass',
				method : 'POST',
				crossDomain: true,
				data : {
					"accessId" :accessId,
					"userId" : userId
				},
				success : function(result) {
					layer.close(index);//关闭弹框
					if (result.resultCode == 1) {
						// obj.del();
						Common.tableReload(currentCount,currentPageIndex,checkLength,"examine_listHolding");
						Common.tableReload(currentCount1,currentPageIndex1,checkLength,"examine_listPass");
					} else {
						layui.layer.alert("失败");
					}
				},
				error : function() {
					layui.layer.alert("失败");
				}
			})

		})
	},
	checkRejectUsersImpl:function(accessId,userId,checkLength){
		layer.confirm('确定驳回该申请？',{icon:3, title:'提示信息'},function(index){
			layui.layer.open({
				title:"驳回原因",
				type: 1,
				btn:["确定","取消"],
				area: ['300px'],
				content: '<div id="mdifyPassword" class="layui-form" style="margin:20px 40px 10px 40px;;">'
					+   '<div class="layui-form-item">'
					+      '<div class="layui-input-block" style="margin: 0 auto;">'
					+        '<textarea id="rejectbody" required name="rejectReason" class="layui-textarea"  placeholder="请输入驳回理由"  maxlength="500" ></textarea>'
					+      '</div>'
					+    '</div>'
					+'</div>'

				,yes: function(index, layero){ //确定按钮的回调
					var rejectBody = $("#rejectbody").val();
					$.ajax({
						url : '/mp/accessPnExamineHoldingList/reject',
						method : 'POST',
						crossDomain: true,
						data : {
							"accessId" :accessId,
							"userId" : userId,
							"rejectReason":rejectBody
						},
						success : function(result) {
							layer.close(index); //关闭弹框
							if (result.resultCode == 1) {
								// obj.del();
								Common.tableReload(currentCount,currentPageIndex,checkLength,"examine_listHolding");
								// Common.tableReload(currentCount1,currentPageIndex1,1,"examine_listPass");
								Common.tableReload(currentCount2,currentPageIndex2,checkLength,"examine_listReject");
							} else {
								layui.layer.alert("失败");
							}
						},
						error : function() {
							layui.layer.alert("失败");
						}
					})
				}
			});
			layer.close(index);
		})
	},
	//向后台请求添加一个定时器，并处理(这个是添加)当前文章
	schedulePushArticle : function(time) {
		var ttContent = MP.editArticleUE.getContent();
		var aticleCover = $("#project_article_update").html();
		var articleFirstName = $("#articleFirstName").val();
		var articleSecondName = $("#articleSecondName").val();
		var articleState = 5;//定时发送文章状态直接，设置为5
		//要得到一个定时发送的时间
		$.ajax({
			url : '/mp/schedulePushArticle',
			method : 'POST',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data : {
				passTimeStr : time,
				title : articleFirstName,
				sub : articleSecondName,
				img : aticleCover,
				content : ttContent,
				articleState : articleState
			},
			success : function(result) {
				layui.layer.alert("成功" + result);
				//定时群发成功，清空表单
				emptyArticleEdit();
			},
			error : function() {
				layui.layer.alert("群发失败");
			}
		})
	},
	//向后台请求添加一个定时器，并处理(这个是修改)当前文章,并发送
	schedulePushCommonArticle : function(time) {
		var ttContent = MP.updateArticleUE.getContent();
		var aticleCover = $("#project_article_update").html();
		var articleFirstName = $("#articleFirstName_update").val();
		var articleSecondName = $("#articleSecondName_update").val();
		var articleState = 5;//定时发送文章状态直接，设置为5

		var objectId = $("#articlePushTimingBtn_update").attr("data-oid");
		var userId = $("#articlePushTimingBtn_update").attr("data-uid");
		//要得到一个定时发送的时间
		$.ajax({
			url : '/mp/schedulePushArticle3',
			method : 'POST',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data : {
				passTimeStr : time,
				title : articleFirstName,
				sub : articleSecondName,
				img : aticleCover,
				content : ttContent,
				objectId : objectId,
				userId : userId,
				articleState : articleState
			},
			success : function(result) {
				layui.layer.alert("成功" + result);
			},
			error : function() {
				layui.layer.alert("群发失败");
			}
		})
	},
	//根据文章ID和选择的定时时间，调用定时发送文章的方法
	schedulePushArticleByIdTime : function(time, objectId) {
		//要得到一个定时发送的时间
		$.ajax({
			url : '/mp/schedulePushArticleByIdTime',
			method : 'POST',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data : {
				passTimeStr : time,
				objectId : objectId
			},
			success : function(result) {
				layui.layer.alert("成功" + result);
				//成功之后，再调用加载列表方法
				MP.articleVerify();
			},
			error : function() {
				layui.layer.alert("群发失败");
			}
		})
	},
	//向后台请求添加一个定时器，并处理(这个是修改)当前文章
	schedulePushArticle3 : function(time) {
		var ttContent = MP.updateArticleUE.getContent();
		var aticleCover = $("#project_article_update").html();
		var articleFirstName = $("#commonArticleFirstName_update").val();
		var articleSecondName = $("#commonArticleSecondName_update").val();
		var articleState = 5;//定时发送文章状态直接，设置为5

		var objectId = $("#commonArticleCommitBtn_update").attr("data-oid");
		//要得到一个定时发送的时间
		$.ajax({
			url : '/mp/schedulePushArticle3',
			method : 'POST',
			//加上这句话
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			data : {
				passTimeStr : time,
				title : articleFirstName,
				sub : articleSecondName,
				img : aticleCover,
				content : ttContent,
				objectId : objectId,
				articleState : articleState
			},
			success : function(result) {
				layui.layer.alert("成功" + result);

			},
			error : function() {
				layui.layer.alert("群发失败");
			}
		})
	}
}

MP.init();

layui.use('form', function(){
	var form = layui.form;

	//监听提交
	form.on('submit(articleSearch-keyworld)', function(data){
		var keyword = data.field.title;
		MP.articleList(0, 0, keyword);
		return false;
	});
});
layui.use('laydate', function() {
	var laydate = layui.laydate;
	$(this).removeAttr("lay-key");
	laydate.render({
		elem: '#scheduleArticleTime',
		trigger : 'click',
		type: 'datetime',
		done: function (value, date) {
			console.log(date + "===========--------" + value);
			MP.schedulePushArticle(value);
		}
	});
	laydate.render({
		elem: '#scheduleArticle3Time',
		trigger : 'click',
		type: 'datetime',
		done: function (value, date) {
			console.log(date + "===========--------" + value);
			MP.schedulePushArticle3(value);
		}
	});
	laydate.render({
		elem: '#scheduleCommonArticleTime',
		trigger : 'click',
		type: 'datetime',
		done: function (value, date) {
			console.log(date + "===========--------" + value);
			MP.schedulePushCommonArticle(value);
		}
	});
});
function uploading(content){
    layui.use(['layer','jquery'],function(){
        var layer = layui.layer, $ = layui.jquery;
        layer.load(1, {
            content: content,
            shade: [0.4, '#393D49'],
            // time: 10 * 1000,
            success: function(uploadLayer) {
                uploadLayer.css('padding-left', '30px');
                uploadLayer.find('.layui-layer-content').css({
                    'padding-top': '40px',
                    'width': "161px",
                    'color':'white',
                    'background-position-x': '16px'
                });
            }
        })
    })
}
function uploadClose(){
    layui.use(['layer','jquery'],function(){
        layer.closeAll();
    })
}