!function(t,e){"function"==typeof define&&define.amd?define([],e):"object"==typeof module&&module.exports?module.exports=e():t.echarts=e()}(this,function(){var t,e;!function(){function i(t,e){if(!e)return t;if(0===t.indexOf(".")){var i=e.split("/"),n=t.split("/"),r=i.length-1,a=n.length,o=0,s=0;t:for(var l=0;a>l;l++)switch(n[l]){case"..":if(!(r>o))break t;o++,s++;break;case".":s++;break;default:break t}return i.length=r-o,n=n.slice(s),i.concat(n).join("/")}return t}function n(t){function e(e,o){if("string"==typeof e){var s=n[e];return s||(s=a(i(e,t)),n[e]=s),s}e instanceof Array&&(o=o||function(){},o.apply(this,r(e,o,t)))}var n={};return e}function r(e,n,r){for(var s=[],l=o[r],c=0,u=Math.min(e.length,n.length);u>c;c++){var h,f=i(e[c],r);switch(f){case"require":h=l&&l.require||t;break;case"exports":h=l.exports;break;case"module":h=l;break;default:h=a(f)}s.push(h)}return s}function a(t){var e=o[t];if(!e)throw new Error("No "+t);if(!e.defined){var i=e.factory,n=i.apply(this,r(e.deps||[],i,t));"undefined"!=typeof n&&(e.exports=n),e.defined=1}return e.exports}var o={};e=function(t,e,i){o[t]={id:t,deps:e,factory:i,defined:0,exports:{},require:n(t)}},t=n("")}();var i="__dirty",n="transform",r="getLineStyle",a="initData",o="rotation",s="parent",l="inside",c="stroke",u="lineWidth",h="applyTransform",f="ecModel",d="getShallow",p="getName",m="mergeOption",v="option",g="undefined",y="dataToCoord",_="parsePercent",x="setHoverStyle",b="emphasis",w="getRawValue",M="label.emphasis",S="label.normal",T="getItemVisual",C="eachItemGraphicEl",k="getItemGraphicEl",L="setItemGraphicEl",A="updateProps",P="getItemModel",z="getItemLayout",D="normal",I="../../echarts",O="../../util/graphic",R="getBaseAxis",E="dataToPoint",N="dimensions",B="retrieve",F="ordinal",V="getAxis",G="category",H="concat",q="createElement",W="getExtent",U="normalize",Z="contain",X="opacity",j="position",Y="center",$="middle",Q="getBoundingRect",K="getTextColor",J="getFont",te="textAlign",ee="textStyle",ie="coordinateSystem",ne="removeAll",re="inherits",ae="indexOf",oe="filter",se="number",le="function",ce="isArray",ue="replace",he="zlevel",fe="setStyle",de="traverse",pe="getDataParams",me="seriesIndex",ve="dataIndex",ge="target",ye="mouseout",_e="mouseover",xe="silent",be="splice",we="series",Me="length",Se="defaults",Te="dispatchAction",Ce="extend",ke="remove",Le="isObject",Ae="updateLayout",Pe="eachSeries",ze="update",De="create",Ie="height",Oe="bottom",Re="ignore",Ee="getHeight",Ne="getWidth",Be="getModel",Fe="animation",Ve="resize",Ge="string",He="prototype",qe="toLowerCase",We="zrender/core/vector",Ue="zrender/core/env",Ze="getData",Xe="zrender/core/util",je="require";e("echarts/chart/bar",[je,Xe,"../coord/cartesian/Grid","./bar/BarSeries","./bar/BarView","../layout/barGrid","../echarts","../component/grid"],function(t){var e=t(Xe);t("../coord/cartesian/Grid"),t("./bar/BarSeries"),t("./bar/BarView");var i=t("../layout/barGrid"),n=t("../echarts");n.registerLayout(e.curry(i,"bar")),n.registerVisual(function(t){t.eachSeriesByType("bar",function(t){var e=t[Ze]();e.setVisual("legendSymbol","roundRect")})}),t("../component/grid")}),e("echarts/echarts",[je,Ue,"./model/Global","./ExtensionAPI","./CoordinateSystem","./model/OptionManager","./model/Component","./model/Series","./view/Component","./view/Chart","./util/graphic","zrender",Xe,"zrender/tool/color","zrender/mixin/Eventful","zrender/core/timsort","./visual/seriesColor","./preprocessor/backwardCompat","./loading/default","./data/List","./model/Model","./util/number","./util/format","zrender/core/matrix",We],function(t){function e(t){return function(e,i,n){e=e&&e[qe](),A[He][t].call(this,e,i,n)}}function i(){A.call(this)}function n(t,e,n){function r(t,e){return t.prio-e.prio}n=n||{},typeof e===Ge&&(e=$[e]),this.id,this.group,this._dom=t,this._zr=C.init(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio}),this._theme=k.clone(e),this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._api=new y(this),this._coordSysMgr=new _,A.call(this),this._messageCenter=new i,this._initEvents(),this[Ve]=k.bind(this[Ve],this),this._pendingActions=[],P(Y,r),P(X,r),this._zr[Fe].on("frame",this._onframe,this)}function r(t,e){var i=this._model;i&&i.eachComponent({mainType:"series",query:e},function(n){var r=this._chartsMap[n.__viewId];r&&r.__alive&&r[t](n,i,this._api,e)},this)}function a(t,e,i){var n=this._api;z(this._componentsViews,function(r){var a=r.__model;r[t](a,e,n,i),p(a,r)},this),e[Pe](function(r){var a=this._chartsMap[r.__viewId];a[t](r,e,n,i),p(r,a),d(r,a)},this),f(this._zr,e)}function o(t,e){for(var i="component"===t,n=i?this._componentsViews:this._chartsViews,r=i?this._componentsMap:this._chartsMap,a=this._zr,o=0;o<n[Me];o++)n[o].__alive=!1;e[i?"eachComponent":Pe](function(t,o){if(i){if(t===we)return}else o=t;var s=o.id+"_"+o.type,l=r[s];if(!l){var c=b.parseClassType(o.type),u=i?M.getClass(c.main,c.sub):S.getClass(c.sub);if(!u)return;l=new u,l.init(e,this._api),r[s]=l,n.push(l),a.add(l.group)}o.__viewId=s,l.__alive=!0,l.__id=s,l.__model=o},this);for(var o=0;o<n[Me];){var s=n[o];s.__alive?o++:(a[ke](s.group),s.dispose(e,this._api),n[be](o,1),delete r[s.__id])}}function s(t,e){z(X,function(i){i.func(t,e)})}function l(t){var e={};t[Pe](function(t){var i=t.get("stack"),n=t[Ze]();if(i&&"list"===n.type){var r=e[i];r&&(n.stackedOn=r),e[i]=n}})}function c(t,e){var i=this._api;z(Y,function(n){n.isLayout&&n.func(t,i,e)})}function u(t,e){var i=this._api;t.clearColorPalette(),t[Pe](function(t){t.clearColorPalette()}),z(Y,function(n){n.func(t,i,e)})}function h(t,e){var i=this._api;z(this._componentsViews,function(n){var r=n.__model;n.render(r,t,i,e),p(r,n)},this),z(this._chartsViews,function(t){t.__alive=!1},this),t[Pe](function(n){var r=this._chartsMap[n.__viewId];r.__alive=!0,r.render(n,t,i,e),r.group[xe]=!!n.get(xe),p(n,r),d(n,r)},this),f(this._zr,t),z(this._chartsViews,function(e){e.__alive||e[ke](t,i)},this)}function f(t,e){var i=t.storage,n=0;i[de](function(t){t.isGroup||n++}),n>e.get("hoverLayerThreshold")&&!v.node&&i[de](function(t){t.isGroup||(t.useHoverLayer=!0)})}function d(t,e){var i=0;e.group[de](function(t){"group"===t.type||t[Re]||i++});var n=+t.get("progressive"),r=i>t.get("progressiveThreshold")&&n&&!v.node;r&&e.group[de](function(t){t.isGroup||(t.progressive=r?Math.floor(i++/n):-1,r&&t.stopAnimation(!0))});var a=t.get("blendMode")||null;e.group[de](function(t){t.isGroup||t[fe]("blend",a)})}function p(t,e){var i=t.get("z"),n=t.get(he);e.group[de](function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=n&&(t[he]=n))})}function m(t){function e(t,e){for(var i=0;i<t[Me];i++){var n=t[i];n[a]=e}}var i=0,n=1,r=2,a="__connectUpdateStatus";k.each(Z,function(o,s){t._messageCenter.on(s,function(o){if(J[t.group]&&t[a]!==i){var s=t.makeActionFromEvent(o),l=[];for(var c in K){var u=K[c];u!==t&&u.group===t.group&&l.push(u)}e(l,i),z(l,function(t){t[a]!==n&&t[Te](s)}),e(l,r)}})})}var v=t(Ue),g=t("./model/Global"),y=t("./ExtensionAPI"),_=t("./CoordinateSystem"),x=t("./model/OptionManager"),b=t("./model/Component"),w=t("./model/Series"),M=t("./view/Component"),S=t("./view/Chart"),T=t("./util/graphic"),C=t("zrender"),k=t(Xe),L=t("zrender/tool/color"),A=t("zrender/mixin/Eventful"),P=t("zrender/core/timsort"),z=k.each,D=1e3,I=5e3,O=1e3,R=2e3,E=3e3,N=4e3,B=5e3,F="__flag_in_main_process",V="_hasGradientOrPatternBg",G="_optionUpdated";i[He].on=e("on"),i[He].off=e("off"),i[He].one=e("one"),k.mixin(i,A);var H=n[He];H._onframe=function(){this[G]&&(this[F]=!0,q.prepareAndUpdate.call(this),this[F]=!1,this[G]=!1)},H.getDom=function(){return this._dom},H.getZr=function(){return this._zr},H.setOption=function(t,e,i){if(this[F]=!0,!this._model||e){var n=new x(this._api),r=this._theme,a=this._model=new g(null,null,r,n);a.init(null,null,r,n)}this._model.setOption(t,j),i?this[G]=!0:(q.prepareAndUpdate.call(this),this._zr.refreshImmediately(),this[G]=!1),this[F]=!1,this._flushPendingActions()},H.setTheme=function(){console.log("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},H[Be]=function(){return this._model},H.getOption=function(){return this._model&&this._model.getOption()},H[Ne]=function(){return this._zr[Ne]()},H[Ee]=function(){return this._zr[Ee]()},H.getRenderedCanvas=function(t){if(v.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr,i=e.storage.getDisplayList();return k.each(i,function(t){t.stopAnimation(!0)}),e.painter.getRenderedCanvas(t)}},H.getDataURL=function(t){t=t||{};var e=t.excludeComponents,i=this._model,n=[],r=this;z(e,function(t){i.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group[Re]||(n.push(e),e.group[Re]=!0)})});var a=this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return z(n,function(t){t.group[Re]=!1}),a},H.getConnectedDataURL=function(t){if(v.canvasSupported){var e=this.group,i=Math.min,n=Math.max,r=1/0;if(J[e]){var a=r,o=r,s=-r,l=-r,c=[],u=t&&t.pixelRatio||1;for(var h in K){var f=K[h];if(f.group===e){var d=f.getRenderedCanvas(k.clone(t)),p=f.getDom().getBoundingClientRect();a=i(p.left,a),o=i(p.top,o),s=n(p.right,s),l=n(p[Oe],l),c.push({dom:d,left:p.left,top:p.top})}}a*=u,o*=u,s*=u,l*=u;var m=s-a,g=l-o,y=k.createCanvas();y.width=m,y[Ie]=g;var _=C.init(y);return z(c,function(t){var e=new T.Image({style:{x:t.left*u-a,y:t.top*u-o,image:t.dom}});_.add(e)}),_.refreshImmediately(),y.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}};var q={update:function(t){var e=this._model,i=this._api,n=this._coordSysMgr,r=this._zr;if(e){e.restoreData(),n[De](this._model,this._api),s.call(this,e,i),l.call(this,e),n[ze](e,i),u.call(this,e,t),h.call(this,e,t);var a=e.get("backgroundColor")||"transparent",o=r.painter;if(o.isSingleCanvas&&o.isSingleCanvas())r.configLayer(0,{clearColor:a});else{if(!v.canvasSupported){var c=L.parse(a);a=L.stringify(c,"rgb"),0===c[3]&&(a="transparent")}a.colorStops||a.image?(r.configLayer(0,{clearColor:a}),this[V]=!0,this._dom.style.background="transparent"):(this[V]&&r.configLayer(0,{clearColor:null}),this[V]=!1,this._dom.style.background=a)}}},updateView:function(t){var e=this._model;e&&(e[Pe](function(t){t[Ze]().clearAllVisual()}),u.call(this,e,t),a.call(this,"updateView",e,t))},updateVisual:function(t){var e=this._model;e&&(e[Pe](function(t){t[Ze]().clearAllVisual()}),u.call(this,e,t),a.call(this,"updateVisual",e,t))},updateLayout:function(t){var e=this._model;e&&(c.call(this,e,t),a.call(this,Ae,e,t))},highlight:function(t){r.call(this,"highlight",t)},downplay:function(t){r.call(this,"downplay",t)},prepareAndUpdate:function(t){var e=this._model;o.call(this,"component",e),o.call(this,"chart",e),q[ze].call(this,t)}};H[Ve]=function(){this[F]=!0,this._zr[Ve]();var t=this._model&&this._model.resetOption("media");q[t?"prepareAndUpdate":ze].call(this),this._loadingFX&&this._loadingFX[Ve](),this[F]=!1,this._flushPendingActions()},H.showLoading=function(t,e){if(k[Le](t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Q[t]){var i=Q[t](this._api,e),n=this._zr;this._loadingFX=i,n.add(i)}},H.hideLoading=function(){this._loadingFX&&this._zr[ke](this._loadingFX),this._loadingFX=null},H.makeActionFromEvent=function(t){var e=k[Ce]({},t);return e.type=Z[t.type],e},H[Te]=function(t,e){var i=U[t.type];if(i){var n=i.actionInfo,r=n[ze]||ze;if(this[F])return void this._pendingActions.push(t);this[F]=!0;var a=[t],o=!1;t.batch&&(o=!0,a=k.map(t.batch,function(e){return e=k[Se](k[Ce]({},e),t),e.batch=null,e}));for(var s,l=[],c="highlight"===t.type||"downplay"===t.type,u=0;u<a[Me];u++){var h=a[u];s=i.action(h,this._model),s=s||k[Ce]({},h),s.type=n.event||s.type,l.push(s),c&&q[r].call(this,h)}"none"===r||c||(this[G]?(q.prepareAndUpdate.call(this,t),this[G]=!1):q[r].call(this,t)),s=o?{type:n.event||t.type,batch:l}:l[0],this[F]=!1,!e&&this._messageCenter.trigger(s.type,s),this._flushPendingActions()}},H._flushPendingActions=function(){for(var t=this._pendingActions;t[Me];){var e=t.shift();this[Te](e)}},H.on=e("on"),H.off=e("off"),H.one=e("one");var W=["click","dblclick",_e,ye,"mousemove","mousedown","mouseup","globalout"];H._initEvents=function(){z(W,function(t){this._zr.on(t,function(e){var i=this[Be](),n=e[ge];if(n&&null!=n[ve]){var r=n.dataModel||i.getSeriesByIndex(n[me]),a=r&&r[pe](n[ve],n.dataType)||{};a.event=e,a.type=t,this.trigger(t,a)}else n&&n.eventData&&this.trigger(t,n.eventData)},this)},this),z(Z,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},H.isDisposed=function(){return this._disposed},H.clear=function(){this.setOption({series:[]},!0)},H.dispose=function(){if(!this._disposed){this._disposed=!0;var t=this._api,e=this._model;z(this._componentsViews,function(i){i.dispose(e,t)}),z(this._chartsViews,function(i){i.dispose(e,t)}),this._zr.dispose(),delete K[this.id]}},k.mixin(n,A);var U=[],Z={},X=[],j=[],Y=[],$={},Q={},K={},J={},te=new Date-0,ee=new Date-0,ie="_echarts_instance_",ne={version:"3.2.3",dependencies:{zrender:"3.1.3"}};ne.init=function(t,e,i){var r=new n(t,e,i);return r.id="ec_"+te++,K[r.id]=r,t.setAttribute&&t.setAttribute(ie,r.id),m(r),r},ne.connect=function(t){if(k[ce](t)){var e=t;t=null,k.each(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+ee++,k.each(e,function(e){e.group=t})}return J[t]=!0,t},ne.disConnect=function(t){J[t]=!1},ne.dispose=function(t){k.isDom(t)?t=ne.getInstanceByDom(t):typeof t===Ge&&(t=K[t]),t instanceof n&&!t.isDisposed()&&t.dispose()},ne.getInstanceByDom=function(t){var e=t.getAttribute(ie);return K[e]},ne.getInstanceById=function(t){return K[t]},ne.registerTheme=function(t,e){$[t]=e},ne.registerPreprocessor=function(t){j.push(t)},ne.registerProcessor=function(t,e){typeof t===le&&(e=t,t=D),X.push({prio:t,func:e})},ne.registerAction=function(t,e,i){typeof e===le&&(i=e,e="");var n=k[Le](t)?t.type:[t,t={event:e}][0];t.event=(t.event||n)[qe](),e=t.event,U[n]||(U[n]={action:i,actionInfo:t}),Z[e]=n},ne.registerCoordinateSystem=function(t,e){_.register(t,e)},ne.registerLayout=function(t,e){typeof t===le&&(e=t,t=O),Y.push({prio:t,func:e,isLayout:!0})},ne.registerVisual=function(t,e){typeof t===le&&(e=t,t=E),Y.push({prio:t,func:e})},ne.registerLoading=function(t,e){Q[t]=e};var je=b.parseClassType;return ne.extendComponentModel=function(t,e){var i=b;if(e){var n=je(e);i=b.getClass(n.main,n.sub,!0)}return i[Ce](t)},ne.extendComponentView=function(t,e){var i=M;if(e){var n=je(e);i=M.getClass(n.main,n.sub,!0)}return i[Ce](t)},ne.extendSeriesModel=function(t,e){var i=w;if(e){e="series."+e[ue]("series.","");var n=je(e);i=w.getClass(n.main,n.sub,!0)}return i[Ce](t)},ne.extendChartView=function(t,e){var i=S;if(e){e[ue]("series.","");var n=je(e);i=S.getClass(n.main,!0)}return i[Ce](t)},ne.setCanvasCreator=function(t){k.createCanvas=t},ne.registerVisual(R,t("./visual/seriesColor")),ne.registerPreprocessor(t("./preprocessor/backwardCompat")),ne.registerLoading("default",t("./loading/default")),ne.registerAction({type:"highlight",event:"highlight",update:"highlight"},k.noop),ne.registerAction({type:"downplay",event:"downplay",update:"downplay"},k.noop),ne.List=t("./data/List"),ne.Model=t("./model/Model"),ne.graphic=t("./util/graphic"),ne[se]=t("./util/number"),ne.format=t("./util/format"),ne.matrix=t("zrender/core/matrix"),ne.vector=t(We),ne.color=t("zrender/tool/color"),ne.util={},z(["map","each",oe,ae,re,"reduce",oe,"bind","curry",ce,"isString",Le,"isFunction",Ce,Se],function(t){ne.util[t]=k[t]}),ne.PRIORITY={PROCESSOR:{FILTER:D,STATISTIC:I},VISUAL:{LAYOUT:O,GLOBAL:R,CHART:E,COMPONENT:N,BRUSH:B}},ne}),e("echarts/chart/pie",[je,Xe,"../echarts","./pie/PieSeries","./pie/PieView","../action/createDataSelectAction","../visual/dataColor","./pie/pieLayout","../processor/dataFilter"],function(t){var e=t(Xe),i=t("../echarts");t("./pie/PieSeries"),t("./pie/PieView"),t("../action/createDataSelectAction")("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),i.registerVisual(e.curry(t("../visual/dataColor"),"pie")),i.registerLayout(e.curry(t("./pie/pieLayout"),"pie")),i.registerProcessor(e.curry(t("../processor/dataFilter"),"pie"))}),e("echarts/chart/line",[je,Xe,"../echarts","./line/LineSeries","./line/LineView","../visual/symbol","../layout/points","../processor/dataSample","../component/grid"],function(t){var e=t(Xe),i=t("../echarts"),n=i.PRIORITY;t("./line/LineSeries"),t("./line/LineView"),i.registerVisual(e.curry(t("../visual/symbol"),"line","circle","line")),i.registerLayout(e.curry(t("../layout/points"),"line")),i.registerProcessor(n.PROCESSOR.STATISTIC,e.curry(t("../processor/dataSample"),"line")),t("../component/grid")}),e("echarts/component/grid",[je,"../util/graphic",Xe,"../echarts","../coord/cartesian/Grid","./axis"],function(t){var e=t("../util/graphic"),i=t(Xe),n=t("../echarts");t("../coord/cartesian/Grid"),t("./axis"),n.extendComponentView({type:"grid",render:function(t){this.group[ne](),t.get("show")&&this.group.add(new e.Rect({shape:t[ie].getRect(),style:i[Se]({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0}))}}),n.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}),e("echarts/component/title",[je,"../echarts","../util/graphic","../util/layout"],function(t){var e=t("../echarts"),i=t("../util/graphic"),n=t("../util/layout");e.extendComponentModel({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),e.extendComponentView({type:"title",render:function(t,e,r){if(this.group[ne](),t.get("show")){var a=this.group,o=t[Be](ee),s=t[Be]("subtextStyle"),l=t.get(te),c=t.get("textBaseline"),u=new i.Text({style:{text:t.get("text"),textFont:o[J](),fill:o[K]()},z2:10}),h=u[Q](),f=t.get("subtext"),d=new i.Text({style:{text:f,textFont:s[J](),fill:s[K](),y:h[Ie]+t.get("itemGap"),textBaseline:"top"},z2:10}),p=t.get("link"),m=t.get("sublink");u[xe]=!p,d[xe]=!m,p&&u.on("click",function(){window.open(p,"_"+t.get(ge))}),m&&d.on("click",function(){window.open(m,"_"+t.get("subtarget"))}),a.add(u),f&&a.add(d);var v=a[Q](),g=t.getBoxLayoutParams();g.width=v.width,g[Ie]=v[Ie];var y=n.getLayoutRect(g,{width:r[Ne](),height:r[Ee]()},t.get("padding"));l||(l=t.get("left")||t.get("right"),l===$&&(l=Y),"right"===l?y.x+=y.width:l===Y&&(y.x+=y.width/2)),c||(c=t.get("top")||t.get(Oe),c===Y&&(c=$),c===Oe?y.y+=y[Ie]:c===$&&(y.y+=y[Ie]/2),c=c||"top"),a.attr(j,[y.x,y.y]);var _={textAlign:l,textVerticalAlign:c};u[fe](_),d[fe](_),v=a[Q]();var x=y.margin,b=t.getItemStyle(["color",X]);b.fill=t.get("backgroundColor");var w=new i.Rect({shape:{x:v.x-x[3],y:v.y-x[0],width:v.width+x[1]+x[3],height:v[Ie]+x[0]+x[2]},style:b,silent:!0});i.subPixelOptimizeRect(w),a.add(w)}}})}),e("echarts/component/legend",[je,"./legend/LegendModel","./legend/legendAction","./legend/LegendView","../echarts","./legend/legendFilter"],function(t){t("./legend/LegendModel"),t("./legend/legendAction"),t("./legend/LegendView");var e=t("../echarts");e.registerProcessor(t("./legend/legendFilter"))}),e("echarts/component/tooltip",[je,"./tooltip/TooltipModel","./tooltip/TooltipView","../echarts"],function(t){t("./tooltip/TooltipModel"),t("./tooltip/TooltipView"),t("../echarts").registerAction({type:"showTip",event:"showTip",update:"none"},function(){}),t("../echarts").registerAction({type:"hideTip",event:"hideTip",update:"none"},function(){})}),e("echarts/component/markPoint",[je,"./marker/MarkPointModel","./marker/MarkPointView","../echarts"],function(t){t("./marker/MarkPointModel"),t("./marker/MarkPointView"),t("../echarts").registerPreprocessor(function(t){t.markPoint=t.markPoint||{}})}),e("echarts/component/markLine",[je,"./marker/MarkLineModel","./marker/MarkLineView","../echarts"],function(t){t("./marker/MarkLineModel"),t("./marker/MarkLineView"),t("../echarts").registerPreprocessor(function(t){t.markLine=t.markLine||{}})}),e("echarts/component/markArea",[je,"./marker/MarkAreaModel","./marker/MarkAreaView","../echarts"],function(t){t("./marker/MarkAreaModel"),t("./marker/MarkAreaView"),t("../echarts").registerPreprocessor(function(t){t.markArea=t.markArea||{}})}),e("echarts/component/timeline",[je,"../echarts","./timeline/preprocessor","./timeline/typeDefaulter","./timeline/timelineAction","./timeline/SliderTimelineModel","./timeline/SliderTimelineView"],function(t){var e=t("../echarts");e.registerPreprocessor(t("./timeline/preprocessor")),t("./timeline/typeDefaulter"),t("./timeline/timelineAction"),t("./timeline/SliderTimelineModel"),t("./timeline/SliderTimelineView")}),e("zrender/vml/vml",[je,"./graphic","../zrender","./Painter"],function(t){t("./graphic"),t("../zrender").registerPainter("vml",t("./Painter"))}),e("echarts/scale/Time",[je,Xe,"../util/number","../util/format","./Interval"],function(t){var e=t(Xe),i=t("../util/number"),n=t("../util/format"),r=t("./Interval"),a=r[He],o=Math.ceil,s=Math.floor,l=1e3,c=60*l,u=60*c,h=24*u,f=function(t,e,i,n){for(;n>i;){var r=i+n>>>1;t[r][2]<e?i=r+1:n=r}return i},d=r[Ce]({type:"time",getLabel:function(t){var e=this._stepLvl,i=new Date(t);return n.formatTime(e[0],i)},niceExtent:function(t,e,n){var r=this._extent;if(r[0]===r[1]&&(r[0]-=h,r[1]+=h),r[1]===-1/0&&1/0===r[0]){var a=new Date;r[1]=new Date(a.getFullYear(),a.getMonth(),a.getDate()),r[0]=r[1]-h}this.niceTicks(t);var l=this._interval;e||(r[0]=i.round(s(r[0]/l)*l)),n||(r[1]=i.round(o(r[1]/l)*l))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0],r=n/t,a=p[Me],l=f(p,r,0,a),c=p[Math.min(l,a-1)],u=c[2];if("year"===c[0]){var h=n/u,d=i.nice(h/t,!0);u*=d}var m=[o(e[0]/u)*u,s(e[1]/u)*u];this._stepLvl=c,this._interval=u,this._niceExtent=m},parse:function(t){return+i.parseDate(t)}});e.each([Z,U],function(t){d[He][t]=function(e){return a[t].call(this,this.parse(e))}});var p=[["hh:mm:ss",1,l],["hh:mm:ss",5,5*l],["hh:mm:ss",10,10*l],["hh:mm:ss",15,15*l],["hh:mm:ss",30,30*l],["hh:mm\nMM-dd",1,c],["hh:mm\nMM-dd",5,5*c],["hh:mm\nMM-dd",10,10*c],["hh:mm\nMM-dd",15,15*c],["hh:mm\nMM-dd",30,30*c],["hh:mm\nMM-dd",1,u],["hh:mm\nMM-dd",2,2*u],["hh:mm\nMM-dd",6,6*u],["hh:mm\nMM-dd",12,12*u],["MM-dd\nyyyy",1,h],["week",7,7*h],["month",1,31*h],["quarter",3,380*h/4],["half-year",6,380*h/2],["year",1,380*h]];return d[De]=function(){return new d},d}),e("echarts/scale/Log",[je,Xe,"./Scale","../util/number","./Interval"],function(t){var e=t(Xe),i=t("./Scale"),n=t("../util/number"),r=t("./Interval"),a=i[He],o=r[He],s=Math.floor,l=Math.ceil,c=Math.pow,u=Math.log,h=i[Ce]({type:"log",base:10,getTicks:function(){return e.map(o.getTicks.call(this),function(t){return n.round(c(this.base,t))},this)},getLabel:o.getLabel,scale:function(t){return t=a.scale.call(this,t),c(this.base,t)},setExtent:function(t,e){var i=this.base;t=u(t)/u(i),e=u(e)/u(i),o.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=a[W].call(this);return e[0]=c(t,e[0]),e[1]=c(t,e[1]),e},unionExtent:function(t){var e=this.base;t[0]=u(t[0])/u(e),t[1]=u(t[1])/u(e),a.unionExtent.call(this,t)},niceTicks:function(t){t=t||10;var e=this._extent,i=e[1]-e[0];if(!(1/0===i||0>=i)){var r=n.quantity(i),a=t/i*r;for(.5>=a&&(r*=10);!isNaN(r)&&Math.abs(r)<1&&Math.abs(r)>0;)r*=10;var o=[n.round(l(e[0]/r)*r),n.round(s(e[1]/r)*r)];this._interval=r,this._niceExtent=o}},niceExtent:o.niceExtent});return e.each([Z,U],function(t){h[He][t]=function(e){return e=u(e)/u(this.base),a[t].call(this,e)}}),h[De]=function(){return new h},h}),e(Xe,[je],function(){function t(e){if("object"==typeof e&&null!==e){var i=e;if(e instanceof Array){i=[];for(var n=0,r=e[Me];r>n;n++)i[n]=t(e[n])}else if(!w(e)&&!M(e)){i={};for(var a in e)e.hasOwnProperty(a)&&(i[a]=t(e[a]))}return i}return e}function e(i,n,r){if(!b(n)||!b(i))return r?t(n):i;for(var a in n)if(n.hasOwnProperty(a)){var o=i[a],s=n[a];!b(s)||!b(o)||y(s)||y(o)||M(s)||M(o)||w(s)||w(o)?!r&&a in i||(i[a]=t(n[a],!0)):e(o,s,r)}return i}function i(t,i){for(var n=t[0],r=1,a=t[Me];a>r;r++)n=e(n,t[r],i);return n}function n(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function r(t,e,i){for(var n in e)e.hasOwnProperty(n)&&(i?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function a(){return document[q]("canvas")}function o(){return k||(k=E.createCanvas().getContext("2d")),k}function s(t,e){if(t){if(t[ae])return t[ae](e);for(var i=0,n=t[Me];n>i;i++)if(t[i]===e)return i}return-1}function l(t,e){function i(){}var n=t[He];i[He]=e[He],t[He]=new i;for(var r in n)t[He][r]=n[r];t[He].constructor=t,t.superClass=e}function c(t,e,i){t=He in t?t[He]:t,e=He in e?e[He]:e,r(t,e,i)}function u(t){return t?typeof t==Ge?!1:typeof t[Me]==se:void 0}function h(t,e,i){if(t&&e)if(t.forEach&&t.forEach===z)t.forEach(e,i);else if(t[Me]===+t[Me])for(var n=0,r=t[Me];r>n;n++)e.call(i,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(i,t[a],a,t)}function f(t,e,i){if(t&&e){if(t.map&&t.map===O)return t.map(e,i);for(var n=[],r=0,a=t[Me];a>r;r++)n.push(e.call(i,t[r],r,t));return n}}function d(t,e,i,n){if(t&&e){if(t.reduce&&t.reduce===R)return t.reduce(e,i,n);for(var r=0,a=t[Me];a>r;r++)i=e.call(n,i,t[r],r,t);return i}}function p(t,e,i){if(t&&e){if(t[oe]&&t[oe]===D)return t[oe](e,i);for(var n=[],r=0,a=t[Me];a>r;r++)e.call(i,t[r],r,t)&&n.push(t[r]);return n}}function m(t,e,i){if(t&&e)for(var n=0,r=t[Me];r>n;n++)if(e.call(i,t[n],n,t))return t[n]}function v(t,e){var i=I.call(arguments,2);return function(){return t.apply(e,i[H](I.call(arguments)))}}function g(t){var e=I.call(arguments,1);return function(){return t.apply(this,e[H](I.call(arguments)))}}function y(t){return"[object Array]"===A.call(t)}function _(t){return typeof t===le}function x(t){return"[object String]"===A.call(t)}function b(t){var e=typeof t;return e===le||!!t&&"object"==e}function w(t){return!!L[A.call(t)]}function M(t){return t&&1===t.nodeType&&typeof t.nodeName==Ge}function S(){for(var t=0,e=arguments[Me];e>t;t++)if(null!=arguments[t])return arguments[t]}function T(){return Function.call.apply(I,arguments)}function C(t,e){if(!t)throw new Error(e)}var k,L={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1},A=Object[He].toString,P=Array[He],z=P.forEach,D=P[oe],I=P.slice,O=P.map,R=P.reduce,E={inherits:l,mixin:c,clone:t,merge:e,mergeAll:i,extend:n,defaults:r,getContext:o,createCanvas:a,indexOf:s,slice:T,find:m,isArrayLike:u,each:h,map:f,reduce:d,filter:p,bind:v,curry:g,isArray:y,isString:x,isObject:b,isFunction:_,isBuildInObject:w,isDom:M,retrieve:S,assert:C,noop:function(){}};return E}),e("echarts/coord/cartesian/Grid",[je,"exports","../../util/layout","../../coord/axisHelper",Xe,"./Cartesian2D","./Axis2D","./GridModel","../../CoordinateSystem"],function(t){function e(t,e){return t.findGridModel()===e}function i(t){var e,i=t.model,n=i.getFormattedLabels(),r=1,a=n[Me];a>40&&(r=Math.ceil(a/40));for(var o=0;a>o;o+=r)if(!t.isLabelIgnored(o)){var s=i.getTextRect(n[o]);e?e.union(s):e=s}return e}function n(t,e,i){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,i),this._model=t}function r(t,e){var i=t[W](),n=i[0]+i[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return n-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return n-t+e}}function a(t,e){return c.map(v,function(i){var n=e.queryComponents({mainType:i,index:t.get(i+"Index"),id:t.get(i+"Id")})[0];return n})}function o(t){return"cartesian2d"===t.get(ie)}var s=t("../../util/layout"),l=t("../../coord/axisHelper"),c=t(Xe),u=t("./Cartesian2D"),h=t("./Axis2D"),f=c.each,d=l.ifAxisCrossZero,p=l.niceScaleExtent;t("./GridModel");var m=n[He];m.type="grid",m.getRect=function(){return this._rect},m[ze]=function(t,e){function i(t){var e=n[t];for(var i in e){var r=e[i];if(r&&(r.type===G||!d(r)))return!0}return!1}var n=this._axesMap;this._updateScale(t,this._model),f(n.x,function(t){p(t,t.model)}),f(n.y,function(t){p(t,t.model)}),f(n.x,function(t){i("y")&&(t.onZero=!1)}),f(n.y,function(t){i("x")&&(t.onZero=!1)}),this[Ve](this._model,e)},m[Ve]=function(t,e){function n(){f(o,function(t){var e=t.isHorizontal(),i=e?[0,a.width]:[0,a[Ie]],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),r(t,e?a.x:a.y)})}var a=s.getLayoutRect(t.getBoxLayoutParams(),{width:e[Ne](),height:e[Ee]()});this._rect=a;var o=this._axesList;n(),t.get("containLabel")&&(f(o,function(t){if(!t.model.get("axisLabel.inside")){var e=i(t);if(e){var n=t.isHorizontal()?Ie:"width",r=t.model.get("axisLabel.margin");a[n]-=e[n]+r,"top"===t[j]?a.y+=e[Ie]+r:"left"===t[j]&&(a.x+=e.width+r)}}}),n())},m[V]=function(t,e){var i=this._axesMap[t];if(null!=i){if(null==e)for(var n in i)return i[n];return i[e]}},m.getCartesian=function(t,e){if(null!=t&&null!=e){var i="x"+t+"y"+e;return this._coordsMap[i]}for(var n=0,r=this._coordsList;n<r[Me];n++)if(r[n][V]("x").index===t||r[n][V]("y").index===e)return r[n]},m._initCartesian=function(t,i){function n(n){return function(s,c){if(e(s,t,i)){var u=s.get(j);"x"===n?"top"!==u&&u!==Oe&&(u=Oe,r[u]&&(u="top"===u?Oe:"top")):"left"!==u&&"right"!==u&&(u="left",r[u]&&(u="left"===u?"right":"left")),r[u]=!0;var f=new h(n,l.createScaleByModel(s),[0,0],s.get("type"),u),d=f.type===G;f.onBand=d&&s.get("boundaryGap"),f.inverse=s.get("inverse"),f.onZero=s.get("axisLine.onZero"),s.axis=f,f.model=s,f.grid=this,f.index=c,this._axesList.push(f),a[n][c]=f,o[n]++}}}var r={left:!1,right:!1,top:!1,bottom:!1},a={x:{},y:{}},o={x:0,y:0};return i.eachComponent("xAxis",n("x"),this),i.eachComponent("yAxis",n("y"),this),o.x&&o.y?(this._axesMap=a,void f(a.x,function(t,e){f(a.y,function(i,n){var r="x"+e+"y"+n,a=new u(r);a.grid=this,this._coordsMap[r]=a,this._coordsList.push(a),a.addAxis(t),a.addAxis(i)},this)},this)):(this._axesMap={},void(this._axesList=[]))},m._updateScale=function(t,i){function n(t,e,i){f(i.coordDimToDataDim(e.dim),function(i){e.scale.unionExtent(t.getDataExtent(i,e.scale.type!==F))})}c.each(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t[Pe](function(r){if(o(r)){var s=a(r,t),l=s[0],c=s[1];if(!e(l,i,t)||!e(c,i,t))return;var u=this.getCartesian(l.componentIndex,c.componentIndex),h=r[Ze](),f=u[V]("x"),d=u[V]("y");"list"===h.type&&(n(h,f,r),n(h,d,r))}},this)};var v=["xAxis","yAxis"];return n[De]=function(t,e){var i=[];return t.eachComponent("grid",function(r,a){var o=new n(r,t,e);o.name="grid_"+a,o[Ve](r,e),r[ie]=o,i.push(o)}),t[Pe](function(e){if(o(e)){var i=a(e,t),n=i[0],r=i[1],s=n.findGridModel(),l=s[ie];e[ie]=l.getCartesian(n.componentIndex,r.componentIndex)}}),i},n[N]=u[He][N],t("../../CoordinateSystem").register("cartesian2d",n),n}),e("echarts/chart/bar/BarSeries",[je,"../../model/Series","../helper/createListFromArray"],function(t){var e=t("../../model/Series"),i=t("../helper/createListFromArray");return e[Ce]({type:"series.bar",dependencies:["grid","polar"],getInitialData:function(t,e){return i(t.data,this,e)},getMarkerPosition:function(t){var e=this[ie];if(e){var i=e[E](t,!0),n=this[Ze](),r=n.getLayout("offset"),a=n.getLayout("size"),o=e[R]().isHorizontal()?0:1;return i[o]+=r+a/2,i}return[0/0,0/0]},brushSelector:"rect",defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,itemStyle:{normal:{},emphasis:{}}}})}),e("echarts/chart/bar/BarView",[je,Xe,O,"../../model/Model","./barItemStyle",I],function(t){function e(t,e){var i=t.width>0?1:-1,n=t[Ie]>0?1:-1;e=Math.min(e,Math.abs(t.width),Math.abs(t[Ie])),t.x+=i*e/2,t.y+=n*e/2,t.width-=i*e,t[Ie]-=n*e}var i=t(Xe),n=t(O);return i[Ce](t("../../model/Model")[He],t("./barItemStyle")),t(I).extendChartView({type:"bar",render:function(t,e,i){var n=t.get(ie);return"cartesian2d"===n&&this._renderOnCartesian(t,e,i),this.group},_renderOnCartesian:function(t){function r(r,a){var s=o[z](r),l=o[P](r).get(f)||0;e(s,l);var c=new n.Rect({shape:i[Ce]({},s)});if(h){var d=c.shape,p=u?Ie:"width",m={};d[p]=0,m[p]=s[p],n[a?A:"initProps"](c,{shape:m},t,r)
}return c}var a=this.group,o=t[Ze](),s=this._data,l=t[ie],c=l[R](),u=c.isHorizontal(),h=t.get(Fe),f=["itemStyle",D,"barBorderWidth"];o.diff(s).add(function(t){if(o.hasValue(t)){var e=r(t);o[L](t,e),a.add(e)}})[ze](function(i,l){var c=s[k](l);if(!o.hasValue(i))return void a[ke](c);c||(c=r(i,!0));var u=o[z](i),h=o[P](i).get(f)||0;e(u,h),n[A](c,{shape:u},t,i),o[L](i,c),a.add(c)})[ke](function(e){var i=s[k](e);i&&(i.style.text="",n[A](i,{shape:{width:0}},t,e,function(){a[ke](i)}))}).execute(),this._updateStyle(t,o,u),this._data=o},_updateStyle:function(t,e,r){function a(t,e,i,r,a){n.setText(t,e,i),t.text=r,"outside"===t.textPosition&&(t.textPosition=a)}e[C](function(o,s){var l=e[P](s),c=e[T](s,"color"),u=e[T](s,X),h=e[z](s),f=l[Be]("itemStyle.normal"),d=l[Be]("itemStyle.emphasis").getBarItemStyle();o.setShape("r",f.get("barBorderRadius")||0),o.useStyle(i[Se]({fill:c,opacity:u},f.getBarItemStyle()));var p=r?h[Ie]>0?Oe:"top":h.width>0?"left":"right",m=l[Be](S),v=l[Be](M),g=o.style;m.get("show")?a(g,m,c,i[B](t.getFormattedLabel(s,D),t[w](s)),p):g.text="",v.get("show")?a(d,v,c,i[B](t.getFormattedLabel(s,b),t[w](s)),p):d.text="",n[x](o,d)})},remove:function(t){var e=this.group;t.get(Fe)?this._data&&this._data[C](function(i){i.style.text="",n[A](i,{shape:{width:0}},t,i[ve],function(){e[ke](i)})}):e[ne]()}})}),e("echarts/layout/barGrid",[je,Xe,"../util/number"],function(t){function e(t){return t.get("stack")||"__ec_stack_"+t[me]}function i(t){return t.dim+t.index}function n(t){var n={};a.each(t,function(t){var r=t[Ze](),a=t[ie],o=a[R](),l=o[W](),c=o.type===G?o.getBandWidth():Math.abs(l[1]-l[0])/r.count(),u=n[i(o)]||{bandWidth:c,remainedWidth:c,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},h=u.stacks;n[i(o)]=u;var f=e(t);h[f]||u.autoWidthCount++,h[f]=h[f]||{width:0,maxWidth:0};var d=s(t.get("barWidth"),c),p=s(t.get("barMaxWidth"),c),m=t.get("barGap"),v=t.get("barCategoryGap");d&&!h[f].width&&(d=Math.min(u.remainedWidth,d),h[f].width=d,u.remainedWidth-=d),p&&(h[f].maxWidth=p),null!=m&&(u.gap=m),null!=v&&(u.categoryGap=v)});var r={};return a.each(n,function(t,e){r[e]={};var i=t.stacks,n=t.bandWidth,o=s(t.categoryGap,n),l=s(t.gap,1),c=t.remainedWidth,u=t.autoWidthCount,h=(c-o)/(u+(u-1)*l);h=Math.max(h,0),a.each(i,function(t){var e=t.maxWidth;!t.width&&e&&h>e&&(e=Math.min(e,c),c-=e,t.width=e,u--)}),h=(c-o)/(u+(u-1)*l),h=Math.max(h,0);var f,d=0;a.each(i,function(t){t.width||(t.width=h),f=t,d+=t.width*(1+l)}),f&&(d-=f.width*l);var p=-d/2;a.each(i,function(t,i){r[e][i]=r[e][i]||{offset:p,width:t.width},p+=t.width*(1+l)})}),r}function r(t,r){var o=n(a[oe](r.getSeriesByType(t),function(t){return!r.isSeriesFiltered(t)&&t[ie]&&"cartesian2d"===t[ie].type})),s={};r.eachSeriesByType(t,function(t){var n=t[Ze](),r=t[ie],a=r[R](),l=e(t),c=o[i(a)][l],u=c.offset,h=c.width,f=r.getOtherAxis(a),d=t.get("barMinHeight")||0,p=a.onZero?f.toGlobalCoord(f[y](0)):f.getGlobalExtent()[0],m=r.dataToPoints(n,!0);s[l]=s[l]||[],n.setLayout({offset:u,size:h}),n.each(f.dim,function(t,e){if(!isNaN(t)){s[l][e]||(s[l][e]={p:p,n:p});var i,r,a,o,c=t>=0?"p":"n",v=m[e],g=s[l][e][c];f.isHorizontal()?(i=g,r=v[1]+u,a=v[0]-g,o=h,Math.abs(a)<d&&(a=(0>a?-1:1)*d),s[l][e][c]+=a):(i=v[0]+u,r=g,a=h,o=v[1]-g,Math.abs(o)<d&&(o=(0>=o?-1:1)*d),s[l][e][c]+=o),n.setItemLayout(e,{x:i,y:r,width:a,height:o})}},!0)},this)}var a=t(Xe),o=t("../util/number"),s=o[_];return r}),e(Ue,[],function(){function t(t){var e={},i={},n=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/);return n&&(i.firefox=!0,i.version=n[1]),r&&(i.ie=!0,i.version=r[1]),r&&(i.ie=!0,i.version=r[1]),a&&(i.edge=!0,i.version=a[1]),{browser:i,os:e,node:!1,canvasSupported:document[q]("canvas").getContext?!0:!1,touchEventsSupported:"ontouchstart"in window&&!i.ie&&!i.edge,pointerEventsSupported:"onpointerdown"in window&&(i.edge||i.ie&&i.version>=10)}}var e={};return e=typeof navigator===g?{browser:{},os:{},node:!0,canvasSupported:!0}:t(navigator.userAgent)}),e("echarts/ExtensionAPI",[je,Xe],function(t){function e(t){i.each(n,function(e){this[e]=i.bind(t[e],t)},this)}var i=t(Xe),n=["getDom","getZr",Ne,Ee,Te,"isDisposed","on","off","getDataURL","getConnectedDataURL",Be,"getOption"];return e}),e("echarts/CoordinateSystem",[je],function(){function t(){this._coordinateSystems=[]}var e={};return t[He]={constructor:t,create:function(t,i){var n=[];for(var r in e){var a=e[r][De](t,i);a&&(n=n[H](a))}this._coordinateSystems=n},update:function(t,e){for(var i=this._coordinateSystems,n=0;n<i[Me];n++)i[n][ze]&&i[n][ze](t,e)}},t.register=function(t,i){e[t]=i},t.get=function(t){return e[t]},t}),e("echarts/model/Global",[je,Xe,"../util/model","./Model","./Component","./globalDefault","./mixin/colorPalette"],function(t){function e(t,e){for(var i in e)x.hasClass(i)||("object"==typeof e[i]?t[i]=t[i]?c.merge(t[i],e[i],!1):c.clone(e[i]):null==t[i]&&(t[i]=e[i]))}function i(t){t=t,this[v]={},this[v][w]=1,this._componentsMap={},this._seriesIndices=null,e(t,this._theme[v]),c.merge(t,b,!1),this[m](t)}function n(t,e){c[ce](e)||(e=e?[e]:[]);var i={};return f(e,function(e){i[e]=(t[e]||[]).slice()}),i}function r(t,e){var i={};f(e,function(t){var e=t.exist;e&&(i[e.id]=t)}),f(e,function(e){var n=e[v];if(c.assert(!n||null==n.id||!i[n.id]||i[n.id]===e,"id duplicates: "+(n&&n.id)),n&&null!=n.id&&(i[n.id]=e),_(n)){var r=a(t,n,e.exist);e.keyInfo={mainType:t,subType:r}}}),f(e,function(t){var e=t.exist,n=t[v],r=t.keyInfo;if(_(n)){if(r.name=null!=n.name?n.name+"":e?e.name:"\x00-",e)r.id=e.id;else if(null!=n.id)r.id=n.id+"";else{var a=0;do r.id="\x00"+r.name+"\x00"+a++;while(i[r.id])}i[r.id]=t}})}function a(t,e,i){var n=e.type?e.type:i?i.subType:x.determineSubType(t,e);return n}function o(t){return p(t,function(t){return t.componentIndex})||[]}function s(t,e){return e.hasOwnProperty("subType")?d(t,function(t){return t.subType===e.subType}):t}function l(t){}var c=t(Xe),u=t("../util/model"),h=t("./Model"),f=c.each,d=c[oe],p=c.map,g=c[ce],y=c[ae],_=c[Le],x=t("./Component"),b=t("./globalDefault"),w="\x00_ec_inner",M=h[Ce]({constructor:M,init:function(t,e,i,n){i=i||{},this[v]=null,this._theme=new h(i),this._optionManager=n},setOption:function(t,e){c.assert(!(w in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption()},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var r=n.mountOption("recreate"===t);this[v]&&"recreate"!==t?(this.restoreData(),this[m](r)):i.call(this,r),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var a=n.getTimelineOption(this);a&&(this[m](a),e=!0)}if(!t||"recreate"===t||"media"===t){var o=n.getMediaOption(this,this._api);o[Me]&&f(o,function(t){this[m](t,e=!0)},this)}return e},mergeOption:function(t){function e(e,s){var l=u.normalizeToArray(t[e]),h=u.mappingToExists(a[e],l);r(e,h);var d=n(a,s);i[e]=[],a[e]=[],f(h,function(t,n){var r=t.exist,o=t[v];if(c.assert(_(o)||r,"Empty component definition"),o){var s=x.getClass(e,t.keyInfo.subType,!0);if(r&&r instanceof s)r[m](o,this),r.optionUpdated(o,!1);else{var l=c[Ce]({dependentModels:d,componentIndex:n},t.keyInfo);r=new s(o,this,this,l),r.init(o,this,this,l),r.optionUpdated(null,!0)}}else r[m]({},this),r.optionUpdated({},!1);a[e][n]=r,i[e][n]=r[v]},this),e===we&&(this._seriesIndices=o(a[we]))}var i=this[v],a=this._componentsMap,s=[];f(t,function(t,e){null!=t&&(x.hasClass(e)?s.push(e):i[e]=null==i[e]?c.clone(t):c.merge(i[e],t,!0))}),x.topologicalTravel(s,x.getAllClassMainTypes(),e,this),this._seriesIndices=this._seriesIndices||[]},getOption:function(){var t=c.clone(this[v]);return f(t,function(e,i){if(x.hasClass(i)){for(var e=u.normalizeToArray(e),n=e[Me]-1;n>=0;n--)u.isIdInner(e[n])&&e[be](n,1);t[i]=e}}),delete t[w],t},getTheme:function(){return this._theme},getComponent:function(t,e){var i=this._componentsMap[t];return i?i[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var i=t.index,n=t.id,r=t.name,a=this._componentsMap[e];if(!a||!a[Me])return[];var o;if(null!=i)g(i)||(i=[i]),o=d(p(i,function(t){return a[t]}),function(t){return!!t});else if(null!=n){var l=g(n);o=d(a,function(t){return l&&y(n,t.id)>=0||!l&&t.id===n})}else if(null!=r){var c=g(r);o=d(a,function(t){return c&&y(r,t.name)>=0||!c&&t.name===r})}else o=a;return s(o,t)},findComponents:function(t){function e(t){var e=r+"Index",i=r+"Id",n=r+"Name";return t&&(t.hasOwnProperty(e)||t.hasOwnProperty(i)||t.hasOwnProperty(n))?{mainType:r,index:t[e],id:t[i],name:t[n]}:null}function i(e){return t[oe]?d(e,t[oe]):e}var n=t.query,r=t.mainType,a=e(n),o=a?this.queryComponents(a):this._componentsMap[r];return i(s(o,t))},eachComponent:function(t,e,i){var n=this._componentsMap;if(typeof t===le)i=e,e=t,f(n,function(t,n){f(t,function(t,r){e.call(i,n,t,r)})});else if(c.isString(t))f(n[t],e,i);else if(_(t)){var r=this.findComponents(t);f(r,e,i)}},getSeriesByName:function(t){var e=this._componentsMap[we];return d(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap[we][t]},getSeriesByType:function(t){var e=this._componentsMap[we];return d(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap[we].slice()},eachSeries:function(t,e){l(this),f(this._seriesIndices,function(i){var n=this._componentsMap[we][i];t.call(e,n,i)},this)},eachRawSeries:function(t,e){f(this._componentsMap[we],t,e)},eachSeriesByType:function(t,e,i){l(this),f(this._seriesIndices,function(n){var r=this._componentsMap[we][n];r.subType===t&&e.call(i,r,n)},this)},eachRawSeriesByType:function(t,e,i){return f(this.getSeriesByType(t),e,i)},isSeriesFiltered:function(t){return l(this),c[ae](this._seriesIndices,t.componentIndex)<0},filterSeries:function(t,e){l(this);var i=d(this._componentsMap[we],t,e);this._seriesIndices=o(i)},restoreData:function(){var t=this._componentsMap;this._seriesIndices=o(t[we]);var e=[];f(t,function(t,i){e.push(i)}),x.topologicalTravel(e,x.getAllClassMainTypes(),function(e){f(t[e],function(t){t.restoreData()})})}});return c.mixin(M,t("./mixin/colorPalette")),M}),e("echarts/model/OptionManager",[je,Xe,"../util/model","./Component"],function(t){function e(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function i(t,e,i){var n,r,a=[],o=[],l=t.timeline;if(t.baseOption&&(r=t.baseOption),(l||t.options)&&(r=r||{},a=(t.options||[]).slice()),t.media){r=r||{};var c=t.media;u(c,function(t){t&&t[v]&&(t.query?o.push(t):n||(n=t))})}return r||(r=t),r.timeline||(r.timeline=l),u([r][H](a)[H](s.map(o,function(t){return t[v]})),function(t){u(e,function(e){e(t,i)})}),{baseOption:r,timelineOptions:a,mediaDefault:n,mediaList:o}}function n(t,e,i){var n={width:e,height:i,aspectratio:e/i},a=!0;return s.each(t,function(t,e){var i=e.match(p);if(i&&i[1]&&i[2]){var o=i[1],s=i[2][qe]();r(n[s],t,o)||(a=!1)}}),a}function r(t,e,i){return"min"===i?t>=e:"max"===i?e>=t:t===e}function a(t,e){return t.join(",")===e.join(",")}function o(t,e){e=e||{},u(e,function(e,i){if(null!=e){var n=t[i];if(c.hasClass(i)){e=l.normalizeToArray(e),n=l.normalizeToArray(n);var r=l.mappingToExists(n,e);t[i]=f(r,function(t){return t[v]&&t.exist?d(t.exist,t[v],!0):t.exist||t[v]})}else t[i]=d(n,e,!0)}})}var s=t(Xe),l=t("../util/model"),c=t("./Component"),u=s.each,h=s.clone,f=s.map,d=s.merge,p=/^(min|max)?(.+)$/;return e[He]={constructor:e,setOption:function(t,e){t=h(t,!0);var n=this._optionBackup,r=i.call(this,t,e,!n);this._newBaseOption=r.baseOption,n?(o(n.baseOption,r.baseOption),r.timelineOptions[Me]&&(n.timelineOptions=r.timelineOptions),r.mediaList[Me]&&(n.mediaList=r.mediaList),r.mediaDefault&&(n.mediaDefault=r.mediaDefault)):this._optionBackup=r},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=f(e.timelineOptions,h),this._mediaList=f(e.mediaList,h),this._mediaDefault=h(e.mediaDefault),this._currentMediaIndices=[],h(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,i=this._timelineOptions;if(i[Me]){var n=t.getComponent("timeline");n&&(e=h(i[n.getCurrentIndex()],!0))}return e},getMediaOption:function(){var t=this._api[Ne](),e=this._api[Ee](),i=this._mediaList,r=this._mediaDefault,o=[],s=[];if(!i[Me]&&!r)return s;for(var l=0,c=i[Me];c>l;l++)n(i[l].query,t,e)&&o.push(l);return!o[Me]&&r&&(o=[-1]),o[Me]&&!a(o,this._currentMediaIndices)&&(s=f(o,function(t){return h(-1===t?r[v]:i[t][v])})),this._currentMediaIndices=o,s}},e}),e("echarts/model/Component",[je,"./Model",Xe,"../util/component","../util/clazz","../util/layout","./mixin/boxLayout"],function(t){function e(t){var e=[];return n.each(l.getClassesByMainType(t),function(t){r.apply(e,t[He].dependencies||[])}),n.map(e,function(t){return o.parseClassType(t).main})}var i=t("./Model"),n=t(Xe),r=Array[He].push,a=t("../util/component"),o=t("../util/clazz"),s=t("../util/layout"),l=i[Ce]({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,r,o){i.call(this,t,e,r,o),n[Ce](this,o),this.uid=a.getUID("componentModel")},init:function(t,e,i){this.mergeDefaultAndTheme(t,i)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,r=i?s.getLayoutParams(t):{},a=e.getTheme();n.merge(t,a.get(this.mainType)),n.merge(t,this.getDefaultOption()),i&&s.mergeLayoutParam(t,r,i)},mergeOption:function(t){n.merge(this[v],t,!0);var e=this.layoutMode;e&&s.mergeLayoutParam(this[v],t,e)},optionUpdated:function(){},getDefaultOption:function(){if(!this.hasOwnProperty("__defaultOption")){for(var t=[],e=this.constructor;e;){var i=e[He].defaultOption;i&&t.push(i),e=e.superClass}for(var r={},a=t[Me]-1;a>=0;a--)r=n.merge(r,t[a],!0);this.__defaultOption=r}return this.__defaultOption}});return o.enableClassManagement(l,{registerWhenExtend:!0}),a.enableSubTypeDefaulter(l),a.enableTopologicalTravel(l,e),n.mixin(l,t("./mixin/boxLayout")),l}),e("echarts/view/Chart",[je,"zrender/container/Group","../util/component","../util/clazz"],function(t){function e(){this.group=new r,this.uid=a.getUID("viewChart")}function i(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var n=0;n<t.childCount();n++)i(t.childAt(n),e)}function n(t,e,n){var r=e&&e[ve],a=e&&e.name;if(null!=r)for(var o=r instanceof Array?r:[r],s=0,l=o[Me];l>s;s++)i(t[k](o[s]),n);else if(a)for(var c=a instanceof Array?a:[a],s=0,l=c[Me];l>s;s++){var r=t.indexOfName(c[s]);i(t[k](r),n)}else t[C](function(t){i(t,n)})}var r=t("zrender/container/Group"),a=t("../util/component"),o=t("../util/clazz");e[He]={type:"chart",init:function(){},render:function(){},highlight:function(t,e,i,r){n(t[Ze](),r,b)},downplay:function(t,e,i,r){n(t[Ze](),r,D)},remove:function(){this.group[ne]()},dispose:function(){}};var s=e[He];return s.updateView=s[Ae]=s.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},o.enableClassExtend(e),o.enableClassManagement(e,{registerWhenExtend:!0}),e}),e("echarts/model/Series",[je,Xe,"../util/format","../util/model","./Component","./mixin/colorPalette",Ue],function(t){var e=t(Xe),i=t("../util/format"),n=t("../util/model"),r=t("./Component"),a=t("./mixin/colorPalette"),o=t(Ue),s=i.encodeHTML,l=i.addCommas,c=r[Ce]({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.normal.color",init:function(t,e,i){this[me]=this.componentIndex,this.mergeDefaultAndTheme(t,i),this._dataBeforeProcessed=this.getInitialData(t,i),this._data=this._dataBeforeProcessed.cloneShallow()},mergeDefaultAndTheme:function(t,i){e.merge(t,i.getTheme().get(this.subType)),e.merge(t,this.getDefaultOption()),n.defaultEmphasis(t.label,n.LABEL_OPTIONS),this.fillDataTextStyle(t.data)},mergeOption:function(t,i){t=e.merge(this[v],t,!0),this.fillDataTextStyle(t.data);var n=this.getInitialData(t,i);n&&(this._data=n,this._dataBeforeProcessed=n.cloneShallow())},fillDataTextStyle:function(t){if(t)for(var e=0;e<t[Me];e++)t[e]&&t[e].label&&n.defaultEmphasis(t[e].label,n.LABEL_OPTIONS)},getInitialData:function(){},getData:function(t){return null==t?this._data:this._data.getLinkedData(t)},setData:function(t){this._data=t},getRawData:function(){return this._dataBeforeProcessed},coordDimToDataDim:function(t){return[t]},dataDimToCoordDim:function(t){return t},getBaseAxis:function(){var t=this[ie];return t&&t[R]&&t[R]()},formatTooltip:function(t,n){function r(t){return e.map(t,function(t,e){var r=a.getDimensionInfo(e),o=r&&r.type;return o===F?t:"time"===o?n?"":i.formatTime("yyyy/mm/dd hh:mm:ss",t):l(t)})[oe](function(t){return!!t}).join(", ")}var a=this._data,o=this[w](t),c=e[ce](o)?r(o):l(o),u=a[p](t),h=a[T](t,"color"),f='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+h+'"></span>',d=this.name;return"\x00-"===d&&(d=""),n?f+s(this.name)+" : "+c:(d&&s(d)+"<br />")+f+(u?s(u)+" : "+c:c)},ifEnableAnimation:function(){if(o.node)return!1;var t=this[d](Fe);return t&&this[Ze]().count()>this[d]("animationThreshold")&&(t=!1),t},restoreData:function(){this._data=this._dataBeforeProcessed.cloneShallow()},getColorFromPalette:function(t,e){var i=this[f],n=a.getColorFromPalette.call(this,t,e);return n||(n=i.getColorFromPalette(t,e)),n},getAxisTooltipDataIndex:null});return e.mixin(c,n.dataFormatMixin),e.mixin(c,a),c}),e("echarts/view/Component",[je,"zrender/container/Group","../util/component","../util/clazz"],function(t){var e=t("zrender/container/Group"),i=t("../util/component"),n=t("../util/clazz"),r=function(){this.group=new e,this.uid=i.getUID("viewComponent")};r[He]={constructor:r,init:function(){},render:function(){},dispose:function(){}};var a=r[He];return a.updateView=a[Ae]=a.updateVisual=function(){},n.enableClassExtend(r),n.enableClassManagement(r,{registerWhenExtend:!0}),r}),e("echarts/util/graphic",[je,Xe,"zrender/tool/path","zrender/graphic/Path","zrender/tool/color","zrender/core/matrix",We,"zrender/graphic/Gradient","zrender/container/Group","zrender/graphic/Image","zrender/graphic/Text","zrender/graphic/shape/Circle","zrender/graphic/shape/Sector","zrender/graphic/shape/Ring","zrender/graphic/shape/Polygon","zrender/graphic/shape/Polyline","zrender/graphic/shape/Rect","zrender/graphic/shape/Line","zrender/graphic/shape/BezierCurve","zrender/graphic/shape/Arc","zrender/graphic/CompoundPath","zrender/graphic/LinearGradient","zrender/graphic/RadialGradient","zrender/core/BoundingRect"],function(t){function e(t){return null!=t&&"none"!=t}function i(t){return typeof t===Ge?k.lift(t,-.1):t}function n(t){if(t.__hoverStlDirty){var n=t.style[c],r=t.style.fill,a=t.__hoverStl;a.fill=a.fill||(e(r)?i(r):null),a[c]=a[c]||(e(n)?i(n):null);var o={};for(var s in a)a.hasOwnProperty(s)&&(o[s]=t.style[s]);t.__normalStl=o,t.__hoverStlDirty=!1}}function r(t){t.__isHover||(n(t),t.useHoverLayer?t.__zr&&t.__zr.addHover(t,t.__hoverStl):(t[fe](t.__hoverStl),t.z2+=1),t.__isHover=!0)}function a(t){if(t.__isHover){var e=t.__normalStl;t.useHoverLayer?t.__zr&&t.__zr.removeHover(t):(e&&t[fe](e),t.z2-=1),t.__isHover=!1}}function f(t){"group"===t.type?t[de](function(t){"group"!==t.type&&r(t)}):r(t)}function p(t){"group"===t.type?t[de](function(t){"group"!==t.type&&a(t)}):a(t)}function m(t,e){t.__hoverStl=t.hoverStyle||e||{},t.__hoverStlDirty=!0,t.__isHover&&n(t)}function v(){!this.__isEmphasis&&f(this)}function g(){!this.__isEmphasis&&p(this)}function y(){this.__isEmphasis=!0,f(this)}function _(){this.__isEmphasis=!1,p(this)}function w(t,e,i,n,r,a){typeof r===le&&(a=r,r=null);var o=n&&(n.ifEnableAnimation?n.ifEnableAnimation():n[d](Fe));if(o){var s=t?"Update":"",l=n&&n[d]("animationDuration"+s),c=n&&n[d]("animationEasing"+s),u=n&&n[d]("animationDelay"+s);typeof u===le&&(u=u(r)),l>0?e.animateTo(i,l,u||0,c,a):(e.attr(i),a&&a())}else e.attr(i),a&&a()}var M=t(Xe),S=t("zrender/tool/path"),T=Math.round,C=t("zrender/graphic/Path"),k=t("zrender/tool/color"),L=t("zrender/core/matrix"),P=t(We),z=(t("zrender/graphic/Gradient"),{});return z.Group=t("zrender/container/Group"),z.Image=t("zrender/graphic/Image"),z.Text=t("zrender/graphic/Text"),z.Circle=t("zrender/graphic/shape/Circle"),z.Sector=t("zrender/graphic/shape/Sector"),z.Ring=t("zrender/graphic/shape/Ring"),z.Polygon=t("zrender/graphic/shape/Polygon"),z.Polyline=t("zrender/graphic/shape/Polyline"),z.Rect=t("zrender/graphic/shape/Rect"),z.Line=t("zrender/graphic/shape/Line"),z.BezierCurve=t("zrender/graphic/shape/BezierCurve"),z.Arc=t("zrender/graphic/shape/Arc"),z.CompoundPath=t("zrender/graphic/CompoundPath"),z.LinearGradient=t("zrender/graphic/LinearGradient"),z.RadialGradient=t("zrender/graphic/RadialGradient"),z.BoundingRect=t("zrender/core/BoundingRect"),z.extendShape=function(t){return C[Ce](t)},z.extendPath=function(t,e){return S.extendFromString(t,e)},z.makePath=function(t,e,i,n){var r=S.createFromString(t,e),a=r[Q]();if(i){var o=a.width/a[Ie];if(n===Y){var s,l=i[Ie]*o;l<=i.width?s=i[Ie]:(l=i.width,s=l/o);var c=i.x+i.width/2,u=i.y+i[Ie]/2;i.x=c-l/2,i.y=u-s/2,i.width=l,i[Ie]=s}this.resizePath(r,i)}return r},z.mergePath=S.mergePath,z.resizePath=function(t,e){if(t[h]){var i=t[Q](),n=i.calculateTransform(e);t[h](n)}},z.subPixelOptimizeLine=function(t){var e=z.subPixelOptimize,i=t.shape,n=t.style[u];return T(2*i.x1)===T(2*i.x2)&&(i.x1=i.x2=e(i.x1,n,!0)),T(2*i.y1)===T(2*i.y2)&&(i.y1=i.y2=e(i.y1,n,!0)),t},z.subPixelOptimizeRect=function(t){var e=z.subPixelOptimize,i=t.shape,n=t.style[u],r=i.x,a=i.y,o=i.width,s=i[Ie];return i.x=e(i.x,n,!0),i.y=e(i.y,n,!0),i.width=Math.max(e(r+o,n,!1)-i.x,0===o?0:1),i[Ie]=Math.max(e(a+s,n,!1)-i.y,0===s?0:1),t},z.subPixelOptimize=function(t,e,i){var n=T(2*t);return(n+T(e))%2===0?n/2:(n+(i?1:-1))/2},z[x]=function(t,e){"group"===t.type?t[de](function(t){"group"!==t.type&&m(t,e)}):m(t,e),t.on(_e,v).on(ye,g),t.on(b,y).on(D,_)},z.setText=function(t,e,i){var n=e[d](j)||l,r=n[ae](l)>=0?"white":i,a=e[Be](ee);M[Ce](t,{textDistance:e[d]("distance")||5,textFont:a[J](),textPosition:n,textFill:a[K]()||r})},z[A]=function(t,e,i,n,r){w(!0,t,e,i,n,r)},z.initProps=function(t,e,i,n,r){w(!1,t,e,i,n,r)},z.getTransform=function(t,e){for(var i=L.identity([]);t&&t!==e;)L.mul(i,t.getLocalTransform(),i),t=t[s];return i},z[h]=function(t,e,i){return i&&(e=L.invert([],e)),P[h]([],t,e)},z.transformDirection=function(t,e,i){var n=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-n:"right"===t?n:0,"top"===t?-r:t===Oe?r:0];return a=z[h](a,e,i),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?Oe:"top"},z.groupTransition=function(t,e,i){function n(t){var e={};return t[de](function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:P.clone(t[j]),rotation:t[o]};return t.shape&&(e.shape=M[Ce]({},t.shape)),e}if(t&&e){var a=n(t);e[de](function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var n=r(t);t.attr(r(e)),z[A](t,n,i,t[ve])}}})}},z}),e("zrender/tool/color",[je],function(){function t(t){return t=Math.round(t),0>t?0:t>255?255:t}function e(t){return t=Math.round(t),0>t?0:t>360?360:t}function i(t){return 0>t?0:t>1?1:t}function n(e){return t(e[Me]&&"%"===e.charAt(e[Me]-1)?parseFloat(e)/100*255:parseInt(e,10))}function r(t){return i(t[Me]&&"%"===t.charAt(t[Me]-1)?parseFloat(t)/100:parseFloat(t))}function a(t,e,i){return 0>i?i+=1:i>1&&(i-=1),1>6*i?t+(e-t)*i*6:1>2*i?e:2>3*i?t+(e-t)*(2/3-i)*6:t}function o(t,e,i){return t+(e-t)*i}function s(t){if(t){t+="";var e=t[ue](/ /g,"")[qe]();if(e in g)return g[e].slice();if("#"!==e.charAt(0)){var i=e[ae]("("),a=e[ae](")");if(-1!==i&&a+1===e[Me]){var o=e.substr(0,i),s=e.substr(i+1,a-(i+1)).split(","),c=1;switch(o){case"rgba":if(4!==s[Me])return;c=r(s.pop());case"rgb":if(3!==s[Me])return;return[n(s[0]),n(s[1]),n(s[2]),c];case"hsla":if(4!==s[Me])return;return s[3]=r(s[3]),l(s);case"hsl":if(3!==s[Me])return;return l(s);default:return}}}else{if(4===e[Me]){var u=parseInt(e.substr(1),16);if(!(u>=0&&4095>=u))return;return[(3840&u)>>4|(3840&u)>>8,240&u|(240&u)>>4,15&u|(15&u)<<4,1]}if(7===e[Me]){var u=parseInt(e.substr(1),16);if(!(u>=0&&16777215>=u))return;return[(16711680&u)>>16,(65280&u)>>8,255&u,1]}}}}function l(e){var i=(parseFloat(e[0])%360+360)%360/360,n=r(e[1]),o=r(e[2]),s=.5>=o?o*(n+1):o+n-o*n,l=2*o-s,c=[t(255*a(l,s,i+1/3)),t(255*a(l,s,i)),t(255*a(l,s,i-1/3))];return 4===e[Me]&&(c[3]=e[3]),c}function c(t){if(t){var e,i,n=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(n,r,a),s=Math.max(n,r,a),l=s-o,c=(s+o)/2;if(0===l)e=0,i=0;else{i=.5>c?l/(s+o):l/(2-s-o);var u=((s-n)/6+l/2)/l,h=((s-r)/6+l/2)/l,f=((s-a)/6+l/2)/l;n===s?e=f-h:r===s?e=1/3+u-f:a===s&&(e=2/3+h-u),0>e&&(e+=1),e>1&&(e-=1)}var d=[360*e,i,c];return null!=t[3]&&d.push(t[3]),d}}function u(t,e){var i=s(t);if(i){for(var n=0;3>n;n++)i[n]=0>e?i[n]*(1-e)|0:(255-i[n])*e+i[n]|0;return v(i,4===i[Me]?"rgba":"rgb")}}function h(t){var e=s(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function f(e,i,n){if(i&&i[Me]&&e>=0&&1>=e){n=n||[0,0,0,0];var r=e*(i[Me]-1),a=Math.floor(r),s=Math.ceil(r),l=i[a],c=i[s],u=r-a;return n[0]=t(o(l[0],c[0],u)),n[1]=t(o(l[1],c[1],u)),n[2]=t(o(l[2],c[2],u)),n[3]=t(o(l[3],c[3],u)),n}}function d(e,n,r){if(n&&n[Me]&&e>=0&&1>=e){var a=e*(n[Me]-1),l=Math.floor(a),c=Math.ceil(a),u=s(n[l]),h=s(n[c]),f=a-l,d=v([t(o(u[0],h[0],f)),t(o(u[1],h[1],f)),t(o(u[2],h[2],f)),i(o(u[3],h[3],f))],"rgba");return r?{color:d,leftIndex:l,rightIndex:c,value:a}:d}}function p(t,i,n,a){return t=s(t),t?(t=c(t),null!=i&&(t[0]=e(i)),null!=n&&(t[1]=r(n)),null!=a&&(t[2]=r(a)),v(l(t),"rgba")):void 0}function m(t,e){return t=s(t),t&&null!=e?(t[3]=i(e),v(t,"rgba")):void 0}function v(t,e){var i=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(i+=","+t[3]),e+"("+i+")"}var g={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};return{parse:s,lift:u,toHex:h,fastMapToColor:f,mapToColor:d,modifyHSL:p,modifyAlpha:m,stringify:v}}),e("zrender/mixin/Eventful",[je],function(){var t=Array[He].slice,e=function(){this._$handlers={}};return e[He]={constructor:e,one:function(t,e,i){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var r=0;r<n[t][Me];r++)if(n[t][r].h===e)return this;return n[t].push({h:e,one:!0,ctx:i||this}),this},on:function(t,e,i){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var r=0;r<n[t][Me];r++)if(n[t][r].h===e)return this;return n[t].push({h:e,one:!1,ctx:i||this}),this},isSilent:function(t){var e=this._$handlers;return e[t]&&e[t][Me]},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],r=0,a=i[t][Me];a>r;r++)i[t][r].h!=e&&n.push(i[t][r]);i[t]=n}i[t]&&0===i[t][Me]&&delete i[t]}else delete i[t];return this},trigger:function(e){if(this._$handlers[e]){var i=arguments,n=i[Me];n>3&&(i=t.call(i,1));for(var r=this._$handlers[e],a=r[Me],o=0;a>o;){switch(n){case 1:r[o].h.call(r[o].ctx);break;case 2:r[o].h.call(r[o].ctx,i[1]);break;case 3:r[o].h.call(r[o].ctx,i[1],i[2]);break;default:r[o].h.apply(r[o].ctx,i)}r[o].one?(r[be](o,1),a--):o++}}return this},triggerWithContext:function(e){if(this._$handlers[e]){var i=arguments,n=i[Me];n>4&&(i=t.call(i,1,i[Me]-1));for(var r=i[i[Me]-1],a=this._$handlers[e],o=a[Me],s=0;o>s;){switch(n){case 1:a[s].h.call(r);break;case 2:a[s].h.call(r,i[1]);break;case 3:a[s].h.call(r,i[1],i[2]);break;default:a[s].h.apply(r,i)}a[s].one?(a[be](s,1),o--):s++}}return this}},e}),e("zrender/core/timsort",[],function(){function t(t){for(var e=0;t>=l;)e|=1&t,t>>=1;return t+e}function e(t,e,n,r){var a=e+1;if(a===n)return 1;if(r(t[a++],t[e])<0){for(;n>a&&r(t[a],t[a-1])<0;)a++;i(t,e,a)}else for(;n>a&&r(t[a],t[a-1])>=0;)a++;return a-e}function i(t,e,i){for(i--;i>e;){var n=t[e];t[e++]=t[i],t[i--]=n}}function n(t,e,i,n,r){for(n===e&&n++;i>n;n++){for(var a,o=t[n],s=e,l=n;l>s;)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var c=n-s;switch(c){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;c>0;)t[s+c]=t[s+c-1],c--}t[s]=o}}function r(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])>0){for(s=n-r;s>l&&a(t,e[i+r+l])>0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}else{for(s=r+1;s>l&&a(t,e[i+r-l])<=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var c=o;o=r-l,l=r-c}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])>0?o=u+1:l=u}return l}function a(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])<0){for(s=r+1;s>l&&a(t,e[i+r-l])<0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var c=o;o=r-l,l=r-c}else{for(s=n-r;s>l&&a(t,e[i+r+l])>=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r
}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])<0?l=u:o=u+1}return l}function o(t,e){function i(t,e){f[y]=t,d[y]=e,y+=1}function n(){for(;y>1;){var t=y-2;if(t>=1&&d[t-1]<=d[t]+d[t+1]||t>=2&&d[t-2]<=d[t]+d[t-1])d[t-1]<d[t+1]&&t--;else if(d[t]>d[t+1])break;s(t)}}function o(){for(;y>1;){var t=y-2;t>0&&d[t-1]<d[t+1]&&t--,s(t)}}function s(i){var n=f[i],o=d[i],s=f[i+1],c=d[i+1];d[i]=o+c,i===y-3&&(f[i+1]=f[i+2],d[i+1]=d[i+2]),y--;var u=a(t[s],t,n,o,0,e);n+=u,o-=u,0!==o&&(c=r(t[n+o-1],t,s,c,c-1,e),0!==c&&(c>=o?l(n,o,s,c):h(n,o,s,c)))}function l(i,n,o,s){var l=0;for(l=0;n>l;l++)_[l]=t[i+l];var u=0,h=o,f=i;if(t[f++]=t[h++],0!==--s){if(1===n){for(l=0;s>l;l++)t[f+l]=t[h+l];return void(t[f+s]=_[u])}for(var d,m,v,g=p;;){d=0,m=0,v=!1;do if(e(t[h],_[u])<0){if(t[f++]=t[h++],m++,d=0,0===--s){v=!0;break}}else if(t[f++]=_[u++],d++,m=0,1===--n){v=!0;break}while(g>(d|m));if(v)break;do{if(d=a(t[h],_,u,n,0,e),0!==d){for(l=0;d>l;l++)t[f+l]=_[u+l];if(f+=d,u+=d,n-=d,1>=n){v=!0;break}}if(t[f++]=t[h++],0===--s){v=!0;break}if(m=r(_[u],t,h,s,0,e),0!==m){for(l=0;m>l;l++)t[f+l]=t[h+l];if(f+=m,h+=m,s-=m,0===s){v=!0;break}}if(t[f++]=_[u++],1===--n){v=!0;break}g--}while(d>=c||m>=c);if(v)break;0>g&&(g=0),g+=2}if(p=g,1>p&&(p=1),1===n){for(l=0;s>l;l++)t[f+l]=t[h+l];t[f+s]=_[u]}else{if(0===n)throw new Error;for(l=0;n>l;l++)t[f+l]=_[u+l]}}else for(l=0;n>l;l++)t[f+l]=_[u+l]}function h(i,n,o,s){var l=0;for(l=0;s>l;l++)_[l]=t[o+l];var u=i+n-1,h=s-1,f=o+s-1,d=0,m=0;if(t[f--]=t[u--],0!==--n){if(1===s){for(f-=n,u-=n,m=f+1,d=u+1,l=n-1;l>=0;l--)t[m+l]=t[d+l];return void(t[f]=_[h])}for(var v=p;;){var g=0,y=0,x=!1;do if(e(_[h],t[u])<0){if(t[f--]=t[u--],g++,y=0,0===--n){x=!0;break}}else if(t[f--]=_[h--],y++,g=0,1===--s){x=!0;break}while(v>(g|y));if(x)break;do{if(g=n-a(_[h],t,i,n,n-1,e),0!==g){for(f-=g,u-=g,n-=g,m=f+1,d=u+1,l=g-1;l>=0;l--)t[m+l]=t[d+l];if(0===n){x=!0;break}}if(t[f--]=_[h--],1===--s){x=!0;break}if(y=s-r(t[u],_,0,s,s-1,e),0!==y){for(f-=y,h-=y,s-=y,m=f+1,d=h+1,l=0;y>l;l++)t[m+l]=_[d+l];if(1>=s){x=!0;break}}if(t[f--]=t[u--],0===--n){x=!0;break}v--}while(g>=c||y>=c);if(x)break;0>v&&(v=0),v+=2}if(p=v,1>p&&(p=1),1===s){for(f-=n,u-=n,m=f+1,d=u+1,l=n-1;l>=0;l--)t[m+l]=t[d+l];t[f]=_[h]}else{if(0===s)throw new Error;for(d=f-(s-1),l=0;s>l;l++)t[d+l]=_[l]}}else for(d=f-(s-1),l=0;s>l;l++)t[d+l]=_[l]}var f,d,p=c,m=0,v=u,g=0,y=0;m=t[Me],2*u>m&&(v=m>>>1);var _=[];g=120>m?5:1542>m?10:119151>m?19:40,f=[],d=[],this.mergeRuns=n,this.forceMergeRuns=o,this.pushRun=i}function s(i,r,a,s){a||(a=0),s||(s=i[Me]);var c=s-a;if(!(2>c)){var u=0;if(l>c)return u=e(i,a,s,r),void n(i,a,s,a+u,r);var h=new o(i,r),f=t(c);do{if(u=e(i,a,s,r),f>u){var d=c;d>f&&(d=f),n(i,a,a+d,a+u,r),u=d}h.pushRun(a,u),h.mergeRuns(),c-=u,a+=u}while(0!==c);h.forceMergeRuns()}}var l=32,c=7,u=256;return s}),e("echarts/visual/seriesColor",[je,"zrender/graphic/Gradient"],function(t){var e=t("zrender/graphic/Gradient");return function(t){function i(i){var n=(i.visualColorAccessPath||"itemStyle.normal.color").split("."),r=i[Ze](),a=i.get(n)||i.getColorFromPalette(i.get("name"));r.setVisual("color",a),t.isSeriesFiltered(i)||(typeof a!==le||a instanceof e||r.each(function(t){r.setItemVisual(t,"color",a(i[pe](t)))}),r.each(function(t){var e=r[P](t),i=e.get(n,!0);null!=i&&r.setItemVisual(t,"color",i)}))}t.eachRawSeries(i)}}),e("echarts/preprocessor/backwardCompat",[je,Xe,"./helper/compatStyle"],function(t){function e(t,e){e=e.split(",");for(var i=t,n=0;n<e[Me]&&(i=i&&i[e[n]],null!=i);n++);return i}function i(t,e,i,n){e=e.split(",");for(var r,a=t,o=0;o<e[Me]-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(n||null==a[e[o]])&&(a[e[o]]=i)}function n(t){c(o,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}var r=t(Xe),a=t("./helper/compatStyle"),o=[["x","left"],["y","top"],["x2","right"],["y2",Oe]],s=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],l=["bar","boxplot","candlestick","chord","effectScatter","funnel","gauge","lines","graph","heatmap","line","map","parallel","pie","radar","sankey","scatter","treemap"],c=r.each;return function(t){c(t[we],function(t){if(r[Le](t)){var o=t.type;if(a(t),("pie"===o||"gauge"===o)&&null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===o){var s=e(t,"pointer.color");null!=s&&i(t,"itemStyle.normal.color",s)}for(var c=0;c<l[Me];c++)if(l[c]===t.type){n(t);break}}}),t.dataRange&&(t.visualMap=t.dataRange),c(s,function(e){var i=t[e];i&&(r[ce](i)||(i=[i]),c(i,function(t){n(t)}))})}}),e("echarts/loading/default",[je,"../util/graphic",Xe],function(t){var e=t("../util/graphic"),i=t(Xe),n=Math.PI;return function(t,r){r=r||{},i[Se](r,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var a=new e.Rect({style:{fill:r.maskColor},zlevel:r[he],z:1e4}),o=new e.Arc({shape:{startAngle:-n/2,endAngle:-n/2+.1,r:10},style:{stroke:r.color,lineCap:"round",lineWidth:5},zlevel:r[he],z:10001}),s=new e.Rect({style:{fill:"none",text:r.text,textPosition:"right",textDistance:10,textFill:r.textColor},zlevel:r[he],z:10001});o.animateShape(!0).when(1e3,{endAngle:3*n/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*n/2}).delay(300).start("circularInOut");var l=new e.Group;return l.add(o),l.add(s),l.add(a),l[Ve]=function(){var e=t[Ne]()/2,i=t[Ee]()/2;o.setShape({cx:e,cy:i});var n=o.shape.r;s.setShape({x:e-n,y:i-n,width:2*n,height:2*n}),a.setShape({x:0,y:0,width:t[Ne](),height:t[Ee]()})},l[Ve](),l}}),e("echarts/data/List",[je,"../model/Model","./DataDiffer",Xe,"../util/model"],function(t){function e(t){return h[ce](t)||(t=[t]),t}function i(t,e){var i=t[N],n=new _(h.map(i,t.getDimensionInfo,t),t.hostModel);y(n,t);for(var r=n._storage={},a=t._storage,o=0;o<i[Me];o++){var s=i[o],l=a[s];r[s]=h[ae](e,s)>=0?new l.constructor(a[s][Me]):a[s]}return n}var n=g,r=typeof window===g?global:window,o=typeof r.Float64Array===n?Array:r.Float64Array,s=typeof r.Int32Array===n?Array:r.Int32Array,l={"float":o,"int":s,ordinal:Array,number:Array,time:Array},c=t("../model/Model"),u=t("./DataDiffer"),h=t(Xe),d=t("../util/model"),m=h[Le],v=["stackedOn","hasItemOption","_nameList","_idList","_rawData"],y=function(t,e){h.each(v[H](e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods},_=function(t,e){t=t||["x","y"];for(var i={},n=[],r=0;r<t[Me];r++){var a,o={};typeof t[r]===Ge?(a=t[r],o={name:a,stackable:!1,type:"number"}):(o=t[r],a=o.name,o.type=o.type||se),n.push(a),i[a]=o}this[N]=n,this._dimensionInfos=i,this.hostModel=e,this.dataType,this.indices=[],this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this.stackedOn=null,this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._rawData,this._extent},x=_[He];x.type="list",x.hasItemOption=!0,x.getDimension=function(t){return isNaN(t)||(t=this[N][t]||t),t},x.getDimensionInfo=function(t){return h.clone(this._dimensionInfos[this.getDimension(t)])},x[a]=function(t,e,i){t=t||[],this._rawData=t;var n=this._storage={},r=this.indices=[],a=this[N],o=t[Me],s=this._dimensionInfos,c=[],u={};e=e||[];for(var h=0;h<a[Me];h++){var f=s[a[h]],p=l[f.type];n[a[h]]=new p(o)}var m=this;i||(m.hasItemOption=!1),i=i||function(t,e,i,n){var r=d.getDataItemValue(t);return d.isDataItemOption(t)&&(m.hasItemOption=!0),d.converDataValue(r instanceof Array?r[n]:r,s[e])};for(var v=0;v<t[Me];v++){for(var g=t[v],y=0;y<a[Me];y++){var _=a[y],x=n[_];x[v]=i(g,_,v,y)}r.push(v)}for(var h=0;h<t[Me];h++){e[h]||t[h]&&null!=t[h].name&&(e[h]=t[h].name);var b=e[h]||"",w=t[h]&&t[h].id;!w&&b&&(u[b]=u[b]||0,w=b,u[b]>0&&(w+="__ec__"+u[b]),u[b]++),w&&(c[h]=w)}this._nameList=e,this._idList=c},x.count=function(){return this.indices[Me]},x.get=function(t,e,i){var n=this._storage,r=this.indices[e];if(null==r)return 0/0;var a=n[t]&&n[t][r];if(i){var o=this._dimensionInfos[t];if(o&&o.stackable)for(var s=this.stackedOn;s;){var l=s.get(t,e);(a>=0&&l>0||0>=a&&0>l)&&(a+=l),s=s.stackedOn}}return a},x.getValues=function(t,e,i){var n=[];h[ce](t)||(i=e,e=t,t=this[N]);for(var r=0,a=t[Me];a>r;r++)n.push(this.get(t[r],e,i));return n},x.hasValue=function(t){for(var e=this[N],i=this._dimensionInfos,n=0,r=e[Me];r>n;n++)if(i[e[n]].type!==F&&isNaN(this.get(e[n],t)))return!1;return!0},x.getDataExtent=function(t,e){t=this.getDimension(t);var i=this._storage[t],n=this.getDimensionInfo(t);e=n&&n.stackable&&e;var r,a=(this._extent||(this._extent={}))[t+!!e];if(a)return a;if(i){for(var o=1/0,s=-1/0,l=0,c=this.count();c>l;l++)r=this.get(t,l,e),o>r&&(o=r),r>s&&(s=r);return this._extent[t+!!e]=[o,s]}return[1/0,-1/0]},x.getSum=function(t,e){var i=this._storage[t],n=0;if(i)for(var r=0,a=this.count();a>r;r++){var o=this.get(t,r,e);isNaN(o)||(n+=o)}return n},x[ae]=function(t,e){var i=this._storage,n=i[t],r=this.indices;if(n)for(var a=0,o=r[Me];o>a;a++){var s=r[a];if(n[s]===e)return a}return-1},x.indexOfName=function(t){for(var e=this.indices,i=this._nameList,n=0,r=e[Me];r>n;n++){var a=e[n];if(i[a]===t)return n}return-1},x.indexOfRawIndex=function(t){for(var e=this.indices,i=0,n=e[Me]-1;n>=i;){var r=(i+n)/2|0;if(e[r]<t)i=r+1;else{if(!(e[r]>t))return r;n=r-1}}return-1},x.indexOfNearest=function(t,e,i,n){var r=this._storage,a=r[t];null==n&&(n=1/0);var o=-1;if(a)for(var s=Number.MAX_VALUE,l=0,c=this.count();c>l;l++){var u=e-this.get(t,l,i),h=Math.abs(u);n>=u&&(s>h||h===s&&u>0)&&(s=h,o=l)}return o},x.getRawIndex=function(t){var e=this.indices[t];return null==e?-1:e},x.getRawDataItem=function(t){return this._rawData[this.getRawIndex(t)]},x[p]=function(t){return this._nameList[this.indices[t]]||""},x.getId=function(t){return this._idList[this.indices[t]]||this.getRawIndex(t)+""},x.each=function(t,i,n,r){typeof t===le&&(r=n,n=i,i=t,t=[]),t=h.map(e(t),this.getDimension,this);var a=[],o=t[Me],s=this.indices;r=r||this;for(var l=0;l<s[Me];l++)switch(o){case 0:i.call(r,l);break;case 1:i.call(r,this.get(t[0],l,n),l);break;case 2:i.call(r,this.get(t[0],l,n),this.get(t[1],l,n),l);break;default:for(var c=0;o>c;c++)a[c]=this.get(t[c],l,n);a[c]=l,i.apply(r,a)}},x.filterSelf=function(t,i,n,r){typeof t===le&&(r=n,n=i,i=t,t=[]),t=h.map(e(t),this.getDimension,this);var a=[],o=[],s=t[Me],l=this.indices;r=r||this;for(var c=0;c<l[Me];c++){var u;if(1===s)u=i.call(r,this.get(t[0],c,n),c);else{for(var f=0;s>f;f++)o[f]=this.get(t[f],c,n);o[f]=c,u=i.apply(r,o)}u&&a.push(l[c])}return this.indices=a,this._extent={},this},x.mapArray=function(t,e,i,n){typeof t===le&&(n=i,i=e,e=t,t=[]);var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},i,n),r},x.map=function(t,n,r,a){t=h.map(e(t),this.getDimension,this);var o=i(this,t),s=o.indices=this.indices,l=o._storage,c=[];return this.each(t,function(){var e=arguments[arguments[Me]-1],i=n&&n.apply(this,arguments);if(null!=i){typeof i===se&&(c[0]=i,i=c);for(var r=0;r<i[Me];r++){var a=t[r],o=l[a],u=s[e];o&&(o[u]=i[r])}}},r,a),o},x.downSample=function(t,e,n,r){for(var a=i(this,[t]),o=this._storage,s=a._storage,l=this.indices,c=a.indices=[],u=[],h=[],f=Math.floor(1/e),d=s[t],p=this.count(),m=0;m<o[t][Me];m++)s[t][m]=o[t][m];for(var m=0;p>m;m+=f){f>p-m&&(f=p-m,u[Me]=f);for(var v=0;f>v;v++){var g=l[m+v];u[v]=d[g],h[v]=g}var y=n(u),g=h[r(u,y)||0];d[g]=y,c.push(g)}return a},x[P]=function(t){var e=this.hostModel;return t=this.indices[t],new c(this._rawData[t],e,e&&e[f])},x.diff=function(t){var e=this._idList,i=t&&t._idList;return new u(t?t.indices:[],this.indices,function(t){return i[t]||t+""},function(t){return e[t]||t+""})},x.getVisual=function(t){var e=this._visual;return e&&e[t]},x.setVisual=function(t,e){if(m(t))for(var i in t)t.hasOwnProperty(i)&&this.setVisual(i,t[i]);else this._visual=this._visual||{},this._visual[t]=e},x.setLayout=function(t,e){if(m(t))for(var i in t)t.hasOwnProperty(i)&&this.setLayout(i,t[i]);else this._layout[t]=e},x.getLayout=function(t){return this._layout[t]},x[z]=function(t){return this._itemLayouts[t]},x.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?h[Ce](this._itemLayouts[t]||{},e):e},x.clearItemLayouts=function(){this._itemLayouts[Me]=0},x[T]=function(t,e,i){var n=this._itemVisuals[t],r=n&&n[e];return null!=r||i?r:this.getVisual(e)},x.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};if(this._itemVisuals[t]=n,m(e))for(var r in e)e.hasOwnProperty(r)&&(n[r]=e[r]);else n[e]=i},x.clearAllVisual=function(){this._visual={},this._itemVisuals=[]};var b=function(t){t[me]=this[me],t[ve]=this[ve],t.dataType=this.dataType};return x[L]=function(t,e){var i=this.hostModel;e&&(e[ve]=t,e.dataType=this.dataType,e[me]=i&&i[me],"group"===e.type&&e[de](b,e)),this._graphicEls[t]=e},x[k]=function(t){return this._graphicEls[t]},x[C]=function(t,e){h.each(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},x.cloneShallow=function(){var t=h.map(this[N],this.getDimensionInfo,this),e=new _(t,this.hostModel);return e._storage=this._storage,y(e,this),e.indices=this.indices.slice(),this._extent&&(e._extent=h[Ce]({},this._extent)),e},x.wrapMethod=function(t,e){var i=this[t];typeof i===le&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=i.apply(this,arguments);return e.apply(this,[t][H](h.slice(arguments)))})},x.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],x.CHANGABLE_METHODS=["filterSelf"],_}),e("echarts/model/Model",[je,Xe,"../util/clazz","./mixin/lineStyle","./mixin/areaStyle","./mixin/textStyle","./mixin/itemStyle"],function(t){function e(t,e,i){this.parentModel=e,this[f]=i,this[v]=t}var i=t(Xe),n=t("../util/clazz");e[He]={constructor:e,init:null,mergeOption:function(t){i.merge(this[v],t,!0)},get:function(t,e){if(!t)return this[v];typeof t===Ge&&(t=t.split("."));for(var i=this[v],n=this.parentModel,r=0;r<t[Me]&&(!t[r]||(i=i&&"object"==typeof i?i[t[r]]:null,null!=i));r++);return null==i&&n&&!e&&(i=n.get(t)),i},getShallow:function(t,e){var i=this[v],n=null==i?i:i[t],r=this.parentModel;return null==n&&r&&!e&&(n=r[d](t)),n},getModel:function(t,i){var n=this.get(t,!0),r=this.parentModel,a=new e(n,i||r&&r[Be](t),this[f]);return a},isEmpty:function(){return null==this[v]},restoreData:function(){},clone:function(){var t=this.constructor;return new t(i.clone(this[v]))},setReadOnly:function(t){n.setReadOnly(this,t)}},n.enableClassExtend(e);var r=i.mixin;return r(e,t("./mixin/lineStyle")),r(e,t("./mixin/areaStyle")),r(e,t("./mixin/textStyle")),r(e,t("./mixin/itemStyle")),e}),e("echarts/util/number",[je],function(){function t(t){return t[ue](/^\s+/,"")[ue](/\s+$/,"")}var e={},i=1e-4;return e.linearMap=function(t,e,i,n){var r=e[1]-e[0],a=i[1]-i[0];if(0===r)return 0===a?i[0]:(i[0]+i[1])/2;if(n)if(r>0){if(t<=e[0])return i[0];if(t>=e[1])return i[1]}else{if(t>=e[0])return i[0];if(t<=e[1])return i[1]}else{if(t===e[0])return i[0];if(t===e[1])return i[1]}return(t-e[0])/r*a+i[0]},e[_]=function(e,i){switch(e){case Y:case $:e="50%";break;case"left":case"top":e="0%";break;case"right":case Oe:e="100%"}return typeof e===Ge?t(e).match(/%$/)?parseFloat(e)/100*i:parseFloat(e):null==e?0/0:+e},e.round=function(t,e){return null==e&&(e=10),+(+t).toFixed(e)},e.asc=function(t){return t.sort(function(t,e){return t-e}),t},e.getPrecision=function(t){if(t=+t,isNaN(t))return 0;for(var e=1,i=0;Math.round(t*e)/e!==t;)e*=10,i++;return i},e.getPrecisionSafe=function(t){var e=t.toString(),i=e[ae](".");return 0>i?0:e[Me]-1-i},e.getPixelPrecision=function(t,e){var i=Math.log,n=Math.LN10,r=Math.floor(i(t[1]-t[0])/n),a=Math.round(i(Math.abs(e[1]-e[0]))/n);return Math.max(-r+a,0)},e.MAX_SAFE_INTEGER=9007199254740991,e.remRadian=function(t){var e=2*Math.PI;return(t%e+e)%e},e.isRadianAroundZero=function(t){return t>-i&&i>t},e.parseDate=function(t){if(t instanceof Date)return t;if(typeof t===Ge){var e=new Date(t);return isNaN(+e)&&(e=new Date(new Date(t[ue](/-/g,"/"))-new Date("1970/01/01"))),e}return new Date(Math.round(t))},e.quantity=function(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))},e.nice=function(t,i){var n,r=e.quantity(t),a=t/r;return n=i?1.5>a?1:2.5>a?2:4>a?3:7>a?5:10:1>a?1:2>a?2:3>a?3:5>a?5:10,n*r},e}),e("echarts/util/format",[je,Xe,"./number","zrender/contain/text"],function(t){var e=t(Xe),i=t("./number"),n=t("zrender/contain/text"),r={};r.addCommas=function(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0][ue](/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t[Me]>1?"."+t[1]:""))},r.toCamelCase=function(t){return t[qe]()[ue](/-(.)/g,function(t,e){return e.toUpperCase()})},r.normalizeCssArray=function(t){var e=t[Me];return typeof t===se?[t,t,t,t]:2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t},r.encodeHTML=function(t){return String(t)[ue](/&/g,"&amp;")[ue](/</g,"&lt;")[ue](/>/g,"&gt;")[ue](/"/g,"&quot;")[ue](/'/g,"&#39;")};var a=["a","b","c","d","e","f","g"],o=function(t,e){return"{"+t+(null==e?"":e)+"}"};r.formatTpl=function(t,i){e[ce](i)||(i=[i]);var n=i[Me];if(!n)return"";for(var r=i[0].$vars||[],s=0;s<r[Me];s++){var l=a[s];t=t[ue](o(l),o(l,0))}for(var c=0;n>c;c++)for(var u=0;u<r[Me];u++)t=t[ue](o(a[u],c),i[c][r[u]]);return t};var s=function(t){return 10>t?"0"+t:t};return r.formatTime=function(t,e){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var n=i.parseDate(e),r=n.getFullYear(),a=n.getMonth()+1,o=n.getDate(),l=n.getHours(),c=n.getMinutes(),u=n.getSeconds();return t=t[ue]("MM",s(a))[qe]()[ue]("yyyy",r)[ue]("yy",r%100)[ue]("dd",s(o))[ue]("d",o)[ue]("hh",s(l))[ue]("h",l)[ue]("mm",s(c))[ue]("m",c)[ue]("ss",s(u))[ue]("s",u)},r.capitalFirst=function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},r.truncateText=n.truncateText,r}),e("zrender/core/matrix",[],function(){var t=typeof Float32Array===g?Array:Float32Array,e={create:function(){var i=new t(6);return e.identity(i),i},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t},mul:function(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],a=e[0]*i[2]+e[2]*i[3],o=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],l=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t},translate:function(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t},rotate:function(t,e,i){var n=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],c=Math.sin(i),u=Math.cos(i);return t[0]=n*u+o*c,t[1]=-n*c+o*u,t[2]=r*u+s*c,t[3]=-r*c+u*s,t[4]=u*a+c*l,t[5]=u*l-c*a,t},scale:function(t,e,i){var n=i[0],r=i[1];return t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r,t},invert:function(t,e){var i=e[0],n=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=i*o-a*n;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-n*l,t[3]=i*l,t[4]=(n*s-o*r)*l,t[5]=(a*r-i*s)*l,t):null}};return e}),e(We,[],function(){var t=typeof Float32Array===g?Array:Float32Array,e={create:function(e,i){var n=new t(2);return null==e&&(e=0),null==i&&(i=0),n[0]=e,n[1]=i,n},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:function(e){var i=new t(2);return i[0]=e[0],i[1]=e[1],i},set:function(t,e,i){return t[0]=e,t[1]=i,t},add:function(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t},scaleAndAdd:function(t,e,i,n){return t[0]=e[0]+i[0]*n,t[1]=e[1]+i[1]*n,t},sub:function(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t},len:function(t){return Math.sqrt(this.lenSquare(t))},lenSquare:function(t){return t[0]*t[0]+t[1]*t[1]},mul:function(t,e,i){return t[0]=e[0]*i[0],t[1]=e[1]*i[1],t},div:function(t,e,i){return t[0]=e[0]/i[0],t[1]=e[1]/i[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:function(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t},normalize:function(t,i){var n=e.len(i);return 0===n?(t[0]=0,t[1]=0):(t[0]=i[0]/n,t[1]=i[1]/n),t},distance:function(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))},distanceSquare:function(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,i,n){return t[0]=e[0]+n*(i[0]-e[0]),t[1]=e[1]+n*(i[1]-e[1]),t},applyTransform:function(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t},min:function(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t},max:function(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}};return e[Me]=e.len,e.lengthSquare=e.lenSquare,e.dist=e.distance,e.distSquare=e.distanceSquare,e}),e("echarts/chart/pie/PieSeries",[je,"../../data/List",Xe,"../../util/model","../../data/helper/completeDimensions","../../component/helper/selectableMixin",I],function(t){var e=t("../../data/List"),i=t(Xe),n=t("../../util/model"),r=t("../../data/helper/completeDimensions"),o=t("../../component/helper/selectableMixin"),s=t(I).extendSeriesModel({type:"series.pie",init:function(t){s.superApply(this,"init",arguments),this.legendDataProvider=function(){return this._dataBeforeProcessed},this.updateSelectedMap(t.data),this._defaultLabelLine(t)},mergeOption:function(t){s.superCall(this,m,t),this.updateSelectedMap(this[v].data)},getInitialData:function(t){var i=r(["value"],t.data),n=new e(i,this);return n[a](t.data),n},getDataParams:function(t){var e=this._data,i=s.superCall(this,pe,t),n=e.getSum("value");return i.percent=n?+(e.get("value",t)/n*100).toFixed(2):0,i.$vars.push("percent"),i},_defaultLabelLine:function(t){n.defaultEmphasis(t.labelLine,["show"]);var e=t.labelLine[D],i=t.labelLine[b];e.show=e.show&&t.label[D].show,i.show=i.show&&t.label[b].show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,selectedOffset:10,avoidLabelOverlap:!0,label:{normal:{rotate:!1,show:!0,position:"outer"},emphasis:{}},labelLine:{normal:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}}},itemStyle:{normal:{borderWidth:1},emphasis:{}},animationEasing:"cubicOut",data:[]}});return i.mixin(s,o),s}),e("echarts/chart/pie/PieView",[je,O,Xe,"../../view/Chart"],function(t){function e(t,e,n,r){var a=e[Ze](),o=this[ve],s=a[p](o),l=e.get("selectedOffset");r[Te]({type:"pieToggleSelect",from:t,name:s,seriesId:e.id}),a.each(function(t){i(a[k](t),a[z](t),e.isSelected(a[p](t)),l,n)})}function i(t,e,i,n,r){var a=(e.startAngle+e.endAngle)/2,o=Math.cos(a),s=Math.sin(a),l=i?n:0,c=[o*l,s*l];r?t.animate().when(200,{position:c}).start("bounceOut"):t.attr(j,c)}function n(t,e){function i(){a[Re]=a.hoverIgnore,o[Re]=o.hoverIgnore}function n(){a[Re]=a.normalIgnore,o[Re]=o.normalIgnore}s.Group.call(this);var r=new s.Sector({z2:2}),a=new s.Polyline,o=new s.Text;this.add(r),this.add(a),this.add(o),this.updateData(t,e,!0),this.on(b,i).on(D,n).on(_e,i).on(ye,n)}function a(t,e,i,n,r){var a=n[Be](ee),o=r===l||"inner"===r;return{fill:a[K]()||(o?"#fff":t[T](e,"color")),opacity:t[T](e,X),textFont:a[J](),text:c[B](t.hostModel.getFormattedLabel(e,i),t[p](e))}}var s=t(O),c=t(Xe),u=n[He];u.updateData=function(t,e,n){function r(){o.stopAnimation(!0),o.animateTo({shape:{r:h.r+10}},300,"elasticOut")}function a(){o.stopAnimation(!0),o.animateTo({shape:{r:h.r}},300,"elasticOut")}var o=this.childAt(0),l=t.hostModel,u=t[P](e),h=t[z](e),f=c[Ce]({},h);f.label=null,n?(o.setShape(f),o.shape.endAngle=h.startAngle,s[A](o,{shape:{endAngle:h.endAngle}},l,e)):s[A](o,{shape:f},l,e);var d=u[Be]("itemStyle"),p=t[T](e,"color");o.useStyle(c[Se]({lineJoin:"bevel",fill:p},d[Be](D).getItemStyle())),o.hoverStyle=d[Be](b).getItemStyle(),i(this,t[z](e),u.get("selected"),l.get("selectedOffset"),l.get(Fe)),o.off(_e).off(ye).off(b).off(D),u.get("hoverAnimation")&&l.ifEnableAnimation()&&o.on(_e,r).on(ye,a).on(b,r).on(D,a),this._updateLabel(t,e),s[x](this)},u._updateLabel=function(t,e){var i=this.childAt(1),n=this.childAt(2),l=t.hostModel,c=t[P](e),u=t[z](e),h=u.label,f=t[T](e,"color");s[A](i,{shape:{points:h.linePoints||[[h.x,h.y],[h.x,h.y],[h.x,h.y]]}},l,e),s[A](n,{style:{x:h.x,y:h.y}},l,e),n.attr({style:{textVerticalAlign:h.verticalAlign,textAlign:h[te],textFont:h.font},rotation:h[o],origin:[h.x,h.y],z2:10});var d=c[Be](S),p=c[Be](M),m=c[Be]("labelLine.normal"),v=c[Be]("labelLine.emphasis"),g=d.get(j)||p.get(j);n[fe](a(t,e,D,d,g)),n[Re]=n.normalIgnore=!d.get("show"),n.hoverIgnore=!p.get("show"),i[Re]=i.normalIgnore=!m.get("show"),i.hoverIgnore=!v.get("show"),i[fe]({stroke:f,opacity:t[T](e,X)}),i[fe](m[Be]("lineStyle")[r]()),n.hoverStyle=a(t,e,b,p,g),i.hoverStyle=v[Be]("lineStyle")[r]();var y=m.get("smooth");y&&y===!0&&(y=.4),i.setShape({smooth:y})},c[re](n,s.Group);var h=t("../../view/Chart")[Ce]({type:"pie",init:function(){var t=new s.Group;this._sectorGroup=t},render:function(t,i,r,a){if(!a||a.from!==this.uid){var o=t[Ze](),s=this._data,l=this.group,u=i.get(Fe),h=!s,f=c.curry(e,this.uid,t,u,r),d=t.get("selectedMode");if(o.diff(s).add(function(t){var e=new n(o,t);h&&e.eachChild(function(t){t.stopAnimation(!0)}),d&&e.on("click",f),o[L](t,e),l.add(e)})[ze](function(t,e){var i=s[k](e);i.updateData(o,t),i.off("click"),d&&i.on("click",f),l.add(i),o[L](t,i)})[ke](function(t){var e=s[k](t);l[ke](e)}).execute(),u&&h&&o.count()>0){var p=o[z](0),m=Math.max(r[Ne](),r[Ee]())/2,v=c.bind(l.removeClipPath,l);l.setClipPath(this._createClipPath(p.cx,p.cy,m,p.startAngle,p.clockwise,v,t))}this._data=o}},_createClipPath:function(t,e,i,n,r,a,o){var l=new s.Sector({shape:{cx:t,cy:e,r0:0,r:i,startAngle:n,endAngle:n,clockwise:r}});return s.initProps(l,{shape:{endAngle:n+(r?1:-1)*Math.PI*2}},o,a),l}});return h}),e("echarts/action/createDataSelectAction",[je,"../echarts",Xe],function(t){var e=t("../echarts"),i=t(Xe);return function(t,n){i.each(n,function(i){i[ze]="updateView",e.registerAction(i,function(e,n){var r={};return n.eachComponent({mainType:"series",subType:t,query:e},function(t){t[i.method]&&t[i.method](e.name);var n=t[Ze]();n.each(function(e){var i=n[p](e);r[i]=t.isSelected(i)||!1})}),{name:e.name,selected:r}})})}}),e("echarts/visual/dataColor",[je],function(){return function(t,e){var i={};e.eachRawSeriesByType(t,function(t){var n=t.getRawData(),r={};if(!e.isSeriesFiltered(t)){var a=t[Ze]();a.each(function(t){var e=a.getRawIndex(t);r[e]=t}),n.each(function(e){var o=n[P](e),s=r[e],l=null!=s&&a[T](s,"color",!0);if(l)n.setItemVisual(e,"color",l);else{var c=o.get("itemStyle.normal.color")||t.getColorFromPalette(n[p](e),i);n.setItemVisual(e,"color",c),null!=s&&a.setItemVisual(s,"color",c)}})}})}}),e("echarts/chart/pie/pieLayout",[je,"../../util/number","./labelLayout",Xe],function(t){var e=t("../../util/number"),i=e[_],n=t("./labelLayout"),r=t(Xe),a=2*Math.PI,o=Math.PI/180;return function(t,s,l){s.eachSeriesByType(t,function(t){var s=t.get(Y),c=t.get("radius");r[ce](c)||(c=[0,c]),r[ce](s)||(s=[s,s]);var u=l[Ne](),h=l[Ee](),f=Math.min(u,h),d=i(s[0],u),p=i(s[1],h),m=i(c[0],f/2),v=i(c[1],f/2),g=t[Ze](),y=-t.get("startAngle")*o,_=t.get("minAngle")*o,x=g.getSum("value"),b=Math.PI/(x||g.count())*2,w=t.get("clockwise"),M=t.get("roseType"),S=g.getDataExtent("value");S[0]=0;var T=a,C=0,k=y,L=w?1:-1;if(g.each("value",function(t,i){var n;n="area"!==M?0===x?b:t*b:a/(g.count()||1),_>n?(n=_,T-=_):C+=t;var r=k+L*n;g.setItemLayout(i,{angle:n,startAngle:k,endAngle:r,clockwise:w,cx:d,cy:p,r0:m,r:M?e.linearMap(t,S,[m,v]):v}),k=r},!0),a>T)if(.001>=T){var A=a/g.count();g.each(function(t){var e=g[z](t);e.startAngle=y+L*t*A,e.endAngle=y+L*(t+1)*A})}else b=T/C,k=y,g.each("value",function(t,e){var i=g[z](e),n=i.angle===_?_:t*b;i.startAngle=k,i.endAngle=k+L*n,k+=n});n(t,v,u,h)})}}),e("echarts/processor/dataFilter",[],function(){return function(t,e){var i=e.findComponents({mainType:"legend"});i&&i[Me]&&e.eachSeriesByType(t,function(t){var e=t[Ze]();e.filterSelf(function(t){for(var n=e[p](t),r=0;r<i[Me];r++)if(!i[r].isSelected(n))return!1;return!0},this)},this)}}),e("echarts/chart/line/LineSeries",[je,"../helper/createListFromArray","../../model/Series"],function(t){var e=t("../helper/createListFromArray"),i=t("../../model/Series");return i[Ce]({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,i){return e(t.data,this,i)},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clipOverflow:!0,label:{normal:{position:"top"}},lineStyle:{normal:{width:2,type:"solid"}},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:!1,connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}})}),e("echarts/chart/line/LineView",[je,Xe,"../helper/SymbolDraw","../helper/Symbol","./lineAnimationDiff",O,"./poly","../../view/Chart"],function(t){function e(t,e){if(t[Me]===e[Me]){for(var i=0;i<t[Me];i++){var n=t[i],r=e[i];if(n[0]!==r[0]||n[1]!==r[1])return}return!0}}function i(t){return typeof t===se?t:t?.3:0}function n(t){var e=t.getGlobalExtent();if(t.onBand){var i=t.getBandWidth()/2-1,n=e[1]>e[0]?1:-1;e[0]+=n*i,e[1]-=n*i}return e}function a(t){return t>=0?1:-1}function o(t,e){var i=t[R](),n=t.getOtherAxis(i),r=i.onZero?0:n.scale[W]()[0],o=n.dim,s="x"===o||"radius"===o?1:0;return e.mapArray([o],function(n,l){for(var c,u=e.stackedOn;u&&a(u.get(o,l))===a(n);){c=u;break}var h=[];return h[s]=e.get(i.dim,l),h[1-s]=c?c.get(o,l,!0):r,t[E](h)},!0)}function s(t,e){return null!=e[ve]?e[ve]:null!=e.name?t.indexOfName(e.name):void 0}function l(t,e,i){var r=n(t[V]("x")),a=n(t[V]("y")),o=t[R]().isHorizontal(),s=Math.min(r[0],r[1]),l=Math.min(a[0],a[1]),c=Math.max(r[0],r[1])-s,u=Math.max(a[0],a[1])-l,h=i.get("lineStyle.normal.width")||2,f=i.get("clipOverflow")?h/2:Math.max(c,u);o?(l-=f,u+=2*f):(s-=f,c+=2*f);var d=new _.Rect({shape:{x:s,y:l,width:c,height:u}});return e&&(d.shape[o?"width":Ie]=0,_.initProps(d,{shape:{width:c,height:u}},i)),d}function c(t,e,i){var n=t.getAngleAxis(),r=t.getRadiusAxis(),a=r[W](),o=n[W](),s=Math.PI/180,l=new _.Sector({shape:{cx:t.cx,cy:t.cy,r0:a[0],r:a[1],startAngle:-o[0]*s,endAngle:-o[1]*s,clockwise:n.inverse}});return e&&(l.shape.endAngle=-o[0]*s,_.initProps(l,{shape:{endAngle:-o[1]*s}},i)),l}function u(t,e,i){return"polar"===t.type?c(t,e,i):l(t,e,i)}function h(t,e,i){for(var n=e[R](),r="x"===n.dim||"radius"===n.dim?0:1,a=[],o=0;o<t[Me]-1;o++){var s=t[o+1],l=t[o];a.push(l);var c=[];switch(i){case"end":c[r]=s[r],c[1-r]=l[1-r],a.push(c);break;case $:var u=(l[r]+s[r])/2,h=[];c[r]=h[r]=u,c[1-r]=l[1-r],h[1-r]=s[1-r],a.push(c),a.push(h);break;default:c[r]=l[r],c[1-r]=s[1-r],a.push(c)}}return t[o]&&a.push(t[o]),a}function f(t,e){return Math.max(Math.min(t,e[1]),e[0])}function d(t,e){var i=t.getVisual("visualMeta");if(i&&i[Me]){for(var n,r=i[Me]-1;r>=0;r--)if(i[r].dimension<2){n=i[r];break}if(n&&"cartesian2d"===e.type){var a=n.dimension,o=t[N][a],s=t.getDataExtent(o),l=n.stops,c=[];l[0].interval&&l.sort(function(t,e){return t.interval[0]-e.interval[0]});var u=l[0],h=l[l[Me]-1],d=u.interval?f(u.interval[0],s):u.value,p=h.interval?f(h.interval[1],s):h.value,m=p-d;if(0===m)return t[T](0,"color");for(var r=0;r<l[Me];r++)if(l[r].interval){if(l[r].interval[1]===l[r].interval[0])continue;c.push({offset:(f(l[r].interval[0],s)-d)/m,color:l[r].color},{offset:(f(l[r].interval[1],s)-d)/m,color:l[r].color})}else c.push({offset:(l[r].value-d)/m,color:l[r].color});var v=new _.LinearGradient(0,0,0,0,c,!0),g=e[V](o),x=Math.round(g.toGlobalCoord(g[y](d))),b=Math.round(g.toGlobalCoord(g[y](p)));return v[o]=x,v[o+"2"]=b,v}}}var p=t(Xe),m=t("../helper/SymbolDraw"),v=t("../helper/Symbol"),g=t("./lineAnimationDiff"),_=t(O),x=t("./poly"),b=t("../../view/Chart");return b[Ce]({type:"line",init:function(){var t=new _.Group,e=new m;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,n,a){var s=t[ie],l=this.group,c=t[Ze](),f=t[Be]("lineStyle.normal"),m=t[Be]("areaStyle.normal"),v=c.mapArray(c[z],!0),g="polar"===s.type,y=this._coordSys,_=this._symbolDraw,x=this._polyline,b=this._polygon,w=this._lineGroup,M=t.get(Fe),S=!m.isEmpty(),T=o(s,c),k=t.get("showSymbol"),A=k&&!g&&!t.get("showAllSymbol")&&this._getSymbolIgnoreFunc(c,s),P=this._data;P&&P[C](function(t,e){t.__temp&&(l[ke](t),P[L](e,null))}),k||_[ke](),l.add(w);var D=!g&&t.get("step");x&&y.type===s.type&&D===this._step?(S&&!b?b=this._newPolygon(v,T,s,M):b&&!S&&(w[ke](b),b=this._polygon=null),w.setClipPath(u(s,!1,t)),k&&_.updateData(c,A),c[C](function(t){t.stopAnimation(!0)}),e(this._stackedOnPoints,T)&&e(this._points,v)||(M?this._updateAnimation(c,T,s,a,D):(D&&(v=h(v,s,D),T=h(T,s,D)),x.setShape({points:v}),b&&b.setShape({points:v,stackedOnPoints:T})))):(k&&_.updateData(c,A),D&&(v=h(v,s,D),T=h(T,s,D)),x=this._newPolyline(v,s,M),S&&(b=this._newPolygon(v,T,s,M)),w.setClipPath(u(s,!0,t)));
var I=d(c,s)||c.getVisual("color");x.useStyle(p[Se](f[r](),{fill:"none",stroke:I,lineJoin:"bevel"}));var O=t.get("smooth");if(O=i(t.get("smooth")),x.setShape({smooth:O,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),b){var R=c.stackedOn,E=0;if(b.useStyle(p[Se](m.getAreaStyle(),{fill:I,opacity:.7,lineJoin:"bevel"})),R){var N=R.hostModel;E=i(N.get("smooth"))}b.setShape({smooth:O,stackedOnSmooth:E,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=c,this._coordSys=s,this._stackedOnPoints=T,this._points=v,this._step=D},highlight:function(t,e,i,n){var r=t[Ze](),a=s(r,n);if(!(a instanceof Array)&&null!=a&&a>=0){var o=r[k](a);if(!o){var l=r[z](a);o=new v(r,a),o[j]=l,o.setZ(t.get(he),t.get("z")),o[Re]=isNaN(l[0])||isNaN(l[1]),o.__temp=!0,r[L](a,o),o.stopSymbolAnimation(!0),this.group.add(o)}o.highlight()}else b[He].highlight.call(this,t,e,i,n)},downplay:function(t,e,i,n){var r=t[Ze](),a=s(r,n);if(null!=a&&a>=0){var o=r[k](a);o&&(o.__temp?(r[L](a,null),this.group[ke](o)):o.downplay())}else b[He].downplay.call(this,t,e,i,n)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup[ke](e),e=new x.Polyline({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var i=this._polygon;return i&&this._lineGroup[ke](i),i=new x.Polygon({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(i),this._polygon=i,i},_getSymbolIgnoreFunc:function(t,e){var i=e.getAxesByScale(F)[0];return i&&i.isLabelIgnored?p.bind(i.isLabelIgnored,i):void 0},_updateAnimation:function(t,e,i,n,r){var a=this._polyline,o=this._polygon,s=t.hostModel,l=g(this._data,t,this._stackedOnPoints,e,this._coordSys,i),c=l.current,u=l.stackedOnCurrent,f=l.next,d=l.stackedOnNext;r&&(c=h(l.current,i,r),u=h(l.stackedOnCurrent,i,r),f=h(l.next,i,r),d=h(l.stackedOnNext,i,r)),a.shape.__points=l.current,a.shape.points=c,_[A](a,{shape:{points:f}},s),o&&(o.setShape({points:c,stackedOnPoints:u}),_[A](o,{shape:{points:f,stackedOnPoints:d,__points:l.next}},s));for(var p=[],m=l.status,v=0;v<m[Me];v++){var y=m[v].cmd;if("="===y){var x=t[k](m[v].idx1);x&&p.push({el:x,ptIdx:v})}}a.animators&&a.animators[Me]&&a.animators[0].during(function(){for(var t=0;t<p[Me];t++){var e=p[t].el;e.attr(j,a.shape.__points[p[t].ptIdx])}})},remove:function(){var t=this.group,e=this._data;this._lineGroup[ne](),this._symbolDraw[ke](!0),e&&e[C](function(i,n){i.__temp&&(t[ke](i),e[L](n,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}})}),e("echarts/visual/symbol",[je],function(){return function(t,e,i,n){n.eachRawSeriesByType(t,function(t){var r=t[Ze](),a=t.get("symbol")||e,o=t.get("symbolSize");r.setVisual({legendSymbol:i||a,symbol:a,symbolSize:o}),n.isSeriesFiltered(t)||(typeof o===le&&r.each(function(e){var i=t[w](e),n=t[pe](e);r.setItemVisual(e,"symbolSize",o(i,n))}),r.each(function(t){var e=r[P](t),i=e[d]("symbol",!0),n=e[d]("symbolSize",!0);null!=i&&r.setItemVisual(t,"symbol",i),null!=n&&r.setItemVisual(t,"symbolSize",n)}))})}}),e("echarts/layout/points",[je],function(){return function(t,e){e.eachSeriesByType(t,function(t){var e=t[Ze](),i=t[ie];if(i){var n=i[N];"singleAxis"===i.type?e.each(n[0],function(t,n){e.setItemLayout(n,isNaN(t)?[0/0,0/0]:i[E](t))}):e.each(n,function(t,n,r){e.setItemLayout(r,isNaN(t)||isNaN(n)?[0/0,0/0]:i[E]([t,n]))},!0)}})}}),e("echarts/processor/dataSample",[],function(){var t={average:function(t){for(var e=0,i=0,n=0;n<t[Me];n++)isNaN(t[n])||(e+=t[n],i++);return 0===i?0/0:e/i},sum:function(t){for(var e=0,i=0;i<t[Me];i++)e+=t[i]||0;return e},max:function(t){for(var e=-1/0,i=0;i<t[Me];i++)t[i]>e&&(e=t[i]);return e},min:function(t){for(var e=1/0,i=0;i<t[Me];i++)t[i]<e&&(e=t[i]);return e},nearest:function(t){return t[0]}},e=function(t){return Math.round(t[Me]/2)};return function(i,n){n.eachSeriesByType(i,function(i){var n=i[Ze](),r=i.get("sampling"),a=i[ie];if("cartesian2d"===a.type&&r){var o=a[R](),s=a.getOtherAxis(o),l=o[W](),c=l[1]-l[0],u=Math.round(n.count()/c);if(u>1){var h;typeof r===Ge?h=t[r]:typeof r===le&&(h=r),h&&(n=n.downSample(s.dim,1/u,h,e),i.setData(n))}}},this)}}),e("echarts/component/axis",[je,"../coord/cartesian/AxisModel","./axis/AxisView"],function(t){t("../coord/cartesian/AxisModel"),t("./axis/AxisView")}),e("echarts/util/layout",[je,Xe,"zrender/core/BoundingRect","./number","./format"],function(t){function e(t,e,i,n,r){var a=0,o=0;null==n&&(n=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,c){var u,h,f=l[j],d=l[Q](),p=e.childAt(c+1),m=p&&p[Q]();if("horizontal"===t){var v=d.width+(m?-m.x+d.x:0);u=a+v,u>n||l.newline?(a=0,u=v,o+=s+i,s=d[Ie]):s=Math.max(s,d[Ie])}else{var g=d[Ie]+(m?-m.y+d.y:0);h=o+g,h>r||l.newline?(a+=s+i,o=0,h=g,s=d.width):s=Math.max(s,d.width)}l.newline||(f[0]=a,f[1]=o,"horizontal"===t?a=u+i:o=h+i)})}var i=t(Xe),n=t("zrender/core/BoundingRect"),r=t("./number"),a=t("./format"),o=r[_],s=i.each,l={},c=["left","right","top",Oe,"width",Ie];return l.box=e,l.vbox=i.curry(e,"vertical"),l.hbox=i.curry(e,"horizontal"),l.getAvailableSize=function(t,e,i){var n=e.width,r=e[Ie],s=o(t.x,n),l=o(t.y,r),c=o(t.x2,n),u=o(t.y2,r);return(isNaN(s)||isNaN(parseFloat(t.x)))&&(s=0),(isNaN(c)||isNaN(parseFloat(t.x2)))&&(c=n),(isNaN(l)||isNaN(parseFloat(t.y)))&&(l=0),(isNaN(u)||isNaN(parseFloat(t.y2)))&&(u=r),i=a.normalizeCssArray(i||0),{width:Math.max(c-s-i[1]-i[3],0),height:Math.max(u-l-i[0]-i[2],0)}},l.getLayoutRect=function(t,e,i){i=a.normalizeCssArray(i||0);var r=e.width,s=e[Ie],l=o(t.left,r),c=o(t.top,s),u=o(t.right,r),h=o(t[Oe],s),f=o(t.width,r),d=o(t[Ie],s),p=i[2]+i[0],m=i[1]+i[3],v=t.aspect;switch(isNaN(f)&&(f=r-u-m-l),isNaN(d)&&(d=s-h-p-c),isNaN(f)&&isNaN(d)&&(v>r/s?f=.8*r:d=.8*s),null!=v&&(isNaN(f)&&(f=v*d),isNaN(d)&&(d=f/v)),isNaN(l)&&(l=r-u-f-m),isNaN(c)&&(c=s-h-d-p),t.left||t.right){case Y:l=r/2-f/2-i[3];break;case"right":l=r-f-m}switch(t.top||t[Oe]){case $:case Y:c=s/2-d/2-i[0];break;case Oe:c=s-d-p}l=l||0,c=c||0,isNaN(f)&&(f=r-l-(u||0)),isNaN(d)&&(d=s-c-(h||0));var g=new n(l+i[3],c+i[0],f,d);return g.margin=i,g},l.positionGroup=function(t,e,n,r){var a=t[Q]();e=i[Ce](i.clone(e),{width:a.width,height:a[Ie]}),e=l.getLayoutRect(e,n,r),t.attr(j,[e.x-a.x,e.y-a.y])},l.mergeLayoutParam=function(t,e,n){function r(i){var r={},l=0,c={},u=0,h=n.ignoreSize?1:2;if(s(i,function(e){c[e]=t[e]}),s(i,function(t){a(e,t)&&(r[t]=c[t]=e[t]),o(r,t)&&l++,o(c,t)&&u++}),u!==h&&l){if(l>=h)return r;for(var f=0;f<i[Me];f++){var d=i[f];if(!a(r,d)&&a(t,d)){r[d]=t[d];break}}return r}return c}function a(t,e){return t.hasOwnProperty(e)}function o(t,e){return null!=t[e]&&"auto"!==t[e]}function l(t,e,i){s(t,function(t){e[t]=i[t]})}!i[Le](n)&&(n={});var c=["width","left","right"],u=[Ie,"top",Oe],h=r(c),f=r(u);l(c,t,h),l(u,t,f)},l.getLayoutParams=function(t){return l.copyLayoutParams({},t)},l.copyLayoutParams=function(t,e){return e&&t&&s(c,function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t},l}),e("echarts/component/legend/LegendModel",[je,Xe,"../../model/Model",I],function(t){var e=t(Xe),i=t("../../model/Model"),n=t(I).extendComponentModel({type:"legend",dependencies:[we],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,i){this.mergeDefaultAndTheme(t,i),t.selected=t.selected||{}},mergeOption:function(t){n.superCall(this,m,t)},optionUpdated:function(){this._updateData(this[f]);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,i=0;i<t[Me];i++){var n=t[i].get("name");if(this.isSelected(n)){this.select(n),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var n=e.map(this.get("data")||[],function(t){return(typeof t===Ge||typeof t===se)&&(t={name:t}),new i(t,this,this[f])},this);this._data=n;var r=e.map(t.getSeries(),function(t){return t.name});t[Pe](function(t){if(t.legendDataProvider){var e=t.legendDataProvider();r=r[H](e.mapArray(e[p]))}}),this._availableNames=r},getData:function(){return this._data},select:function(t){var i=this[v].selected,n=this.get("selectedMode");if("single"===n){var r=this._data;e.each(r,function(t){i[t.get("name")]=!1})}i[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this[v].selected[t]=!1)},toggleSelected:function(t){var e=this[v].selected;t in e||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},isSelected:function(t){var i=this[v].selected;return!(t in i&&!i[t])&&e[ae](this._availableNames,t)>=0},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:"top",align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",textStyle:{color:"#333"},selectedMode:!0,tooltip:{show:!1}}});return n}),e("echarts/component/legend/legendAction",[je,I,Xe],function(t){function e(t,e,i){var r,a={},o="toggleSelected"===t;return i.eachComponent("legend",function(i){o&&null!=r?i[r?"select":"unSelect"](e.name):(i[t](e.name),r=i.isSelected(e.name));var s=i[Ze]();n.each(s,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var n=i.isSelected(e);a[e]=e in a?a[e]&&n:n}})}),{name:e.name,selected:a}}var i=t(I),n=t(Xe);i.registerAction("legendToggleSelect","legendselectchanged",n.curry(e,"toggleSelected")),i.registerAction("legendSelect","legendselected",n.curry(e,"select")),i.registerAction("legendUnSelect","legendunselected",n.curry(e,"unSelect"))}),e("echarts/component/legend/LegendView",[je,Xe,"../../util/symbol",O,"../helper/listComponent",I],function(t){function e(t,e){e[Te]({type:"legendToggleSelect",name:t})}function i(t,e,i){var n=i.getZr().storage.getDisplayList()[0];n&&n.useHoverLayer||t.get("legendHoverLink")&&i[Te]({type:"highlight",seriesName:t.name,name:e})}function n(t,e,i){var n=i.getZr().storage.getDisplayList()[0];n&&n.useHoverLayer||t.get("legendHoverLink")&&i[Te]({type:"downplay",seriesName:t.name,name:e})}var r=t(Xe),a=t("../../util/symbol"),o=t(O),s=t("../helper/listComponent"),l=r.curry;return t(I).extendComponentView({type:"legend",init:function(){this._symbolTypeStore={}},render:function(t,a,c){var u=this.group;if(u[ne](),t.get("show")){var h=t.get("selectedMode"),f=t.get("align");"auto"===f&&(f="right"===t.get("left")&&"vertical"===t.get("orient")?"right":"left");var d={};r.each(t[Ze](),function(r){var s=r.get("name");if(""===s||"\n"===s)return void u.add(new o.Group({newline:!0}));var p=a.getSeriesByName(s)[0];if(!d[s])if(p){var m=p[Ze](),v=m.getVisual("color");typeof v===le&&(v=v(p[pe](0)));var g=m.getVisual("legendSymbol")||"roundRect",y=m.getVisual("symbol"),_=this._createItem(s,r,t,g,y,f,v,h);_.on("click",l(e,s,c)).on(_e,l(i,p,"",c)).on(ye,l(n,p,"",c)),d[s]=!0}else a.eachRawSeries(function(a){if(!d[s]&&a.legendDataProvider){var o=a.legendDataProvider(),u=o.indexOfName(s);if(0>u)return;var p=o[T](u,"color"),m="roundRect",v=this._createItem(s,r,t,m,null,f,p,h);v.on("click",l(e,s,c)).on(_e,l(i,a,s,c)).on(ye,l(n,a,s,c)),d[s]=!0}},this)},this),s.layout(u,t,c),s.addBackground(u,t)}},_createItem:function(t,e,i,n,s,l,c,u){var h=i.get("itemWidth"),f=i.get("itemHeight"),d=i.get("inactiveColor"),p=i.isSelected(t),m=new o.Group,g=e[Be](ee),y=e.get("icon"),_=e[Be]("tooltip");if(n=y||n,m.add(a.createSymbol(n,0,0,h,f,p?c:d)),!y&&s&&(s!==n||"none"==s)){var b=.8*f;"none"===s&&(s="circle"),m.add(a.createSymbol(s,(h-b)/2,(f-b)/2,b,b,p?c:d))}var w="left"===l?h+5:-5,M=l,S=i.get("formatter"),T=t;typeof S===Ge&&S?T=S[ue]("{name}",t):typeof S===le&&(T=S(t));var C=new o.Text({style:{text:T,x:w,y:f/2,fill:p?g[K]():d,textFont:g[J](),textAlign:M,textVerticalAlign:"middle"}});m.add(C);var k=new o.Rect({shape:m[Q](),invisible:!0,tooltip:_.get("show")?r[Ce]({content:t,formatter:function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},_[v]):null});return m.add(k),m.eachChild(function(t){t[xe]=!0}),k[xe]=!u,this.group.add(m),o[x](m),m}})}),e("echarts/component/legend/legendFilter",[],function(){return function(t){var e=t.findComponents({mainType:"legend"});e&&e[Me]&&t.filterSeries(function(t){for(var i=0;i<e[Me];i++)if(!e[i].isSelected(t.name))return!1;return!0})}}),e("echarts/component/tooltip/TooltipModel",[je,I],function(t){t(I).extendComponentModel({type:"tooltip",defaultOption:{zlevel:0,z:8,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove",alwaysShowContent:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:!0,animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",lineStyle:{color:"#555",width:1,type:"solid"},crossStyle:{color:"#555",width:1,type:"dashed",textStyle:{}},shadowStyle:{color:"rgba(150,150,150,0.3)"}},textStyle:{color:"#fff",fontSize:14}}})}),e("echarts/component/marker/MarkPointModel",[je,"./MarkerModel"],function(t){return t("./MarkerModel")[Ce]({type:"markPoint",defaultOption:{zlevel:0,z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{normal:{show:!0,position:"inside"},emphasis:{show:!0}},itemStyle:{normal:{borderWidth:2}}}})}),e("echarts/component/marker/MarkPointView",[je,"../../chart/helper/SymbolDraw",Xe,"../../util/number","../../data/List","./markerHelper","./MarkerView"],function(t){function e(t,e,i){var n=e[ie];t.each(function(r){var a,s=t[P](r),l=o[_](s.get("x"),i[Ne]()),c=o[_](s.get("y"),i[Ee]());if(isNaN(l)||isNaN(c)){if(e.getMarkerPosition)a=e.getMarkerPosition(t.getValues(t[N],r));else if(n){var u=t.get(n[N][0],r),h=t.get(n[N][1],r);a=n[E]([u,h])}}else a=[l,c];isNaN(l)||(a[0]=l),isNaN(c)||(a[1]=c),t.setItemLayout(r,a)})}function i(t,e,i){var n;n=t?r.map(t&&t[N],function(t){var i=e[Ze]().getDimensionInfo(e.coordDimToDataDim(t)[0])||{};return i.name=t,i}):[{name:"value",type:"float"}];var o=new s(n,i),c=r.map(i.get("data"),r.curry(l.dataTransform,e));return t&&(c=r[oe](c,r.curry(l.dataFilter,t))),o[a](c,null,t?l.dimValueGetter:function(t){return t.value}),o}var n=t("../../chart/helper/SymbolDraw"),r=t(Xe),o=t("../../util/number"),s=t("../../data/List"),l=t("./markerHelper");t("./MarkerView")[Ce]({type:"markPoint",updateLayout:function(t,i,n){i[Pe](function(t){var i=t.markPointModel;i&&(e(i[Ze](),t,n),this.markerGroupMap[t.name][Ae](i))},this)},renderSeries:function(t,r,a,o){var s=t[ie],l=t.name,c=t[Ze](),u=this.markerGroupMap,h=u[l];h||(h=u[l]=new n);var f=i(s,t,r);r.setData(f),e(r[Ze](),t,o),f.each(function(t){var e=f[P](t),i=e[d]("symbolSize");typeof i===le&&(i=i(r[w](t),r[pe](t))),f.setItemVisual(t,{symbolSize:i,color:e.get("itemStyle.normal.color")||c.getVisual("color"),symbol:e[d]("symbol")})}),h.updateData(f),this.group.add(h.group),f[C](function(t){t[de](function(t){t.dataModel=r})}),h.__keep=!0,h.group[xe]=r.get(xe)||t.get(xe)}})}),e("echarts/component/marker/MarkLineModel",[je,"./MarkerModel"],function(t){return t("./MarkerModel")[Ce]({type:"markLine",defaultOption:{zlevel:0,z:5,symbol:["circle","arrow"],symbolSize:[8,16],precision:2,tooltip:{trigger:"item"},label:{normal:{show:!0,position:"end"},emphasis:{show:!0}},lineStyle:{normal:{type:"dashed"},emphasis:{width:3}},animationEasing:"linear"}})}),e("echarts/component/tooltip/TooltipView",[je,"./TooltipContent",O,Xe,"../../util/format","../../util/number",Ue,"../../model/Model",I],function(t){function e(t,e){if(!t||!e)return!1;var i=b.round;return i(t[0])===i(e[0])&&i(t[1])===i(e[1])}function i(t,e,i,n){return{x1:t,y1:e,x2:i,y2:n}}function a(t,e,i,n){return{x:t,y:e,width:i,height:n}}function o(t,e,i,n,r,a){return{cx:t,cy:e,r0:i,r:n,startAngle:r,endAngle:a,clockwise:!0}}function s(t,e,i,n,r){var a=i.clientWidth,o=i.clientHeight,s=20;return t+a+s>n?t-=a+s:t+=s,e+o+s>r?e-=o+s:e+=s,[t,e]}function u(t,e,i){var n=i.clientWidth,r=i.clientHeight,a=5,o=0,s=0,c=e.width,u=e[Ie];switch(t){case l:o=e.x+c/2-n/2,s=e.y+u/2-r/2;break;case"top":o=e.x+c/2-n/2,s=e.y-r-a;break;case Oe:o=e.x+c/2-n/2,s=e.y+u+a;break;case"left":o=e.x-n-a,s=e.y+u/2-r/2;break;case"right":o=e.x+c+a,s=e.y+u/2-r/2}return[o,s]}function d(t,e,i,r,a,o,l){var c=l[Ne](),f=l[Ee](),d=o&&o[Q]().clone();if(o&&d[h](o[n]),typeof t===le&&(t=t([e,i],a,r.el,d)),y[ce](t))e=w(t[0],c),i=w(t[1],f);else if(typeof t===Ge&&o){var p=u(t,d,r.el);e=p[0],i=p[1]}else{var p=s(e,i,r.el,c,f);e=p[0],i=p[1]}r.moveTo(e,i)}function m(t){var e=t[ie],i=t.get("tooltip.trigger",!0);return!(!e||"cartesian2d"!==e.type&&"polar"!==e.type&&"singleAxis"!==e.type||"item"===i)}var v=t("./TooltipContent"),g=t(O),y=t(Xe),x=t("../../util/format"),b=t("../../util/number"),w=b[_],M=t(Ue),S=t("../../model/Model");t(I).extendComponentView({type:"tooltip",_axisPointers:{},init:function(t,e){if(!M.node){var i=new v(e.getDom(),e);this._tooltipContent=i,e.on("showTip",this._manuallyShowTip,this),e.on("hideTip",this._manuallyHideTip,this)}},render:function(t,e,i){if(!M.node){this.group[ne](),this._axisPointers={},this._tooltipModel=t,this._ecModel=e,this._api=i,this._lastHover={};var n=this._tooltipContent;n[ze](),n.enterable=t.get("enterable"),this._alwaysShowContent=t.get("alwaysShowContent"),this._seriesGroupByAxis=this._prepareAxisTriggerData(t,e);var r=this._crossText;if(r&&this.group.add(r),null!=this._lastX&&null!=this._lastY){var a=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){a._manuallyShowTip({x:a._lastX,y:a._lastY})})}var o=this._api.getZr();o.off("click",this._tryShow),o.off("mousemove",this._mousemove),o.off(ye,this._hide),o.off("globalout",this._hide),"click"===t.get("triggerOn")?o.on("click",this._tryShow,this):(o.on("mousemove",this._mousemove,this),o.on(ye,this._hide,this),o.on("globalout",this._hide,this))}},_mousemove:function(t){var e=this._tooltipModel.get("showDelay"),i=this;clearTimeout(this._showTimeout),e>0?this._showTimeout=setTimeout(function(){i._tryShow(t)},e):this._tryShow(t)},_manuallyShowTip:function(t){if(t.from!==this.uid){var e=this._ecModel,i=t[me],r=t[ve],a=e.getSeriesByIndex(i),o=this._api;if(null==t.x||null==t.y){if(a||e[Pe](function(t){m(t)&&!a&&(a=t)}),a){var s=a[Ze]();null==r&&(r=s.indexOfName(t.name));var l,c,u=s[k](r),f=a[ie];if(f&&f[E]){var d=f[E](s.getValues(y.map(f[N],function(t){return a.coordDimToDataDim(t)[0]}),r,!0));l=d&&d[0],c=d&&d[1]}else if(u){var p=u[Q]().clone();p[h](u[n]),l=p.x+p.width/2,c=p.y+p[Ie]/2}null!=l&&null!=c&&this._tryShow({offsetX:l,offsetY:c,target:u,event:{}})}}else{var u=o.getZr().handler.findHover(t.x,t.y);this._tryShow({offsetX:t.x,offsetY:t.y,target:u,event:{}})}}},_manuallyHideTip:function(t){t.from!==this.uid&&this._hide()},_prepareAxisTriggerData:function(t,e){var i={};return e[Pe](function(t){if(m(t)){var e,n,r=t[ie];"cartesian2d"===r.type?(e=r[R](),n=e.dim+e.index):"singleAxis"===r.type?(e=r[V](),n=e.dim+e.type):(e=r[R](),n=e.dim+r.name),i[n]=i[n]||{coordSys:[],series:[]},i[n].coordSys.push(r),i[n][we].push(t)}},this),i},_tryShow:function(t){var e=t[ge],i=this._tooltipModel,n=i.get("trigger"),r=this._ecModel,a=this._api;if(i)if(this._lastX=t.offsetX,this._lastY=t.offsetY,e&&null!=e[ve]){var o=e.dataModel||r.getSeriesByIndex(e[me]),s=e[ve],l=o[Ze]()[P](s);"axis"===(l.get("tooltip.trigger")||n)?this._showAxisTooltip(i,r,t):(this._ticket="",this._hideAxisPointer(),this._resetLastHover(),this._showItemTooltipContent(o,s,e.dataType,t)),a[Te]({type:"showTip",from:this.uid,dataIndex:e[ve],seriesIndex:e[me]})}else if(e&&e.tooltip){var c=e.tooltip;if(typeof c===Ge){var u=c;c={content:u,formatter:u}}var h=new S(c,i),f=h.get("content"),d=Math.random();this._showTooltipContent(h,f,h.get("formatterParams")||{},d,t.offsetX,t.offsetY,e,a)}else"item"===n?this._hide():this._showAxisTooltip(i,r,t),"cross"===i.get("axisPointer.type")&&a[Te]({type:"showTip",from:this.uid,x:t.offsetX,y:t.offsetY})},_showAxisTooltip:function(t,i,n){var r=t[Be]("axisPointer"),a=r.get("type");if("cross"===a){var o=n[ge];if(o&&null!=o[ve]){var s=i.getSeriesByIndex(o[me]),l=o[ve];this._showItemTooltipContent(s,l,o.dataType,n)}}this._showAxisPointer();var c=!0;y.each(this._seriesGroupByAxis,function(t){var i=t.coordSys,o=i[0],s=[n.offsetX,n.offsetY];if(!o.containPoint(s))return void this._hideAxisPointer(o.name);c=!1;var l=o[N],u=o.pointToData(s,!0);s=o[E](u);var h=o[R](),f=r.get("axis");"auto"===f&&(f=h.dim);var d=!1,p=this._lastHover;if("cross"===a)e(p.data,u)&&(d=!0),p.data=u;else{var m=y[ae](l,f);p.data===u[m]&&(d=!0),p.data=u[m]}"cartesian2d"!==o.type||d?"polar"!==o.type||d?"singleAxis"!==o.type||d||this._showSinglePointer(r,o,f,s):this._showPolarPointer(r,o,f,s):this._showCartesianPointer(r,o,f,s),"cross"!==a&&this._dispatchAndShowSeriesTooltipContent(o,t[we],s,u,d)},this),this._tooltipModel.get("show")||this._hideAxisPointer(),c&&this._hide()},_showCartesianPointer:function(t,e,n,r){function o(n,r,a){var o="x"===n?i(r[0],a[0],r[0],a[1]):i(a[0],r[1],a[1],r[1]),s=l._getPointerElement(e,t,n,o);g.subPixelOptimizeLine({shape:o,style:s.style}),h?g[A](s,{shape:o},t):s.attr({shape:o})}function s(i,n,r){var o=e[V](i),s=o.getBandWidth(),c=r[1]-r[0],u="x"===i?a(n[0]-s/2,r[0],s,c):a(r[0],n[1]-s/2,c,s),f=l._getPointerElement(e,t,i,u);h?g[A](f,{shape:u},t):f.attr({shape:u})}var l=this,c=t.get("type"),u=e[R](),h="cross"!==c&&u.type===G&&u.getBandWidth()>20;if("cross"===c)o("x",r,e[V]("y").getGlobalExtent()),o("y",r,e[V]("x").getGlobalExtent()),this._updateCrossText(e,r,t);else{var f=e[V]("x"===n?"y":"x"),d=f.getGlobalExtent();"cartesian2d"===e.type&&("line"===c?o:s)(n,r,d)}},_showSinglePointer:function(t,e,n,r){function a(n,r,a){var s=e[V](),c=s.orient,u="horizontal"===c?i(r[0],a[0],r[0],a[1]):i(a[0],r[1],a[1],r[1]),h=o._getPointerElement(e,t,n,u);l?g[A](h,{shape:u},t):h.attr({shape:u})}var o=this,s=t.get("type"),l="cross"!==s&&e[R]().type===G,c=e.getRect(),u=[c.y,c.y+c[Ie]];a(n,r,u)},_showPolarPointer:function(t,e,n,r){function a(n,r,a){var o,s=e.pointToCoord(r);if("angle"===n){var c=e.coordToPoint([a[0],s[1]]),u=e.coordToPoint([a[1],s[1]]);o=i(c[0],c[1],u[0],u[1])}else o={cx:e.cx,cy:e.cy,r:s[0]};var h=l._getPointerElement(e,t,n,o);f?g[A](h,{shape:o},t):h.attr({shape:o})}function s(i,n,r){var a,s=e[V](i),c=s.getBandWidth(),u=e.pointToCoord(n),h=Math.PI/180;a="angle"===i?o(e.cx,e.cy,r[0],r[1],(-u[1]-c/2)*h,(-u[1]+c/2)*h):o(e.cx,e.cy,u[0]-c/2,u[0]+c/2,0,2*Math.PI);var d=l._getPointerElement(e,t,i,a);f?g[A](d,{shape:a},t):d.attr({shape:a})}var l=this,c=t.get("type"),u=e.getAngleAxis(),h=e.getRadiusAxis(),f="cross"!==c&&e[R]().type===G;if("cross"===c)a("angle",r,h[W]()),a("radius",r,u[W]()),this._updateCrossText(e,r,t);else{var d=e[V]("radius"===n?"angle":"radius"),p=d[W]();("line"===c?a:s)(n,r,p)}},_updateCrossText:function(t,e,i){var n=i[Be]("crossStyle"),r=n[Be](ee),a=this._tooltipModel,o=this._crossText;o||(o=this._crossText=new g.Text({style:{textAlign:"left",textVerticalAlign:"bottom"}}),this.group.add(o));var s=t.pointToData(e),l=t[N];s=y.map(s,function(e,i){var n=t[V](l[i]);return e=n.type===G||"time"===n.type?n.scale.getLabel(e):x.addCommas(e.toFixed(n.getPixelPrecision()))}),o[fe]({fill:r[K]()||n.get("color"),textFont:r[J](),text:s.join(", "),x:e[0]+5,y:e[1]-5}),o.z=a.get("z"),o[he]=a.get(he)},_getPointerElement:function(t,e,i,n){var a=this._tooltipModel,o=a.get("z"),s=a.get(he),l=this._axisPointers,u=t.name;if(l[u]=l[u]||{},l[u][i])return l[u][i];var h=e.get("type"),f=e[Be](h+"Style"),d="shadow"===h,p=f[d?"getAreaStyle":r](),m="polar"===t.type?d?"Sector":"radius"===i?"Circle":"Line":d?"Rect":"Line";d?p[c]=null:p.fill=null;var v=l[u][i]=new g[m]({style:p,z:o,zlevel:s,silent:!0,shape:n});return this.group.add(v),v},_dispatchAndShowSeriesTooltipContent:function(t,e,i,n,r){var a=this._tooltipModel,o=t[R](),s="x"===o.dim||"radius"===o.dim?0:1,l=y.map(e,function(t){return{seriesIndex:t[me],dataIndex:t.getAxisTooltipDataIndex?t.getAxisTooltipDataIndex(t.coordDimToDataDim(o.dim),n,o):t[Ze]().indexOfNearest(t.coordDimToDataDim(o.dim)[0],n[s],!1,o.type===G?.5:null)}}),c=this._lastHover,u=this._api;if(c.payloadBatch&&!r&&u[Te]({type:"downplay",batch:c.payloadBatch}),r||(u[Te]({type:"highlight",batch:l}),c.payloadBatch=l),u[Te]({type:"showTip",dataIndex:l[0][ve],seriesIndex:l[0][me],from:this.uid}),o&&a.get("showContent")&&a.get("show")){var h=y.map(e,function(t,e){return t[pe](l[e][ve])});if(r)d(a.get(j),i[0],i[1],this._tooltipContent,h,null,u);else{var f=l[0][ve],m="time"===o.type?o.scale.getLabel(n[s]):e[0][Ze]()[p](f),v=(m?m+"<br />":"")+y.map(e,function(t,e){return t.formatTooltip(l[e][ve],!0)}).join("<br />"),g="axis_"+t.name+"_"+f;this._showTooltipContent(a,v,h,g,i[0],i[1],null,u)}}},_showItemTooltipContent:function(t,e,i,n){var r=this._api,a=t[Ze](i),o=a[P](e),s=o.get("tooltip",!0);if(typeof s===Ge){var l=s;s={formatter:l}}var c=this._tooltipModel,u=t[Be]("tooltip",c),h=new S(s,u,u[f]),d=t[pe](e,i),p=t.formatTooltip(e,!1,i),m="item_"+t.name+"_"+e;this._showTooltipContent(h,p,d,m,n.offsetX,n.offsetY,n[ge],r)},_showTooltipContent:function(t,e,i,n,r,a,o,s){if(this._ticket="",t.get("showContent")&&t.get("show")){var l=this._tooltipContent,c=t.get("formatter"),u=t.get(j),h=e;if(c)if(typeof c===Ge)h=x.formatTpl(c,i);else if(typeof c===le){var f=this,p=n,m=function(t,e){t===f._ticket&&(l.setContent(e),d(u,r,a,l,i,o,s))};f._ticket=p,h=c(i,p,m)}l.show(t),l.setContent(h),d(u,r,a,l,i,o,s)}},_showAxisPointer:function(t){if(t){var e=this._axisPointers[t];e&&y.each(e,function(t){t.show()})}else this.group.eachChild(function(t){t.show()}),this.group.show()},_resetLastHover:function(){var t=this._lastHover;t.payloadBatch&&this._api[Te]({type:"downplay",batch:t.payloadBatch}),this._lastHover={}},_hideAxisPointer:function(t){if(t){var e=this._axisPointers[t];e&&y.each(e,function(t){t.hide()})}else this.group.children()[Me]&&this.group.hide()},_hide:function(){clearTimeout(this._showTimeout),this._hideAxisPointer(),this._resetLastHover(),this._alwaysShowContent||this._tooltipContent.hideLater(this._tooltipModel.get("hideDelay")),this._api[Te]({type:"hideTip",from:this.uid}),this._lastX=this._lastY=null},dispose:function(t,e){if(!M.node){var i=e.getZr();this._tooltipContent.hide(),i.off("click",this._tryShow),i.off("mousemove",this._mousemove),i.off(ye,this._hide),i.off("globalout",this._hide),e.off("showTip",this._manuallyShowTip),e.off("hideTip",this._manuallyHideTip)}}})}),e("echarts/component/marker/MarkLineView",[je,Xe,"../../data/List","../../util/number","./markerHelper","../../chart/helper/LineDraw","./MarkerView"],function(t){function e(t){return!isNaN(t)&&!isFinite(t)}function i(t,i,n,r){var a=1-t,o=r[N][t];return e(i[a])&&e(n[a])&&i[t]===n[t]&&r[V](o).containData(i[t])}function n(t,e){if("cartesian2d"===t.type){var n=e[0].coord,r=e[1].coord;if(n&&r&&(i(1,n,r,t)||i(0,n,r,t)))return!0}return u.dataFilter(t,e[0])&&u.dataFilter(t,e[1])}function r(t,i,n,r,a){var o,s=r[ie],l=t[P](i),u=c[_](l.get("x"),a[Ne]()),h=c[_](l.get("y"),a[Ee]());if(isNaN(u)||isNaN(h)){if(r.getMarkerPosition)o=r.getMarkerPosition(t.getValues(t[N],i));else{var f=s[N],d=t.get(f[0],i),p=t.get(f[1],i);o=s[E]([d,p])}if("cartesian2d"===s.type){var m=s[V]("x"),v=s[V]("y"),f=s[N];e(t.get(f[0],i))?o[0]=m.toGlobalCoord(m[W]()[n?0:1]):e(t.get(f[1],i))&&(o[1]=v.toGlobalCoord(v[W]()[n?0:1]))}isNaN(u)||(o[0]=u),isNaN(h)||(o[1]=h)}else o=[u,h];t.setItemLayout(i,o)}function o(t,e,i){var r;r=t?s.map(t&&t[N],function(t){var i=e[Ze]().getDimensionInfo(e.coordDimToDataDim(t)[0])||{};return i.name=t,i}):[{name:"value",type:"float"}];var o=new l(r,i),c=new l(r,i),h=new l([],i),d=s.map(i.get("data"),s.curry(f,e,t,i));t&&(d=s[oe](d,s.curry(n,t)));var p=t?u.dimValueGetter:function(t){return t.value};return o[a](s.map(d,function(t){return t[0]}),null,p),c[a](s.map(d,function(t){return t[1]}),null,p),h[a](s.map(d,function(t){return t[2]})),h.hasItemOption=!0,{from:o,to:c,line:h}}var s=t(Xe),l=t("../../data/List"),c=t("../../util/number"),u=t("./markerHelper"),h=t("../../chart/helper/LineDraw"),f=function(t,e,i,n){var r=t[Ze](),a=n.type;if(!s[ce](n)&&("min"===a||"max"===a||"average"===a||null!=n.xAxis||null!=n.yAxis)){var o,l,c;if(null!=n.yAxis||null!=n.xAxis)l=null!=n.yAxis?"y":"x",o=e[V](l),c=s[B](n.yAxis,n.xAxis);else{var h=u.getAxisInfo(n,r,e,t);l=h.valueDataDim,o=h.valueAxis,c=u.numCalculate(r,l,a)}var f="x"===l?0:1,d=1-f,p=s.clone(n),m={};p.type=null,p.coord=[],m.coord=[],p.coord[d]=-1/0,m.coord[d]=1/0;var v=i.get("precision");v>=0&&(c=+c.toFixed(v)),p.coord[f]=m.coord[f]=c,n=[p,m,{type:a,valueIndex:n.valueIndex,value:c}]}return n=[u.dataTransform(t,n[0]),u.dataTransform(t,n[1]),s[Ce]({},n[2])],n[2].type=n[2].type||"",s.merge(n[2],n[0]),s.merge(n[2],n[1]),n};t("./MarkerView")[Ce]({type:"markLine",updateLayout:function(t,e,i){e[Pe](function(t){var e=t.markLineModel;if(e){var n=e[Ze](),a=e.__from,o=e.__to;a.each(function(e){r(a,e,!0,t,i),r(o,e,!1,t,i)}),n.each(function(t){n.setItemLayout(t,[a[z](t),o[z](t)])}),this.markerGroupMap[t.name][Ae]()}},this)},renderSeries:function(t,e,i,n){function a(e,i,a){var o=e[P](i);r(e,i,a,t,n),e.setItemVisual(i,{symbolSize:o.get("symbolSize")||_[a?0:1],symbol:o.get("symbol",!0)||y[a?0:1],color:o.get("itemStyle.normal.color")||u.getVisual("color")})}var l=t[ie],c=t.name,u=t[Ze](),f=this.markerGroupMap,d=f[c];d||(d=f[c]=new h),this.group.add(d.group);var p=o(l,t,e),m=p.from,v=p.to,g=p.line;e.__from=m,e.__to=v,e.setData(g);var y=e.get("symbol"),_=e.get("symbolSize");s[ce](y)||(y=[y,y]),typeof _===se&&(_=[_,_]),p.from.each(function(t){a(m,t,!0),a(v,t,!1)}),g.each(function(t){var e=g[P](t).get("lineStyle.normal.color");g.setItemVisual(t,{color:e||m[T](t,"color")}),g.setItemLayout(t,[m[z](t),v[z](t)]),g.setItemVisual(t,{fromSymbolSize:m[T](t,"symbolSize"),fromSymbol:m[T](t,"symbol"),toSymbolSize:v[T](t,"symbolSize"),toSymbol:v[T](t,"symbol")})}),d.updateData(g),p.line[C](function(t){t[de](function(t){t.dataModel=e})}),d.__keep=!0,d.group[xe]=e.get(xe)||t.get(xe)}})}),e("echarts/component/marker/MarkAreaModel",[je,"./MarkerModel"],function(t){return t("./MarkerModel")[Ce]({type:"markArea",defaultOption:{zlevel:0,z:1,tooltip:{trigger:"item"},animation:!1,label:{normal:{show:!0,position:"top"},emphasis:{show:!0,position:"top"}},itemStyle:{normal:{borderWidth:0}}}})}),e("echarts/component/marker/MarkAreaView",[je,Xe,"../../data/List","../../util/number",O,"zrender/tool/color","./markerHelper","./MarkerView"],function(t){function e(t){return!isNaN(t)&&!isFinite(t)}function i(t,i,n){var r=1-t;return e(i[r])&&e(n[r])}function n(t,e){var n=e.coord[0],r=e.coord[1];return"cartesian2d"===t.type&&n&&r&&(i(1,n,r,t)||i(0,n,r,t))?!0:f.dataFilter(t,{coord:n,x:e.x0,y:e.y0})||f.dataFilter(t,{coord:r,x:e.x1,y:e.y1})}function r(t,i,n,r,a){var o,s=r[ie],l=t[P](i),u=c[_](l.get(n[0]),a[Ne]()),h=c[_](l.get(n[1]),a[Ee]());if(isNaN(u)||isNaN(h)){if(r.getMarkerPosition)o=r.getMarkerPosition(t.getValues(n,i));else{var f=t.get(n[0],i),d=t.get(n[1],i);o=s[E]([f,d],!0)}if("cartesian2d"===s.type){var p=s[V]("x"),m=s[V]("y"),f=t.get(n[0],i),d=t.get(n[1],i);e(f)?o[0]=p.toGlobalCoord(p[W]()["x0"===n[0]?0:1]):e(d)&&(o[1]=m.toGlobalCoord(m[W]()["y0"===n[1]?0:1]))}isNaN(u)||(o[0]=u),isNaN(h)||(o[1]=h)}else o=[u,h];return o}function o(t,e,i){var r,o,c=["x0","y0","x1","y1"];t?(r=s.map(t&&t[N],function(t){var i=e[Ze]().getDimensionInfo(e.coordDimToDataDim(t)[0])||{};return i.name=t,i}),o=new l(s.map(c,function(t,e){return{name:t,type:r[e%2].type}}),i)):(r=[{name:"value",type:"float"}],o=new l(r,i));var u=s.map(i.get("data"),s.curry(d,e,t,i));t&&(u=s[oe](u,s.curry(n,t)));var h=t?function(t,e,i,n){return t.coord[Math.floor(n/2)][n%2]}:function(t){return t.value};return o[a](u,null,h),o.hasItemOption=!0,o}var s=t(Xe),l=t("../../data/List"),c=t("../../util/number"),u=t(O),h=t("zrender/tool/color"),f=t("./markerHelper"),d=function(t,e,i,n){var r=f.dataTransform(t,n[0]),a=f.dataTransform(t,n[1]),o=s[B],l=r.coord,c=a.coord;l[0]=o(l[0],-1/0),l[1]=o(l[1],-1/0),c[0]=o(c[0],1/0),c[1]=o(c[1],1/0);var u=s.mergeAll([{},r,a]);return u.coord=[r.coord,a.coord],u.x0=r.x,u.y0=r.y,u.x1=a.x,u.y1=a.y,u},m=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]];t("./MarkerView")[Ce]({type:"markArea",updateLayout:function(t,e,i){e[Pe](function(t){var e=t.markAreaModel;if(e){var n=e[Ze]();n.each(function(e){var a=s.map(m,function(a){return r(n,e,a,t,i)});n.setItemLayout(e,a);var o=n[k](e);
o.setShape("points",a)})}},this)},renderSeries:function(t,e,i,n){var a=t[ie],l=t.name,c=t[Ze](),f=this.markerGroupMap,d=f[l];d||(d=f[l]={group:new u.Group}),this.group.add(d.group),d.__keep=!0;var v=o(a,t,e);e.setData(v),v.each(function(e){v.setItemLayout(e,s.map(m,function(i){return r(v,e,i,t,n)})),v.setItemVisual(e,{color:c.getVisual("color")})}),v.diff(d.__data).add(function(t){var e=new u.Polygon({shape:{points:v[z](t)}});v[L](t,e),d.group.add(e)})[ze](function(t,i){var n=d.__data[k](i);u[A](n,{shape:{points:v[z](t)}},e,t),d.group.add(n),v[L](t,n)})[ke](function(t){var e=d.__data[k](t);d.group[ke](e)}).execute(),v[C](function(t,i){var n=v[P](i),r=n[Be](S),a=n[Be](M),o=v[T](i,"color");t.useStyle(s[Se](n[Be]("itemStyle.normal").getItemStyle(),{fill:h.modifyAlpha(o,.4),stroke:o})),t.hoverStyle=n[Be]("itemStyle.normal").getItemStyle();var l=v[p](i)||"",c=o||t.style.fill;u.setText(t.style,r,c),t.style.text=s[B](e.getFormattedLabel(i,D),l),u.setText(t.hoverStyle,a,c),t.hoverStyle.text=s[B](e.getFormattedLabel(i,b),l),u[x](t,{}),t.dataModel=e}),d.__data=v,d.group[xe]=e.get(xe)||t.get(xe)}})}),e("echarts/component/timeline/preprocessor",[je,Xe],function(t){function e(t){var e=t.type,a={number:"value",time:"time"};if(a[e]&&(t.axisType=a[e],delete t.type),i(t),n(t,"controlPosition")){var o=t.controlStyle||(t.controlStyle={});n(o,j)||(o[j]=t.controlPosition),"none"!==o[j]||n(o,"show")||(o.show=!1,delete o[j]),delete t.controlPosition}r.each(t.data||[],function(t){r[Le](t)&&!r[ce](t)&&(!n(t,"value")&&n(t,"name")&&(t.value=t.name),i(t))})}function i(t){var e=t.itemStyle||(t.itemStyle={}),i=e[b]||(e[b]={}),a=t.label||t.label||{},o=a[D]||(a[D]={}),s={normal:1,emphasis:1};r.each(a,function(t,e){s[e]||n(o,e)||(o[e]=t)}),i.label&&!n(a,b)&&(a[b]=i.label,delete i.label)}function n(t,e){return t.hasOwnProperty(e)}var r=t(Xe);return function(t){var i=t&&t.timeline;r[ce](i)||(i=i?[i]:[]),r.each(i,function(t){t&&e(t)})}}),e("echarts/component/timeline/typeDefaulter",[je,"../../model/Component"],function(t){t("../../model/Component").registerSubTypeDefaulter("timeline",function(){return"slider"})}),e("echarts/component/timeline/timelineAction",[je,I],function(t){var e=t(I);e.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(t,e){var i=e.getComponent("timeline");i&&null!=t.currentIndex&&(i.setCurrentIndex(t.currentIndex),!i.get("loop",!0)&&i.isIndexMax()&&i.setPlayState(!1)),e.resetOption("timeline")}),e.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(t,e){var i=e.getComponent("timeline");i&&null!=t.playState&&i.setPlayState(t.playState)})}),e("echarts/component/timeline/SliderTimelineModel",[je,"./TimelineModel",Xe,"../../util/model"],function(t){var e=t("./TimelineModel"),i=t(Xe),n=t("../../util/model"),r=e[Ce]({type:"timeline.slider",defaultOption:{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"emptyCircle",symbolSize:10,lineStyle:{show:!0,width:2,color:"#304654"},label:{position:"auto",normal:{show:!0,interval:"auto",rotate:0,textStyle:{color:"#304654"}},emphasis:{show:!0,textStyle:{color:"#c23531"}}},itemStyle:{normal:{color:"#304654",borderWidth:1},emphasis:{color:"#c23531"}},checkpointStyle:{symbol:"circle",symbolSize:13,color:"#c23531",borderWidth:5,borderColor:"rgba(194,53,49, 0.5)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:22,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"path://M18.6,50.8l22.5-22.5c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.5-0.3-0.7L18.7,4.4c-0.1-0.1-0.2-0.3-0.2-0.5 c0-0.4,0.3-0.8,0.8-0.8c0.2,0,0.5,0.1,0.6,0.3l23.5,23.5l0,0c0.2,0.2,0.3,0.4,0.3,0.7c0,0.3-0.1,0.5-0.3,0.7l-0.1,0.1L19.7,52 c-0.1,0.1-0.3,0.2-0.5,0.2c-0.4,0-0.8-0.3-0.8-0.8C18.4,51.2,18.5,51,18.6,50.8z",prevIcon:"path://M43,52.8L20.4,30.3c-0.2-0.2-0.3-0.4-0.3-0.7c0-0.3,0.1-0.5,0.3-0.7L42.9,6.4c0.1-0.1,0.2-0.3,0.2-0.5 c0-0.4-0.3-0.8-0.8-0.8c-0.2,0-0.5,0.1-0.6,0.3L18.3,28.8l0,0c-0.2,0.2-0.3,0.4-0.3,0.7c0,0.3,0.1,0.5,0.3,0.7l0.1,0.1L41.9,54 c0.1,0.1,0.3,0.2,0.5,0.2c0.4,0,0.8-0.3,0.8-0.8C43.2,53.2,43.1,53,43,52.8z",normal:{color:"#304654",borderColor:"#304654",borderWidth:1},emphasis:{color:"#c23531",borderColor:"#c23531",borderWidth:2}},data:[]}});return i.mixin(r,n.dataFormatMixin),r}),e("echarts/component/timeline/SliderTimelineView",[je,Xe,O,"../../util/layout","./TimelineView","./TimelineAxis","../../util/symbol","../../coord/axisHelper","zrender/core/BoundingRect","zrender/core/matrix","../../util/number","../../util/format"],function(t){function e(t,e){return c.getLayoutRect(t.getBoxLayoutParams(),{width:e[Ne](),height:e[Ee]()},t.get("padding"))}function i(t,e,i,n){var r=l.makePath(t.get(e)[ue](/^path:\/\//,""),s.clone(n||{}),new v(i[0],i[1],i[2],i[3]),Y);return r}function n(t,e,i,n,r,a){var o=t.get("symbol"),l=e.get("color"),c=t.get("symbolSize"),u=c/2,h=e.getItemStyle(["color","symbol","symbolSize"]);return r?(r[fe](h),r.setColor(l),i.add(r),a&&a.onUpdate(r)):(r=p.createSymbol(o,-u,-u,c,c,l),i.add(r),a&&a.onCreate(r)),n=s.merge({rectHover:!0,style:h,z2:100},n,!0),r.attr(n),r}function a(t,e,i,n,r){if(!t.dragging){var a=n[Be]("checkpointStyle"),o=i[y](n[Ze]().get(["value"],e));r||!a.get(Fe,!0)?t.attr({position:[o,0]}):(t.stopAnimation(!0),t.animateTo({position:[o,0]},a.get("animationDuration",!0),a.get("animationEasing",!0)))}}var s=t(Xe),l=t(O),c=t("../../util/layout"),u=t("./TimelineView"),d=t("./TimelineAxis"),p=t("../../util/symbol"),m=t("../../coord/axisHelper"),v=t("zrender/core/BoundingRect"),g=t("zrender/core/matrix"),_=t("../../util/number"),b=t("../../util/format"),w=b.encodeHTML,M=s.bind,T=s.each,C=Math.PI;return u[Ce]({type:"timeline.slider",init:function(t,e){this.api=e,this._axis,this._viewRect,this._timer,this._currentPointer,this._mainGroup,this._labelGroup},render:function(t,e,i){if(this.model=t,this.api=i,this[f]=e,this.group[ne](),t.get("show",!0)){var n=this._layout(t,i),r=this._createGroup("mainGroup"),a=this._createGroup("labelGroup"),o=this._axis=this._createAxis(n,t);t.formatTooltip=function(t){return w(o.scale.getLabel(t))},T(["AxisLine","AxisTick","Control","CurrentPointer"],function(e){this["_render"+e](n,r,o,t)},this),this._renderAxisLabel(n,a,o,t),this._position(n,t)}this._doPlayStop()},remove:function(){this._clearTimer(),this.group[ne]()},dispose:function(){this._clearTimer()},_layout:function(t,i){var n=t.get("label.normal.position"),r=t.get("orient"),a=e(t,i);null==n||"auto"===n?n="horizontal"===r?a.y+a[Ie]/2<i[Ee]()/2?"-":"+":a.x+a.width/2<i[Ne]()/2?"+":"-":isNaN(n)&&(n={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[r][n]);var o={horizontal:"center",vertical:n>=0||"+"===n?"left":"right"},s={horizontal:n>=0||"+"===n?"top":Oe,vertical:"middle"},l={horizontal:0,vertical:C/2},c="vertical"===r?a[Ie]:a.width,u=t[Be]("controlStyle"),h=u.get("show"),f=h?u.get("itemSize"):0,d=h?u.get("itemGap"):0,p=f+d,m=t.get("label.normal.rotate")||0;m=m*C/180;var v,g,y,_,x=u.get(j,!0),h=u.get("show",!0),b=h&&u.get("showPlayBtn",!0),w=h&&u.get("showPrevBtn",!0),M=h&&u.get("showNextBtn",!0),S=0,T=c;return"left"===x||x===Oe?(b&&(v=[0,0],S+=p),w&&(g=[S,0],S+=p),M&&(y=[T-f,0],T-=p)):(b&&(v=[T-f,0],T-=p),w&&(g=[0,0],S+=p),M&&(y=[T-f,0],T-=p)),_=[S,T],t.get("inverse")&&_.reverse(),{viewRect:a,mainLength:c,orient:r,rotation:l[r],labelRotation:m,labelPosOpt:n,labelAlign:o[r],labelBaseline:s[r],playPosition:v,prevBtnPosition:g,nextBtnPosition:y,axisExtent:_,controlSize:f,controlGap:d}},_position:function(t){function e(t){var e=t[j];t.origin=[f[0][0]-e[0],f[1][0]-e[1]]}function i(t){return[[t.x,t.x+t.width],[t.y,t.y+t[Ie]]]}function n(t,e,i,n,r){t[n]+=i[n][r]-e[n][r]}var r=this._mainGroup,a=this._labelGroup,s=t.viewRect;if("vertical"===t.orient){var l=g[De](),c=s.x,u=s.y+s[Ie];g.translate(l,l,[-c,-u]),g.rotate(l,l,-C/2),g.translate(l,l,[c,u]),s=s.clone(),s[h](l)}var f=i(s),d=i(r[Q]()),p=i(a[Q]()),m=r[j],v=a[j];v[0]=m[0]=f[0][0];var y=t.labelPosOpt;if(isNaN(y)){var _="+"===y?0:1;n(m,d,f,1,_),n(v,p,f,1,1-_)}else{var _=y>=0?0:1;n(m,d,f,1,_),v[1]=m[1]+y}r.attr(j,m),a.attr(j,v),r[o]=a[o]=t[o],e(r),e(a)},_createAxis:function(t,e){var i=e[Ze](),n=e.get("axisType"),r=m.createScaleByModel(e,n),a=i.getDataExtent("value");r.setExtent(a[0],a[1]),this._customizeScale(r,i),r.niceTicks();var o=new d("value",r,t.axisExtent,n);return o.model=e,o},_customizeScale:function(t,e){t.getTicks=function(){return e.mapArray(["value"],function(t){return t})},t.getTicksLabels=function(){return s.map(this.getTicks(),t.getLabel,t)}},_createGroup:function(t){var e=this["_"+t]=new l.Group;return this.group.add(e),e},_renderAxisLine:function(t,e,i,n){var a=i[W]();n.get("lineStyle.show")&&e.add(new l.Line({shape:{x1:a[0],y1:0,x2:a[1],y2:0},style:s[Ce]({lineCap:"round"},n[Be]("lineStyle")[r]()),silent:!0,z2:1}))},_renderAxisTick:function(t,e,i,r){var a=r[Ze](),o=i.scale.getTicks();T(o,function(t,o){var s=i[y](t),c=a[P](o),u=c[Be]("itemStyle.normal"),h=c[Be]("itemStyle.emphasis"),f={position:[s,0],onclick:M(this._changeTimeline,this,o)},d=n(c,u,e,f);l[x](d,h.getItemStyle()),c.get("tooltip")?(d[ve]=o,d.dataModel=r):d[ve]=d.dataModel=null},this)},_renderAxisLabel:function(t,e,i,n){var r=n[Be](S);if(r.get("show")){var a=n[Ze](),s=i.scale.getTicks(),c=m.getFormattedLabels(i,r.get("formatter")),u=i.getLabelInterval();T(s,function(n,r){if(!i.isLabelIgnored(r,u)){var s=a[P](r),h=s[Be]("label.normal.textStyle"),f=s[Be]("label.emphasis.textStyle"),d=i[y](n),p=new l.Text({style:{text:c[r],textAlign:t.labelAlign,textVerticalAlign:t.labelBaseline,textFont:h[J](),fill:h[K]()},position:[d,0],rotation:t.labelRotation-t[o],onclick:M(this._changeTimeline,this,r),silent:!1});e.add(p),l[x](p,f.getItemStyle())}},this)}},_renderControl:function(t,e,n,r){function a(t,n,a,o){if(t){var d={position:t,origin:[s/2,0],rotation:o?-c:0,rectHover:!0,style:u,onclick:a},p=i(r,n,f,d);e.add(p),l[x](p,h)}}var s=t.controlSize,c=t[o],u=r[Be]("controlStyle.normal").getItemStyle(),h=r[Be]("controlStyle.emphasis").getItemStyle(),f=[0,-s/2,s,s],d=r.getPlayState(),p=r.get("inverse",!0);a(t.nextBtnPosition,"controlStyle.nextIcon",M(this._changeTimeline,this,p?"-":"+")),a(t.prevBtnPosition,"controlStyle.prevIcon",M(this._changeTimeline,this,p?"+":"-")),a(t.playPosition,"controlStyle."+(d?"stopIcon":"playIcon"),M(this._handlePlayClick,this,!d),!0)},_renderCurrentPointer:function(t,e,i,r){var o=r[Ze](),s=r.getCurrentIndex(),l=o[P](s)[Be]("checkpointStyle"),c=this,u={onCreate:function(t){t.draggable=!0,t.drift=M(c._handlePointerDrag,c),t.ondragend=M(c._handlePointerDragend,c),a(t,s,i,r,!0)},onUpdate:function(t){a(t,s,i,r)}};this._currentPointer=n(l,l,this._mainGroup,{},this._currentPointer,u)},_handlePlayClick:function(t){this._clearTimer(),this.api[Te]({type:"timelinePlayChange",playState:t,from:this.uid})},_handlePointerDrag:function(t,e,i){this._clearTimer(),this._pointerChangeTimeline([i.offsetX,i.offsetY])},_handlePointerDragend:function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},_pointerChangeTimeline:function(t,e){var i=this._toAxisCoord(t)[0],n=this._axis,r=_.asc(n[W]().slice());i>r[1]&&(i=r[1]),i<r[0]&&(i=r[0]),this._currentPointer[j][0]=i,this._currentPointer.dirty();var a=this._findNearestTick(i),o=this.model;(e||a!==o.getCurrentIndex()&&o.get("realtime"))&&this._changeTimeline(a)},_doPlayStop:function(){function t(){var t=this.model;this._changeTimeline(t.getCurrentIndex()+(t.get("rewind",!0)?-1:1))}this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(M(t,this),this.model.get("playInterval")))},_toAxisCoord:function(t){var e=this._mainGroup.getLocalTransform();return l[h](t,e,!0)},_findNearestTick:function(t){var e,i=this.model[Ze](),n=1/0,r=this._axis;return i.each(["value"],function(i,a){var o=r[y](i),s=Math.abs(o-t);n>s&&(n=s,e=a)}),e},_clearTimer:function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},_changeTimeline:function(t){var e=this.model.getCurrentIndex();"+"===t?t=e+1:"-"===t&&(t=e-1),this.api[Te]({type:"timelineChange",currentIndex:t,from:this.uid})}})}),e("zrender/vml/Painter",[je,"../core/log","./core"],function(t){function e(t){return parseInt(t,10)}function n(t,e){o.initVML(),this.root=t,this.storage=e;var i=document[q]("div"),n=document[q]("div");i.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",n.style.cssText="position:absolute;left:0;top:0;",t.appendChild(i),this._vmlRoot=n,this._vmlViewport=i,this[Ve]();var r=e.delFromMap,a=e.addToMap;e.delFromMap=function(t){var i=e.get(t);r.call(e,t),i&&i.onRemove&&i.onRemove(n)},e.addToMap=function(t){t.onAdd&&t.onAdd(n),a.call(e,t)},this._firstPaint=!0}function r(t){return function(){a('In IE8.0 VML mode painter not support method "'+t+'"')}}var a=t("../core/log"),o=t("./core");n[He]={constructor:n,getViewportRoot:function(){return this._vmlViewport},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,n=0;n<t[Me];n++){var r=t[n];r.invisible||r[Re]?(r.__alreadyNotVisible||r.onRemove(e),r.__alreadyNotVisible=!0):(r.__alreadyNotVisible&&r.onAdd(e),r.__alreadyNotVisible=!1,r[i]&&(r.beforeBrush&&r.beforeBrush(),(r.brushVML||r.brush).call(r,e),r.afterBrush&&r.afterBrush())),r[i]=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(){var t=this._getWidth(),e=this._getHeight();if(this._width!=t&&this._height!=e){this._width=t,this._height=e;var i=this._vmlViewport.style;i.width=t+"px",i[Ie]=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this.root.removeChild(this.vmlViewport)},_getWidth:function(){var t=this.root,i=t.currentStyle;return(t.clientWidth||e(i.width))-e(i.paddingLeft)-e(i.paddingRight)|0},_getHeight:function(){var t=this.root,i=t.currentStyle;return(t.clientHeight||e(i[Ie]))-e(i.paddingTop)-e(i.paddingBottom)|0}};for(var s=["getLayer","insertLayer","eachLayer","eachBuildinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],l=0;l<s[Me];l++){var c=s[l];n[He][c]=r(c)}return n}),e("echarts/scale/Interval",[je,"../util/number","../util/format","./Scale"],function(t){var e=t("../util/number"),i=t("../util/format"),n=t("./Scale"),r=Math.floor,a=Math.ceil,o=e.getPrecisionSafe,s=e.round,l=n[Ce]({type:"interval",_interval:0,setExtent:function(t,e){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(e)||(i[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),l[He].setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval||this.niceTicks(),this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice()},getTicks:function(){this._interval||this.niceTicks();var t=this._interval,e=this._extent,i=[],n=1e4;if(t){var r=this._niceExtent,a=o(t)+2;e[0]<r[0]&&i.push(e[0]);for(var l=r[0];l<=r[1];)if(i.push(l),l=s(l+t,a),i[Me]>n)return[];e[1]>r[1]&&i.push(e[1])}return i},getTicksLabels:function(){for(var t=[],e=this.getTicks(),i=0;i<e[Me];i++)t.push(this.getLabel(e[i]));return t},getLabel:function(t){return i.addCommas(t)},niceTicks:function(t){t=t||5;var i=this._extent,n=i[1]-i[0];if(isFinite(n)){0>n&&(n=-n,i.reverse());var l=s(e.nice(n/t,!0),Math.max(o(i[0]),o(i[1]))+2),c=o(l)+2,u=[s(a(i[0]/l)*l,c),s(r(i[1]/l)*l,c)];this._interval=l,this._niceExtent=u}},niceExtent:function(t,e,i){var n=this._extent;if(n[0]===n[1])if(0!==n[0]){var o=n[0];i?n[0]-=o/2:(n[1]+=o/2,n[0]-=o/2)}else n[1]=1;var l=n[1]-n[0];isFinite(l)||(n[0]=0,n[1]=1),this.niceTicks(t);var c=this._interval;e||(n[0]=s(r(n[0]/c)*c)),i||(n[1]=s(a(n[1]/c)*c))}});return l[De]=function(){return new l},l}),e("echarts/scale/Scale",[je,"../util/clazz"],function(t){function e(){this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}var i=t("../util/clazz"),n=e[He];return n.parse=function(t){return t},n[Z]=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},n[U]=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},n.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},n.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},n[W]=function(){return this._extent.slice()},n.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},n.getTicksLabels=function(){for(var t=[],e=this.getTicks(),i=0;i<e[Me];i++)t.push(this.getLabel(e[i]));return t},i.enableClassExtend(e),i.enableClassManagement(e,{registerWhenExtend:!0}),e}),e("echarts/coord/axisHelper",[je,"../scale/Ordinal","../scale/Interval","../scale/Time","../scale/Log","../scale/Scale","../util/number",Xe,"zrender/contain/text"],function(t){var e=t("../scale/Ordinal"),i=t("../scale/Interval");t("../scale/Time"),t("../scale/Log");var n=t("../scale/Scale"),r=t("../util/number"),a=t(Xe),o=t("zrender/contain/text"),s={};return s.getScaleExtent=function(t,e){var i=t.scale,n=i[W](),o=n[1]-n[0];if(i.type===F)return isFinite(o)?n:[0,0];var s=e.getMin?e.getMin():e.get("min"),l=e.getMax?e.getMax():e.get("max"),c=e.getNeedCrossZero?e.getNeedCrossZero():!e.get("scale"),u=e.get("boundaryGap");a[ce](u)||(u=[u||0,u||0]),u[0]=r[_](u[0],1),u[1]=r[_](u[1],1);var h=!0,f=!0;return null==s&&(s=n[0]-u[0]*o,h=!1),null==l&&(l=n[1]+u[1]*o,f=!1),"dataMin"===s&&(s=n[0]),"dataMax"===l&&(l=n[1]),c&&(s>0&&l>0&&!h&&(s=0),0>s&&0>l&&!f&&(l=0)),[s,l]},s.niceScaleExtent=function(t,e){var i=t.scale,n=s.getScaleExtent(t,e),r=null!=(e.getMin?e.getMin():e.get("min")),a=null!=(e.getMax?e.getMax():e.get("max")),o=e.get("splitNumber");"log"===i.type&&(i.base=e.get("logBase")),i.setExtent(n[0],n[1]),i.niceExtent(o,r,a);var l=e.get("minInterval");if(isFinite(l)&&!r&&!a&&"interval"===i.type){var c=i.getInterval(),u=Math.max(Math.abs(c),l)/c;n=i[W](),i.setExtent(u*n[0],n[1]*u),i.niceExtent(o)}var c=e.get("interval");null!=c&&i.setInterval&&i.setInterval(c)},s.createScaleByModel=function(t,r){if(r=r||t.get("type"))switch(r){case G:return new e(t.getCategories(),[1/0,-1/0]);case"value":return new i;default:return(n.getClass(r)||i)[De](t)}},s.ifAxisCrossZero=function(t){var e=t.scale[W](),i=e[0],n=e[1];return!(i>0&&n>0||0>i&&0>n)},s.getAxisLabelInterval=function(t,e,i,n){var r,a=0,s=0,l=1;e[Me]>40&&(l=Math.floor(e[Me]/40));for(var c=0;c<t[Me];c+=l){var u=t[c],h=o[Q](e[c],i,Y,"top");h[n?"x":"y"]+=u,h[n?"width":Ie]*=1.3,r?r.intersect(h)?(s++,a=Math.max(a,s)):(r.union(h),s=0):r=h.clone()}return 0===a&&l>1?l:(a+1)*l-1},s.getFormattedLabels=function(t,e){var i=t.scale,n=i.getTicksLabels(),r=i.getTicks();return typeof e===Ge?(e=function(t){return function(e){return t[ue]("{value}",e)}}(e),a.map(n,e)):typeof e===le?a.map(r,function(n,r){return e(t.type===G?i.getLabel(n):n,r)},this):n},s}),e("echarts/coord/cartesian/Cartesian2D",[je,Xe,"./Cartesian"],function(t){function e(t){n.call(this,t)}var i=t(Xe),n=t("./Cartesian");return e[He]={constructor:e,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale(F)[0]||this.getAxesByScale("time")[0]||this[V]("x")},containPoint:function(t){var e=this[V]("x"),i=this[V]("y");return e[Z](e.toLocalCoord(t[0]))&&i[Z](i.toLocalCoord(t[1]))},containData:function(t){return this[V]("x").containData(t[0])&&this[V]("y").containData(t[1])},dataToPoints:function(t,e){return t.mapArray(["x","y"],function(t,e){return this[E]([t,e])},e,this)},dataToPoint:function(t,e){var i=this[V]("x"),n=this[V]("y");return[i.toGlobalCoord(i[y](t[0],e)),n.toGlobalCoord(n[y](t[1],e))]},pointToData:function(t,e){var i=this[V]("x"),n=this[V]("y");return[i.coordToData(i.toLocalCoord(t[0]),e),n.coordToData(n.toLocalCoord(t[1]),e)]},getOtherAxis:function(t){return this[V]("x"===t.dim?"y":"x")}},i[re](e,n),e}),e("echarts/coord/cartesian/Axis2D",[je,Xe,"../Axis","./axisLabelInterval"],function(t){var e=t(Xe),i=t("../Axis"),n=t("./axisLabelInterval"),r=function(t,e,n,r,a){i.call(this,t,e,n),this.type=r||"value",this[j]=a||Oe};return r[He]={constructor:r,index:0,onZero:!1,model:null,isHorizontal:function(){var t=this[j];return"top"===t||t===Oe},getGlobalExtent:function(){var t=this[W]();return t[0]=this.toGlobalCoord(t[0]),t[1]=this.toGlobalCoord(t[1]),t},getLabelInterval:function(){var t=this._labelInterval;return t||(t=this._labelInterval=n(this)),t},isLabelIgnored:function(t){if(this.type===G){var e=this.getLabelInterval();return typeof e===le&&!e(t,this.scale.getLabel(t))||t%(e+1)}},toLocalCoord:null,toGlobalCoord:null},e[re](r,i),r}),e("echarts/coord/cartesian/GridModel",[je,"./AxisModel","../../model/Component"],function(t){t("./AxisModel");var e=t("../../model/Component");return e[Ce]({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}})}),e("echarts/util/model",[je,"./format","./number","../model/Model",Xe],function(t){var e=t("./format"),i=t("./number"),n=t("../model/Model"),r=t(Xe),a={};return a.normalizeToArray=function(t){return t instanceof Array?t:null==t?[]:[t]},a.defaultEmphasis=function(t,e){if(t){var i=t[b]=t[b]||{},n=t[D]=t[D]||{};r.each(e,function(t){var e=r[B](i[t],n[t]);null!=e&&(i[t]=e)})}},a.LABEL_OPTIONS=[j,"show",ee,"distance","formatter"],a.getDataItemValue=function(t){return t&&(null==t.value?t:t.value)},a.isDataItemOption=function(t){return r[Le](t)&&!(t instanceof Array)},a.converDataValue=function(t,e){var n=e&&e.type;return n===F?t:("time"!==n||isFinite(t)||null==t||"-"===t||(t=+i.parseDate(t)),null==t||""===t?0/0:+t)},a.createDataFormatModel=function(t,e){var i=new n;return r.mixin(i,a.dataFormatMixin),i[me]=e[me],i.name=e.name||"",i.mainType=e.mainType,i.subType=e.subType,i[Ze]=function(){return t},i},a.dataFormatMixin={getDataParams:function(t,e){var i=this[Ze](e),n=this[me],r=this.name,a=this[w](t,e),o=i.getRawIndex(t),s=i[p](t,!0),l=i.getRawDataItem(t);return{componentType:this.mainType,componentSubType:this.subType,seriesType:this.mainType===we?this.subType:null,seriesIndex:n,seriesName:r,name:s,dataIndex:o,data:l,dataType:e,value:a,color:i[T](t,"color"),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,i,n,r){i=i||D;var a=this[Ze](n),o=a[P](t),s=this[pe](t,n);null!=r&&s.value instanceof Array&&(s.value=s.value[r]);var l=o.get(["label",i,"formatter"]);return typeof l===le?(s.status=i,l(s)):typeof l===Ge?e.formatTpl(l,s):void 0},getRawValue:function(t,e){var i=this[Ze](e),n=i.getRawDataItem(t);return null!=n?!r[Le](n)||n instanceof Array?n:n.value:void 0},formatTooltip:r.noop},a.mappingToExists=function(t,e){e=(e||[]).slice();var i=r.map(t||[],function(t){return{exist:t}});return r.each(e,function(t,n){if(r[Le](t)){for(var o=0;o<i[Me];o++)if(!i[o][v]&&null!=t.id&&i[o].exist.id===t.id+"")return i[o][v]=t,void(e[n]=null);for(var o=0;o<i[Me];o++){var s=i[o].exist;if(!(i[o][v]||null!=s.id&&null!=t.id||null==t.name||a.isIdInner(t)||a.isIdInner(s)||s.name!==t.name+""))return i[o][v]=t,void(e[n]=null)}}}),r.each(e,function(t){if(r[Le](t)){for(var e=0;e<i[Me];e++){var n=i[e].exist;if(!i[e][v]&&!a.isIdInner(n)&&null==t.id){i[e][v]=t;break}}e>=i[Me]&&i.push({option:t})}}),i},a.isIdInner=function(t){return r[Le](t)&&t.id&&0===(t.id+"")[ae]("\x00_ec_\x00")},a.compressBatches=function(t,e){function i(t,e,i){for(var n=0,r=t[Me];r>n;n++)for(var o=t[n].seriesId,s=a.normalizeToArray(t[n][ve]),l=i&&i[o],c=0,u=s[Me];u>c;c++){var h=s[c];l&&l[h]?l[h]=null:(e[o]||(e[o]={}))[h]=1}}function n(t,e){var i=[];for(var r in t)if(t.hasOwnProperty(r)&&null!=t[r])if(e)i.push(+r);else{var a=n(t[r],!0);a[Me]&&i.push({seriesId:r,dataIndex:a})}return i}var r={},o={};return i(t||[],r),i(e||[],o,r),[n(r),n(o)]},a}),e("zrender/vml/graphic",[je,"../core/env","../core/vector","../core/BoundingRect","../core/PathProxy","../tool/color","../contain/text","../graphic/mixin/RectText","../graphic/Displayable","../graphic/Image","../graphic/Text","../graphic/Path","../graphic/Gradient","./core"],function(t){if(!t("../core/env").canvasSupported){var e=t("../core/vector"),i=t("../core/BoundingRect"),r=t("../core/PathProxy").CMD,a=t("../tool/color"),o=t("../contain/text"),s=t("../graphic/mixin/RectText"),l=t("../graphic/Displayable"),f=t("../graphic/Image"),d=t("../graphic/Text"),p=t("../graphic/Path"),m=t("../graphic/Gradient"),v=t("./core"),g=Math.round,y=Math.sqrt,_=Math.abs,x=Math.cos,b=Math.sin,w=Math.max,M=e[h],S=",",T="progid:DXImageTransform.Microsoft",C=21600,k=C/2,L=1e5,A=1e3,P=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=C+","+C,t.coordorigin="0,0"},z=function(t){return String(t)[ue](/&/g,"&amp;")[ue](/"/g,"&quot;")},I=function(t,e,i){return"rgb("+[t,e,i].join(",")+")"},O=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},R=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},E=function(t,e,i){return(parseFloat(t)||0)*L+(parseFloat(e)||0)*A+i},N=function(t,e){return typeof t===Ge?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t},B=function(t,e,i){var n=a.parse(e);i=+i,isNaN(i)&&(i=1),n&&(t.color=I(n[0],n[1],n[2]),t[X]=i*n[3])},F=function(t){var e=a.parse(t);return[I(e[0],e[1],e[2]),e[3]]},V=function(t,e,i){var r=e.fill;if(null!=r)if(r instanceof m){var a,o=0,s=[0,0],l=0,c=1,u=i[Q](),h=u.width,f=u[Ie];if("linear"===r.type){a="gradient";var d=i[n],p=[r.x*h,r.y*f],v=[r.x2*h,r.y2*f];d&&(M(p,p,d),M(v,v,d));var g=v[0]-p[0],y=v[1]-p[1];o=180*Math.atan2(g,y)/Math.PI,0>o&&(o+=360),1e-6>o&&(o=0)}else{a="gradientradial";var p=[r.x*h,r.y*f],d=i[n],_=i.scale,x=h,b=f;s=[(p[0]-u.x)/x,(p[1]-u.y)/b],d&&M(p,p,d),x/=_[0]*C,b/=_[1]*C;var S=w(x,b);l=0/S,c=2*r.r/S-l}var T=r.colorStops.slice();T.sort(function(t,e){return t.offset-e.offset});for(var k=T[Me],L=[],A=[],P=0;k>P;P++){var z=T[P],D=F(z.color);A.push(z.offset*c+l+" "+D[0]),(0===P||P===k-1)&&L.push(D)}if(k>=2){var I=L[0][0],O=L[1][0],R=L[0][1]*e[X],E=L[1][1]*e[X];t.type=a,t.method="none",t.focus="100%",t.angle=o,t.color=I,t.color2=O,t.colors=A.join(","),t[X]=E,t.opacity2=R}"radial"===a&&(t.focusposition=s.join(","))}else B(t,r,e[X])},G=function(t,e){null!=e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e[c]||e[c]instanceof m||B(t,e[c],e[X])},H=function(t,e,i,n){var r="fill"==e,a=t.getElementsByTagName(e)[0];null!=i[e]&&"none"!==i[e]&&(r||!r&&i[u])?(t[r?"filled":"stroked"]="true",i[e]instanceof m&&R(t,a),a||(a=v.createNode(e)),r?V(a,i,n):G(a,i),O(t,a)):(t[r?"filled":"stroked"]="false",R(t,a))},W=[[],[],[]],U=function(t,e){var i,n,a,o,s,l,c=r.M,u=r.C,h=r.L,f=r.A,d=r.Q,p=[];for(o=0;o<t[Me];){switch(a=t[o++],n="",i=0,a){case c:n=" m ",i=1,s=t[o++],l=t[o++],W[0][0]=s,W[0][1]=l;break;case h:n=" l ",i=1,s=t[o++],l=t[o++],W[0][0]=s,W[0][1]=l;break;case d:case u:n=" c ",i=3;var m,v,_=t[o++],w=t[o++],T=t[o++],L=t[o++];a===d?(m=T,v=L,T=(T+2*_)/3,L=(L+2*w)/3,_=(s+2*_)/3,w=(l+2*w)/3):(m=t[o++],v=t[o++]),W[0][0]=_,W[0][1]=w,W[1][0]=T,W[1][1]=L,W[2][0]=m,W[2][1]=v,s=m,l=v;break;case f:var A=0,P=0,z=1,D=1,I=0;e&&(A=e[4],P=e[5],z=y(e[0]*e[0]+e[1]*e[1]),D=y(e[2]*e[2]+e[3]*e[3]),I=Math.atan2(-e[1]/D,e[0]/z));var O=t[o++],R=t[o++],E=t[o++],N=t[o++],B=t[o++]+I,F=t[o++]+B+I;o++;var V=t[o++],G=O+x(B)*E,H=R+b(B)*N,_=O+x(F)*E,w=R+b(F)*N,q=V?" wa ":" at ";Math.abs(G-_)<1e-10&&(Math.abs(F-B)>.01?V&&(G+=270/C):Math.abs(H-R)<1e-10?V&&O>G||!V&&G>O?w-=270/C:w+=270/C:V&&R>H||!V&&H>R?_+=270/C:_-=270/C),p.push(q,g(((O-E)*z+A)*C-k),S,g(((R-N)*D+P)*C-k),S,g(((O+E)*z+A)*C-k),S,g(((R+N)*D+P)*C-k),S,g((G*z+A)*C-k),S,g((H*D+P)*C-k),S,g((_*z+A)*C-k),S,g((w*D+P)*C-k)),s=_,l=w;break;case r.R:var U=W[0],Z=W[1];U[0]=t[o++],U[1]=t[o++],Z[0]=U[0]+t[o++],Z[1]=U[1]+t[o++],e&&(M(U,U,e),M(Z,Z,e)),U[0]=g(U[0]*C-k),Z[0]=g(Z[0]*C-k),U[1]=g(U[1]*C-k),Z[1]=g(Z[1]*C-k),p.push(" m ",U[0],S,U[1]," l ",Z[0],S,U[1]," l ",Z[0],S,Z[1]," l ",U[0],S,Z[1]);break;case r.Z:p.push(" x ")}if(i>0){p.push(n);for(var X=0;i>X;X++){var j=W[X];e&&M(j,j,e),p.push(g(j[0]*C-k),S,g(j[1]*C-k),i-1>X?S:"")}}}return p.join("")};p[He].brushVML=function(t){var e=this.style,i=this._vmlEl;i||(i=v.createNode("shape"),P(i),this._vmlEl=i),H(i,"fill",e,this),H(i,c,e,this);var r=this[n],a=null!=r,o=i.getElementsByTagName(c)[0];if(o){var s=e[u];if(a&&!e.strokeNoScale){var l=r[0]*r[3]-r[1]*r[2];s*=y(_(l))}o.weight=s+"px"}var h=this.path;this.__dirtyPath&&(h.beginPath(),this.buildPath(h,this.shape),h.toStatic(),this.__dirtyPath=!1),i.path=U(h.data,this[n]),i.style.zIndex=E(this[he],this.z,this.z2),O(t,i),e.text?this.drawRectText(t,this[Q]()):this.removeRectText(t)},p[He].onRemove=function(t){R(t,this._vmlEl),this.removeRectText(t)},p[He].onAdd=function(t){O(t,this._vmlEl),this.appendRectText(t)};var Z=function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()};f[He].brushVML=function(t){var e,i,r=this.style,a=r.image;if(Z(a)){var o=a.src;if(o===this._imageSrc)e=this._imageWidth,i=this._imageHeight;else{var s=a.runtimeStyle,l=s.width,c=s[Ie];s.width="auto",s[Ie]="auto",e=a.width,i=a[Ie],s.width=l,s[Ie]=c,this._imageSrc=o,this._imageWidth=e,this._imageHeight=i}a=o}else a===this._imageSrc&&(e=this._imageWidth,i=this._imageHeight);if(a){var u=r.x||0,h=r.y||0,f=r.width,d=r[Ie],p=r.sWidth,m=r.sHeight,_=r.sx||0,x=r.sy||0,b=p&&m,C=this._vmlEl;C||(C=v.doc[q]("div"),P(C),this._vmlEl=C);var k,L=C.style,A=!1,z=1,D=1;if(this[n]&&(k=this[n],z=y(k[0]*k[0]+k[1]*k[1]),D=y(k[2]*k[2]+k[3]*k[3]),A=k[1]||k[2]),A){var I=[u,h],R=[u+f,h],N=[u,h+d],B=[u+f,h+d];M(I,I,k),M(R,R,k),M(N,N,k),M(B,B,k);var F=w(I[0],R[0],N[0],B[0]),V=w(I[1],R[1],N[1],B[1]),G=[];G.push("M11=",k[0]/z,S,"M12=",k[2]/D,S,"M21=",k[1]/z,S,"M22=",k[3]/D,S,"Dx=",g(u*z+k[4]),S,"Dy=",g(h*D+k[5])),L.padding="0 "+g(F)+"px "+g(V)+"px 0",L[oe]=T+".Matrix("+G.join("")+", SizingMethod=clip)"}else k&&(u=u*z+k[4],h=h*D+k[5]),L[oe]="",L.left=g(u)+"px",L.top=g(h)+"px";var H=this._imageEl,W=this._cropEl;H||(H=v.doc[q]("div"),this._imageEl=H);var U=H.style;if(b){if(e&&i)U.width=g(z*e*f/p)+"px",U[Ie]=g(D*i*d/m)+"px";else{var j=new Image,Y=this;j.onload=function(){j.onload=null,e=j.width,i=j[Ie],U.width=g(z*e*f/p)+"px",U[Ie]=g(D*i*d/m)+"px",Y._imageWidth=e,Y._imageHeight=i,Y._imageSrc=a},j.src=a}W||(W=v.doc[q]("div"),W.style.overflow="hidden",this._cropEl=W);var $=W.style;$.width=g((f+_*f/p)*z),$[Ie]=g((d+x*d/m)*D),$[oe]=T+".Matrix(Dx="+-_*f/p*z+",Dy="+-x*d/m*D+")",W.parentNode||C.appendChild(W),H.parentNode!=W&&W.appendChild(H)}else U.width=g(z*f)+"px",U[Ie]=g(D*d)+"px",C.appendChild(H),W&&W.parentNode&&(C.removeChild(W),this._cropEl=null);var K="",J=r[X];1>J&&(K+=".Alpha(opacity="+g(100*J)+") "),K+=T+".AlphaImageLoader(src="+a+", SizingMethod=scale)",U[oe]=K,C.style.zIndex=E(this[he],this.z,this.z2),O(t,C),r.text&&this.drawRectText(t,this[Q]())}},f[He].onRemove=function(t){R(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},f[He].onAdd=function(t){O(t,this._vmlEl),this.appendRectText(t)};var j,K=D,J={},ee=0,ie=100,ne=document[q]("div"),re=function(t){var e=J[t];if(!e){ee>ie&&(ee=0,J={});var i,n=ne.style;try{n.font=t,i=n.fontFamily.split(",")[0]}catch(r){}e={style:n.fontStyle||K,variant:n.fontVariant||K,weight:n.fontWeight||K,size:0|parseFloat(n.fontSize||12),family:i||"Microsoft YaHei"},J[t]=e,ee++
}return e};o.measureText=function(t,e){var i=v.doc;j||(j=i[q]("div"),j.style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",v.doc.body.appendChild(j));try{j.style.font=e}catch(n){}return j.innerHTML="",j.appendChild(i.createTextNode(t)),{width:j.offsetWidth}};for(var ae=new i,se=function(t,e,i,r){var a=this.style,s=a.text;if(s){var l,u,f=a[te],d=re(a.textFont),p=d.style+" "+d.variant+" "+d.weight+" "+d.size+'px "'+d.family+'"',m=a.textBaseline,y=a.textVerticalAlign;i=i||o[Q](s,p,f,m);var _=this[n];if(_&&!r&&(ae.copy(e),ae[h](_),e=ae),r)l=e.x,u=e.y;else{var x=a.textPosition,b=a.textDistance;if(x instanceof Array)l=e.x+N(x[0],e.width),u=e.y+N(x[1],e[Ie]),f=f||"left",m=m||"top";else{var w=o.adjustTextPositionOnRect(x,e,i,b);l=w.x,u=w.y,f=f||w[te],m=m||w.textBaseline}}if(y){switch(y){case $:u-=i[Ie]/2;break;case Oe:u-=i[Ie]}m="top"}var T=d.size;switch(m){case"hanging":case"top":u+=T/1.75;break;case $:break;default:u-=T/2.25}switch(f){case"left":break;case Y:l-=i.width/2;break;case"right":l-=i.width}var C,k,L,A=v.createNode,D=this._textVmlEl;D?(L=D.firstChild,C=L.nextSibling,k=C.nextSibling):(D=A("line"),C=A("path"),k=A("textpath"),L=A("skew"),k.style["v-text-align"]="left",P(D),C.textpathok=!0,k.on=!0,D.from="0 0",D.to="1000 0.05",O(D,L),O(D,C),O(D,k),this._textVmlEl=D);var I=[l,u],R=D.style;_&&r?(M(I,I,_),L.on=!0,L.matrix=_[0].toFixed(3)+S+_[2].toFixed(3)+S+_[1].toFixed(3)+S+_[3].toFixed(3)+",0,0",L.offset=(g(I[0])||0)+","+(g(I[1])||0),L.origin="0 0",R.left="0px",R.top="0px"):(L.on=!1,R.left=g(l)+"px",R.top=g(u)+"px"),k[Ge]=z(s);try{k.style.font=p}catch(B){}H(D,"fill",{fill:r?a.fill:a.textFill,opacity:a[X]},this),H(D,c,{stroke:r?a[c]:a.textStroke,opacity:a[X],lineDash:a.lineDash},this),D.style.zIndex=E(this[he],this.z,this.z2),O(t,D)}},le=function(t){R(t,this._textVmlEl),this._textVmlEl=null},ce=function(t){O(t,this._textVmlEl)},fe=[s,l,f,p,d],de=0;de<fe[Me];de++){var pe=fe[de][He];pe.drawRectText=se,pe.removeRectText=le,pe.appendRectText=ce}d[He].brushVML=function(t){var e=this.style;e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this[Q](),!0):this.removeRectText(t)},d[He].onRemove=function(t){this.removeRectText(t)},d[He].onAdd=function(t){this.appendRectText(t)}}}),e("echarts/model/globalDefault",[],function(){var t="";return typeof navigator!==g&&(t=navigator.platform||""),{color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],textStyle:{fontFamily:t.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:!0,animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3}}),e("echarts/model/mixin/colorPalette",[],function(){return{clearColorPalette:function(){this._colorIdx=0,this._colorNameMap={}},getColorFromPalette:function(t,e){e=e||this;var i=e._colorIdx||0,n=e._colorNameMap||(e._colorNameMap={});if(n[t])return n[t];var r=this.get("color",!0)||[];if(r[Me]){var a=r[i];return t&&(n[t]=a),e._colorIdx=(i+1)%r[Me],a}}}}),e("zrender/tool/path",[je,"../graphic/Path","../core/PathProxy","./transformPath","../core/matrix"],function(t){function e(t,e,i,n,r,a,o,s,l,c,h){var m=l*(p/180),y=d(m)*(t-i)/2+f(m)*(e-n)/2,_=-1*f(m)*(t-i)/2+d(m)*(e-n)/2,x=y*y/(o*o)+_*_/(s*s);x>1&&(o*=u(x),s*=u(x));var b=(r===a?-1:1)*u((o*o*s*s-o*o*_*_-s*s*y*y)/(o*o*_*_+s*s*y*y))||0,w=b*o*_/s,M=b*-s*y/o,S=(t+i)/2+d(m)*w-f(m)*M,T=(e+n)/2+f(m)*w+d(m)*M,C=g([1,0],[(y-w)/o,(_-M)/s]),k=[(y-w)/o,(_-M)/s],L=[(-1*y-w)/o,(-1*_-M)/s],A=g(k,L);v(k,L)<=-1&&(A=p),v(k,L)>=1&&(A=0),0===a&&A>0&&(A-=2*p),1===a&&0>A&&(A+=2*p),h.addData(c,S,T,o,s,C,A,m,a)}function n(t){if(!t)return[];var i,n=t[ue](/-/g," -")[ue](/  /g," ")[ue](/ /g,",")[ue](/,,/g,",");for(i=0;i<c[Me];i++)n=n[ue](new RegExp(c[i],"g"),"|"+c[i]);var r,a=n.split("|"),s=0,l=0,u=new o,h=o.CMD;for(i=1;i<a[Me];i++){var f,d=a[i],p=d.charAt(0),m=0,v=d.slice(1)[ue](/e,-/g,"e-").split(",");v[Me]>0&&""===v[0]&&v.shift();for(var g=0;g<v[Me];g++)v[g]=parseFloat(v[g]);for(;m<v[Me]&&!isNaN(v[m])&&!isNaN(v[0]);){var y,_,x,b,w,M,S,T=s,C=l;switch(p){case"l":s+=v[m++],l+=v[m++],f=h.L,u.addData(f,s,l);break;case"L":s=v[m++],l=v[m++],f=h.L,u.addData(f,s,l);break;case"m":s+=v[m++],l+=v[m++],f=h.M,u.addData(f,s,l),p="l";break;case"M":s=v[m++],l=v[m++],f=h.M,u.addData(f,s,l),p="L";break;case"h":s+=v[m++],f=h.L,u.addData(f,s,l);break;case"H":s=v[m++],f=h.L,u.addData(f,s,l);break;case"v":l+=v[m++],f=h.L,u.addData(f,s,l);break;case"V":l=v[m++],f=h.L,u.addData(f,s,l);break;case"C":f=h.C,u.addData(f,v[m++],v[m++],v[m++],v[m++],v[m++],v[m++]),s=v[m-2],l=v[m-1];break;case"c":f=h.C,u.addData(f,v[m++]+s,v[m++]+l,v[m++]+s,v[m++]+l,v[m++]+s,v[m++]+l),s+=v[m-2],l+=v[m-1];break;case"S":y=s,_=l;var k=u.len(),L=u.data;r===h.C&&(y+=s-L[k-4],_+=l-L[k-3]),f=h.C,T=v[m++],C=v[m++],s=v[m++],l=v[m++],u.addData(f,y,_,T,C,s,l);break;case"s":y=s,_=l;var k=u.len(),L=u.data;r===h.C&&(y+=s-L[k-4],_+=l-L[k-3]),f=h.C,T=s+v[m++],C=l+v[m++],s+=v[m++],l+=v[m++],u.addData(f,y,_,T,C,s,l);break;case"Q":T=v[m++],C=v[m++],s=v[m++],l=v[m++],f=h.Q,u.addData(f,T,C,s,l);break;case"q":T=v[m++]+s,C=v[m++]+l,s+=v[m++],l+=v[m++],f=h.Q,u.addData(f,T,C,s,l);break;case"T":y=s,_=l;var k=u.len(),L=u.data;r===h.Q&&(y+=s-L[k-4],_+=l-L[k-3]),s=v[m++],l=v[m++],f=h.Q,u.addData(f,y,_,s,l);break;case"t":y=s,_=l;var k=u.len(),L=u.data;r===h.Q&&(y+=s-L[k-4],_+=l-L[k-3]),s+=v[m++],l+=v[m++],f=h.Q,u.addData(f,y,_,s,l);break;case"A":x=v[m++],b=v[m++],w=v[m++],M=v[m++],S=v[m++],T=s,C=l,s=v[m++],l=v[m++],f=h.A,e(T,C,s,l,M,S,x,b,w,f,u);break;case"a":x=v[m++],b=v[m++],w=v[m++],M=v[m++],S=v[m++],T=s,C=l,s+=v[m++],l+=v[m++],f=h.A,e(T,C,s,l,M,S,x,b,w,f,u)}}("z"===p||"Z"===p)&&(f=h.Z,u.addData(f)),r=f}return u.toStatic(),u}function r(t,e){var i,r=n(t);return e=e||{},e.buildPath=function(t){t.setData(r.data),i&&s(t,i);var e=t.getContext();e&&t.rebuildPath(e)},e[h]=function(t){i||(i=l[De]()),l.mul(i,t,i),this.dirty(!0)},e}var a=t("../graphic/Path"),o=t("../core/PathProxy"),s=t("./transformPath"),l=t("../core/matrix"),c=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"],u=Math.sqrt,f=Math.sin,d=Math.cos,p=Math.PI,m=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},v=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(m(t)*m(e))},g=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(v(t,e))};return{createFromString:function(t,e){return new a(r(t,e))},extendFromString:function(t,e){return a[Ce](r(t,e))},mergePath:function(t,e){for(var n=[],r=t[Me],o=0;r>o;o++){var s=t[o];s[i]&&s.buildPath(s.path,s.shape,!0),n.push(s.path)}var l=new a(e);return l.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},l}}}),e("zrender/graphic/Path",[je,"./Displayable","../core/util","../core/PathProxy","../contain/path","./Pattern"],function(t){function e(t){r.call(this,t),this.path=new o}var r=t("./Displayable"),a=t("../core/util"),o=t("../core/PathProxy"),s=t("../contain/path"),l=t("./Pattern"),h=l[He].getCanvasPattern,f=Math.abs;return e[He]={constructor:e,type:"path",__dirtyPath:!0,strokeContainThreshold:5,brush:function(t,e){var n=this.style,r=this.path,a=n.hasStroke(),o=n.hasFill(),s=n.fill,l=n[c],u=o&&!!s.colorStops,f=a&&!!l.colorStops,d=o&&!!s.image,p=a&&!!l.image;if(n.bind(t,this,e),this.setTransform(t),this[i]){var m=this[Q]();u&&(this._fillGradient=n.getGradient(t,s,m)),f&&(this._strokeGradient=n.getGradient(t,l,m))}u?t.fillStyle=this._fillGradient:d&&(t.fillStyle=h.call(s,t)),f?t.strokeStyle=this._strokeGradient:p&&(t.strokeStyle=h.call(l,t));var v=n.lineDash,g=n.lineDashOffset,y=!!t.setLineDash,_=this.getGlobalScale();r.setScale(_[0],_[1]),this.__dirtyPath||v&&!y&&a?(r=this.path.beginPath(t),v&&!y&&(r.setLineDash(v),r.setLineDashOffset(g)),this.buildPath(r,this.shape,!1),this.__dirtyPath=!1):(t.beginPath(),this.path.rebuildPath(t)),o&&r.fill(t),v&&y&&(t.setLineDash(v),t.lineDashOffset=g),a&&r[c](t),v&&y&&t.setLineDash([]),this.restoreTransform(t),(n.text||0===n.text)&&this.drawRectText(t,this[Q]())},buildPath:function(){},getBoundingRect:function(){var t=this._rect,e=this.style,n=!t;if(n){var r=this.path;this.__dirtyPath&&(r.beginPath(),this.buildPath(r,this.shape,!1)),t=r[Q]()}if(this._rect=t,e.hasStroke()){var a=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this[i]||n){a.copy(t);var o=e[u],s=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),s>1e-10&&(a.width+=o/s,a[Ie]+=o/s,a.x-=o/s/2,a.y-=o/s/2)}return a}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this[Q](),r=this.style;if(t=i[0],e=i[1],n[Z](t,e)){var a=this.path.data;if(r.hasStroke()){var o=r[u],l=r.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(r.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),s.containStroke(a,o/l,t,e)))return!0}if(r.hasFill())return s[Z](a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this[i]=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):r[He].attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(a[Le](t))for(var n in t)i[n]=t[n];else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this[n];return t&&f(t[0]-1)>1e-10&&f(t[3]-1)>1e-10?Math.sqrt(f(t[0]*t[3]-t[2]*t[1])):1}},e[Ce]=function(t){var i=function(i){e.call(this,i),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var r=this.shape;for(var a in n)!r.hasOwnProperty(a)&&n.hasOwnProperty(a)&&(r[a]=n[a])}t.init&&t.init.call(this,i)};a[re](i,e);for(var n in t)"style"!==n&&"shape"!==n&&(i[He][n]=t[n]);return i},a[re](e,r),e}),e("zrender/graphic/Gradient",[je],function(){var t=function(t){this.colorStops=t||[]};return t[He]={constructor:t,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}},t}),e("zrender/container/Group",[je,"../core/util","../Element","../core/BoundingRect"],function(t){var e=t("../core/util"),n=t("../Element"),r=t("../core/BoundingRect"),a=function(t){t=t||{},n.call(this,t);for(var e in t)this[e]=t[e];this._children=[],this.__storage=null,this[i]=!0};return a[He]={constructor:a,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e[Me];i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children[Me]},add:function(t){return t&&t!==this&&t[s]!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t[s]!==this&&e&&e[s]===this){var i=this._children,n=i[ae](e);n>=0&&(i[be](n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t[s]&&t[s][ke](t),t[s]=this;var e=this.__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToMap(t),t instanceof a&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var i=this.__zr,n=this.__storage,r=this._children,o=e[ae](r,t);return 0>o?this:(r[be](o,1),t[s]=null,n&&(n.delFromMap(t.id),t instanceof a&&t.delChildrenFromStorage(n)),i&&i.refresh(),this)},removeAll:function(){var t,e,i=this._children,n=this.__storage;for(e=0;e<i[Me];e++)t=i[e],n&&(n.delFromMap(t.id),t instanceof a&&t.delChildrenFromStorage(n)),t[s]=null;return i[Me]=0,this},eachChild:function(t,e){for(var i=this._children,n=0;n<i[Me];n++){var r=i[n];t.call(e,r,n)}return this},traverse:function(t,e){for(var i=0;i<this._children[Me];i++){var n=this._children[i];t.call(e,n),"group"===n.type&&n[de](t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children[Me];e++){var i=this._children[e];t.addToMap(i),i instanceof a&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children[Me];e++){var i=this._children[e];t.delFromMap(i.id),i instanceof a&&i.delChildrenFromStorage(t)}},dirty:function(){return this[i]=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new r(0,0,0,0),n=t||this._children,a=[],o=0;o<n[Me];o++){var s=n[o];if(!s[Re]&&!s.invisible){var l=s[Q](),c=s.getLocalTransform(a);c?(i.copy(l),i[h](c),e=e||i.clone(),e.union(i)):(e=e||l.clone(),e.union(l))}}return e||i}},e[re](a,n),a}),e("zrender/graphic/Image",[je,"./Displayable","../core/BoundingRect","../core/util","../core/LRU"],function(t){function e(t){i.call(this,t)}var i=t("./Displayable"),n=t("../core/BoundingRect"),r=t("../core/util"),a=t("../core/LRU"),o=new a(50);return e[He]={constructor:e,type:"image",brush:function(t,e){var i,n=this.style,r=n.image;if(n.bind(t,this,e),i=typeof r===Ge?this._image:r,!i&&r){var a=o.get(r);if(!a)return i=new Image,i.onload=function(){i.onload=null;for(var t=0;t<a.pending[Me];t++)a.pending[t].dirty()},a={image:i,pending:[this]},i.src=r,o.put(r,a),void(this._image=i);if(i=a.image,this._image=i,!i.width||!i[Ie])return void a.pending.push(this)}if(i){var s=n.width||i.width,l=n[Ie]||i[Ie],c=n.x||0,u=n.y||0;if(!i.width||!i[Ie])return;if(this.setTransform(t),n.sWidth&&n.sHeight){var h=n.sx||0,f=n.sy||0;t.drawImage(i,h,f,n.sWidth,n.sHeight,c,u,s,l)}else if(n.sx&&n.sy){var h=n.sx,f=n.sy,d=s-h,p=l-f;t.drawImage(i,h,f,d,p,c,u,s,l)}else t.drawImage(i,c,u,s,l);null==n.width&&(n.width=s),null==n[Ie]&&(n[Ie]=l),this.restoreTransform(t),null!=n.text&&this.drawRectText(t,this[Q]())}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new n(t.x||0,t.y||0,t.width||0,t[Ie]||0)),this._rect}},r[re](e,i),e}),e("zrender/graphic/Text",[je,"./Displayable","../core/util","../contain/text"],function(t){var e=t("./Displayable"),i=t("../core/util"),n=t("../contain/text"),r=function(t){e.call(this,t)};return r[He]={constructor:r,type:"text",brush:function(t,e){var i=this.style,r=i.x||0,a=i.y||0,o=i.text;if(null!=o&&(o+=""),i.bind(t,this,e),o){this.setTransform(t);var s,l=i[te],c=i.textFont||i.font;if(i.textVerticalAlign){var u=n[Q](o,c,i[te],"top");switch(s=$,i.textVerticalAlign){case $:a-=u[Ie]/2-u.lineHeight/2;break;case Oe:a-=u[Ie]-u.lineHeight/2;break;default:a+=u.lineHeight/2}}else s=i.textBaseline;t.font=c||"12px sans-serif",t[te]=l||"left",t[te]!==l&&(t[te]="left"),t.textBaseline=s||"alphabetic",t.textBaseline!==s&&(t.textBaseline="alphabetic");for(var h=n.measureText("国",t.font).width,f=o.split("\n"),d=0;d<f[Me];d++)i.hasFill()&&t.fillText(f[d],r,a),i.hasStroke()&&t.strokeText(f[d],r,a),a+=h;this.restoreTransform(t)}},getBoundingRect:function(){if(!this._rect){var t=this.style,e=t.textVerticalAlign,i=n[Q](t.text+"",t.textFont||t.font,t[te],e?"top":t.textBaseline);switch(e){case $:i.y-=i[Ie]/2;break;case Oe:i.y-=i[Ie]}i.x+=t.x||0,i.y+=t.y||0,this._rect=i}return this._rect}},i[re](r,e),r}),e("zrender/graphic/shape/Circle",[je,"../Path"],function(t){return t("../Path")[Ce]({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}})}),e("zrender/graphic/shape/Sector",[je,"../Path"],function(t){return t("../Path")[Ce]({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,c=Math.cos(o),u=Math.sin(o);t.moveTo(c*r+i,u*r+n),t.lineTo(c*a+i,u*a+n),t.arc(i,n,a,o,s,!l),t.lineTo(Math.cos(s)*r+i,Math.sin(s)*r+n),0!==r&&t.arc(i,n,r,s,o,l),t.closePath()}})}),e("zrender/graphic/shape/Ring",[je,"../Path"],function(t){return t("../Path")[Ce]({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=2*Math.PI;t.moveTo(i+e.r,n),t.arc(i,n,e.r,0,r,!1),t.moveTo(i+e.r0,n),t.arc(i,n,e.r0,0,r,!0)}})}),e("zrender/graphic/shape/Polygon",[je,"../helper/poly","../Path"],function(t){var e=t("../helper/poly");return t("../Path")[Ce]({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,i){e.buildPath(t,i,!0)}})}),e("zrender/graphic/shape/Polyline",[je,"../helper/poly","../Path"],function(t){var e=t("../helper/poly");return t("../Path")[Ce]({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,i){e.buildPath(t,i,!1)}})}),e("zrender/graphic/shape/Rect",[je,"../helper/roundRect","../Path"],function(t){var e=t("../helper/roundRect");return t("../Path")[Ce]({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,i){var n=i.x,r=i.y,a=i.width,o=i[Ie];i.r?e.buildPath(t,i):t.rect(n,r,a,o),t.closePath()}})}),e("zrender/graphic/shape/Line",[je,"../Path"],function(t){return t("../Path")[Ce]({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,r=e.x2,a=e.y2,o=e.percent;0!==o&&(t.moveTo(i,n),1>o&&(r=i*(1-o)+r*o,a=n*(1-o)+a*o),t.lineTo(r,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}})}),e("zrender/graphic/shape/BezierCurve",[je,"../../core/curve","../../core/vector","../Path"],function(t){function e(t,e,i){var n=t.cpx2,r=t.cpy2;return null===n||null===r?[(i?c:s)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?c:s)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?l:o)(t.x1,t.cpx1,t.x2,e),(i?l:o)(t.y1,t.cpy1,t.y2,e)]}var i=t("../../core/curve"),n=t("../../core/vector"),r=i.quadraticSubdivide,a=i.cubicSubdivide,o=i.quadraticAt,s=i.cubicAt,l=i.quadraticDerivativeAt,c=i.cubicDerivativeAt,u=[];return t("../Path")[Ce]({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,o=e.x2,s=e.y2,l=e.cpx1,c=e.cpy1,h=e.cpx2,f=e.cpy2,d=e.percent;0!==d&&(t.moveTo(i,n),null==h||null==f?(1>d&&(r(i,l,o,d,u),l=u[1],o=u[2],r(n,c,s,d,u),c=u[1],s=u[2]),t.quadraticCurveTo(l,c,o,s)):(1>d&&(a(i,l,h,o,d,u),l=u[1],h=u[2],o=u[3],a(n,c,f,s,d,u),c=u[1],f=u[2],s=u[3]),t.bezierCurveTo(l,c,h,f,o,s)))},pointAt:function(t){return e(this.shape,t,!1)},tangentAt:function(t){var i=e(this.shape,t,!0);return n[U](i,i)}})}),e("zrender/graphic/shape/Arc",[je,"../Path"],function(t){return t("../Path")[Ce]({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),c=Math.sin(a);t.moveTo(l*r+i,c*r+n),t.arc(i,n,r,a,o,!s)}})}),e("zrender/graphic/CompoundPath",[je,"./Path"],function(t){var e=t("./Path");return e[Ce]({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e[Me];n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this[i]=this[i]||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t[Me];i++)t[i].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var i=e.paths||[],n=0;n<i[Me];n++)i[n].buildPath(t,i[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths,e=0;e<t[Me];e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),e[He][Q].call(this)}})}),e("zrender/graphic/LinearGradient",[je,"../core/util","./Gradient"],function(t){var e=t("../core/util"),i=t("./Gradient"),n=function(t,e,n,r,a,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==r?0:r,this.type="linear",this.global=o||!1,i.call(this,a)};return n[He]={constructor:n},e[re](n,i),n}),e("zrender/graphic/RadialGradient",[je,"../core/util","./Gradient"],function(t){var e=t("../core/util"),i=t("./Gradient"),n=function(t,e,n,r,a){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=a||!1,i.call(this,r)};return n[He]={constructor:n},e[re](n,i),n}),e("zrender/core/BoundingRect",[je,"./vector","./matrix"],function(t){function e(t,e,i,n){this.x=t,this.y=e,this.width=i,this[Ie]=n}var i=t("./vector"),n=t("./matrix"),r=i[h],a=Math.min,o=Math.abs,s=Math.max;return e[He]={constructor:e,union:function(t){var e=a(t.x,this.x),i=a(t.y,this.y);this.width=s(t.x+t.width,this.x+this.width)-e,this[Ie]=s(t.y+t[Ie],this.y+this[Ie])-i,this.x=e,this.y=i},applyTransform:function(){var t=[],e=[];return function(i){i&&(t[0]=this.x,t[1]=this.y,e[0]=this.x+this.width,e[1]=this.y+this[Ie],r(t,t,i),r(e,e,i),this.x=a(t[0],e[0]),this.y=a(t[1],e[1]),this.width=o(e[0]-t[0]),this[Ie]=o(e[1]-t[1]))}}(),calculateTransform:function(t){var e=this,i=t.width/e.width,r=t[Ie]/e[Ie],a=n[De]();return n.translate(a,a,[-e.x,-e.y]),n.scale(a,a,[i,r]),n.translate(a,a,[t.x,t.y]),a},intersect:function(t){var e=this,i=e.x,n=e.x+e.width,r=e.y,a=e.y+e[Ie],o=t.x,s=t.x+t.width,l=t.y,c=t.y+t[Ie];return!(o>n||i>s||l>a||r>c)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i[Ie]},clone:function(){return new e(this.x,this.y,this.width,this[Ie])},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this[Ie]=t[Ie]}},e}),e("zrender/contain/text",[je,"../core/util","../core/BoundingRect"],function(t){function e(t,e){var i=t+":"+e;if(o[i])return o[i];for(var n=(t+"").split("\n"),r=0,a=0,l=n[Me];l>a;a++)r=Math.max(d.measureText(n[a],e).width,r);return s>c&&(s=0,o={}),s++,o[i]=r,r}function i(t,i,n,r){var a=((t||"")+"").split("\n")[Me],o=e(t,i),s=e("国",i),l=a*s,c=new h(0,0,o,l);switch(c.lineHeight=s,r){case Oe:case"alphabetic":c.y-=s;break;case $:c.y-=s/2}switch(n){case"end":case"right":c.x-=c.width;break;case Y:c.x-=c.width/2}return c}function n(t,e,i,n){var r=e.x,a=e.y,o=e[Ie],s=e.width,c=i[Ie],u=o/2-c/2,h="left";switch(t){case"left":r-=n,a+=u,h="right";break;case"right":r+=n+s,a+=u,h="left";break;case"top":r+=s/2,a-=n+c,h=Y;break;case Oe:r+=s/2,a+=o+n,h=Y;break;case l:r+=s/2,a+=u,h=Y;break;case"insideLeft":r+=n,a+=u,h="left";break;case"insideRight":r+=s-n,a+=u,h="right";break;case"insideTop":r+=s/2,a+=n,h=Y;break;case"insideBottom":r+=s/2,a+=o-c-n,h=Y;break;case"insideTopLeft":r+=n,a+=n,h="left";break;case"insideTopRight":r+=s-n,a+=n,h="right";break;case"insideBottomLeft":r+=n,a+=o-c-n;break;case"insideBottomRight":r+=s-n,a+=o-c-n,h="right"}return{x:r,y:a,textAlign:h,textBaseline:"top"}}function r(t,i,n,r,o){if(!i)return"";o=o||{},r=f(r,"...");for(var s=f(o.maxIterations,2),l=f(o.minChar,0),c=e("国",n),u=e("a",n),h=f(o.placeholder,""),d=i=Math.max(0,i-1),p=0;l>p&&d>=u;p++)d-=u;var m=e(r);m>d&&(r="",m=0),d=i-m;for(var v=(t+"").split("\n"),p=0,g=v[Me];g>p;p++){var y=v[p],_=e(y,n);if(!(i>=_)){for(var x=0;;x++){if(d>=_||x>=s){y+=r;break}var b=0===x?a(y,d,u,c):_>0?Math.floor(y[Me]*d/_):0;y=y.substr(0,b),_=e(y,n)}""===y&&(y=h),v[p]=y}}return v.join("\n")}function a(t,e,i,n){for(var r=0,a=0,o=t[Me];o>a&&e>r;a++){var s=t.charCodeAt(a);r+=s>=0&&127>=s?i:n}return a}var o={},s=0,c=5e3,u=t("../core/util"),h=t("../core/BoundingRect"),f=u[B],d={getWidth:e,getBoundingRect:i,adjustTextPositionOnRect:n,truncateText:r,measureText:function(t,e){var i=u.getContext();return i.font=e||"12px sans-serif",i.measureText(t)}};return d}),e("echarts/util/clazz",[je,Xe],function(t){function e(t,e){var i=n.slice(arguments,2);return this.superClass[He][e].apply(t,i)}function i(t,e,i){return this.superClass[He][e].apply(t,i)}var n=t(Xe),r={},a=".",o="___EC__COMPONENT__CONTAINER___",s=r.parseClassType=function(t){var e={main:"",sub:""};return t&&(t=t.split(a),e.main=t[0]||"",e.sub=t[1]||""),e};return r.enableClassExtend=function(t){t.$constructor=t,t[Ce]=function(t){var r=this,a=function(){t.$constructor?t.$constructor.apply(this,arguments):r.apply(this,arguments)};return n[Ce](a[He],t),a[Ce]=this[Ce],a.superCall=e,a.superApply=i,n[re](a,this),a.superClass=r,a}},r.enableClassManagement=function(t,e){function i(t){var e=r[t.main];return e&&e[o]||(e=r[t.main]={},e[o]=!0),e}e=e||{};var r={};if(t.registerClass=function(t,e){if(e)if(e=s(e),e.sub){if(e.sub!==o){var n=i(e);n[e.sub]=t}}else r[e.main]=t;return t},t.getClass=function(t,e,i){var n=r[t];if(n&&n[o]&&(n=e?n[e]:null),i&&!n)throw new Error("Component "+t+"."+(e||"")+" not exists. Load it first.");return n},t.getClassesByMainType=function(t){t=s(t);var e=[],i=r[t.main];return i&&i[o]?n.each(i,function(t,i){i!==o&&e.push(t)}):e.push(i),e},t.hasClass=function(t){return t=s(t),!!r[t.main]},t.getAllClassMainTypes=function(){var t=[];return n.each(r,function(e,i){t.push(i)}),t},t.hasSubTypes=function(t){t=s(t);var e=r[t.main];return e&&e[o]},t.parseClassType=s,e.registerWhenExtend){var a=t[Ce];a&&(t[Ce]=function(e){var i=a.call(this,e);return t.registerClass(i,e.type)})}return t},r.setReadOnly=function(){},r}),e("echarts/model/mixin/lineStyle",[je,"./makeStyleMapper"],function(t){var e=t("./makeStyleMapper")([[u,"width"],[c,"color"],[X],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);return{getLineStyle:function(t){var i=e.call(this,t),n=this.getLineDash();return n&&(i.lineDash=n),i},getLineDash:function(){var t=this.get("type");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[2,2]}}}),e("echarts/model/mixin/itemStyle",[je,"./makeStyleMapper"],function(t){var e=t("./makeStyleMapper")([["fill","color"],[c,"borderColor"],[u,"borderWidth"],[X],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);return{getItemStyle:function(t){var i=e.call(this,t),n=this.getBorderLineDash();return n&&(i.lineDash=n),i},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}}}),e("echarts/model/mixin/areaStyle",[je,"./makeStyleMapper"],function(t){return{getAreaStyle:t("./makeStyleMapper")([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],[X],["shadowColor"]])}}),e("echarts/model/mixin/textStyle",[je,"zrender/contain/text"],function(t){function e(t,e){return t&&t[d](e)}var i=t("zrender/contain/text");return{getTextColor:function(){var t=this[f];return this[d]("color")||t&&t.get("textStyle.color")},getFont:function(){var t=this[f],i=t&&t[Be](ee);return[this[d]("fontStyle")||e(i,"fontStyle"),this[d]("fontWeight")||e(i,"fontWeight"),(this[d]("fontSize")||e(i,"fontSize")||12)+"px",this[d]("fontFamily")||e(i,"fontFamily")||"sans-serif"].join(" ")},getTextRect:function(t){var e=this.get(ee)||{};return i[Q](t,this[J](),e.align,e.baseline)},truncateText:function(t,e,n,r){return i.truncateText(t,e,this[J](),n,r)}}}),e("echarts/component/marker/MarkerModel",[je,"../../util/model",Xe,Ue,"../../util/format",I],function(t){function e(t){i.defaultEmphasis(t.label,i.LABEL_OPTIONS)}var i=t("../../util/model"),n=t(Xe),r=t(Ue),a=t("../../util/format"),o=a.addCommas,s=a.encodeHTML,l=t(I).extendComponentModel({type:"marker",dependencies:[we,"grid","polar","geo"],init:function(t,e,i,n){this.mergeDefaultAndTheme(t,i),this[m](t,i,n.createdBySelf,!0)},ifEnableAnimation:function(){if(r.node)return!1;var t=this.__hostSeries;return this[d](Fe)&&t&&t.ifEnableAnimation()},mergeOption:function(t,i,r,a){var o=this.constructor,s=this.mainType+"Model";r||i[Pe](function(t){var r=t.get(this.mainType),l=t[s];if(!r||!r.data)return void(t[s]=null);if(l)l[m](r,i,!0);else{a&&e(r),n.each(r.data,function(t){t instanceof Array?(e(t[0]),e(t[1])):e(t)});var c={mainType:this.mainType,seriesIndex:t[me],name:t.name,createdBySelf:!0};l=new o(r,this,i,c),l.__hostSeries=t}t[s]=l},this)},formatTooltip:function(t){var e=this[Ze](),i=this[w](t),r=n[ce](i)?n.map(i,o).join(", "):o(i),a=e[p](t),l=this.name;return(null!=i||a)&&(l+="<br />"),a&&(l+=s(a),null!=i&&(l+=" : ")),null!=i&&(l+=r),l},getData:function(){return this._data},setData:function(t){this._data=t}});return n.mixin(l,i.dataFormatMixin),l}),e("zrender/core/PathProxy",[je,"./curve","./vector","./bbox","./BoundingRect","../config"],function(t){var e=t("./curve"),i=t("./vector"),n=t("./bbox"),r=t("./BoundingRect"),a=t("../config").devicePixelRatio,o={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},s=[],l=[],u=[],h=[],f=Math.min,d=Math.max,p=Math.cos,m=Math.sin,v=Math.sqrt,y=Math.abs,_=typeof Float32Array!=g,x=function(){this.data=[],this._len=0,this._ctx=null,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._ux=0,this._uy=0};return x[He]={constructor:x,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=y(1/a/t)||0,this._uy=y(1/a/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._len=0,this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(o.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=y(t-this._xi)>this._ux||y(e-this._yi)>this._uy||this._len<5;return this.addData(o.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,n,r,a){return this.addData(o.C,t,e,i,n,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,n,r,a):this._ctx.bezierCurveTo(t,e,i,n,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,i,n){return this.addData(o.Q,t,e,i,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,n):this._ctx.quadraticCurveTo(t,e,i,n)),this._xi=i,this._yi=n,this},arc:function(t,e,i,n,r,a){return this.addData(o.A,t,e,i,i,n,r-n,0,a?0:1),this._ctx&&this._ctx.arc(t,e,i,n,r,a),this._xi=p(r)*i+t,this._xi=m(r)*i+t,this},arcTo:function(t,e,i,n,r){return this._ctx&&this._ctx.arcTo(t,e,i,n,r),this},rect:function(t,e,i,n){return this._ctx&&this._ctx.rect(t,e,i,n),this.addData(o.R,t,e,i,n),this},closePath:function(){this.addData(o.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t[c](),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,i=0;i<t[Me];i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t[Me];this.data&&this.data[Me]==e||!_||(this.data=new Float32Array(e));for(var i=0;e>i;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t[Me],i=0,n=this._len,r=0;e>r;r++)i+=t[r].len();_&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var r=0;e>r;r++)for(var a=t[r].data,o=0;o<a[Me];o++)this.data[n++]=a[o];this._len=n},addData:function(t){var e=this.data;this._len+arguments[Me]>e[Me]&&(this._expandData(),e=this.data);for(var i=0;i<arguments[Me];i++)e[this._len++]=arguments[i];this._prevCmd=t},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,n,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,c=this._yi,u=t-l,h=e-c,p=v(u*u+h*h),m=l,g=c,y=o[Me];for(u/=p,h/=p,0>a&&(a=r+a),a%=r,m-=a*u,g-=a*h;u>0&&t>=m||0>u&&m>=t||0==u&&(h>0&&e>=g||0>h&&g>=e);)n=this._dashIdx,i=o[n],m+=u*i,g+=h*i,this._dashIdx=(n+1)%y,u>0&&l>m||0>u&&m>l||h>0&&c>g||0>h&&g>c||s[n%2?"moveTo":"lineTo"](u>=0?f(m,t):d(m,t),h>=0?f(g,e):d(g,e));u=m-t,h=g-e,this._dashOffset=-v(u*u+h*h)},_dashedBezierTo:function(t,i,n,r,a,o){var s,l,c,u,h,f=this._dashSum,d=this._dashOffset,p=this._lineDash,m=this._ctx,g=this._xi,y=this._yi,_=e.cubicAt,x=0,b=this._dashIdx,w=p[Me],M=0;for(0>d&&(d=f+d),d%=f,s=0;1>s;s+=.1)l=_(g,t,n,a,s+.1)-_(g,t,n,a,s),c=_(y,i,r,o,s+.1)-_(y,i,r,o,s),x+=v(l*l+c*c);for(;w>b&&(M+=p[b],!(M>d));b++);for(s=(M-d)/x;1>=s;)u=_(g,t,n,a,s),h=_(y,i,r,o,s),b%2?m.moveTo(u,h):m.lineTo(u,h),s+=p[b]/x,b=(b+1)%w;b%2!==0&&m.lineTo(a,o),l=a-u,c=o-h,this._dashOffset=-v(l*l+c*c)},_dashedQuadraticTo:function(t,e,i,n){var r=i,a=n;i=(i+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,n,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t[Me]=this._len,_&&(this.data=new Float32Array(t)))},getBoundingRect:function(){s[0]=s[1]=u[0]=u[1]=Number.MAX_VALUE,l[0]=l[1]=h[0]=h[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,a=0,c=0,f=0,d=0;d<t[Me];){var v=t[d++];
switch(1==d&&(e=t[d],a=t[d+1],c=e,f=a),v){case o.M:c=t[d++],f=t[d++],e=c,a=f,u[0]=c,u[1]=f,h[0]=c,h[1]=f;break;case o.L:n.fromLine(e,a,t[d],t[d+1],u,h),e=t[d++],a=t[d++];break;case o.C:n.fromCubic(e,a,t[d++],t[d++],t[d++],t[d++],t[d],t[d+1],u,h),e=t[d++],a=t[d++];break;case o.Q:n.fromQuadratic(e,a,t[d++],t[d++],t[d],t[d+1],u,h),e=t[d++],a=t[d++];break;case o.A:var g=t[d++],y=t[d++],_=t[d++],x=t[d++],b=t[d++],w=t[d++]+b,M=(t[d++],1-t[d++]);1==d&&(c=p(b)*_+g,f=m(b)*x+y),n.fromArc(g,y,_,x,b,w,M,u,h),e=p(w)*_+g,a=m(w)*x+y;break;case o.R:c=e=t[d++],f=a=t[d++];var S=t[d++],T=t[d++];n.fromLine(c,f,c+S,f+T,u,h);break;case o.Z:e=c,a=f}i.min(s,s,u),i.max(l,l,h)}return 0===d&&(s[0]=s[1]=l[0]=l[1]=0),new r(s[0],s[1],l[0]-s[0],l[1]-s[1])},rebuildPath:function(t){for(var e,i,n,r,a,s,l=this.data,c=this._ux,u=this._uy,h=this._len,f=0;h>f;){var d=l[f++];switch(1==f&&(n=l[f],r=l[f+1],e=n,i=r),d){case o.M:e=n=l[f++],i=r=l[f++],t.moveTo(n,r);break;case o.L:a=l[f++],s=l[f++],(y(a-n)>c||y(s-r)>u||f===h-1)&&(t.lineTo(a,s),n=a,r=s);break;case o.C:t.bezierCurveTo(l[f++],l[f++],l[f++],l[f++],l[f++],l[f++]),n=l[f-2],r=l[f-1];break;case o.Q:t.quadraticCurveTo(l[f++],l[f++],l[f++],l[f++]),n=l[f-2],r=l[f-1];break;case o.A:var v=l[f++],g=l[f++],_=l[f++],x=l[f++],b=l[f++],w=l[f++],M=l[f++],S=l[f++],T=_>x?_:x,C=_>x?1:_/x,k=_>x?x/_:1,L=Math.abs(_-x)>.001,A=b+w;L?(t.translate(v,g),t.rotate(M),t.scale(C,k),t.arc(0,0,T,b,A,1-S),t.scale(1/C,1/k),t.rotate(-M),t.translate(-v,-g)):t.arc(v,g,T,b,A,1-S),1==f&&(e=p(b)*_+v,i=m(b)*x+g),n=p(A)*_+v,r=m(A)*x+g;break;case o.R:e=n=l[f],i=r=l[f+1],t.rect(l[f++],l[f++],l[f++],l[f++]);break;case o.Z:t.closePath(),n=e,r=i}}}},x.CMD=o,x}),e("zrender/graphic/mixin/RectText",[je,"../../contain/text","../../core/BoundingRect"],function(t){function e(t,e){return typeof t===Ge?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}var i=t("../../contain/text"),r=t("../../core/BoundingRect"),a=new r,o=function(){};return o[He]={constructor:o,drawRectText:function(t,r,o){var s=this.style,l=s.text;if(null!=l&&(l+=""),l){t.save();var c,u,f=s.textPosition,d=s.textDistance,p=s[te],m=s.textFont||s.font,v=s.textBaseline,g=s.textVerticalAlign;o=o||i[Q](l,m,p,v);var y=this[n];if(s.textTransform?this.setTransform(t):y&&(a.copy(r),a[h](y),r=a),f instanceof Array){if(c=r.x+e(f[0],r.width),u=r.y+e(f[1],r[Ie]),p=p||"left",v=v||"top",g){switch(g){case $:u-=o[Ie]/2-o.lineHeight/2;break;case Oe:u-=o[Ie]-o.lineHeight/2;break;default:u+=o.lineHeight/2}v=$}}else{var _=i.adjustTextPositionOnRect(f,r,o,d);c=_.x,u=_.y,p=p||_[te],v=v||_.textBaseline}t[te]=p||"left",t.textBaseline=v||"alphabetic";var x=s.textFill,b=s.textStroke;x&&(t.fillStyle=x),b&&(t.strokeStyle=b),t.font=m||"12px sans-serif",t.shadowBlur=s.textShadowBlur,t.shadowColor=s.textShadowColor||"transparent",t.shadowOffsetX=s.textShadowOffsetX,t.shadowOffsetY=s.textShadowOffsetY;var w=l.split("\n");s.textRotation&&(y&&t.translate(y[4],y[5]),t.rotate(s.textRotation),y&&t.translate(-y[4],-y[5]));for(var M=0;M<w[Me];M++)x&&t.fillText(w[M],c,u),b&&t.strokeText(w[M],c,u),u+=o.lineHeight;t.restore()}}},o}),e("zrender/graphic/Displayable",[je,"../core/util","./Style","../Element","./mixin/RectText"],function(t){function e(t){t=t||{},a.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new r(t.style),this._rect=null,this.__clipPaths=[]}var n=t("../core/util"),r=t("./Style"),a=t("../Element"),o=t("./mixin/RectText");return e[He]={constructor:e,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:-1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this[Q]();return n[Z](i[0],i[1])},dirty:function(){this[i]=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?a[He].attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new r(t),this.dirty(!1),this}},n[re](e,a),n.mixin(e,o),e}),e("zrender/vml/core",[je,"exports","module","../core/env"],function(t,e,i){if(!t("../core/env").canvasSupported){var n,r="urn:schemas-microsoft-com:vml",a=window,o=a.document,s=!1;try{!o.namespaces.zrvml&&o.namespaces.add("zrvml",r),n=function(t){return o[q]("<zrvml:"+t+' class="zrvml">')}}catch(l){n=function(t){return o[q]("<"+t+' xmlns="'+r+'" class="zrvml">')}}var c=function(){if(!s){s=!0;var t=o.styleSheets;t[Me]<31?o.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}};i.exports={doc:o,initVML:c,createNode:n}}}),e("zrender/tool/transformPath",[je,"../core/PathProxy","../core/vector"],function(t){function e(t,e){var n,l,c,u,h,f,d=t.data,p=i.M,m=i.C,v=i.L,g=i.R,y=i.A,_=i.Q;for(c=0,u=0;c<d[Me];){switch(n=d[c++],u=c,l=0,n){case p:l=1;break;case v:l=1;break;case m:l=3;break;case _:l=2;break;case y:var x=e[4],b=e[5],w=o(e[0]*e[0]+e[1]*e[1]),M=o(e[2]*e[2]+e[3]*e[3]),S=s(-e[1]/M,e[0]/w);d[c++]+=x,d[c++]+=b,d[c++]*=w,d[c++]*=M,d[c++]+=S,d[c++]+=S,c+=2,u=c;break;case g:f[0]=d[c++],f[1]=d[c++],r(f,f,e),d[u++]=f[0],d[u++]=f[1],f[0]+=d[c++],f[1]+=d[c++],r(f,f,e),d[u++]=f[0],d[u++]=f[1]}for(h=0;l>h;h++){var f=a[h];f[0]=d[c++],f[1]=d[c++],r(f,f,e),d[u++]=f[0],d[u++]=f[1]}}}var i=t("../core/PathProxy").CMD,n=t("../core/vector"),r=n[h],a=[[],[],[]],o=Math.sqrt,s=Math.atan2;return e}),e("zrender/contain/path",[je,"../core/PathProxy","./line","./cubic","./quadratic","./arc","./util","../core/curve","./windingLine"],function(t){function e(t,e){return Math.abs(t-e)<g}function i(){var t=_[0];_[0]=_[1],_[1]=t}function n(t,e,n,r,a,o,s,l,c,u){if(u>e&&u>r&&u>o&&u>l||e>u&&r>u&&o>u&&l>u)return 0;var h=d.cubicRootAt(e,r,o,l,u,y);if(0===h)return 0;for(var f,p,m=0,v=-1,g=0;h>g;g++){var x=y[g],b=0===x||1===x?.5:1,w=d.cubicAt(t,n,a,s,x);c>w||(0>v&&(v=d.cubicExtrema(e,r,o,l,_),_[1]<_[0]&&v>1&&i(),f=d.cubicAt(e,r,o,l,_[0]),v>1&&(p=d.cubicAt(e,r,o,l,_[1]))),m+=2==v?x<_[0]?e>f?b:-b:x<_[1]?f>p?b:-b:p>l?b:-b:x<_[0]?e>f?b:-b:f>l?b:-b)}return m}function r(t,e,i,n,r,a,o,s){if(s>e&&s>n&&s>a||e>s&&n>s&&a>s)return 0;var l=d.quadraticRootAt(e,n,a,s,y);if(0===l)return 0;var c=d.quadraticExtremum(e,n,a);if(c>=0&&1>=c){for(var u=0,h=d.quadraticAt(e,n,a,c),f=0;l>f;f++){var p=0===y[f]||1===y[f]?.5:1,m=d.quadraticAt(t,i,r,y[f]);o>m||(u+=y[f]<c?e>h?p:-p:h>a?p:-p)}return u}var p=0===y[0]||1===y[0]?.5:1,m=d.quadraticAt(t,i,r,y[0]);return o>m?0:e>a?p:-p}function a(t,e,i,n,r,a,o,s){if(s-=e,s>i||-i>s)return 0;var l=Math.sqrt(i*i-s*s);y[0]=-l,y[1]=l;var c=Math.abs(n-r);if(1e-4>c)return 0;if(1e-4>c%v){n=0,r=v;var u=a?1:-1;return o>=y[0]+t&&o<=y[1]+t?u:0}if(a){var l=n;n=f(r),r=f(l)}else n=f(n),r=f(r);n>r&&(r+=v);for(var h=0,d=0;2>d;d++){var p=y[d];if(p+t>o){var m=Math.atan2(s,p),u=a?1:-1;0>m&&(m=v+m),(m>=n&&r>=m||m+v>=n&&r>=m+v)&&(m>Math.PI/2&&m<1.5*Math.PI&&(u=-u),h+=u)}}return h}function o(t,i,o,l,f){for(var d=0,v=0,g=0,y=0,_=0,x=0;x<t[Me];){var b=t[x++];switch(b===s.M&&x>1&&(o||(d+=p(v,g,y,_,l,f))),1==x&&(v=t[x],g=t[x+1],y=v,_=g),b){case s.M:y=t[x++],_=t[x++],v=y,g=_;break;case s.L:if(o){if(m(v,g,t[x],t[x+1],i,l,f))return!0}else d+=p(v,g,t[x],t[x+1],l,f)||0;v=t[x++],g=t[x++];break;case s.C:if(o){if(c.containStroke(v,g,t[x++],t[x++],t[x++],t[x++],t[x],t[x+1],i,l,f))return!0}else d+=n(v,g,t[x++],t[x++],t[x++],t[x++],t[x],t[x+1],l,f)||0;v=t[x++],g=t[x++];break;case s.Q:if(o){if(u.containStroke(v,g,t[x++],t[x++],t[x],t[x+1],i,l,f))return!0}else d+=r(v,g,t[x++],t[x++],t[x],t[x+1],l,f)||0;v=t[x++],g=t[x++];break;case s.A:var w=t[x++],M=t[x++],S=t[x++],T=t[x++],C=t[x++],k=t[x++],L=(t[x++],1-t[x++]),A=Math.cos(C)*S+w,P=Math.sin(C)*T+M;x>1?d+=p(v,g,A,P,l,f):(y=A,_=P);var z=(l-w)*T/S+w;if(o){if(h.containStroke(w,M,T,C,C+k,L,i,z,f))return!0}else d+=a(w,M,T,C,C+k,L,z,f);v=Math.cos(C+k)*S+w,g=Math.sin(C+k)*T+M;break;case s.R:y=v=t[x++],_=g=t[x++];var D=t[x++],I=t[x++],A=y+D,P=_+I;if(o){if(m(y,_,A,_,i,l,f)||m(A,_,A,P,i,l,f)||m(A,P,y,P,i,l,f)||m(y,P,y,_,i,l,f))return!0}else d+=p(A,_,A,P,l,f),d+=p(y,P,y,_,l,f);break;case s.Z:if(o){if(m(v,g,y,_,i,l,f))return!0}else d+=p(v,g,y,_,l,f);v=y,g=_}}return o||e(g,_)||(d+=p(v,g,y,_,l,f)||0),0!==d}var s=t("../core/PathProxy").CMD,l=t("./line"),c=t("./cubic"),u=t("./quadratic"),h=t("./arc"),f=t("./util").normalizeRadian,d=t("../core/curve"),p=t("./windingLine"),m=l.containStroke,v=2*Math.PI,g=1e-4,y=[-1,-1,-1],_=[-1,-1];return{contain:function(t,e,i){return o(t,0,!1,e,i)},containStroke:function(t,e,i,n){return o(t,e,!0,i,n)}}}),e("zrender/graphic/Pattern",[je],function(){var t=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};return t[He].getCanvasPattern=function(t){return this._canvasPattern||(this._canvasPattern=t.createPattern(this.image,this.repeat))},t}),e("echarts/scale/Ordinal",[je,Xe,"./Scale"],function(t){var e=t(Xe),i=t("./Scale"),n=i[He],r=i[Ce]({type:"ordinal",init:function(t,e){this._data=t,this._extent=e||[0,t[Me]-1]},parse:function(t){return typeof t===Ge?e[ae](this._data,t):Math.round(t)},contain:function(t){return t=this.parse(t),n[Z].call(this,t)&&null!=this._data[t]},normalize:function(t){return n[U].call(this,this.parse(t))},scale:function(t){return Math.round(n.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,i=e[0];i<=e[1];)t.push(i),i++;return t},getLabel:function(t){return this._data[t]},count:function(){return this._extent[1]-this._extent[0]+1},niceTicks:e.noop,niceExtent:e.noop});return r[De]=function(){return new r},r}),e("echarts/model/mixin/makeStyleMapper",[je,Xe],function(t){var e=t(Xe);return function(t){for(var i=0;i<t[Me];i++)t[i][1]||(t[i][1]=t[i][0]);return function(i){for(var n={},r=0;r<t[Me];r++){var a=t[r][1];if(!(i&&e[ae](i,a)>=0)){var o=this[d](a);null!=o&&(n[t[r][0]]=o)}}return n}}}),e("zrender/core/curve",[je,"./vector"],function(t){function e(t){return t>-x&&x>t}function i(t){return t>x||-x>t}function n(t,e,i,n,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*n+3*a*i)}function r(t,e,i,n,r){var a=1-r;return 3*(((e-t)*a+2*(i-e)*r)*a+(n-i)*r*r)}function a(t,i,n,r,a,o){var s=r+3*(i-n)-t,l=3*(n-2*i+t),c=3*(i-t),u=t-a,h=l*l-3*s*c,f=l*c-9*s*u,d=c*c-3*l*u,p=0;if(e(h)&&e(f))if(e(l))o[0]=0;else{var m=-c/l;m>=0&&1>=m&&(o[p++]=m)}else{var v=f*f-4*h*d;if(e(v)){var g=f/h,m=-l/s+g,x=-g/2;m>=0&&1>=m&&(o[p++]=m),x>=0&&1>=x&&(o[p++]=x)}else if(v>0){var b=_(v),S=h*l+1.5*s*(-f+b),T=h*l+1.5*s*(-f-b);S=0>S?-y(-S,M):y(S,M),T=0>T?-y(-T,M):y(T,M);var m=(-l-(S+T))/(3*s);m>=0&&1>=m&&(o[p++]=m)}else{var C=(2*h*l-3*s*f)/(2*_(h*h*h)),k=Math.acos(C)/3,L=_(h),A=Math.cos(k),m=(-l-2*L*A)/(3*s),x=(-l+L*(A+w*Math.sin(k)))/(3*s),P=(-l+L*(A-w*Math.sin(k)))/(3*s);m>=0&&1>=m&&(o[p++]=m),x>=0&&1>=x&&(o[p++]=x),P>=0&&1>=P&&(o[p++]=P)}}return p}function o(t,n,r,a,o){var s=6*r-12*n+6*t,l=9*n+3*a-3*t-9*r,c=3*n-3*t,u=0;if(e(l)){if(i(s)){var h=-c/s;h>=0&&1>=h&&(o[u++]=h)}}else{var f=s*s-4*l*c;if(e(f))o[0]=-s/(2*l);else if(f>0){var d=_(f),h=(-s+d)/(2*l),p=(-s-d)/(2*l);h>=0&&1>=h&&(o[u++]=h),p>=0&&1>=p&&(o[u++]=p)}}return u}function s(t,e,i,n,r,a){var o=(e-t)*r+t,s=(i-e)*r+e,l=(n-i)*r+i,c=(s-o)*r+o,u=(l-s)*r+s,h=(u-c)*r+c;a[0]=t,a[1]=o,a[2]=c,a[3]=h,a[4]=h,a[5]=u,a[6]=l,a[7]=n}function l(t,e,i,r,a,o,s,l,c,u,h){var f,d,p,m,v,y=.005,x=1/0;S[0]=c,S[1]=u;for(var w=0;1>w;w+=.05)T[0]=n(t,i,a,s,w),T[1]=n(e,r,o,l,w),m=g(S,T),x>m&&(f=w,x=m);x=1/0;for(var M=0;32>M&&!(b>y);M++)d=f-y,p=f+y,T[0]=n(t,i,a,s,d),T[1]=n(e,r,o,l,d),m=g(T,S),d>=0&&x>m?(f=d,x=m):(C[0]=n(t,i,a,s,p),C[1]=n(e,r,o,l,p),v=g(C,S),1>=p&&x>v?(f=p,x=v):y*=.5);return h&&(h[0]=n(t,i,a,s,f),h[1]=n(e,r,o,l,f)),_(x)}function c(t,e,i,n){var r=1-n;return r*(r*t+2*n*e)+n*n*i}function u(t,e,i,n){return 2*((1-n)*(e-t)+n*(i-e))}function h(t,n,r,a,o){var s=t-2*n+r,l=2*(n-t),c=t-a,u=0;if(e(s)){if(i(l)){var h=-c/l;h>=0&&1>=h&&(o[u++]=h)}}else{var f=l*l-4*s*c;if(e(f)){var h=-l/(2*s);h>=0&&1>=h&&(o[u++]=h)}else if(f>0){var d=_(f),h=(-l+d)/(2*s),p=(-l-d)/(2*s);h>=0&&1>=h&&(o[u++]=h),p>=0&&1>=p&&(o[u++]=p)}}return u}function f(t,e,i){var n=t+i-2*e;return 0===n?.5:(t-e)/n}function d(t,e,i,n,r){var a=(e-t)*n+t,o=(i-e)*n+e,s=(o-a)*n+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=i}function p(t,e,i,n,r,a,o,s,l){var u,h=.005,f=1/0;S[0]=o,S[1]=s;for(var d=0;1>d;d+=.05){T[0]=c(t,i,r,d),T[1]=c(e,n,a,d);var p=g(S,T);f>p&&(u=d,f=p)}f=1/0;for(var m=0;32>m&&!(b>h);m++){var v=u-h,y=u+h;T[0]=c(t,i,r,v),T[1]=c(e,n,a,v);var p=g(T,S);if(v>=0&&f>p)u=v,f=p;else{C[0]=c(t,i,r,y),C[1]=c(e,n,a,y);var x=g(C,S);1>=y&&f>x?(u=y,f=x):h*=.5}}return l&&(l[0]=c(t,i,r,u),l[1]=c(e,n,a,u)),_(f)}var m=t("./vector"),v=m[De],g=m.distSquare,y=Math.pow,_=Math.sqrt,x=1e-8,b=1e-4,w=_(3),M=1/3,S=v(),T=v(),C=v();return{cubicAt:n,cubicDerivativeAt:r,cubicRootAt:a,cubicExtrema:o,cubicSubdivide:s,cubicProjectPoint:l,quadraticAt:c,quadraticDerivativeAt:u,quadraticRootAt:h,quadraticExtremum:f,quadraticSubdivide:d,quadraticProjectPoint:p}}),e("zrender/core/bbox",[je,"./vector","./curve"],function(t){var e=t("./vector"),i=t("./curve"),n={},r=Math.min,a=Math.max,o=Math.sin,s=Math.cos,l=e[De](),c=e[De](),u=e[De](),h=2*Math.PI;n.fromPoints=function(t,e,i){if(0!==t[Me]){var n,o=t[0],s=o[0],l=o[0],c=o[1],u=o[1];for(n=1;n<t[Me];n++)o=t[n],s=r(s,o[0]),l=a(l,o[0]),c=r(c,o[1]),u=a(u,o[1]);e[0]=s,e[1]=c,i[0]=l,i[1]=u}},n.fromLine=function(t,e,i,n,o,s){o[0]=r(t,i),o[1]=r(e,n),s[0]=a(t,i),s[1]=a(e,n)};var f=[],d=[];return n.fromCubic=function(t,e,n,o,s,l,c,u,h,p){var m,v=i.cubicExtrema,g=i.cubicAt,y=v(t,n,s,c,f);for(h[0]=1/0,h[1]=1/0,p[0]=-1/0,p[1]=-1/0,m=0;y>m;m++){var _=g(t,n,s,c,f[m]);h[0]=r(_,h[0]),p[0]=a(_,p[0])}for(y=v(e,o,l,u,d),m=0;y>m;m++){var x=g(e,o,l,u,d[m]);h[1]=r(x,h[1]),p[1]=a(x,p[1])}h[0]=r(t,h[0]),p[0]=a(t,p[0]),h[0]=r(c,h[0]),p[0]=a(c,p[0]),h[1]=r(e,h[1]),p[1]=a(e,p[1]),h[1]=r(u,h[1]),p[1]=a(u,p[1])},n.fromQuadratic=function(t,e,n,o,s,l,c,u){var h=i.quadraticExtremum,f=i.quadraticAt,d=a(r(h(t,n,s),1),0),p=a(r(h(e,o,l),1),0),m=f(t,n,s,d),v=f(e,o,l,p);c[0]=r(t,s,m),c[1]=r(e,l,v),u[0]=a(t,s,m),u[1]=a(e,l,v)},n.fromArc=function(t,i,n,r,a,f,d,p,m){var v=e.min,g=e.max,y=Math.abs(a-f);if(1e-4>y%h&&y>1e-4)return p[0]=t-n,p[1]=i-r,m[0]=t+n,void(m[1]=i+r);if(l[0]=s(a)*n+t,l[1]=o(a)*r+i,c[0]=s(f)*n+t,c[1]=o(f)*r+i,v(p,l,c),g(m,l,c),a%=h,0>a&&(a+=h),f%=h,0>f&&(f+=h),a>f&&!d?f+=h:f>a&&d&&(a+=h),d){var _=f;f=a,a=_}for(var x=0;f>x;x+=Math.PI/2)x>a&&(u[0]=s(x)*n+t,u[1]=o(x)*r+i,v(p,u,p),g(m,u,m))},n}),e("zrender/config",[],function(){var t=1;typeof window!==g&&(t=Math.max(window.devicePixelRatio||1,1));var e={debugMode:0,devicePixelRatio:t};return e}),e("zrender/graphic/Style",[je],function(){function t(t,e,i){var n=e.x,r=e.x2,a=e.y,o=e.y2;e.global||(n=n*i.width+i.x,r=r*i.width+i.x,a=a*i[Ie]+i.y,o=o*i[Ie]+i.y);var s=t.createLinearGradient(n,a,r,o);return s}function e(t,e,i){var n=i.width,r=i[Ie],a=Math.min(n,r),o=e.x,s=e.y,l=e.r;e.global||(o=o*n+i.x,s=s*r+i.y,l*=a);var c=t.createRadialGradient(o,s,0,o,s,l);return c}var i=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],n=function(t){this.extendFrom(t)};n[He]={constructor:n,fill:"#000000",stroke:null,opacity:1,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,textFill:"#000",textStroke:null,textPosition:"inside",textBaseline:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textTransform:!1,textRotation:0,blend:null,bind:function(t,e,n){for(var r=this,a=n&&n.style,o=!a,s=0;s<i[Me];s++){var l=i[s],h=l[0];(o||r[h]!==a[h])&&(t[h]=r[h]||l[1])}if((o||r.fill!==a.fill)&&(t.fillStyle=r.fill),(o||r[c]!==a[c])&&(t.strokeStyle=r[c]),(o||r[X]!==a[X])&&(t.globalAlpha=null==r[X]?1:r[X]),(o||r.blend!==a.blend)&&(t.globalCompositeOperation=r.blend||"source-over"),this.hasStroke()){var f=r[u];t[u]=f/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this[c];return null!=t&&"none"!==t&&this[u]>0},extendFrom:function(t,e){if(t){var i=this;for(var n in t)!t.hasOwnProperty(n)||!e&&i.hasOwnProperty(n)||(i[n]=t[n])}},set:function(t,e){typeof t===Ge?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(i,n,r){for(var a="radial"===n.type?e:t,o=a(i,n,r),s=n.colorStops,l=0;l<s[Me];l++)o.addColorStop(s[l].offset,s[l].color);return o}};for(var r=n[He],a=0;a<i[Me];a++){var o=i[a];o[0]in r||(r[o[0]]=o[1])}return n.getGradient=r.getGradient,n}),e("zrender/Element",[je,"./core/guid","./mixin/Eventful","./mixin/Transformable","./mixin/Animatable","./core/util"],function(t){var e=t("./core/guid"),i=t("./mixin/Eventful"),r=t("./mixin/Transformable"),a=t("./mixin/Animatable"),o=t("./core/util"),s=function(t){r.call(this,t),i.call(this,t),a.call(this,t),this.id=t.id||e()};return s[He]={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this[n];i||(i=this[n]=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if(t===j||"scale"===t||"origin"===t){if(e){var i=this[t];i||(i=this[t]=[]),i[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this[Re]=!0,this.__zr&&this.__zr.refresh()},show:function(){this[Re]=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if(typeof t===Ge)this.attrKV(t,e);else if(o[Le](t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e[Me];i++)t[Fe].addAnimator(e[i]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e[Me];i++)t[Fe].removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},o.mixin(s,a),o.mixin(s,r),o.mixin(s,i),s}),e("echarts/coord/cartesian/Cartesian",[je,Xe],function(t){function e(t){return this._axes[t]}var i=t(Xe),n=function(t){this._axes={},this._dimList=[],this.name=t||""};return n[He]={constructor:n,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return i.map(this._dimList,e,this)},getAxesByScale:function(t){return t=t[qe](),i[oe](this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,y)},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var i=this._dimList,n=t instanceof Array?[]:{},r=0;r<i[Me];r++){var a=i[r],o=this._axes[a];n[a]=o[e](t[a])}return n}},n}),e("echarts/util/component",[je,Xe,"./clazz"],function(t){var e=t(Xe),i=t("./clazz"),n=i.parseClassType,r=0,a={},o="_";return a.getUID=function(t){return[t||"",r++,Math.random()].join(o)},a.enableSubTypeDefaulter=function(t){var e={};return t.registerSubTypeDefaulter=function(t,i){t=n(t),e[t.main]=i},t.determineSubType=function(i,r){var a=r.type;if(!a){var o=n(i).main;t.hasSubTypes(i)&&e[o]&&(a=e[o](r))}return a},t},a.enableTopologicalTravel=function(t,i){function n(t){var n={},o=[];return e.each(t,function(s){var l=r(n,s),c=l.originalDeps=i(s),u=a(c,t);l.entryCount=u[Me],0===l.entryCount&&o.push(s),e.each(u,function(t){e[ae](l.predecessor,t)<0&&l.predecessor.push(t);var i=r(n,t);e[ae](i.successor,t)<0&&i.successor.push(s)})}),{graph:n,noEntryList:o}}function r(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function a(t,i){var n=[];return e.each(t,function(t){e[ae](i,t)>=0&&n.push(t)}),n}t.topologicalTravel=function(t,i,r,a){function o(t){c[t].entryCount--,0===c[t].entryCount&&u.push(t)}function s(t){h[t]=!0,o(t)}if(t[Me]){var l=n(i),c=l.graph,u=l.noEntryList,h={};for(e.each(t,function(t){h[t]=!0});u[Me];){var f=u.pop(),d=c[f],p=!!h[f];p&&(r.call(a,f,d.originalDeps.slice()),delete h[f]),e.each(d.successor,p?s:o)}e.each(h,function(){throw new Error("Circle dependency may exists")})}}},a}),e("echarts/model/mixin/boxLayout",[je],function(){return{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get(Oe),width:this.get("width"),height:this.get(Ie)}}}}),e("zrender/core/guid",[],function(){var t=2311;return function(){return t++}}),e("zrender/mixin/Transformable",[je,"../core/matrix","../core/vector"],function(t){function e(t){return t>l||-l>t}var i=t("../core/matrix"),r=t("../core/vector"),a=i.identity,l=5e-5,c=function(t){t=t||{},t[j]||(this[j]=[0,0]),null==t[o]&&(this[o]=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},u=c[He];u[n]=null,u.needLocalTransform=function(){return e(this[o])||e(this[j][0])||e(this[j][1])||e(this.scale[0]-1)||e(this.scale[1]-1)},u.updateTransform=function(){var t=this[s],e=t&&t[n],r=this.needLocalTransform(),o=this[n];return r||e?(o=o||i[De](),r?this.getLocalTransform(o):a(o),e&&(r?i.mul(o,t[n],o):i.copy(o,t[n])),this[n]=o,this.invTransform=this.invTransform||i[De](),void i.invert(this.invTransform,o)):void(o&&a(o))},u.getLocalTransform=function(t){t=t||[],a(t);var e=this.origin,n=this.scale,r=this[o],s=this[j];return e&&(t[4]-=e[0],t[5]-=e[1]),i.scale(t,t,n),r&&i.rotate(t,t,r),e&&(t[4]+=e[0],t[5]+=e[1]),t[4]+=s[0],t[5]+=s[1],t},u.setTransform=function(t){var e=this[n],i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},u.restoreTransform=function(t){var e=(this[n],t.dpr||1);t.setTransform(e,0,0,e,0,0)};var f=[];return u.decomposeTransform=function(){if(this[n]){var t=this[s],r=this[n];t&&t[n]&&(i.mul(f,t.invTransform,r),r=f);var a=r[0]*r[0]+r[1]*r[1],l=r[2]*r[2]+r[3]*r[3],c=this[j],u=this.scale;e(a-1)&&(a=Math.sqrt(a)),e(l-1)&&(l=Math.sqrt(l)),r[0]<0&&(a=-a),r[3]<0&&(l=-l),c[0]=r[4],c[1]=r[5],u[0]=a,u[1]=l,this[o]=Math.atan2(-r[1]/l,r[0]/a)}},u.getGlobalScale=function(){var t=this[n];if(!t)return[1,1];var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]),i=Math.sqrt(t[2]*t[2]+t[3]*t[3]);return t[0]<0&&(e=-e),t[3]<0&&(i=-i),[e,i]},u.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&r[h](i,i,n),i},u.transformCoordToGlobal=function(t,e){var i=[t,e],a=this[n];return a&&r[h](i,i,a),i},c}),e("zrender/mixin/Animatable",[je,"../animation/Animator","../core/util","../core/log"],function(t){var e=t("../animation/Animator"),i=t("../core/util"),n=i.isString,r=i.isFunction,a=i[Le],o=t("../core/log"),s=function(){this.animators=[]};return s[He]={constructor:s,animate:function(t,n){var r,a=!1,s=this,l=this.__zr;if(t){var c=t.split("."),u=s;a="shape"===c[0];for(var h=0,f=c[Me];f>h;h++)u&&(u=u[c[h]]);u&&(r=u)}else r=s;if(!r)return void o('Property "'+t+'" is not existed in element '+s.id);var d=s.animators,p=new e(r,n);return p.during(function(){s.dirty(a)}).done(function(){d[be](i[ae](d,p),1)}),d.push(p),l&&l[Fe].addAnimator(p),p},stopAnimation:function(t){for(var e=this.animators,i=e[Me],n=0;i>n;n++)e[n].stop(t);return e[Me]=0,this},animateTo:function(t,e,i,a,o){function s(){c--,c||o&&o()}n(i)?(o=a,a=i,i=0):r(a)?(o=a,a="linear",i=0):r(i)?(o=i,i=0):r(e)?(o=e,e=500):e||(e=500),this.stopAnimation(),this._animateToShallow("",this,t,e,i,a,o);var l=this.animators.slice(),c=l[Me];c||o&&o();for(var u=0;u<l[Me];u++)l[u].done(s).start(a)},_animateToShallow:function(t,e,n,r,o){var s={},l=0;for(var c in n)if(null!=e[c])a(n[c])&&!i.isArrayLike(n[c])?this._animateToShallow(t?t+"."+c:c,e[c],n[c],r,o):(s[c]=n[c],l++);else if(null!=n[c])if(t){var u={};u[t]={},u[t][c]=n[c],this.attr(u)}else this.attr(c,n[c]);return l>0&&this.animate(t,!1).when(null==r?500:r,s).delay(o||0),this}},s}),e("echarts/coord/Axis",[je,"../util/number",Xe],function(t){function e(t,e){var i=t[1]-t[0],n=e,r=i/n/2;t[0]+=r,t[1]-=r}var i=t("../util/number"),n=i.linearMap,r=t(Xe),a=[0,1],o=function(t,e,i){this.dim=t,this.scale=e,this._extent=i||[0,0],this.inverse=!1,this.onBand=!1};return o[He]={constructor:o,contain:function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&n>=t},containData:function(t){return this[Z](this[y](t))},getExtent:function(){var t=this._extent.slice();return t},getPixelPrecision:function(t){return i.getPixelPrecision(t||this.scale[W](),this._extent)},setExtent:function(t,e){var i=this._extent;i[0]=t,i[1]=e},dataToCoord:function(t,i){var r=this._extent,o=this.scale;return t=o[U](t),this.onBand&&o.type===F&&(r=r.slice(),e(r,o.count())),n(t,a,r,i)},coordToData:function(t,i){var r=this._extent,o=this.scale;this.onBand&&o.type===F&&(r=r.slice(),e(r,o.count()));var s=n(t,r,a,i);return this.scale.scale(s)},getTicksCoords:function(t){if(this.onBand&&!t){for(var e=this.getBands(),i=[],n=0;n<e[Me];n++)i.push(e[n][0]);return e[n-1]&&i.push(e[n-1][1]),i}return r.map(this.scale.getTicks(),this[y],this)},getLabelsCoords:function(){return r.map(this.scale.getTicks(),this[y],this)},getBands:function(){for(var t=this[W](),e=[],i=this.scale.count(),n=t[0],r=t[1],a=r-n,o=0;i>o;o++)e.push([a*o/i+n,a*(o+1)/i+n]);return e},getBandWidth:function(){var t=this._extent,e=this.scale[W](),i=e[1]-e[0]+(this.onBand?1:0);0===i&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i}},o}),e("echarts/coord/cartesian/axisLabelInterval",[je,Xe,"../axisHelper"],function(t){var e=t(Xe),i=t("../axisHelper");return function(t){var n=t.model,r=n[Be]("axisLabel"),a=r.get("interval");return t.type!==G||"auto"!==a?"auto"===a?0:a:i.getAxisLabelInterval(e.map(t.scale.getTicks(),t[y],t),n.getFormattedLabels(),r[Be](ee)[J](),t.isHorizontal())}}),e("zrender/animation/Animator",[je,"./Clip","../tool/color","../core/util"],function(t){function e(t,e){return t[e]}function i(t,e,i){t[e]=i}function n(t,e,i){return(e-t)*i+t}function r(t,e,i){return i>.5?e:t}function a(t,e,i,r,a){var o=t[Me];if(1==a)for(var s=0;o>s;s++)r[s]=n(t[s],e[s],i);else for(var l=t[0][Me],s=0;o>s;s++)for(var c=0;l>c;c++)r[s][c]=n(t[s][c],e[s][c],i)}function o(t,e,i){var n=t[Me],r=e[Me];if(n!==r){var a=n>r;if(a)t[Me]=r;else for(var o=n;r>o;o++)t.push(1===i?e[o]:g.call(e[o]))}for(var s=t[0]&&t[0][Me],o=0;o<t[Me];o++)if(1===i)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;s>l;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function s(t,e,i){if(t===e)return!0;var n=t[Me];if(n!==e[Me])return!1;if(1===i){for(var r=0;n>r;r++)if(t[r]!==e[r])return!1}else for(var a=t[0][Me],r=0;n>r;r++)for(var o=0;a>o;o++)if(t[r][o]!==e[r][o])return!1;return!0}function l(t,e,i,n,r,a,o,s,l){var u=t[Me];if(1==l)for(var h=0;u>h;h++)s[h]=c(t[h],e[h],i[h],n[h],r,a,o);else for(var f=t[0][Me],h=0;u>h;h++)for(var d=0;f>d;d++)s[h][d]=c(t[h][d],e[h][d],i[h][d],n[h][d],r,a,o)}function c(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function u(t){if(v(t)){var e=t[Me];if(v(t[0])){for(var i=[],n=0;e>n;n++)i.push(g.call(t[n]));return i}return g.call(t)}return t}function h(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function f(t,e,i,u,f){var m=t._getter,g=t._setter,y="spline"===e,_=u[Me];if(_){var x,b=u[0].value,w=v(b),M=!1,S=!1,T=w&&v(b[0])?2:1;u.sort(function(t,e){return t.time-e.time}),x=u[_-1].time;for(var C=[],k=[],L=u[0].value,A=!0,P=0;_>P;P++){C.push(u[P].time/x);var z=u[P].value;if(w&&s(z,L,T)||!w&&z===L||(A=!1),L=z,typeof z==Ge){var D=p.parse(z);D?(z=D,M=!0):S=!0}k.push(z)}if(!A){for(var I=k[_-1],P=0;_-1>P;P++)w?o(k[P],I,T):!isNaN(k[P])||isNaN(I)||S||M||(k[P]=I);w&&o(m(t._target,f),I,T);var O,R,E,N,B,F,V=0,G=0;if(M)var H=[0,0,0,0];var q=function(t,e){var i;if(0>e)i=0;else if(G>e){for(O=Math.min(V+1,_-1),i=O;i>=0&&!(C[i]<=e);i--);i=Math.min(i,_-2)}else{for(i=V;_>i&&!(C[i]>e);i++);i=Math.min(i-1,_-2)}V=i,G=e;var o=C[i+1]-C[i];if(0!==o)if(R=(e-C[i])/o,y)if(N=k[i],E=k[0===i?i:i-1],B=k[i>_-2?_-1:i+1],F=k[i>_-3?_-1:i+2],w)l(E,N,B,F,R,R*R,R*R*R,m(t,f),T);else{var s;if(M)s=l(E,N,B,F,R,R*R,R*R*R,H,1),s=h(H);else{if(S)return r(N,B,R);s=c(E,N,B,F,R,R*R,R*R*R)}g(t,f,s)}else if(w)a(k[i],k[i+1],R,m(t,f),T);else{var s;if(M)a(k[i],k[i+1],R,H,1),s=h(H);else{if(S)return r(k[i],k[i+1],R);s=n(k[i],k[i+1],R)}g(t,f,s)}},W=new d({target:t._target,life:x,loop:t._loop,delay:t._delay,onframe:q,ondestroy:i});return e&&"spline"!==e&&(W.easing=e),W}}}var d=t("./Clip"),p=t("../tool/color"),m=t("../core/util"),v=m.isArrayLike,g=Array[He].slice,y=function(t,n,r,a){this._tracks={},this._target=t,this._loop=n||!1,this._getter=r||e,this._setter=a||i,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};return y[He]={when:function(t,e){var i=this._tracks;for(var n in e){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:u(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},_doneCallback:function(){this._tracks={},this._clipList[Me]=0;for(var t=this._doneList,e=t[Me],i=0;e>i;i++)t[i].call(this)},start:function(t){var e,i=this,n=0,r=function(){n--,n||i._doneCallback()};for(var a in this._tracks){var o=f(this,t,r,this._tracks[a],a);o&&(this._clipList.push(o),n++,this[Fe]&&this[Fe].addClip(o),e=o)}if(e){var s=e.onframe;e.onframe=function(t,e){s(t,e);for(var n=0;n<i._onframeList[Me];n++)i._onframeList[n](t,e)}}return n||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this[Fe],n=0;n<e[Me];n++){var r=e[n];t&&r.onframe(this._target,1),i&&i.removeClip(r)}e[Me]=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}},y}),e("zrender/core/log",[je,"../config"],function(t){var e=t("../config");return function(){if(0!==e.debugMode)if(1==e.debugMode)for(var t in arguments)throw new Error(arguments[t]);else if(e.debugMode>1)for(var t in arguments)console.log(arguments[t])}}),e("echarts/coord/cartesian/AxisModel",[je,"../../model/Component",Xe,"../axisModelCreator","../axisModelCommonMixin"],function(t){function e(t,e){return e.type||(e.data?G:"value")}var i=t("../../model/Component"),n=t(Xe),r=t("../axisModelCreator"),a=i[Ce]({type:"cartesian2dAxis",axis:null,init:function(){a.superApply(this,"init",arguments),this._resetRange()},mergeOption:function(){a.superApply(this,m,arguments),this._resetRange()},restoreData:function(){a.superApply(this,"restoreData",arguments),this._resetRange()},setRange:function(t,e){this[v].rangeStart=t,this[v].rangeEnd=e},getMin:function(){var t=this[v];return null!=t.rangeStart?t.rangeStart:t.min},getMax:function(){var t=this[v];return null!=t.rangeEnd?t.rangeEnd:t.max},getNeedCrossZero:function(){var t=this[v];return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},findGridModel:function(){return this[f].queryComponents({mainType:"grid",index:this.get("gridIndex"),id:this.get("gridId")})[0]},_resetRange:function(){this[v].rangeStart=this[v].rangeEnd=null}});n.merge(a[He],t("../axisModelCommonMixin"));var o={offset:0};return r("x",a,e,o),r("y",a,e,o),a}),e("zrender/animation/Clip",[je,"./easing"],function(t){function e(t){this._target=t[ge],this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart}var i=t("./easing");return e[He]={constructor:e,step:function(t){this._initialized||(this._startTime=t+this._delay,this._initialized=!0);var e=(t-this._startTime)/this._life;if(!(0>e)){e=Math.min(e,1);var n=this.easing,r=typeof n==Ge?i[n]:n,a=typeof r===le?r(e):e;
return this.fire("frame",a),1==e?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime)%this._life;this._startTime=t-e+this.gap,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)}},e}),e("echarts/coord/axisModelCreator",[je,"./axisDefault",Xe,"../model/Component","../util/layout"],function(t){var e=t("./axisDefault"),i=t(Xe),n=t("../model/Component"),r=t("../util/layout"),a=["value",G,"time","log"];return function(t,o,s,l){i.each(a,function(n){o[Ce]({type:t+"Axis."+n,mergeDefaultAndTheme:function(e,a){var o=this.layoutMode,l=o?r.getLayoutParams(e):{},c=a.getTheme();i.merge(e,c.get(n+"Axis")),i.merge(e,this.getDefaultOption()),e.type=s(t,e),o&&r.mergeLayoutParam(e,l,o)},defaultOption:i.mergeAll([{},e[n+"Axis"],l],!0)})}),n.registerSubTypeDefaulter(t+"Axis",i.curry(s,t))}}),e("echarts/coord/axisModelCommonMixin",[je,Xe,"./axisHelper"],function(t){function e(t){return r[Le](t)&&null!=t.value?t.value:t}function i(){return this.get("type")===G&&r.map(this.get("data"),e)}function n(){return a.getFormattedLabels(this.axis,this.get("axisLabel.formatter"))}var r=t(Xe),a=t("./axisHelper");return{getFormattedLabels:n,getCategories:i}}),e("zrender/animation/easing",[],function(){var t={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)))},elasticOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/n)+1)},elasticInOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?-.5*i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n):i*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(e){return 1-t.bounceOut(1-e)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(e){return.5>e?.5*t.bounceIn(2*e):.5*t.bounceOut(2*e-1)+.5}};return t}),e("echarts/coord/axisDefault",[je,Xe],function(t){var e=t(Xe),i={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisLine:{show:!0,onZero:!0,lineStyle:{color:"#333",width:1,type:"solid"}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,margin:8,textStyle:{fontSize:12}},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},n=e.merge({boundaryGap:!0,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},i),r=e.merge({boundaryGap:[0,0],splitNumber:5},i),a=e[Se]({scale:!0,min:"dataMin",max:"dataMax"},r),o=e[Se]({logBase:10},r);return o.scale=!0,{categoryAxis:n,valueAxis:r,timeAxis:a,logAxis:o}}),e("zrender/contain/line",[],function(){return{containStroke:function(t,e,i,n,r,a,o){if(0===r)return!1;var s=r,l=0,c=t;if(o>e+s&&o>n+s||e-s>o&&n-s>o||a>t+s&&a>i+s||t-s>a&&i-s>a)return!1;if(t===i)return Math.abs(a-t)<=s/2;l=(e-n)/(t-i),c=(t*n-i*e)/(t-i);var u=l*a-o+c,h=u*u/(l*l+1);return s/2*s/2>=h}}}),e("zrender/contain/quadratic",[je,"../core/curve"],function(t){var e=t("../core/curve");return{containStroke:function(t,i,n,r,a,o,s,l,c){if(0===s)return!1;var u=s;if(c>i+u&&c>r+u&&c>o+u||i-u>c&&r-u>c&&o-u>c||l>t+u&&l>n+u&&l>a+u||t-u>l&&n-u>l&&a-u>l)return!1;var h=e.quadraticProjectPoint(t,i,n,r,a,o,l,c,null);return u/2>=h}}}),e("zrender/contain/cubic",[je,"../core/curve"],function(t){var e=t("../core/curve");return{containStroke:function(t,i,n,r,a,o,s,l,c,u,h){if(0===c)return!1;var f=c;if(h>i+f&&h>r+f&&h>o+f&&h>l+f||i-f>h&&r-f>h&&o-f>h&&l-f>h||u>t+f&&u>n+f&&u>a+f&&u>s+f||t-f>u&&n-f>u&&a-f>u&&s-f>u)return!1;var d=e.cubicProjectPoint(t,i,n,r,a,o,s,l,u,h,null);return f/2>=d}}}),e("zrender/contain/arc",[je,"./util"],function(t){var e=t("./util").normalizeRadian,i=2*Math.PI;return{containStroke:function(t,n,r,a,o,s,l,c,u){if(0===l)return!1;var h=l;c-=t,u-=n;var f=Math.sqrt(c*c+u*u);if(f-h>r||r>f+h)return!1;if(Math.abs(a-o)%i<1e-4)return!0;if(s){var d=a;a=e(o),o=e(d)}else a=e(a),o=e(o);a>o&&(o+=i);var p=Math.atan2(u,c);return 0>p&&(p+=i),p>=a&&o>=p||p+i>=a&&o>=p+i}}}),e("zrender/contain/util",[je],function(){var t=2*Math.PI;return{normalizeRadian:function(e){return e%=t,0>e&&(e+=t),e}}}),e("zrender/contain/windingLine",[],function(){return function(t,e,i,n,r,a){if(a>e&&a>n||e>a&&n>a)return 0;if(n===e)return 0;var o=e>n?1:-1,s=(a-e)/(n-e);(1===s||0===s)&&(o=e>n?.5:-.5);var l=s*(i-t)+t;return l>r?o:0}}),e("zrender/core/LRU",[je],function(){var t=function(){this.head=null,this.tail=null,this._len=0},e=t[He];e.insert=function(t){var e=new i(t);return this.insertEntry(e),e},e.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,this.tail=t):this.head=this.tail=t,this._len++},e[ke]=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},e.len=function(){return this._len};var i=function(t){this.value=t,this.next,this.prev},n=function(e){this._list=new t,this._map={},this._maxSize=e||10},r=n[He];return r.put=function(t,e){var i=this._list,n=this._map;if(null==n[t]){var r=i.len();if(r>=this._maxSize&&r>0){var a=i.head;i[ke](a),delete n[a.key]}var o=i.insert(e);o.key=t,n[t]=o}},r.get=function(t){var e=this._map[t],i=this._list;return null!=e?(e!==i.tail&&(i[ke](e),i.insertEntry(e)),e.value):void 0},r.clear=function(){this._list.clear(),this._map={}},n}),e("echarts/chart/helper/createListFromArray",[je,"../../data/List","../../data/helper/completeDimensions",Xe,"../../util/model","../../CoordinateSystem"],function(t){function e(t){for(var e=0;e<t[Me]&&null==t[e];)e++;return t[e]}function i(t){var i=e(t);return null!=i&&!u[ce](d(i))}function n(t,e,n){t=t||[];var r=e.get(ie),o=m[r],v=f.get(r),g=o&&o(t,e,n),y=g&&g[N];y||(y=v&&v[N]||["x","y"],y=c(y,t,y[H](["value"])));var _=g?g.categoryIndex:-1,x=new l(y,e),b=s(g,t),w={},M=_>=0&&i(t)?function(t,e,i,n){return h.isDataItemOption(t)&&(x.hasItemOption=!0),n===_?i:p(d(t),y[n])}:function(t,e,i,n){var r=d(t),a=p(r&&r[n],y[n]);h.isDataItemOption(t)&&(x.hasItemOption=!0);var o=g&&g.categoryAxesModels;return o&&o[e]&&typeof a===Ge&&(w[e]=w[e]||o[e].getCategories(),a=u[ae](w[e],a),0>a&&!isNaN(a)&&(a=+a)),a};return x.hasItemOption=!1,x[a](t,b,M),x}function r(t){return t!==G&&"time"!==t}function o(t){return t===G?F:"time"===t?"time":"float"}function s(t,e){var i,n=[],r=t&&t[N][t.categoryIndex];if(r&&(i=t.categoryAxesModels[r.name]),i){var a=i.getCategories();if(a){var o=e[Me];if(u[ce](e[0])&&e[0][Me]>1){n=[];for(var s=0;o>s;s++)n[s]=a[e[s][t.categoryIndex||0]]}else n=a.slice(0)}}return n}var l=t("../../data/List"),c=t("../../data/helper/completeDimensions"),u=t(Xe),h=t("../../util/model"),f=t("../../CoordinateSystem"),d=h.getDataItemValue,p=h.converDataValue,m={cartesian2d:function(t,e,i){var n=u.map(["xAxis","yAxis"],function(t){return i.queryComponents({mainType:t,index:e.get(t+"Index"),id:e.get(t+"Id")})[0]}),a=n[0],s=n[1],l=a.get("type"),h=s.get("type"),f=[{name:"x",type:o(l),stackable:r(l)},{name:"y",type:o(h),stackable:r(h)}],d=l===G,p=h===G;c(f,t,["x","y","z"]);var m={};return d&&(m.x=a),p&&(m.y=s),{dimensions:f,categoryIndex:d?0:p?1:-1,categoryAxesModels:m}},polar:function(t,e,i){var n=i.queryComponents({mainType:"polar",index:e.get("polarIndex"),id:e.get("polarId")})[0],a=n.findAxisModel("angleAxis"),s=n.findAxisModel("radiusAxis"),l=s.get("type"),u=a.get("type"),h=[{name:"radius",type:o(l),stackable:r(l)},{name:"angle",type:o(u),stackable:r(u)}],f=u===G,d=l===G;c(h,t,["radius","angle","value"]);var p={};return d&&(p.radius=s),f&&(p.angle=a),{dimensions:h,categoryIndex:f?1:d?0:-1,categoryAxesModels:p}},geo:function(t){return{dimensions:c([{name:"lng"},{name:"lat"}],t,["lng","lat","value"])}}};return n}),e("zrender/graphic/helper/poly",[je,"./smoothSpline","./smoothBezier"],function(t){var e=t("./smoothSpline"),i=t("./smoothBezier");return{buildPath:function(t,n,r){var a=n.points,o=n.smooth;if(a&&a[Me]>=2){if(o&&"spline"!==o){var s=i(a,o,r,n.smoothConstraint);t.moveTo(a[0][0],a[0][1]);for(var l=a[Me],c=0;(r?l:l-1)>c;c++){var u=s[2*c],h=s[2*c+1],f=a[(c+1)%l];t.bezierCurveTo(u[0],u[1],h[0],h[1],f[0],f[1])}}else{"spline"===o&&(a=e(a,r)),t.moveTo(a[0][0],a[0][1]);for(var c=1,d=a[Me];d>c;c++)t.lineTo(a[c][0],a[c][1])}r&&t.closePath()}}}}),e("echarts/data/helper/completeDimensions",[je,Xe],function(t){function e(t,e,a,o){if(!e)return t;var s=i(e[0]),l=n[ce](s)&&s[Me]||1;a=a||[],o=o||"extra";for(var c=0;l>c;c++)if(!t[c]){var u=a[c]||o+(c-a[Me]);t[c]=r(e,c)?{type:"ordinal",name:u}:u}return t}function i(t){return n[ce](t)?t:n[Le](t)?t.value:t}var n=t(Xe),r=e.guessOrdinal=function(t,e){for(var r=0,a=t[Me];a>r;r++){var o=i(t[r]);if(!n[ce](o))return!1;var o=o[e];if(null!=o&&isFinite(o))return!1;if(n.isString(o)&&"-"!==o)return!0}return!1};return e}),e("echarts/data/DataDiffer",[je],function(){function t(t){return t}function e(e,i,n,r){this._old=e,this._new=i,this._oldKeyGetter=n||t,this._newKeyGetter=r||t}function i(t,e,i,n){for(var r=0;r<t[Me];r++){var a=n(t[r],r),o=e[a];null==o?(i.push(a),e[a]=r):(o[Me]||(e[a]=o=[o]),o.push(r))}}return e[He]={constructor:e,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,n=this._new,r=this._oldKeyGetter,a=this._newKeyGetter,o={},s={},l=[],c=[];for(i(e,o,l,r),i(n,s,c,a),t=0;t<e[Me];t++){var u=l[t],h=s[u];if(null!=h){var f=h[Me];f?(1===f&&(s[u]=null),h=h.unshift()):s[u]=null,this._update&&this._update(h,t)}else this._remove&&this._remove(t)}for(var t=0;t<c[Me];t++){var u=c[t];if(s.hasOwnProperty(u)){var h=s[u];if(null==h)continue;if(h[Me])for(var d=0,f=h[Me];f>d;d++)this._add&&this._add(h[d]);else this._add&&this._add(h)}}}},e}),e("zrender/graphic/helper/smoothSpline",[je,"../../core/vector"],function(t){function e(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}var i=t("../../core/vector");return function(t,n){for(var r=t[Me],a=[],o=0,s=1;r>s;s++)o+=i.distance(t[s-1],t[s]);var l=o/2;l=r>l?r:l;for(var s=0;l>s;s++){var c,u,h,f=s/(l-1)*(n?r:r-1),d=Math.floor(f),p=f-d,m=t[d%r];n?(c=t[(d-1+r)%r],u=t[(d+1)%r],h=t[(d+2)%r]):(c=t[0===d?d:d-1],u=t[d>r-2?r-1:d+1],h=t[d>r-3?r-1:d+2]);var v=p*p,g=p*v;a.push([e(c[0],m[0],u[0],h[0],p,v,g),e(c[1],m[1],u[1],h[1],p,v,g)])}return a}}),e("zrender/graphic/helper/smoothBezier",[je,"../../core/vector"],function(t){var e=t("../../core/vector"),i=e.min,n=e.max,r=e.scale,a=e.distance,o=e.add;return function(t,s,l,c){var u,h,f,d,p=[],m=[],v=[],g=[];if(c){f=[1/0,1/0],d=[-1/0,-1/0];for(var y=0,_=t[Me];_>y;y++)i(f,f,t[y]),n(d,d,t[y]);i(f,f,c[0]),n(d,d,c[1])}for(var y=0,_=t[Me];_>y;y++){var x=t[y];if(l)u=t[y?y-1:_-1],h=t[(y+1)%_];else{if(0===y||y===_-1){p.push(e.clone(t[y]));continue}u=t[y-1],h=t[y+1]}e.sub(m,h,u),r(m,m,s);var b=a(x,u),w=a(x,h),M=b+w;0!==M&&(b/=M,w/=M),r(v,m,-b),r(g,m,w);var S=o([],x,v),T=o([],x,g);c&&(n(S,S,f),i(S,S,d),n(T,T,f),i(T,T,d)),p.push(S),p.push(T)}return l&&p.push(p.shift()),p}}),e("echarts/chart/bar/barItemStyle",[je,"../../model/mixin/makeStyleMapper"],function(t){var e=t("../../model/mixin/makeStyleMapper")([["fill","color"],[c,"borderColor"],[u,"borderWidth"],[c,"barBorderColor"],[u,"barBorderWidth"],[X],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);return{getBarItemStyle:function(t){var i=e.call(this,t);if(this.getBorderLineDash){var n=this.getBorderLineDash();n&&(i.lineDash=n)}return i}}}),e("zrender/graphic/helper/roundRect",[je],function(){return{buildPath:function(t,e){var i,n,r,a,o=e.x,s=e.y,l=e.width,c=e[Ie],u=e.r;0>l&&(o+=l,l=-l),0>c&&(s+=c,c=-c),typeof u===se?i=n=r=a=u:u instanceof Array?1===u[Me]?i=n=r=a=u[0]:2===u[Me]?(i=r=u[0],n=a=u[1]):3===u[Me]?(i=u[0],n=a=u[1],r=u[2]):(i=u[0],n=u[1],r=u[2],a=u[3]):i=n=r=a=0;var h;i+n>l&&(h=i+n,i*=l/h,n*=l/h),r+a>l&&(h=r+a,r*=l/h,a*=l/h),n+r>c&&(h=n+r,n*=c/h,r*=c/h),i+a>c&&(h=i+a,i*=c/h,a*=c/h),t.moveTo(o+i,s),t.lineTo(o+l-n,s),0!==n&&t.quadraticCurveTo(o+l,s,o+l,s+n),t.lineTo(o+l,s+c-r),0!==r&&t.quadraticCurveTo(o+l,s+c,o+l-r,s+c),t.lineTo(o+a,s+c),0!==a&&t.quadraticCurveTo(o,s+c,o,s+c-a),t.lineTo(o,s+i),0!==i&&t.quadraticCurveTo(o,s,o+i,s)}}}),e("zrender/zrender",[je,"./core/guid","./core/env","./Handler","./Storage","./animation/Animation","./dom/HandlerProxy","./Painter"],function(t){function e(t){delete u[t]}var i=t("./core/guid"),n=t("./core/env"),r=t("./Handler"),a=t("./Storage"),o=t("./animation/Animation"),s=t("./dom/HandlerProxy"),l=!n.canvasSupported,c={canvas:t("./Painter")},u={},h={};h.version="3.1.3",h.init=function(t,e){var n=new f(i(),t,e);return u[n.id]=n,n},h.dispose=function(t){if(t)t.dispose();else{for(var e in u)u[e].dispose();u={}}return h},h.getInstance=function(t){return u[t]},h.registerPainter=function(t,e){c[t]=e};var f=function(t,e,i){i=i||{},this.dom=e,this.id=t;var u=this,h=new a,f=i.renderer;if(l){if(!c.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");f="vml"}else f&&c[f]||(f="canvas");var d=new c[f](e,h,i);this.storage=h,this.painter=d;var p=n.node?null:new s(d.getViewportRoot());this.handler=new r(h,d,p),this[Fe]=new o({stage:{update:function(){u._needsRefresh&&u.refreshImmediately(),u._needsRefreshHover&&u.refreshHoverImmediately()}}}),this[Fe].start(),this._needsRefresh;var m=h.delFromMap,v=h.addToMap;h.delFromMap=function(t){var e=h.get(t);m.call(h,t),e&&e.removeSelfFromZr(u)},h.addToMap=function(t){v.call(h,t),t.addSelfToZr(u)}};return f[He]={constructor:f,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer(t,e),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},addHover:function(t,e){this.painter.addHover&&(this.painter.addHover(t,e),this.refreshHover())},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(){this.painter[Ve](),this.handler[Ve]()},clearAnimation:function(){this[Fe].clear()},getWidth:function(){return this.painter[Ne]()},getHeight:function(){return this.painter[Ee]()},pathToImage:function(t,e,n){var r=i();return this.painter.pathToImage(r,t,e,n)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this[Fe].stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this[Fe]=this.storage=this.painter=this.handler=null,e(this.id)}},h}),e("zrender/Handler",[je,"./core/util","./mixin/Draggable","./mixin/Eventful"],function(t){function e(t,e,i){return{type:t,event:i,target:e,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta}}function i(){}function n(t,e,i){if(t[t.rectHover?"rectContain":Z](e,i)){for(var n=t;n;){if(n[xe]||n.clipPath&&!n.clipPath[Z](e,i))return!1;n=n[s]}return!0}return!1}var r=t("./core/util"),a=t("./mixin/Draggable"),o=t("./mixin/Eventful");i[He].dispose=function(){};var l=["click","dblclick","mousewheel",ye,"mouseup","mousedown","mousemove"],c=function(t,e,n){o.call(this),this.storage=t,this.painter=e,n=n||new i,this.proxy=n,n.handler=this,this._hovered,this._lastTouchMoment,this._lastX,this._lastY,a.call(this),r.each(l,function(t){n.on&&n.on(t,this[t],this)},this)};return c[He]={constructor:c,mousemove:function(t){var e=t.zrX,i=t.zrY,n=this.findHover(e,i,null),r=this._hovered,a=this.proxy;this._hovered=n,a.setCursor&&a.setCursor(n?n.cursor:"default"),r&&n!==r&&r.__zr&&this.dispatchToElement(r,ye,t),this.dispatchToElement(n,"mousemove",t),n&&n!==r&&this.dispatchToElement(n,_e,t)},mouseout:function(t){this.dispatchToElement(this._hovered,ye,t),this.trigger("globalout",{event:t})},resize:function(){this._hovered=null},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,i,n){for(var r="on"+i,a=e(i,t,n),o=t;o&&(o[r]&&(a.cancelBubble=o[r].call(o,a)),o.trigger(i,a),o=o[s],!a.cancelBubble););a.cancelBubble||(this.trigger(i,a),this.painter&&this.painter.eachOtherLayer(function(t){typeof t[r]==le&&t[r].call(t,a),t.trigger&&t.trigger(i,a)}))},findHover:function(t,e,i){for(var r=this.storage.getDisplayList(),a=r[Me]-1;a>=0;a--)if(!r[a][xe]&&r[a]!==i&&!r[a][Re]&&n(r[a],t,e))return r[a]}},r.each(["click","mousedown","mouseup","mousewheel","dblclick"],function(t){c[He][t]=function(e){var i=this.findHover(e.zrX,e.zrY,null);if("mousedown"===t)this._downel=i,this._upel=i;else if("mosueup"===t)this._upel=i;else if("click"===t&&this._downel!==this._upel)return;this.dispatchToElement(i,t,e)}}),r.mixin(c,o),r.mixin(c,a),c}),e("zrender/Storage",[je,"./core/util","./core/env","./container/Group","./core/timsort"],function(t){function e(t,e){return t[he]===e[he]?t.z===e.z?t.z2-e.z2:t.z-e.z:t[he]-e[he]}var n=t("./core/util"),r=t("./core/env"),a=t("./container/Group"),o=t("./core/timsort"),l=function(){this._elements={},this._roots=[],this._displayList=[],this._displayListLen=0};return l[He]={constructor:l,traverse:function(t,e){for(var i=0;i<this._roots[Me];i++)this._roots[i][de](t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var i=this._roots,n=this._displayList,a=0,s=i[Me];s>a;a++)this._updateAndAddDisplayable(i[a],null,t);n[Me]=this._displayListLen,r.canvasSupported&&o(n,e)},_updateAndAddDisplayable:function(t,e,n){if(!t[Re]||n){t.beforeUpdate(),t[i]&&t[ze](),t.afterUpdate();var r=t.clipPath;if(r&&(r[s]=t,r.updateTransform(),e?(e=e.slice(),e.push(r)):e=[r]),t.isGroup){for(var a=t._children,o=0;o<a[Me];o++){var l=a[o];t[i]&&(l[i]=!0),this._updateAndAddDisplayable(l,e,n)}t[i]=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){this._elements[t.id]||(t instanceof a&&t.addChildrenToStorage(this),this.addToMap(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots[Me];e++){var i=this._roots[e];i instanceof a&&i.delChildrenFromStorage(this)}return this._elements={},this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,r=t[Me];r>e;e++)this.delRoot(t[e]);else{var o;o=typeof t==Ge?this._elements[t]:t;var s=n[ae](this._roots,o);s>=0&&(this.delFromMap(o.id),this._roots[be](s,1),o instanceof a&&o.delChildrenFromStorage(this))}},addToMap:function(t){return t instanceof a&&(t.__storage=this),t.dirty(!1),this._elements[t.id]=t,this},get:function(t){return this._elements[t]},delFromMap:function(t){var e=this._elements,i=e[t];return i&&(delete e[t],i instanceof a&&(i.__storage=null)),this},dispose:function(){this._elements=this._renderList=this._roots=null},displayableSortFunc:e},l}),e("zrender/animation/Animation",[je,"../core/util","../core/event","./requestAnimationFrame","./Animator"],function(t){var e=t("../core/util"),i=t("../core/event").Dispatcher,n=t("./requestAnimationFrame"),r=t("./Animator"),a=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,i.call(this)};return a[He]={constructor:a,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t[Fe]=this;for(var e=t.getClips(),i=0;i<e[Me];i++)this.addClip(e[i])},removeClip:function(t){var i=e[ae](this._clips,t);i>=0&&this._clips[be](i,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e[Me];i++)this.removeClip(e[i]);t[Fe]=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,n=i[Me],r=[],a=[],o=0;n>o;o++){var s=i[o],l=s.step(t);l&&(r.push(l),a.push(s))}for(var o=0;n>o;)i[o]._needsRemove?(i[o]=i[n-1],i.pop(),n--):o++;n=r[Me];for(var o=0;n>o;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage[ze]&&this.stage[ze]()},_startLoop:function(){function t(){e._running&&(n(t),!e._paused&&e._update())}var e=this;this._running=!0,n(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},animate:function(t,e){e=e||{};var i=new r(t,e.loop,e.getter,e.setter);return i}},e.mixin(a,i),a}),e("zrender/dom/HandlerProxy",[je,"../core/event","../core/util","../mixin/Eventful","../core/env","../core/GestureMgr"],function(t){function e(t){return"mousewheel"===t&&u.browser.firefox?"DOMMouseScroll":t}function i(t,e,i){var n=t._gestureMgr;"start"===i&&n.clear();var r=n.recognize(e,t.handler.findHover(e.zrX,e.zrY,null),t.dom);if("end"===i&&n.clear(),r){var a=r.type;e.gestureEvent=a,t.handler.dispatchToElement(r[ge],a,r.event)}}function n(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout(function(){t._touching=!1},700)}function r(){return u.touchEventsSupported}function a(t){function e(t,e){return function(){return e._touching?void 0:t.apply(e,arguments)}}for(var i=0;i<g[Me];i++){var n=g[i];t._handlers[n]=l.bind(y[n],t)}for(var i=0;i<v[Me];i++){var n=v[i];t._handlers[n]=e(y[n],t)}}function o(t){function i(i,n){l.each(i,function(i){f(t,e(i),n._handlers[i])},n)}c.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._gestureMgr=new h,this._handlers={},a(this),r()&&i(g,this),i(v,this)}var s=t("../core/event"),l=t("../core/util"),c=t("../mixin/Eventful"),u=t("../core/env"),h=t("../core/GestureMgr"),f=s.addEventListener,d=s.removeEventListener,p=s.normalizeEvent,m=300,v=["click","dblclick","mousewheel",ye,"mouseup","mousedown","mousemove"],g=["touchstart","touchend","touchmove"],y={mousemove:function(t){t=p(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){t=p(this.dom,t);var e=t.toElement||t.relatedTarget;if(e!=this.dom)for(;e&&9!=e.nodeType;){if(e===this.dom)return;e=e.parentNode}this.trigger(ye,t)},touchstart:function(t){t=p(this.dom,t),this._lastTouchMoment=new Date,i(this,t,"start"),y.mousemove.call(this,t),y.mousedown.call(this,t),n(this)},touchmove:function(t){t=p(this.dom,t),i(this,t,"change"),y.mousemove.call(this,t),n(this)},touchend:function(t){t=p(this.dom,t),i(this,t,"end"),y.mouseup.call(this,t),+new Date-this._lastTouchMoment<m&&y.click.call(this,t),n(this)}};l.each(["click","mousedown","mouseup","mousewheel","dblclick"],function(t){y[t]=function(e){e=p(this.dom,e),this.trigger(t,e)}});var _=o[He];return _.dispose=function(){for(var t=v[H](g),i=0;i<t[Me];i++){var n=t[i];d(this.dom,e(n),this._handlers[n])}},_.setCursor=function(t){this.dom.style.cursor=t||"default"},l.mixin(o,c),o}),e("zrender/Painter",[je,"./config","./core/util","./core/log","./core/BoundingRect","./core/timsort","./Layer","./animation/requestAnimationFrame","./graphic/Image"],function(t){function e(t){return parseInt(t,10)}function r(t){return t?t.isBuildin?!0:typeof t[Ve]!==le||typeof t.refresh!==le?!1:!0:!1}function a(t){t.__unusedCount++}function s(t){1==t.__unusedCount&&t.clear()}function l(t,e,i){return b.copy(t[Q]()),t[n]&&b[h](t[n]),w.width=e,w[Ie]=i,!b.intersect(w)}function c(t,e){if(t==e)return!1;if(!t||!e||t[Me]!==e[Me])return!0;for(var i=0;i<t[Me];i++)if(t[i]!==e[i])return!0}function u(t,e){for(var i=0;i<t[Me];i++){var n=t[i],r=n.path;n.setTransform(e),r.beginPath(e),n.buildPath(r,n.shape),e.clip(),n.restoreTransform(e)}}function f(t,e){var i=document[q]("div"),n=i.style;return n[j]="relative",n.overflow="hidden",n.width=t+"px",n[Ie]=e+"px",i}var d=t("./config"),p=t("./core/util"),m=t("./core/log"),v=t("./core/BoundingRect"),g=t("./core/timsort"),y=t("./Layer"),_=t("./animation/requestAnimationFrame"),x=5,b=new v(0,0,0,0),w=new v(0,0,0,0),M=function(t,e,i){var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();i=i||{},this.dpr=i.devicePixelRatio||d.devicePixelRatio,this._singleCanvas=n,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],o=this._layers={};if(this._layerConfig={},n){var s=t.width,l=t[Ie];this._width=s,this._height=l;var c=new y(t,this,1);c.initContext(),o[0]=c,a.push(0)}else{this._width=this._getWidth(),this._height=this._getHeight();var u=this._domRoot=f(this._width,this._height);t.appendChild(u)}this.pathToImage=this._createPathToImage(),this._progressiveLayers=[],this._hoverlayer,this._hoverElements=[]};return M[He]={constructor:M,isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._singleCanvas?this._layers[0].dom:this._domRoot},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._paintList(e,t);for(var n=0;n<i[Me];n++){var r=i[n],a=this._layers[r];!a.isBuildin&&a.refresh&&a.refresh()}return this.refreshHover(),this._progressiveLayers[Me]&&this._startProgessive(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape});i.__from=t,t.__hoverMir=i,i[fe](e),this._hoverElements.push(i)}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,n=p[ae](i,e);n>=0&&i[be](n,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t[Me];e++){var i=t[e].__from;i&&(i.__hoverMir=null)}t[Me]=0},refreshHover:function(){var t=this._hoverElements,e=t[Me],i=this._hoverlayer;if(i&&i.clear(),e){g(t,this.storage.displayableSortFunc),i||(i=this._hoverlayer=this.getLayer(1e5));var r={};i.ctx.save();for(var a=0;e>a;){var o=t[a],s=o.__from;s&&s.__zr?(a++,s.invisible||(o[n]=s[n],o.invTransform=s.invTransform,o.__clipPaths=s.__clipPaths,this._doPaintEl(o,i,!0,r))):(t[be](a,1),s.__hoverMir=null,e--)}i.ctx.restore()}},_startProgessive:function(){function t(){i===e._progressiveToken&&e.storage&&(e._doPaintList(e.storage.getDisplayList()),e._furtherProgressive?(e._progress++,_(t)):e._progressiveToken=-1)}var e=this;if(e._furtherProgressive){var i=e._progressiveToken=+new Date;e._progress++,_(t)}},_clearProgressive:function(){this._progressiveToken=-1,this._progress=0,p.each(this._progressiveLayers,function(t){t[i]&&t.clear()})},_paintList:function(t,e){null==e&&(e=!1),this._updateLayerStatus(t),this._clearProgressive(),this.eachBuildinLayer(a),this._doPaintList(t,e),this.eachBuildinLayer(s)},_doPaintList:function(t,e){function n(t){var e=o.dpr||1;o.save(),o.globalAlpha=1,o.shadowBlur=0,r[i]=!0,o.setTransform(1,0,0,1,0,0),o.drawImage(t.dom,0,0,h*e,f*e),o.restore()}for(var r,a,o,s,l,c,u=0,h=this._width,f=this._height,d=this._progress,v=0,g=t[Me];g>v;v++){var y=t[v],_=this._singleCanvas?0:y[he],b=y.__frame;if(0>b&&l&&(n(l),l=null),a!==_&&(o&&o.restore(),s={},a=_,r=this.getLayer(a),r.isBuildin||m("ZLevel "+a+" has been used by unkown layer "+r.id),o=r.ctx,o.save(),r.__unusedCount=0,(r[i]||e)&&r.clear()),r[i]||e){if(b>=0){if(!l){if(l=this._progressiveLayers[Math.min(u++,x-1)],l.ctx.save(),l.renderScope={},l&&l.__progress>l.__maxProgress){v=l.__nextIdxNotProg-1;continue}c=l.__progress,l[i]||(d=c),l.__progress=d+1}b===d&&this._doPaintEl(y,l,!0,l.renderScope)}else this._doPaintEl(y,r,e,s);y[i]=!1}}l&&n(l),o&&o.restore(),this._furtherProgressive=!1,p.each(this._progressiveLayers,function(t){t.__maxProgress>=t.__progress&&(this._furtherProgressive=!0)},this)},_doPaintEl:function(t,e,r,a){var o=e.ctx,s=t[n];if(!(!e[i]&&!r||t.invisible||0===t.style[X]||s&&!s[0]&&!s[3]||t.culling&&l(t,this._width,this._height))){var h=t.__clipPaths;(a.prevClipLayer!==e||c(h,a.prevElClipPaths))&&(a.prevElClipPaths&&(a.prevClipLayer.ctx.restore(),a.prevClipLayer=a.prevElClipPaths=null,a.prevEl=null),h&&(o.save(),u(h,o),a.prevClipLayer=e,a.prevElClipPaths=h)),t.beforeBrush&&t.beforeBrush(o),t.brush(o,a.prevEl||null),a.prevEl=t,t.afterBrush&&t.afterBrush(o)}},getLayer:function(t){if(this._singleCanvas)return this._layers[0];var e=this._layers[t];return e||(e=new y("zr_"+t,this,this.dpr),e.isBuildin=!0,this._layerConfig[t]&&p.merge(e,this._layerConfig[t],!0),this.insertLayer(t,e),e.initContext()),e},insertLayer:function(t,e){var i=this._layers,n=this._zlevelList,a=n[Me],o=null,s=-1,l=this._domRoot;if(i[t])return void m("ZLevel "+t+" has been used already");if(!r(e))return void m("Layer of zlevel "+t+" is not valid");if(a>0&&t>n[0]){for(s=0;a-1>s&&!(n[s]<t&&n[s+1]>t);s++);o=i[n[s]]}if(n[be](s+1,0,t),o){var c=o.dom;c.nextSibling?l.insertBefore(e.dom,c.nextSibling):l.appendChild(e.dom)}else l.firstChild?l.insertBefore(e.dom,l.firstChild):l.appendChild(e.dom);i[t]=e},eachLayer:function(t,e){var i,n,r=this._zlevelList;for(n=0;n<r[Me];n++)i=r[n],t.call(e,this._layers[i],i)},eachBuildinLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a[Me];r++)n=a[r],i=this._layers[n],i.isBuildin&&t.call(e,i,n)},eachOtherLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a[Me];r++)n=a[r],i=this._layers[n],i.isBuildin||t.call(e,i,n)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){var e=this._layers,n=this._progressiveLayers,r={},a={};this.eachBuildinLayer(function(t,e){r[e]=t.elCount,t.elCount=0,t[i]=!1}),p.each(n,function(t,e){a[e]=t.elCount,t.elCount=0,t[i]=!1});for(var o,s,l=0,c=0,u=0,h=t[Me];h>u;u++){var f=t[u],d=this._singleCanvas?0:f[he],m=e[d],v=f.progressive;if(m&&(m.elCount++,m[i]=m[i]||f[i]),v>=0){s!==v&&(s=v,c++);var g=f.__frame=c-1;if(!o){var _=Math.min(l,x-1);o=n[_],o||(o=n[_]=new y("progressive",this,this.dpr),o.initContext()),o.__maxProgress=0}o[i]=o[i]||f[i],o.elCount++,o.__maxProgress=Math.max(o.__maxProgress,g),o.__maxProgress>=o.__progress&&(m[i]=!0)
}else f.__frame=-1,o&&(o.__nextIdxNotProg=u,l++,o=null)}o&&(l++,o.__nextIdxNotProg=u),this.eachBuildinLayer(function(t,e){r[e]!==t.elCount&&(t[i]=!0)}),n[Me]=Math.min(l,x),p.each(n,function(t,e){a[e]!==t.elCount&&(f[i]=!0),t[i]&&(t.__progress=0)})},clear:function(){return this.eachBuildinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?p.merge(i[t],e,!0):i[t]=e;var n=this._layers[t];n&&p.merge(n,i[t],!0)}},delLayer:function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i[be](p[ae](i,t),1))},resize:function(t,e){var i=this._domRoot;if(i.style.display="none",t=t||this._getWidth(),e=e||this._getHeight(),i.style.display="",this._width!=t||e!=this._height){i.style.width=t+"px",i.style[Ie]=e+"px";for(var n in this._layers)this._layers[n][Ve](t,e);this.refresh(!0)}return this._width=t,this._height=e,this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas)return this._layers[0].dom;var e=new y("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clearColor=t.backgroundColor,e.clear();for(var i=this.storage.getDisplayList(!0),n={},r=0;r<i[Me];r++){var a=i[r];this._doPaintEl(a,e,!0,n)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getWidth:function(){var t=this.root,i=document.defaultView.getComputedStyle(t);return(t.clientWidth||e(i.width)||e(t.style.width))-(e(i.paddingLeft)||0)-(e(i.paddingRight)||0)|0},_getHeight:function(){var t=this.root,i=document.defaultView.getComputedStyle(t);return(t.clientHeight||e(i[Ie])||e(t.style[Ie]))-(e(i.paddingTop)||0)-(e(i.paddingBottom)||0)|0},_pathToImage:function(e,i,n,r,a){var s=document[q]("canvas"),l=s.getContext("2d");s.width=n*a,s[Ie]=r*a,l.clearRect(0,0,n*a,r*a);var c={position:i[j],rotation:i[o],scale:i.scale};i[j]=[0,0,0],i[o]=0,i.scale=[1,1],i&&i.brush(l);var u=t("./graphic/Image"),h=new u({id:e,style:{x:0,y:0,image:s}});return null!=c[j]&&(h[j]=i[j]=c[j]),null!=c[o]&&(h[o]=i[o]=c[o]),null!=c.scale&&(h.scale=i.scale=c.scale),h},_createPathToImage:function(){var t=this;return function(e,i,n,r){return t._pathToImage(e,i,n,r,t.dpr)}}},M}),e("zrender/mixin/Draggable",[je],function(){function t(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}return t[He]={constructor:t,_dragStart:function(t){var e=t[ge];e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(e,"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,r=i-this._x,a=n-this._y;this._x=i,this._y=n,e.drift(r,a,t),this.dispatchToElement(e,"drag",t.event);var o=this.findHover(i,n,e),s=this._dropTarget;this._dropTarget=o,e!==o&&(s&&o!==s&&this.dispatchToElement(s,"dragleave",t.event),o&&o!==s&&this.dispatchToElement(o,"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(e,"dragend",t.event),this._dropTarget&&this.dispatchToElement(this._dropTarget,"drop",t.event),this._draggingTarget=null,this._dropTarget=null}},t}),e("zrender/core/event",[je,"../mixin/Eventful"],function(t){function e(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function i(t,i,n){var r=e(t);return n=n||{},n.zrX=i.clientX-r.left,n.zrY=i.clientY-r.top,n}function n(t,e){if(e=e||window.event,null!=e.zrX)return e;var n=e.type,r=n&&n[ae]("touch")>=0;if(r){var a="touchend"!=n?e.targetTouches[0]:e.changedTouches[0];a&&i(t,a,e)}else i(t,e,e),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;return e}function r(t,e,i){s?t.addEventListener(e,i):t.attachEvent("on"+e,i)}function a(t,e,i){s?t.removeEventListener(e,i):t.detachEvent("on"+e,i)}var o=t("../mixin/Eventful"),s=typeof window!==g&&!!window.addEventListener,l=s?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};return{clientToLocal:i,normalizeEvent:n,addEventListener:r,removeEventListener:a,stop:l,Dispatcher:o}}),e("zrender/animation/requestAnimationFrame",[je],function(){return typeof window!==g&&(window.requestAnimationFrame||window.msRequestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)}}),e("zrender/core/GestureMgr",[je,"./event"],function(t){function e(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}function i(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}var n=t("./event"),r=function(){this._track=[]};r[He]={constructor:r,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track[Me]=0,this},_doTrack:function(t,e,i){var r=t.touches;if(r){for(var a={points:[],touches:[],target:e,event:t},o=0,s=r[Me];s>o;o++){var l=r[o],c=n.clientToLocal(i,l);a.points.push([c.zrX,c.zrY]),a.touches.push(l)}this._track.push(a)}},_recognize:function(t){for(var e in a)if(a.hasOwnProperty(e)){var i=a[e](this._track,t);if(i)return i}}};var a={pinch:function(t,n){var r=t[Me];if(r){var a=(t[r-1]||{}).points,o=(t[r-2]||{}).points||a;if(o&&o[Me]>1&&a&&a[Me]>1){var s=e(a)/e(o);!isFinite(s)&&(s=1),n.pinchScale=s;var l=i(a);return n.pinchX=l[0],n.pinchY=l[1],{type:"pinch",target:t[0][ge],event:n}}}}};return r}),e("zrender/Layer",[je,"./core/util","./config","./graphic/Style","./graphic/Pattern"],function(t){function e(){return!1}function i(t,e,i,n){var r=document[q](e),a=i[Ne](),o=i[Ee](),s=r.style;return s[j]="absolute",s.left=0,s.top=0,s.width=a+"px",s[Ie]=o+"px",r.width=a*n,r[Ie]=o*n,r.setAttribute("data-zr-dom-id",t),r}var n=t("./core/util"),r=t("./config"),a=t("./graphic/Style"),o=t("./graphic/Pattern"),s=function(t,a,o){var s;o=o||r.devicePixelRatio,typeof t===Ge?s=i(t,"canvas",a,o):n[Le](t)&&(s=t,t=s.id),this.id=t,this.dom=s;var l=s.style;l&&(s.onselectstart=e,l["-webkit-user-select"]="none",l["user-select"]="none",l["-webkit-touch-callout"]="none",l["-webkit-tap-highlight-color"]="rgba(0,0,0,0)"),this.domBack=null,this.ctxBack=null,this.painter=a,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=o};return s[He]={constructor:s,elCount:0,__dirty:!0,initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=i("back-"+this.id,"canvas",this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!=t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,n=this.dom,r=n.style,a=this.domBack;r.width=t+"px",r[Ie]=e+"px",n.width=t*i,n[Ie]=e*i,a&&(a.width=t*i,a[Ie]=e*i,1!=i&&this.ctxBack.scale(i,i))},clear:function(t){var e=this.dom,i=this.ctx,n=e.width,r=e[Ie],s=this.clearColor,l=this.motionBlur&&!t,c=this.lastFrameAlpha,u=this.dpr;if(l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(e,0,0,n/u,r/u)),i.clearRect(0,0,n,r),s){var h;s.colorStops?(h=s.__canvasGradient||a.getGradient(i,s,{x:0,y:0,width:n,height:r}),s.__canvasGradient=h):s.image&&(h=o[He].getCanvasPattern.call(s,i)),i.save(),i.fillStyle=h||s,i.fillRect(0,0,n,r),i.restore()}if(l){var f=this.domBack;i.save(),i.globalAlpha=c,i.drawImage(f,0,0,n,r),i.restore()}}},s}),e("echarts/preprocessor/helper/compatStyle",[je,Xe],function(t){function e(t){var e=t&&t.itemStyle;e&&i.each(n,function(n){var r=e[D],a=e[b];r&&r[n]&&(t[n]=t[n]||{},t[n][D]?i.merge(t[n][D],r[n]):t[n][D]=r[n],r[n]=null),a&&a[n]&&(t[n]=t[n]||{},t[n][b]?i.merge(t[n][b],a[n]):t[n][b]=a[n],a[n]=null)})}var i=t(Xe),n=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];return function(t){if(t){e(t),e(t.markPoint),e(t.markLine);var n=t.data;if(n){for(var r=0;r<n[Me];r++)e(n[r]);var a=t.markPoint;if(a&&a.data)for(var o=a.data,r=0;r<o[Me];r++)e(o[r]);var s=t.markLine;if(s&&s.data)for(var l=s.data,r=0;r<l[Me];r++)i[ce](l[r])?(e(l[r][0]),e(l[r][1])):e(l[r])}}}}),e("echarts/component/axis/AxisView",[je,Xe,O,"./AxisBuilder",I],function(t){function e(t,e){function i(t){var e=n[V](t);return e.toGlobalCoord(e[y](0))}var n=t[ie],r=e.axis,a={},s=r[j],c=r.onZero?"onZero":s,u=r.dim,h=n.getRect(),f=[h.x,h.x+h.width,h.y,h.y+h[Ie]],d=e.get("offset")||0,p={x:{top:f[2]-d,bottom:f[3]+d},y:{left:f[0]-d,right:f[1]+d}};p.x.onZero=Math.max(Math.min(i("y"),p.x[Oe]),p.x.top),p.y.onZero=Math.max(Math.min(i("x"),p.y.right),p.y.left),a[j]=["y"===u?p.y[c]:f[0],"x"===u?p.x[c]:f[3]],a[o]=Math.PI/2*("x"===u?0:1);var m={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=m[s],r.onZero&&(a.labelOffset=p[u][s]-p[u].onZero),e[Be]("axisTick").get(l)&&(a.tickDirection=-a.tickDirection),e[Be]("axisLabel").get(l)&&(a.labelDirection=-a.labelDirection);var v=e[Be]("axisLabel").get("rotate");return a.labelRotation="top"===c?-v:v,a.labelInterval=r.getLabelInterval(),a.z2=1,a}var i=t(Xe),n=t(O),a=t("./AxisBuilder"),s=a.ifIgnoreOnTick,c=a.getInterval,u=["axisLine","axisLabel","axisTick","axisName"],h=["splitArea","splitLine"],f=t(I).extendComponentView({type:"axis",render:function(t){this.group[ne]();var r=this._axisGroup;if(this._axisGroup=new n.Group,this.group.add(this._axisGroup),t.get("show")){var o=t.findGridModel(),s=e(o,t),l=new a(t,s);i.each(u,l.add,l),this._axisGroup.add(l.getGroup()),i.each(h,function(e){t.get(e+".show")&&this["_"+e](t,o,s.labelInterval)},this),n.groupTransition(r,this._axisGroup,t)}},_splitLine:function(t,e,a){var o=t.axis,l=t[Be]("splitLine"),u=l[Be]("lineStyle"),h=u.get("color"),f=c(l,a);h=i[ce](h)?h:[h];for(var d=e[ie].getRect(),p=o.isHorizontal(),m=0,v=o.getTicksCoords(),g=o.scale.getTicks(),y=[],_=[],x=u[r](),b=0;b<v[Me];b++)if(!s(o,b,f)){var w=o.toGlobalCoord(v[b]);p?(y[0]=w,y[1]=d.y,_[0]=w,_[1]=d.y+d[Ie]):(y[0]=d.x,y[1]=w,_[0]=d.x+d.width,_[1]=w);var M=m++%h[Me];this._axisGroup.add(new n.Line(n.subPixelOptimizeLine({anid:"line_"+g[b],shape:{x1:y[0],y1:y[1],x2:_[0],y2:_[1]},style:i[Se]({stroke:h[M]},x),silent:!0})))}},_splitArea:function(t,e,r){var a=t.axis,o=t[Be]("splitArea"),l=o[Be]("areaStyle"),u=l.get("color"),h=e[ie].getRect(),f=a.getTicksCoords(),d=a.scale.getTicks(),p=a.toGlobalCoord(f[0]),m=a.toGlobalCoord(f[0]),v=0,g=c(o,r),y=l.getAreaStyle();u=i[ce](u)?u:[u];for(var _=1;_<f[Me];_++)if(!s(a,_,g)){var x,b,w,M,S=a.toGlobalCoord(f[_]);a.isHorizontal()?(x=p,b=h.y,w=S-x,M=h[Ie]):(x=h.x,b=m,w=h.width,M=S-b);var T=v++%u[Me];this._axisGroup.add(new n.Rect({anid:"area_"+d[_],shape:{x:x,y:b,width:w,height:M},style:i[Se]({fill:u[T]},y),silent:!0})),p=x+w,m=b+M}}});f[Ce]({type:"xAxis"}),f[Ce]({type:"yAxis"})}),e("echarts/chart/helper/SymbolDraw",[je,O,"./Symbol"],function(t){function e(t){this.group=new n.Group,this._symbolCtor=t||r}function i(t,e,i){var n=t[z](e);return!(!n||isNaN(n[0])||isNaN(n[1])||i&&i(e)||"none"===t[T](e,"symbol"))}var n=t(O),r=t("./Symbol"),a=e[He];return a.updateData=function(t,e){var r=this.group,a=t.hostModel,o=this._data,s=this._symbolCtor,l={itemStyle:a[Be]("itemStyle.normal").getItemStyle(["color"]),hoverItemStyle:a[Be]("itemStyle.emphasis").getItemStyle(),symbolRotate:a.get("symbolRotate"),symbolOffset:a.get("symbolOffset"),hoverAnimation:a.get("hoverAnimation"),labelModel:a[Be](S),hoverLabelModel:a[Be](M)};t.diff(o).add(function(n){var a=t[z](n);if(i(t,n,e)){var o=new s(t,n,l);o.attr(j,a),t[L](n,o),r.add(o)}})[ze](function(c,u){var h=o[k](u),f=t[z](c);return i(t,c,e)?(h?(h.updateData(t,c,l),n[A](h,{position:f},a)):(h=new s(t,c),h.attr(j,f)),r.add(h),void t[L](c,h)):void r[ke](h)})[ke](function(t){var e=o[k](t);e&&e.fadeOut(function(){r[ke](e)})}).execute(),this._data=t},a[Ae]=function(){var t=this._data;t&&t[C](function(e,i){var n=t[z](i);e.attr(j,n)})},a[ke]=function(t){var e=this.group,i=this._data;i&&(t?i[C](function(t){t.fadeOut(function(){e[ke](t)})}):e[ne]())},e}),e("echarts/chart/line/lineAnimationDiff",[je],function(){function t(t){return t>=0?1:-1}function e(e,i,n){for(var r,a=e[R](),o=e.getOtherAxis(a),s=a.onZero?0:o.scale[W]()[0],l=o.dim,c="x"===l||"radius"===l?1:0,u=i.stackedOn,h=i.get(l,n);u&&t(u.get(l,n))===t(h);){r=u;break}var f=[];return f[c]=i.get(a.dim,n),f[1-c]=r?r.get(l,n,!0):s,e[E](f)}function i(t,e){var i=[];return e.diff(t).add(function(t){i.push({cmd:"+",idx:t})})[ze](function(t,e){i.push({cmd:"=",idx:e,idx1:t})})[ke](function(t){i.push({cmd:"-",idx:t})}).execute(),i}return function(t,n,r,a,o,s){for(var l=i(t,n),c=[],u=[],h=[],f=[],d=[],p=[],m=[],v=s[N],g=0;g<l[Me];g++){var y=l[g],_=!0;switch(y.cmd){case"=":var x=t[z](y.idx),b=n[z](y.idx1);(isNaN(x[0])||isNaN(x[1]))&&(x=b.slice()),c.push(x),u.push(b),h.push(r[y.idx]),f.push(a[y.idx1]),m.push(n.getRawIndex(y.idx1));break;case"+":var w=y.idx;c.push(o[E]([n.get(v[0],w,!0),n.get(v[1],w,!0)])),u.push(n[z](w).slice()),h.push(e(o,n,w)),f.push(a[w]),m.push(n.getRawIndex(w));break;case"-":var w=y.idx,M=t.getRawIndex(w);M!==w?(c.push(t[z](w)),u.push(s[E]([t.get(v[0],w,!0),t.get(v[1],w,!0)])),h.push(r[w]),f.push(e(s,t,w)),m.push(M)):_=!1}_&&(d.push(y),p.push(p[Me]))}p.sort(function(t,e){return m[t]-m[e]});for(var S=[],T=[],C=[],k=[],L=[],g=0;g<p[Me];g++){var w=p[g];S[g]=c[w],T[g]=u[w],C[g]=h[w],k[g]=f[w],L[g]=d[w]}return{current:S,next:T,stackedOnCurrent:C,stackedOnNext:k,status:L}}}),e("echarts/chart/line/poly",[je,"zrender/graphic/Path",We],function(t){function e(t){return isNaN(t[0])||isNaN(t[1])}function i(t,i,n,r,d,p,m,v,g,y,_){for(var x=0,b=n,w=0;r>w;w++){var M=i[b];if(b>=d||0>b)break;if(e(M)){if(_){b+=p;continue}break}if(b===n)t[p>0?"moveTo":"lineTo"](M[0],M[1]),c(h,M);else if(g>0){var S=b+p,T=i[S];if(_)for(;T&&e(i[S]);)S+=p,T=i[S];var C=.5,k=i[x],T=i[S];if(!T||e(T))c(f,M);else{e(T)&&!_&&(T=M),a.sub(u,T,k);var L,A;if("x"===y||"y"===y){var P="x"===y?0:1;L=Math.abs(M[P]-k[P]),A=Math.abs(M[P]-T[P])}else L=a.dist(M,k),A=a.dist(M,T);C=A/(A+L),l(f,M,u,-g*(1-C))}o(h,h,v),s(h,h,m),o(f,f,v),s(f,f,m),t.bezierCurveTo(h[0],h[1],f[0],f[1],M[0],M[1]),l(h,M,u,g*C)}else t.lineTo(M[0],M[1]);x=b,b+=p}return w}function n(t,e){var i=[1/0,1/0],n=[-1/0,-1/0];if(e)for(var r=0;r<t[Me];r++){var a=t[r];a[0]<i[0]&&(i[0]=a[0]),a[1]<i[1]&&(i[1]=a[1]),a[0]>n[0]&&(n[0]=a[0]),a[1]>n[1]&&(n[1]=a[1])}return{min:e?i:n,max:e?n:i}}var r=t("zrender/graphic/Path"),a=t(We),o=a.min,s=a.max,l=a.scaleAndAdd,c=a.copy,u=[],h=[],f=[];return{Polyline:r[Ce]({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},buildPath:function(t,r){var a=r.points,o=0,s=a[Me],l=n(a,r.smoothConstraint);if(r.connectNulls){for(;s>0&&e(a[s-1]);s--);for(;s>o&&e(a[o]);o++);}for(;s>o;)o+=i(t,a,o,s,s,1,l.min,l.max,r.smooth,r.smoothMonotone,r.connectNulls)+1}}),Polygon:r[Ce]({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},buildPath:function(t,r){var a=r.points,o=r.stackedOnPoints,s=0,l=a[Me],c=r.smoothMonotone,u=n(a,r.smoothConstraint),h=n(o,r.smoothConstraint);if(r.connectNulls){for(;l>0&&e(a[l-1]);l--);for(;l>s&&e(a[s]);s++);}for(;l>s;){var f=i(t,a,s,l,l,1,u.min,u.max,r.smooth,c,r.connectNulls);i(t,o,s+f-1,f,l,-1,h.min,h.max,r.stackedOnSmooth,c,r.connectNulls),s+=f+1,t.closePath()}}})}}),e("echarts/chart/helper/Symbol",[je,Xe,"../../util/symbol",O,"../../util/number"],function(t){function e(t){return t instanceof Array||(t=[+t,+t]),t}function i(t,e,i){l.Group.call(this),this.updateData(t,e,i)}function n(t,e){this[s].drift(t,e)}var r=t(Xe),a=t("../../util/symbol"),l=t(O),c=t("../../util/number"),u=i[He];u._createSymbol=function(t,i,r){this[ne]();var o=i.hostModel,s=i[T](r,"color"),c=a.createSymbol(t,-.5,-.5,1,1,s);c.attr({z2:100,culling:!0,scale:[0,0]}),c.drift=n;var u=e(i[T](r,"symbolSize"));l.initProps(c,{scale:u},o,r),this._symbolType=t,this.add(c)},u.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},u.getSymbolPath=function(){return this.childAt(0)},u.getScale=function(){return this.childAt(0).scale},u.highlight=function(){this.childAt(0).trigger(b)},u.downplay=function(){this.childAt(0).trigger(D)},u.setZ=function(t,e){var i=this.childAt(0);i[he]=t,i.z=e},u.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":"pointer"},u.updateData=function(t,i,n){this[xe]=!1;var r=t[T](i,"symbol")||"circle",a=t.hostModel,o=e(t[T](i,"symbolSize"));if(r!==this._symbolType)this._createSymbol(r,t,i);else{var s=this.childAt(0);l[A](s,{scale:o},a,i)}this._updateCommon(t,i,o,n),this._seriesModel=a};var h=["itemStyle",D],f=["itemStyle",b],p=["label",D],m=["label",b];return u._updateCommon=function(t,i,n,a){var s=this.childAt(0),u=t.hostModel,v=t[T](i,"color");"image"!==s.type&&s.useStyle({strokeNoScale:!0}),a=a||null;var g=a&&a.itemStyle,y=a&&a.hoverItemStyle,w=a&&a.symbolRotate,M=a&&a.symbolOffset,S=a&&a.labelModel,C=a&&a.hoverLabelModel,k=a&&a.hoverAnimation;if(!a||t.hasItemOption){var L=t[P](i);g=L[Be](h).getItemStyle(["color"]),y=L[Be](f).getItemStyle(),w=L[d]("symbolRotate"),M=L[d]("symbolOffset"),S=L[Be](p),C=L[Be](m),k=L[d]("hoverAnimation")}else y=r[Ce]({},y);var A=s.style;s[o]=(w||0)*Math.PI/180||0,M&&s.attr(j,[c[_](M[0],n[0]),c[_](M[1],n[1])]),s.setColor(v),s[fe](g);var z=t[T](i,X);null!=z&&(A[X]=z);for(var I,O,R=t[N].slice();R[Me]&&(I=R.pop(),O=t.getDimensionInfo(I).type,O===F||"time"===O););null!=I&&S[d]("show")?(l.setText(A,S,v),A.text=r[B](u.getFormattedLabel(i,D),t.get(I,i))):A.text="",null!=I&&C[d]("show")?(l.setText(y,C,v),y.text=r[B](u.getFormattedLabel(i,b),t.get(I,i))):y.text="";var E=e(t[T](i,"symbolSize"));if(s.off(_e).off(ye).off(b).off(D),s.hoverStyle=y,l[x](s),k&&u.ifEnableAnimation()){var V=function(){var t=E[1]/E[0];this.animateTo({scale:[Math.max(1.1*E[0],E[0]+3),Math.max(1.1*E[1],E[1]+3*t)]},400,"elasticOut")},G=function(){this.animateTo({scale:E},400,"elasticOut")};s.on(_e,V).on(ye,G).on(b,V).on(D,G)}},u.fadeOut=function(t){var e=this.childAt(0);this[xe]=!0,e.style.text="",l[A](e,{scale:[0,0]},this._seriesModel,this[ve],t)},r[re](i,l.Group),i}),e("echarts/component/helper/selectableMixin",[je,Xe],function(t){var e=t(Xe);return{updateSelectedMap:function(t){this._selectTargetMap=e.reduce(t||[],function(t,e){return t[e.name]=e,t},{})},select:function(t){var i=this._selectTargetMap,n=i[t],r=this.get("selectedMode");"single"===r&&e.each(i,function(t){t.selected=!1}),n&&(n.selected=!0)},unSelect:function(t){var e=this._selectTargetMap[t];e&&(e.selected=!1)},toggleSelected:function(t){var e=this._selectTargetMap[t];return null!=e?(this[e.selected?"unSelect":"select"](t),e.selected):void 0},isSelected:function(t){var e=this._selectTargetMap[t];return e&&e.selected}}}),e("echarts/util/symbol",[je,"./graphic","zrender/core/BoundingRect"],function(t){var e=t("./graphic"),i=t("zrender/core/BoundingRect"),n=e.extendShape({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e[Ie]/2;t.moveTo(i,n-a),t.lineTo(i+r,n+a),t.lineTo(i-r,n+a),t.closePath()}}),r=e.extendShape({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e[Ie]/2;t.moveTo(i,n-a),t.lineTo(i+r,n),t.lineTo(i,n+a),t.lineTo(i-r,n),t.closePath()}}),a=e.extendShape({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width/5*3,a=Math.max(r,e[Ie]),o=r/2,s=o*o/(a-o),l=n-a+o+s,c=Math.asin(s/o),u=Math.cos(c)*o,h=Math.sin(c),f=Math.cos(c);t.arc(i,l,o,Math.PI-c,2*Math.PI+c);var d=.6*o,p=.7*o;t.bezierCurveTo(i+u-h*d,l+s+f*d,i,n-p,i,n),t.bezierCurveTo(i,n-p,i-u+h*d,l+s+f*d,i-u,l+s),t.closePath()}}),o=e.extendShape({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e[Ie],n=e.width,r=e.x,a=e.y,o=n/3*2;t.moveTo(r,a),t.lineTo(r+o,a+i),t.lineTo(r,a+i/4*3),t.lineTo(r-o,a+i),t.lineTo(r,a),t.closePath()}}),s={line:e.Line,rect:e.Rect,roundRect:e.Rect,square:e.Rect,circle:e.Circle,diamond:r,pin:a,arrow:o,triangle:n},u={line:function(t,e,i,n,r){r.x1=t,r.y1=e+n/2,r.x2=t+i,r.y2=e+n/2},rect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r[Ie]=n},roundRect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r[Ie]=n,r.r=Math.min(i,n)/4},square:function(t,e,i,n,r){var a=Math.min(i,n);r.x=t,r.y=e,r.width=a,r[Ie]=a},circle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.r=Math.min(i,n)/2},diamond:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r[Ie]=n},pin:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r[Ie]=n},arrow:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r[Ie]=n},triangle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r[Ie]=n}},h={};for(var f in s)h[f]=new s[f];var d=e.extendShape({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style,e=this.shape;"pin"===e.symbolType&&t.textPosition===l&&(t.textPosition=["50%","40%"],t[te]=Y,t.textVerticalAlign=$)},buildPath:function(t,e,i){var n=e.symbolType,r=h[n];"none"!==e.symbolType&&(r||(n="rect",r=h[n]),u[n](e.x,e.y,e.width,e[Ie],r.shape),r.buildPath(t,r.shape,i))}}),p=function(t){if("image"!==this.type){var e=this.style,i=this.shape;i&&"line"===i.symbolType?e[c]=t:this.__isEmptyBrush?(e[c]=t,e.fill="#fff"):(e.fill&&(e.fill=t),e[c]&&(e[c]=t)),this.dirty(!1)}},m={createSymbol:function(t,n,r,a,o,s){var l=0===t[ae]("empty");l&&(t=t.substr(5,1)[qe]()+t.substr(6));var c;return c=0===t[ae]("image://")?new e.Image({style:{image:t.slice(8),x:n,y:r,width:a,height:o}}):0===t[ae]("path://")?e.makePath(t.slice(7),{},new i(n,r,a,o)):new d({shape:{symbolType:t,x:n,y:r,width:a,height:o}}),c.__isEmptyBrush=l,c.setColor=p,c.setColor(s),c}};return m}),e("echarts/component/helper/listComponent",[je,"../../util/layout","../../util/format",O],function(t){function e(t,e,n){i.positionGroup(t,e.getBoxLayoutParams(),{width:n[Ne](),height:n[Ee]()},e.get("padding"))}var i=t("../../util/layout"),n=t("../../util/format"),r=t(O);return{layout:function(t,n,r){var a=i.getLayoutRect(n.getBoxLayoutParams(),{width:r[Ne](),height:r[Ee]()},n.get("padding"));i.box(n.get("orient"),t,n.get("itemGap"),a.width,a[Ie]),e(t,n,r)},addBackground:function(t,e){var i=n.normalizeCssArray(e.get("padding")),a=t[Q](),o=e.getItemStyle(["color",X]);o.fill=e.get("backgroundColor");var s=new r.Rect({shape:{x:a.x-i[3],y:a.y-i[0],width:a.width+i[1]+i[3],height:a[Ie]+i[0]+i[2]},style:o,silent:!0,z2:-1});r.subPixelOptimizeRect(s),t.add(s)}}}),e("echarts/component/tooltip/TooltipContent",[je,Xe,"zrender/tool/color","zrender/core/event","../../util/format",Ue],function(t){function e(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+t+"s "+e+",top "+t+"s "+e;return o.map(d,function(t){return t+"transition:"+i}).join(";")}function i(t){var e=[],i=t.get("fontSize"),n=t[K]();return n&&e.push("color:"+n),e.push("font:"+t[J]()),i&&e.push("line-height:"+Math.round(3*i/2)+"px"),u(["decoration","align"],function(i){var n=t.get(i);n&&e.push("text-"+i+":"+n)}),e.join(";")}function n(t){t=t;var n=[],r=t.get("transitionDuration"),a=t.get("backgroundColor"),o=t[Be](ee),l=t.get("padding");return r&&n.push(e(r)),a&&(f.canvasSupported?n.push("background-Color:"+a):(n.push("background-Color:#"+s.toHex(a)),n.push("filter:alpha(opacity=70)"))),u(["width","color","radius"],function(e){var i="border-"+e,r=h(i),a=t.get(r);null!=a&&n.push(i+":"+a+("color"===e?"":"px"))}),n.push(i(o)),null!=l&&n.push("padding:"+c.normalizeCssArray(l).join("px ")+"px"),n.join(";")+";"}function r(t,e){var i=document[q]("div"),n=e.getZr();this.el=i,this._x=e[Ne]()/2,this._y=e[Ee]()/2,t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var r=this;i.onmouseenter=function(){r.enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},i.onmousemove=function(e){if(!r.enterable){var i=n.handler;l.normalizeEvent(t,e),i.dispatch("mousemove",e)}},i.onmouseleave=function(){r.enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1},a(i,t)}function a(t,e){function i(t){n(t[ge])&&t.preventDefault()}function n(i){for(;i&&i!==e;){if(i===t)return!0;i=i.parentNode}}l.addEventListener(e,"touchstart",i),l.addEventListener(e,"touchmove",i),l.addEventListener(e,"touchend",i)}var o=t(Xe),s=t("zrender/tool/color"),l=t("zrender/core/event"),c=t("../../util/format"),u=o.each,h=c.toCamelCase,f=t(Ue),d=["","-webkit-","-moz-","-o-"],p="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";return r[He]={constructor:r,enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),i=t.style;"absolute"!==i[j]&&"absolute"!==e[j]&&(i[j]="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText=p+n(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",this._show=!0},setContent:function(t){var e=this.el;e.innerHTML=t,e.style.display=t?"block":"none"},moveTo:function(t,e){var i=this.el.style;i.left=t+"px",i.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this.enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(o.bind(this.hide,this),t)):this.hide())},isShow:function(){return this._show}},r}),e("echarts/component/marker/MarkerView",[je,I],function(t){return t(I).extendComponentView({type:"marker",init:function(){this.markerGroupMap={}},render:function(t,e,i){var n=this.markerGroupMap;for(var r in n)n[r].__keep=!1;var a=this.type+"Model";e[Pe](function(t){var n=t[a];n&&this.renderSeries(t,n,e,i)},this);for(var r in n)n[r].__keep||this.group[ke](n[r].group)},renderSeries:function(){}})}),e("echarts/component/marker/markerHelper",[je,Xe,"../../util/number"],function(t){function e(t){return!(isNaN(parseFloat(t.x))&&isNaN(parseFloat(t.y)))}function i(t){return!isNaN(parseFloat(t.x))&&!isNaN(parseFloat(t.y))}function n(t,e,i){var n=-1;do n=Math.max(o.getPrecision(t.get(e,i)),n),t=t.stackedOn;while(t);return n}function r(t,e,i,r,a,o){var s=[],l=p(e,r,t),c=e.indexOfNearest(r,l,!0);s[a]=e.get(i,c,!0),s[o]=e.get(r,c,!0);var u=n(e,r,c);return u>=0&&(s[o]=+s[o].toFixed(u)),s}var a=t(Xe),o=t("../../util/number"),s=a[ae],l=a.curry,c={min:l(r,"min"),max:l(r,"max"),average:l(r,"average")},u=function(t,e){var n=t[Ze](),r=t[ie];if(e&&!i(e)&&!a[ce](e.coord)&&r){var o=r[N],l=h(e,n,r,t);if(e=a.clone(e),e.type&&c[e.type]&&l.baseAxis&&l.valueAxis){var u=s(o,l.baseAxis.dim),f=s(o,l.valueAxis.dim);e.coord=c[e.type](n,l.baseDataDim,l.valueDataDim,u,f),e.value=e.coord[f]}else{for(var d=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis],m=0;2>m;m++)if(c[d[m]]){var v=t.coordDimToDataDim(o[m])[0];d[m]=p(n,v,d[m])}e.coord=d}}return e},h=function(t,e,i,n){var r={};return null!=t.valueIndex||null!=t.valueDim?(r.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,r.valueAxis=i[V](n.dataDimToCoordDim(r.valueDataDim)),r.baseAxis=i.getOtherAxis(r.valueAxis),r.baseDataDim=n.coordDimToDataDim(r.baseAxis.dim)[0]):(r.baseAxis=n[R](),r.valueAxis=i.getOtherAxis(r.baseAxis),r.baseDataDim=n.coordDimToDataDim(r.baseAxis.dim)[0],r.valueDataDim=n.coordDimToDataDim(r.valueAxis.dim)[0]),r},f=function(t,i){return t&&t.containData&&i.coord&&!e(i)?t.containData(i.coord):!0},d=function(t,e,i,n){return 2>n?t.coord&&t.coord[n]:t.value},p=function(t,e,i){if("average"===i){var n=0,r=0;return t.each(e,function(t){isNaN(t)||(n+=t,r++)},!0),n/r}return t.getDataExtent(e,!0)["max"===i?1:0]};return{dataTransform:u,dataFilter:f,dimValueGetter:d,getAxisInfo:h,numCalculate:p}}),e("echarts/chart/helper/LineDraw",[je,O,"./Line"],function(t){function e(t){return isNaN(t[0])||isNaN(t[1])}function i(t){return!e(t[0])&&!e(t[1])}function n(t){this._ctor=t||o,this.group=new a.Group}var a=t(O),o=t("./Line"),s=n[He];return s.updateData=function(t){var e=this._lineData,n=this.group,a=this._ctor,o=t.hostModel,s={lineStyle:o[Be]("lineStyle.normal")[r](),hoverLineStyle:o[Be]("lineStyle.emphasis")[r](),labelModel:o[Be](S),hoverLabelModel:o[Be](M)};t.diff(e).add(function(e){if(i(t[z](e))){var r=new a(t,e,s);t[L](e,r),n.add(r)}})[ze](function(r,o){var l=e[k](o);return i(t[z](r))?(l?l.updateData(t,r,s):l=new a(t,r,s),t[L](r,l),void n.add(l)):void n[ke](l)})[ke](function(t){n[ke](e[k](t))}).execute(),this._lineData=t},s[Ae]=function(){var t=this._lineData;t[C](function(e,i){e[Ae](t,i)},this)},s[ke]=function(){this.group[ne]()},n}),e("echarts/component/timeline/TimelineModel",[je,"../../model/Component","../../data/List",Xe,"../../util/model"],function(t){var e=t("../../model/Component"),i=t("../../data/List"),n=t(Xe),r=t("../../util/model"),o=e[Ce]({type:"timeline",layoutMode:"box",defaultOption:{zlevel:0,z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{normal:{},emphasis:{}},label:{normal:{textStyle:{color:"#000"}},emphasis:{}},data:[]},init:function(t,e,i){this._data,this._names,this.mergeDefaultAndTheme(t,i),this._initData()},mergeOption:function(){o.superApply(this,m,arguments),this._initData()},setCurrentIndex:function(t){null==t&&(t=this[v].currentIndex);var e=this._data.count();this[v].loop?t=(t%e+e)%e:(t>=e&&(t=e-1),0>t&&(t=0)),this[v].currentIndex=t},getCurrentIndex:function(){return this[v].currentIndex},isIndexMax:function(){return this.getCurrentIndex()>=this._data.count()-1},setPlayState:function(t){this[v].autoPlay=!!t},getPlayState:function(){return!!this[v].autoPlay},_initData:function(){var t=this[v],e=t.data||[],o=t.axisType,s=this._names=[];if(o===G){var l=[];n.each(e,function(t,e){var i,a=r.getDataItemValue(t);n[Le](t)?(i=n.clone(t),i.value=e):i=e,l.push(i),n.isString(a)||null!=a&&!isNaN(a)||(a=""),s.push(a+"")}),e=l}var c={category:"ordinal",time:"time"}[o]||se,u=this._data=new i([{name:"value",type:c}],this);u[a](e,s)},getData:function(){return this._data},getCategories:function(){return this.get("axisType")===G?this._names.slice():void 0}});return o}),e("echarts/component/axis/AxisBuilder",[je,Xe,"../../util/format",O,"../../model/Model","../../util/number",We],function(t){function e(t){var e={componentType:t.mainType};return e[t.mainType+"Index"]=t.componentIndex,e}function i(t,e,i){var n,r,a=m(e-t[o]);return v(a)?(r=i>0?"top":Oe,n=Y):v(a-b)?(r=i>0?Oe:"top",n=Y):(r=$,n=a>0&&b>a?i>0?"right":"left":i>0?"left":"right"),{rotation:a,textAlign:n,verticalAlign:r}}function a(t,e,i,n){var r,a,s=m(i-t[o]),l=n[0]>n[1],c="start"===e&&!l||"start"!==e&&l;return v(s-b/2)?(a=c?Oe:"top",r=Y):v(s-1.5*b)?(a=c?"top":Oe,r=Y):(a=$,r=1.5*b>s&&s>b/2?c?"left":"right":c?"right":"left"),{rotation:s,textAlign:r,verticalAlign:a}}function s(t){var e=t.get("tooltip");return t.get(xe)||!(t.get("triggerEvent")||e&&e.show)}var l=t(Xe),c=t("../../util/format"),u=t(O),d=t("../../model/Model"),p=t("../../util/number"),m=p.remRadian,v=p.isRadianAroundZero,g=t(We),_=g[h],x=l[B],b=Math.PI,w=function(t,e){this.opt=e,this.axisModel=t,l[Se](e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new u.Group;var i=new u.Group({position:e[j].slice(),rotation:e[o]});i.updateTransform(),this._transform=i[n],this._dumbGroup=i};w[He]={constructor:w,hasBuilder:function(t){return!!M[t]},add:function(t){M[t].call(this)},getGroup:function(){return this.group}};var M={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var i=this.axisModel.axis[W](),n=this._transform,a=[i[0],0],o=[i[1],0];n&&(_(a,a,n),_(o,o,n)),this.group.add(new u.Line(u.subPixelOptimizeLine({anid:"line",shape:{x1:a[0],y1:a[1],x2:o[0],y2:o[1]},style:l[Ce]({lineCap:"round"},e[Be]("axisLine.lineStyle")[r]()),strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})))}},axisTick:function(){var t=this.axisModel;if(t.get("axisTick.show"))for(var e=t.axis,i=t[Be]("axisTick"),n=this.opt,a=i[Be]("lineStyle"),o=i.get(Me),s=T(i,n.labelInterval),c=e.getTicksCoords(i.get("alignWithLabel")),h=e.scale.getTicks(),f=[],d=[],p=this._transform,m=0;m<c[Me];m++)if(!S(e,m,s)){var v=c[m];
f[0]=v,f[1]=0,d[0]=v,d[1]=n.tickDirection*o,p&&(_(f,f,p),_(d,d,p)),this.group.add(new u.Line(u.subPixelOptimizeLine({anid:"tick_"+h[m],shape:{x1:f[0],y1:f[1],x2:d[0],y2:d[1]},style:l[Se](a[r](),{stroke:t.get("axisLine.lineStyle.color")}),z2:2,silent:!0})))}},axisLabel:function(){function t(t,e){var i=t&&t[Q]().clone(),n=e&&e[Q]().clone();return i&&n?(i[h](t.getLocalTransform()),n[h](e.getLocalTransform()),i.intersect(n)):void 0}var n=this.opt,r=this.axisModel,a=x(n.axisLabelShow,r.get("axisLabel.show"));if(a){var l=r.axis,c=r[Be]("axisLabel"),p=c[Be](ee),m=c.get("margin"),v=l.scale.getTicks(),g=r.getFormattedLabels(),_=x(n.labelRotation,c.get("rotate"))||0;_=_*b/180;for(var w=i(n,_,n.labelDirection),M=r.get("data"),T=[],C=s(r),k=r.get("triggerEvent"),L=0;L<v[Me];L++)if(!S(l,L,n.labelInterval)){var A=p;M&&M[L]&&M[L][ee]&&(A=new d(M[L][ee],p,r[f]));var P=A[K]()||r.get("axisLine.lineStyle.color"),z=l[y](v[L]),D=[z,n.labelOffset+n.labelDirection*m],I=l.scale.getLabel(v[L]),O=new u.Text({anid:"label_"+v[L],style:{text:g[L],textAlign:A.get("align",!0)||w[te],textVerticalAlign:A.get("baseline",!0)||w.verticalAlign,textFont:A[J](),fill:typeof P===le?P(I):P},position:D,rotation:w[o],silent:C,z2:10});k&&(O.eventData=e(r),O.eventData.targetType="axisLabel",O.eventData.value=I),this._dumbGroup.add(O),O.updateTransform(),T.push(O),this.group.add(O),O.decomposeTransform()}if(l.type!==G){if(r.getMin?r.getMin():r.get("min")){var R=T[0],E=T[1];t(R,E)&&(R[Re]=!0)}if(r.getMax?r.getMax():r.get("max")){var N=T[T[Me]-1],B=T[T[Me]-2];t(B,N)&&(N[Re]=!0)}}}},axisName:function(){var t=this.opt,n=this.axisModel,r=x(t.axisName,n.get("name"));if(r){var h,f=n.get("nameLocation"),d=t.nameDirection,p=n[Be]("nameTextStyle"),m=n.get("nameGap")||0,v=this.axisModel.axis[W](),g=v[0]>v[1]?-1:1,y=["start"===f?v[0]-g*m:"end"===f?v[1]+g*m:(v[0]+v[1])/2,f===$?t.labelOffset+d*m:0],_=n.get("nameRotate");null!=_&&(_=_*b/180);var w;f===$?h=i(t,null!=_?_:t[o],d):(h=a(t,f,_||0,v),w=t.axisNameAvailableWidth,null!=w&&(w=Math.abs(w/Math.sin(h[o])),!isFinite(w)&&(w=null)));var M=p[J](),S=n.get("nameTruncate",!0)||{},T=S.ellipsis,C=x(S.maxWidth,w),k=null!=T&&null!=C?c.truncateText(r,C,M,T,{minChar:2,placeholder:S.placeholder}):r,L=n.get("tooltip",!0),A=n.mainType,P={componentType:A,name:r,$vars:["name"]};P[A+"Index"]=n.componentIndex;var z=new u.Text({anid:"name",__fullText:r,__truncatedText:k,style:{text:k,textFont:M,fill:p[K]()||n.get("axisLine.lineStyle.color"),textAlign:h[te],textVerticalAlign:h.verticalAlign},position:y,rotation:h[o],silent:s(n),z2:1,tooltip:L&&L.show?l[Ce]({content:r,formatter:function(){return r},formatterParams:P},L):null});n.get("triggerEvent")&&(z.eventData=e(n),z.eventData.targetType="axisName",z.eventData.name=r),this._dumbGroup.add(z),z.updateTransform(),this.group.add(z),z.decomposeTransform()}}},S=w.ifIgnoreOnTick=function(t,e,i){var n,r=t.scale;return r.type===F&&(typeof i===le?(n=r.getTicks()[e],!i(n,r.getLabel(n))):e%(i+1))},T=w.getInterval=function(t,e){var i=t.get("interval");return(null==i||"auto"==i)&&(i=e),i};return w}),e("echarts/chart/pie/labelLayout",[je,"zrender/contain/text"],function(t){function e(t,e,i,n,r,a,o){function s(e,i,n){for(var r=e;i>r;r++)if(t[r].y+=n,r>e&&i>r+1&&t[r+1].y>t[r].y+t[r][Ie])return void l(r,n/2);l(i-1,n/2)}function l(e,i){for(var n=e;n>=0&&(t[n].y-=i,!(n>0&&t[n].y>t[n-1].y+t[n-1][Ie]));n--);}function c(t,e,i,n,r,a){for(var o=a>0?e?Number.MAX_VALUE:0:e?Number.MAX_VALUE:0,s=0,l=t[Me];l>s;s++)if(t[s][j]!==Y){var c=Math.abs(t[s].y-n),u=t[s].len,h=t[s].len2,f=r+u>c?Math.sqrt((r+u+h)*(r+u+h)-c*c):Math.abs(t[s].x-i);e&&f>=o&&(f=o-10),!e&&o>=f&&(f=o+10),t[s].x=i+f*a,o=f}}t.sort(function(t,e){return t.y-e.y});for(var u,h=0,f=t[Me],d=[],p=[],m=0;f>m;m++)u=t[m].y-h,0>u&&s(m,f,-u,r),h=t[m].y+t[m][Ie];0>o-h&&l(f-1,h-o);for(var m=0;f>m;m++)t[m].y>=i?p.push(t[m]):d.push(t[m]);c(d,!1,e,i,n,r),c(p,!0,e,i,n,r)}function i(t,i,n,r,a,o){for(var s=[],l=[],c=0;c<t[Me];c++)t[c].x<i?s.push(t[c]):l.push(t[c]);e(l,i,n,r,1,a,o),e(s,i,n,r,-1,a,o);for(var c=0;c<t[Me];c++){var u=t[c].linePoints;if(u){var h=u[1][0]-u[2][0];u[2][0]=t[c].x<i?t[c].x+3:t[c].x-3,u[1][1]=u[2][1]=t[c].y,u[1][0]=u[2][0]+h}}}var n=t("zrender/contain/text");return function(t,e,r,a){var o,s,c=t[Ze](),u=[],h=!1;c.each(function(i){var r,a,f,d,m=c[z](i),v=c[P](i),g=v[Be](S),y=g.get(j)||v.get("label.emphasis.position"),_=v[Be]("labelLine.normal"),x=_.get(Me),b=_.get("length2"),w=(m.startAngle+m.endAngle)/2,M=Math.cos(w),T=Math.sin(w);o=m.cx,s=m.cy;var C=y===l||"inner"===y;if(y===Y)r=m.cx,a=m.cy,d=Y;else{var k=(C?(m.r+m.r0)/2*M:m.r*M)+o,L=(C?(m.r+m.r0)/2*T:m.r*T)+s;if(r=k+3*M,a=L+3*T,!C){var A=k+M*(x+e-m.r),I=L+T*(x+e-m.r),O=A+(0>M?-1:1)*b,R=I;r=O+(0>M?-5:5),a=R,f=[[k,L],[A,I],[O,R]]}d=C?Y:M>0?"left":"right"}var E=g[Be](ee)[J](),N=g.get("rotate")?0>M?-w+Math.PI:-w:0,B=t.getFormattedLabel(i,D)||c[p](i),F=n[Q](B,E,d,"top");h=!!N,m.label={x:r,y:a,position:y,height:F[Ie],len:x,len2:b,linePoints:f,textAlign:d,verticalAlign:"middle",font:E,rotation:N},C||u.push(m.label)}),!h&&t.get("avoidLabelOverlap")&&i(u,o,s,e,r,a)}}),e("echarts/chart/helper/Line",[je,"../../util/symbol",We,"./LinePath",O,Xe,"../../util/number"],function(t){function e(t){return"_"+t+"Type"}function n(t,e,i){var n=e[T](i,"color"),r=e[T](i,t),a=e[T](i,t+"Size");if(r&&"none"!==r){g[ce](a)||(a=[a,a]);var o=h.createSymbol(r,-a[0]/2,-a[1]/2,a[0],a[1],n);return o.name=t,o}}function a(t){var e=new m({name:"line"});return l(e.shape,t),e}function l(t,e){var i=e[0],n=e[1],r=e[2];t.x1=i[0],t.y1=i[1],t.x2=n[0],t.y2=n[1],t.percent=1,r?(t.cpx1=r[0],t.cpy1=r[1]):(t.cpx1=0/0,t.cpy1=0/0)}function c(){var t=this,e=t.childOfName("fromSymbol"),n=t.childOfName("toSymbol"),r=t.childOfName("label");if(e||n||!r[Re]){for(var a=1,l=this[s];l;)l.scale&&(a/=l.scale[0]),l=l[s];var c=t.childOfName("line");if(this[i]||c[i]){var u=c.shape.percent,h=c.pointAt(0),d=c.pointAt(u),p=f.sub([],d,h);if(f[U](p,p),e){e.attr(j,h);var m=c.tangentAt(0);e.attr(o,Math.PI/2-Math.atan2(m[1],m[0])),e.attr("scale",[a*u,a*u])}if(n){n.attr(j,d);var m=c.tangentAt(1);n.attr(o,-Math.PI/2-Math.atan2(m[1],m[0])),n.attr("scale",[a*u,a*u])}if(!r[Re]){r.attr(j,d);var v,g,y,_=5*a;if("end"===r.__position)v=[p[0]*_+d[0],p[1]*_+d[1]],g=p[0]>.8?"left":p[0]<-.8?"right":Y,y=p[1]>.8?"top":p[1]<-.8?Oe:$;else if(r.__position===$){var x=u/2,m=c.tangentAt(x),b=[m[1],-m[0]],w=c.pointAt(x);b[1]>0&&(b[0]=-b[0],b[1]=-b[1]),v=[w[0]+b[0]*_,w[1]+b[1]*_],g=Y,y=Oe;var M=-Math.atan2(m[1],m[0]);d[0]<h[0]&&(M=Math.PI+M),r.attr(o,M)}else v=[-p[0]*_+h[0],-p[1]*_+h[1]],g=p[0]>.8?"right":p[0]<-.8?"left":Y,y=p[1]>.8?Oe:p[1]<-.8?"top":$;r.attr({style:{textVerticalAlign:r.__verticalAlign||y,textAlign:r.__textAlign||g},position:v,scale:[a,a]})}}}}function u(t,e,i){v.Group.call(this),this._createLine(t,e,i)}var h=t("../../util/symbol"),f=t(We),m=t("./LinePath"),v=t(O),g=t(Xe),y=t("../../util/number"),_=["fromSymbol","toSymbol"],C=u[He];return C.beforeUpdate=c,C._createLine=function(t,i,r){var o=t.hostModel,s=t[z](i),l=a(s);l.shape.percent=0,v.initProps(l,{shape:{percent:1}},o,i),this.add(l);var c=new v.Text({name:"label"});this.add(c),g.each(_,function(r){var a=n(r,t,i);this.add(a),this[e(r)]=t[T](i,r)},this),this._updateCommonStl(t,i,r)},C.updateData=function(t,i,r){var a=t.hostModel,o=this.childOfName("line"),s=t[z](i),c={shape:{}};l(c.shape,s),v[A](o,c,a,i),g.each(_,function(r){var a=t[T](i,r),o=e(r);if(this[o]!==a){this[ke](this.childOfName(r));var s=n(r,t,i);this.add(s)}this[o]=a},this),this._updateCommonStl(t,i,r)},C._updateCommonStl=function(t,e,i){var n=t.hostModel,a=this.childOfName("line"),o=i&&i.lineStyle,s=i&&i.hoverLineStyle,l=i&&i.labelModel,c=i&&i.hoverLabelModel;if(!i||t.hasItemOption){var u=t[P](e);o=u[Be]("lineStyle.normal")[r](),s=u[Be]("lineStyle.emphasis")[r](),l=u[Be](S),c=u[Be](M)}var h=t[T](e,"color"),f=g[B](t[T](e,X),o[X],1);isNaN(m)&&(m=t[p](e)),a.useStyle(g[Se]({strokeNoScale:!0,fill:"none",stroke:h,opacity:f},o)),a.hoverStyle=s,g.each(_,function(t){var e=this.childOfName(t);e&&(e.setColor(h),e[fe]({opacity:f}))},this);var m,C,k=l[d]("show"),L=c[d]("show"),A=this.childOfName("label");if((k||L)&&(m=y.round(n[w](e)),C=h||"#000"),k){var z=l[Be](ee);A[fe]({text:g[B](n.getFormattedLabel(e,D,t.dataType),m),textFont:z[J](),fill:z[K]()||C}),A.__textAlign=z.get("align"),A.__verticalAlign=z.get("baseline"),A.__position=l.get(j)}else A[fe]("text","");if(L){var I=c[Be](ee);A.hoverStyle={text:g[B](n.getFormattedLabel(e,b,t.dataType),m),textFont:I[J](),fill:I[K]()||C}}else A.hoverStyle={text:""};A[Re]=!k&&!L,v[x](this)},C[Ae]=function(t,e){this.setLinePoints(t[z](e))},C.setLinePoints=function(t){var e=this.childOfName("line");l(e.shape,t),e.dirty()},g[re](u,v.Group),u}),e("echarts/component/timeline/TimelineView",[je,"../../view/Component"],function(t){var e=t("../../view/Component");return e[Ce]({type:"timeline"})}),e("echarts/component/timeline/TimelineAxis",[je,Xe,"../../coord/Axis","../../coord/axisHelper"],function(t){var e=t(Xe),i=t("../../coord/Axis"),n=t("../../coord/axisHelper"),r=function(t,e,n,r){i.call(this,t,e,n),this.type=r||"value",this._autoLabelInterval,this.model=null};return r[He]={constructor:r,getLabelInterval:function(){var t=this.model,i=t[Be](S),r=i.get("interval");if(null!=r&&"auto"!=r)return r;var r=this._autoLabelInterval;return r||(r=this._autoLabelInterval=n.getAxisLabelInterval(e.map(this.scale.getTicks(),this[y],this),n.getFormattedLabels(this,i.get("formatter")),i[Be](ee)[J](),"horizontal"===t.get("orient"))),r},isLabelIgnored:function(t){if(this.type===G){var e=this.getLabelInterval();return typeof e===le&&!e(t,this.scale.getLabel(t))||t%(e+1)}}},e[re](r,i),r}),e("echarts/chart/helper/LinePath",[je,O,We],function(t){function e(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}var i=t(O),n=t(We),r=i.Line[He],a=i.BezierCurve[He];return i.extendShape({type:"ec-line",style:{stroke:"#000",fill:null},shape:{x1:0,y1:0,x2:0,y2:0,percent:1,cpx1:null,cpy1:null},buildPath:function(t,i){(e(i)?r:a).buildPath(t,i)},pointAt:function(t){return e(this.shape)?r.pointAt.call(this,t):a.pointAt.call(this,t)},tangentAt:function(t){var i=this.shape,r=e(i)?[i.x2-i.x1,i.y2-i.y1]:a.tangentAt.call(this,t);return n[U](r,r)}})}),e("zrender",["zrender/zrender"],function(t){return t}),e("echarts",["echarts/echarts"],function(t){return t});var Ye=t("echarts");return Ye.graphic=t("echarts/util/graphic"),Ye.number=t("echarts/util/number"),Ye.format=t("echarts/util/format"),t("echarts/chart/bar"),t("echarts/chart/line"),t("echarts/chart/pie"),t("echarts/component/grid"),t("echarts/component/title"),t("echarts/component/legend"),t("echarts/component/tooltip"),t("echarts/component/markPoint"),t("echarts/component/markLine"),t("echarts/component/markArea"),t("echarts/component/timeline"),t("zrender/vml/vml"),Ye});