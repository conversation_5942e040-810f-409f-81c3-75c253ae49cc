{"version": 3, "sources": ["jquery.contextMenu.min.js"], "names": ["factory", "define", "amd", "exports", "require", "j<PERSON><PERSON><PERSON>", "$", "splitAccesskey", "val", "k", "t", "split", "keys", "i", "char<PERSON>t", "toUpperCase", "push", "inputLabel", "node", "id", "name", "menu<PERSON><PERSON><PERSON>n", "items", "$children", "counter", "each", "label", "item", "$node", "this", "nodeName", "toLowerCase", "find", "length", "text", "children", "first", "get", "attr", "disabled", "callback", "click", "undefined", "icon", "type", "selected", "radio", "value", "options", "html", "clone", "support", "htmlMenuitem", "window", "htmlCommand", "eventSelectstart", "document", "documentElement", "ui", "widget", "cleanData", "orig", "elems", "events", "elem", "_data", "remove", "<PERSON><PERSON><PERSON><PERSON>", "e", "$currentTrigger", "initialized", "$win", "namespaces", "menus", "types", "defaults", "selector", "appendTo", "trigger", "autoHide", "delay", "reposition", "selectableSubMenu", "classNames", "hover", "visible", "notSelectable", "iconEdit", "iconCut", "iconCopy", "iconPaste", "iconDelete", "iconAdd", "iconQuit", "iconLoadingClass", "determinePosition", "$menu", "position", "css", "my", "at", "of", "offset", "collision", "top", "outerHeight", "left", "outerWidth", "opt", "x", "y", "offsetParentOffset", "offsetParent", "bottom", "scrollTop", "height", "right", "scrollLeft", "width", "call", "positionSubmenu", "zIndex", "animation", "duration", "show", "hide", "noop", "hoveract", "timer", "pageX", "pageY", "zindex", "$t", "zin", "$tt", "Math", "max", "parseInt", "parent", "indexOf", "prop", "handle", "abortevent", "preventDefault", "stopImmediatePropagation", "contextmenu", "$this", "data", "originalEvent", "mouseButton", "hasClass", "build", "built", "extend", "isEmptyObject", "console", "error", "log", "Error", "$trigger", "op", "create", "showMenu", "hasOwnProperty", "isFunction", "currentTarget", "Event", "mousedown", "is", "button", "mouseup", "removeData", "mouseenter", "$related", "relatedTarget", "$document", "closest", "on", "mousemove", "setTimeout", "off", "mouseleave", "clearTimeout", "layerClick", "target", "root", "$window", "triggerAction", "elementFromPoint", "$layer", "isContentEditable", "range", "createRange", "sel", "getSelection", "selectNode", "collapse", "removeAllRanges", "addRange", "one", "contextMenu", "keyStop", "isInput", "stopPropagation", "key", "targetZIndex", "getZIndexOfTriggerTarget", "style", "parentElement", "keyCode", "shift<PERSON>ey", "$selected", "blur", "$parent", "itemdata", "String", "fromCharCode", "accesskeys", "prevItem", "$s", "$prev", "prev", "last", "$round", "itemMouseleave", "itemMouseenter", "$input", "focus", "nextItem", "$next", "next", "focusInput", "contextMenuRoot", "blurInput", "menuMouseenter", "hovering", "menuMouseleave", "itemClick", "contextMenuKey", "callbacks", "Object", "prototype", "update", "inputClick", "hideMenu", "force", "focusItem", "addClass", "join", "not", "removeClass", "filter", "blurItem", "additionalZValue", "layer", "pos", "createNameNode", "$name", "_accesskey", "_beforeAccesskey", "append", "createTextNode", "_afterAccesskey", "isHtmlName", "accesskey", "className", "$label", "ak", "aks", "matched", "match", "RegExp", "commands", "hasTypes", "inputs", "prependTo", "then", "processPromises", "_icon", "substring", "body", "resize", "nested", "domMenu", "display", "getBoundingClientRect", "ceil", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "addBack", "$item", "z-index", "opacity", "background-color", "insertBefore", "promise", "errorPromise", "errorItem", "finishPromiseProcess", "bind", "fn", "operation", "$o", "context", "isPlainObject", "o", "$context", "_hasContext", "ns", "itemClickEvent", "contextMenuItemObj", "contextmenu:focus.contextMenu", "contextmenu:blur.contextMenu", "contextmenu.contextMenu", "mouseenter.contextMenu", "mouseleave.contextMenu", "contextmenu:hide.contextMenu", "prevcommand.contextMenu", "nextcommand.contextMenu", "$visibleMenu", "fromMenu", "setInputValues", "getInputValues", "element"], "mappings": "CAkBA,SAAWA,GACe,mBAAXC,QAAyBA,OAAOC,IAEvCD,QAAQ,UAAWD,GAGnBA,EAF0B,iBAAZG,QAENC,QAAQ,UAGRC,QAThB,CAWG,SAAUC,GAET,aA89CA,SAASC,EAAeC,GAIpB,IAAK,IAAWC,EAHZC,EAAIF,EAAIG,MAAM,OACdC,KAEKC,EAAI,EAAMJ,EAAIC,EAAEG,GAAIA,IACzBJ,EAAIA,EAAEK,OAAO,GAAGC,cAGhBH,EAAKI,KAAKP,GAGd,OAAOG,EA0TX,SAASK,EAAWC,GAChB,OAAQA,EAAKC,IAAMb,EAAE,cAAgBY,EAAKC,GAAK,MAAMX,OAAUU,EAAKE,KAIxE,SAASC,EAAaC,EAAOC,EAAWC,GA0KpC,OAzKKA,IACDA,EAAU,GAGdD,EAAUE,KAAK,WACX,IAGIC,EACAC,EAJAC,EAAQtB,EAAEuB,MACVX,EAAOW,KACPC,EAAWD,KAAKC,SAASC,cAoB7B,OAfiB,UAAbD,GAAwBF,EAAMI,KAAK,2BAA2BC,SAC9DP,EAAQE,EAAMM,OAGdJ,GADAZ,GADAU,EAAQA,EAAMO,WAAWC,SACZC,IAAI,IACDP,SAASC,eAWrBD,GAEJ,IAAK,OACDH,GAAQP,KAAMQ,EAAMU,KAAK,SAAUhB,UACnCE,EAAUH,EAAaM,EAAKL,MAAOM,EAAMO,WAAYX,GACrD,MAGJ,IAAK,IAEL,IAAK,SACDG,GACIP,KAAMQ,EAAMM,OACZK,WAAYX,EAAMU,KAAK,YACvBE,SACW,WACHZ,EAAMS,IAAI,GAAGI,UAIzB,MAGJ,IAAK,WACL,IAAK,UACD,OAAQb,EAAMU,KAAK,SACf,UAAKI,EACL,IAAK,UACL,IAAK,WACDf,GACIP,KAAMQ,EAAMU,KAAK,SACjBC,WAAYX,EAAMU,KAAK,YACvBK,KAAMf,EAAMU,KAAK,QACjBE,SACW,WACHZ,EAAMS,IAAI,GAAGI,UAIzB,MAEJ,IAAK,WACDd,GACIiB,KAAM,WACNL,WAAYX,EAAMU,KAAK,YACvBlB,KAAMQ,EAAMU,KAAK,SACjBO,WAAYjB,EAAMU,KAAK,YAE3B,MACJ,IAAK,QACDX,GACIiB,KAAM,QACNL,WAAYX,EAAMU,KAAK,YACvBlB,KAAMQ,EAAMU,KAAK,SACjBQ,MAAOlB,EAAMU,KAAK,cAClBS,MAAOnB,EAAMU,KAAK,MAClBO,WAAYjB,EAAMU,KAAK,YAE3B,MAEJ,QACIX,OAAOe,EAEf,MAEJ,IAAK,KACDf,EAAO,UACP,MAEJ,IAAK,QACD,OAAQC,EAAMU,KAAK,SACf,IAAK,OACDX,GACIiB,KAAM,OACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBS,MAAOnB,EAAMpB,OAEjB,MAEJ,IAAK,WACDmB,GACIiB,KAAM,WACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBO,WAAYjB,EAAMU,KAAK,YAE3B,MAEJ,IAAK,QACDX,GACIiB,KAAM,QACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBQ,QAASlB,EAAMU,KAAK,QACpBS,MAAOnB,EAAMpB,MACbqC,WAAYjB,EAAMU,KAAK,YAE3B,MAEJ,QACIX,OAAOe,EAGf,MAEJ,IAAK,SACDf,GACIiB,KAAM,SACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBO,SAAUjB,EAAMpB,MAChBwC,YAEJpB,EAAMO,WAAWV,KAAK,WAClBE,EAAKqB,QAAQnB,KAAKkB,OAASzC,EAAEuB,MAAMK,SAEvC,MAEJ,IAAK,WACDP,GACIiB,KAAM,WACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBS,MAAOnB,EAAMpB,OAEjB,MAEJ,IAAK,QACD,MAEJ,QACImB,GAAQiB,KAAM,OAAQK,KAAMrB,EAAMsB,OAAM,IAI5CvB,IAEAL,EAAM,SADNE,GACyBG,KAI1BH,EA38DXlB,EAAE6C,QAAQC,aAAgB,wBAAyBC,OACnD/C,EAAE6C,QAAQG,YAAe,uBAAwBD,OACjD/C,EAAE6C,QAAQI,iBAAoB,kBAAmBC,SAASC,gBAwBrDnD,EAAEoD,IAAOpD,EAAEqD,SAEZrD,EAAEsD,UAAY,SAAWC,GACrB,OAAO,SAAUC,GACb,IAAIC,EAAQC,EAAMnD,EAClB,IAAKA,EAAI,EAAe,MAAZiD,EAAMjD,GAAYA,IAAK,CAC/BmD,EAAOF,EAAMjD,GACb,KAEIkD,EAASzD,EAAE2D,MAAMD,EAAM,YACTD,EAAOG,QACjB5D,EAAE0D,GAAMG,eAAe,UAI7B,MAAOC,KAGbP,EAAKC,IAhBC,CAkBXxD,EAAEsD,YAKT,IACIS,EAAkB,KAElBC,GAAc,EAEdC,EAAOjE,EAAE+C,QAET7B,EAAU,EAEVgD,KAEAC,KAEAC,KAEAC,GAEIC,SAAU,KAEVC,SAAU,KAEVC,QAAS,QAETC,UAAU,EAEVC,MAAO,IAGPC,YAAY,EAGZC,mBAAmB,EAGnBC,YACIC,MAAO,qBACP7C,SAAU,wBACV8C,QAAS,uBACTC,cAAe,8BAEf3C,KAAM,oBACN4C,SAAU,yBACVC,QAAS,wBACTC,SAAU,yBACVC,UAAW,0BACXC,WAAY,2BACZC,QAAS,wBACTC,SAAU,yBACVC,iBAAkB,6BAItBC,kBAAmB,SAAUC,GAEzB,GAAI1F,EAAEoD,IAAMpD,EAAEoD,GAAGuC,SAGbD,EAAME,IAAI,UAAW,SAASD,UAC1BE,GAAI,aACJC,GAAI,gBACJC,GAAIxE,KACJyE,OAAQ,MACRC,UAAW,QACZL,IAAI,UAAW,YACf,CAEH,IAAII,EAASzE,KAAKyE,SAClBA,EAAOE,KAAO3E,KAAK4E,cACnBH,EAAOI,MAAQ7E,KAAK8E,aAAe,EAAIX,EAAMW,aAAe,EAC5DX,EAAME,IAAII,KAIlBL,SAAU,SAAUW,EAAKC,EAAGC,GACxB,IAAIR,EAEJ,GAAKO,GAAMC,EAAX,CAGO,GAAU,aAAND,GAA0B,aAANC,EAE3BR,EAASM,EAAIZ,MAAMC,eAChB,CAEH,IAAIc,EAAqBH,EAAIZ,MAAMgB,eAAeV,SAClDA,GAAUE,IAAKM,EAAIC,EAAmBP,IAAKE,KAAMG,EAAGE,EAAmBL,MAI3E,IAAIO,EAAS1C,EAAK2C,YAAc3C,EAAK4C,SACjCC,EAAQ7C,EAAK8C,aAAe9C,EAAK+C,QACjCH,EAASP,EAAIZ,MAAMS,cACnBa,EAAQV,EAAIZ,MAAMW,aAElBL,EAAOE,IAAMW,EAASF,IACtBX,EAAOE,KAAOW,GAGdb,EAAOE,IAAM,IACbF,EAAOE,IAAM,GAGbF,EAAOI,KAAOY,EAAQF,IACtBd,EAAOI,MAAQY,GAGfhB,EAAOI,KAAO,IACdJ,EAAOI,KAAO,GAGlBE,EAAIZ,MAAME,IAAII,QAjCVM,EAAIb,kBAAkBwB,KAAK1F,KAAM+E,EAAIZ,QAoC7CwB,gBAAiB,SAAUxB,GACvB,QAAqB,IAAVA,EAMX,GAAI1F,EAAEoD,IAAMpD,EAAEoD,GAAGuC,SAGbD,EAAME,IAAI,UAAW,SAASD,UAC1BE,GAAI,aACJC,GAAI,YACJC,GAAIxE,KACJ0E,UAAW,gBACZL,IAAI,UAAW,QACf,CAEH,IAAII,GACAE,KAAM,EACNE,KAAM7E,KAAK8E,aAAe,GAE9BX,EAAME,IAAII,KAIlBmB,OAAQ,EAERC,WACIC,SAAU,GACVC,KAAM,YACNC,KAAM,WAGV9D,QACI6D,KAAMtH,EAAEwH,KACRD,KAAMvH,EAAEwH,MAGZtF,SAAU,KAEVlB,UAGJyG,GACIC,MAAO,KACPC,MAAO,KACPC,MAAO,MAGXC,EAAS,SAAUC,GAIf,IAHA,IAAIC,EAAM,EACNC,EAAMF,IAKN,GAFAC,EAAME,KAAKC,IAAIH,EAAKI,SAASH,EAAIpC,IAAI,WAAY,KAAO,KACxDoC,EAAMA,EAAII,YACGJ,EAAIrG,QAAU,YAAY0G,QAAQL,EAAIM,KAAK,YAAY7G,gBAAkB,EAClF,MAGR,OAAOsG,GAGXQ,GAEIC,WAAY,SAAU1E,GAClBA,EAAE2E,iBACF3E,EAAE4E,4BAGNC,YAAa,SAAU7E,GACnB,IAAI8E,EAAQ5I,EAAEuB,MASd,GANuB,UAAnBuC,EAAE+E,KAAKrE,UACPV,EAAE2E,iBACF3E,EAAE4E,8BAIkB,UAAnB5E,EAAE+E,KAAKrE,SAA0C,WAAnBV,EAAE+E,KAAKrE,SAAyBV,EAAEgF,sBAKxC,IAAlBhF,EAAEiF,cAA+BjF,EAAE+E,MACjB,SAAnB/E,EAAE+E,KAAKrE,SAAwC,IAAlBV,EAAEiF,aAA2C,UAAnBjF,EAAE+E,KAAKrE,SAAyC,IAAlBV,EAAEiF,cAO7FH,EAAMI,SAAS,wBAIdJ,EAAMI,SAAS,0BAA0B,CAO1C,GADAjF,EAAkB6E,EACd9E,EAAE+E,KAAKI,MAAO,CACd,IAAIC,EAAQpF,EAAE+E,KAAKI,MAAMlF,EAAiBD,GAE1C,IAAc,IAAVoF,EACA,OAOJ,GAHApF,EAAE+E,KAAO7I,EAAEmJ,QAAO,KAAU9E,EAAUP,EAAE+E,KAAMK,QAGzCpF,EAAE+E,KAAK7H,OAAShB,EAAEoJ,cAActF,EAAE+E,KAAK7H,OAMxC,MAJI+B,OAAOsG,UACNA,QAAQC,OAASD,QAAQE,KAAKtC,KAAKoC,QAAS,6CAG3C,IAAIG,MAAM,sBAIpB1F,EAAE+E,KAAKY,SAAW1F,EAElB2F,EAAGC,OAAO7F,EAAE+E,MAEhB,IAAIe,GAAW,EACf,IAAK,IAAIvI,KAAQyC,EAAE+E,KAAK7H,MACpB,GAAI8C,EAAE+E,KAAK7H,MAAM6I,eAAexI,GAAO,EAE/BrB,EAAE8J,WAAWhG,EAAE+E,KAAK7H,MAAMK,GAAM0D,SACtBjB,EAAE+E,KAAK7H,MAAMK,GAAM0D,QAAQkC,KAAKjH,EAAE8D,EAAEiG,eAAgB1I,EAAMyC,EAAE+E,WACjC,IAAvB/E,EAAE+E,KAAK7H,MAAMK,KAAyByC,EAAE+E,KAAK7H,MAAMK,GAAM0D,UAC9B,IAA/BjB,EAAE+E,KAAK7H,MAAMK,GAAM0D,WAK7B6E,GAAW,GAInBA,GAEAF,EAAGpC,KAAKL,KAAK2B,EAAO9E,EAAE+E,KAAM/E,EAAE6D,MAAO7D,EAAE8D,SAKnDzF,MAAO,SAAU2B,GACbA,EAAE2E,iBACF3E,EAAE4E,2BACF1I,EAAEuB,MAAMiD,QAAQxE,EAAEgK,MAAM,eAAgBnB,KAAM/E,EAAE+E,KAAMlB,MAAO7D,EAAE6D,MAAOC,MAAO9D,EAAE8D,UAGnFqC,UAAW,SAAUnG,GAEjB,IAAI8E,EAAQ5I,EAAEuB,MAGVwC,GAAmBA,EAAgBpC,SAAWoC,EAAgBmG,GAAGtB,IACjE7E,EAAgB8E,KAAK,eAAenD,MAAMlB,QAAQ,oBAIrC,IAAbV,EAAEqG,SACFpG,EAAkB6E,EAAMC,KAAK,qBAAqB,KAI1DuB,QAAS,SAAUtG,GAEf,IAAI8E,EAAQ5I,EAAEuB,MACVqH,EAAMC,KAAK,sBAAwB9E,GAAmBA,EAAgBpC,QAAUoC,EAAgBmG,GAAGtB,KAAWA,EAAMI,SAAS,2BAC7HlF,EAAE2E,iBACF3E,EAAE4E,2BACF3E,EAAkB6E,EAClBA,EAAMpE,QAAQxE,EAAEgK,MAAM,eAAgBnB,KAAM/E,EAAE+E,KAAMlB,MAAO7D,EAAE6D,MAAOC,MAAO9D,EAAE8D,UAGjFgB,EAAMyB,WAAW,sBAGrBC,WAAY,SAAUxG,GAClB,IAAI8E,EAAQ5I,EAAEuB,MACVgJ,EAAWvK,EAAE8D,EAAE0G,eACfC,EAAYzK,EAAEkD,UAGdqH,EAASL,GAAG,uBAAyBK,EAASG,QAAQ,sBAAsB/I,QAK5EoC,GAAmBA,EAAgBpC,SAIvC8F,EAASE,MAAQ7D,EAAE6D,MACnBF,EAASG,MAAQ9D,EAAE8D,MACnBH,EAASoB,KAAO/E,EAAE+E,KAClB4B,EAAUE,GAAG,4BAA6BpC,EAAOqC,WACjDnD,EAASC,MAAQmD,WAAW,WACxBpD,EAASC,MAAQ,KACjB+C,EAAUK,IAAI,6BACd/G,EAAkB6E,EAClBA,EAAMpE,QAAQxE,EAAEgK,MAAM,eAClBnB,KAAMpB,EAASoB,KACflB,MAAOF,EAASE,MAChBC,MAAOH,EAASG,UAErB9D,EAAE+E,KAAKnE,SAGdkG,UAAW,SAAU9G,GACjB2D,EAASE,MAAQ7D,EAAE6D,MACnBF,EAASG,MAAQ9D,EAAE8D,OAGvBmD,WAAY,SAAUjH,GAElB,IAAIyG,EAAWvK,EAAE8D,EAAE0G,eACnB,IAAID,EAASL,GAAG,wBAAyBK,EAASG,QAAQ,sBAAsB/I,OAAhF,CAIA,IACIqJ,aAAavD,EAASC,OACxB,MAAO5D,IAGT2D,EAASC,MAAQ,OAGrBuD,WAAY,SAAUnH,GAClB,IAKIoH,EACAlF,EALAmF,EADQnL,EAAEuB,MACGsH,KAAK,mBAClBsB,EAASrG,EAAEqG,OACX5D,EAAIzC,EAAE6D,MACNnB,EAAI1C,EAAE8D,MAIV9D,EAAE2E,iBAEFoC,WAAW,WACP,IAAIO,EACAC,EAAmC,SAAjBF,EAAK3G,SAAiC,IAAX2F,GAAmC,UAAjBgB,EAAK3G,SAAkC,IAAX2F,EAG/F,GAAIjH,SAASoI,kBAAoBH,EAAKI,OAAQ,CAM1C,GALAJ,EAAKI,OAAOhE,QACZ2D,EAAShI,SAASoI,iBAAiB/E,EAAItC,EAAK8C,aAAcP,EAAIvC,EAAK2C,cAIxD4E,kBAAmB,CAC1B,IAAIC,EAAQvI,SAASwI,cACjBC,EAAM5I,OAAO6I,eACjBH,EAAMI,WAAWX,GACjBO,EAAMK,UAAS,GACfH,EAAII,kBACJJ,EAAIK,SAASP,GAEjBzL,EAAEkL,GAAQ1G,QAAQV,GAClBqH,EAAKI,OAAOjE,OAGhB,GAAI6D,EAAKxG,YAAc0G,EACnB,GAAInI,SAASoI,kBACT,GAAIH,EAAK1B,SAASS,GAAGgB,GAEjB,YADAC,EAAKxF,SAASsB,KAAKkE,EAAK1B,SAAU0B,EAAM5E,EAAGC,QAS/C,GALAR,EAASmF,EAAK1B,SAASzD,SACvBoF,EAAUpL,EAAE+C,QAGZiD,EAAOE,KAAOkF,EAAQxE,YAClBZ,EAAOE,KAAOpC,EAAE8D,QAChB5B,EAAOI,MAAQgF,EAAQrE,aACnBf,EAAOI,MAAQtC,EAAE6D,QACjB3B,EAAOW,OAASX,EAAOE,IAAMiF,EAAK1B,SAAStD,cACvCH,EAAOW,QAAU7C,EAAE8D,QACnB5B,EAAOc,MAAQd,EAAOI,KAAO+E,EAAK1B,SAASpD,aACvCL,EAAOc,OAAShD,EAAE6D,SAGlB,YADAwD,EAAKxF,SAASsB,KAAKkE,EAAK1B,SAAU0B,EAAM5E,EAAGC,GAS/D0E,GAAUG,GACVF,EAAK1B,SAASwC,IAAI,qBAAsB,WACpCjM,EAAEkL,GAAQgB,aAAa3F,EAAGA,EAAGC,EAAGA,EAAG2D,OAAQA,MAItC,OAATgB,QAAiC,IAATA,GAAuC,OAAfA,EAAKzF,YAAyC,IAAfyF,EAAKzF,OACpFyF,EAAKzF,MAAMlB,QAAQ,qBAExB,KAGP2H,QAAS,SAAUrI,EAAGwC,GACbA,EAAI8F,SACLtI,EAAE2E,iBAGN3E,EAAEuI,mBAENC,IAAK,SAAUxI,GAEX,IAAIwC,KAGAvC,IACAuC,EAAMvC,EAAgB8E,KAAK,yBAGL,IAAfvC,EAAIa,SACXb,EAAIa,OAAS,GAEjB,IAAIoF,EAAe,EACfC,EAA2B,SAAUtB,GACT,KAAxBA,EAAOuB,MAAMtF,OACboF,EAAerB,EAAOuB,MAAMtF,OAEA,OAAxB+D,EAAOxE,mBAAwD,IAAxBwE,EAAOxE,aAC9C8F,EAAyBtB,EAAOxE,cAEF,OAAzBwE,EAAOwB,oBAA0D,IAAzBxB,EAAOwB,eACpDF,EAAyBtB,EAAOwB,gBAQ5C,GAJAF,EAAyB1I,EAAEoH,UAIvB5E,EAAIZ,OAASyC,SAASoE,EAAa,IAAMpE,SAAS7B,EAAIZ,MAAME,IAAI,UAAU,KAA9E,CAGA,OAAQ9B,EAAE6I,SACN,KAAK,EACL,KAAK,GAGD,GAFApE,EAAO4D,QAAQrI,EAAGwC,GAEdA,EAAI8F,QAAS,CACb,GAAkB,IAAdtI,EAAE6I,SAAiB7I,EAAE8I,SAQrB,OAPA9I,EAAE2E,iBACEnC,EAAIuG,WACJvG,EAAIuG,UAAUnL,KAAK,2BAA2BoL,YAEhC,OAAdxG,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMlB,QAAQ,gBAGnB,GAAkB,KAAdV,EAAE6I,SAAiF,aAA/DrG,EAAIuG,UAAUnL,KAAK,2BAA2B4G,KAAK,QAG9E,YADAxE,EAAE2E,sBAGH,GAAkB,IAAd3E,EAAE6I,SAAiB7I,EAAE8I,SAI5B,YAHkB,OAAdtG,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMlB,QAAQ,gBAI1B,MAGJ,KAAK,GAED,GADA+D,EAAO4D,QAAQrI,EAAGwC,IACdA,EAAI8F,QAmBJ,YAHkB,OAAd9F,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMlB,QAAQ,gBAhBtB,GAAkB,IAAdV,EAAE6I,QAQF,OAPA7I,EAAE2E,iBACEnC,EAAIuG,WACJvG,EAAIuG,UAAUnL,KAAK,2BAA2BoL,YAEhC,OAAdxG,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMlB,QAAQ,gBAGnB,GAAkB,KAAdV,EAAE6I,SAAiF,aAA/DrG,EAAIuG,UAAUnL,KAAK,2BAA2B4G,KAAK,QAG9E,YADAxE,EAAE2E,iBASV,MAEJ,KAAK,GAED,GADAF,EAAO4D,QAAQrI,EAAGwC,GACdA,EAAI8F,UAAY9F,EAAIuG,YAAcvG,EAAIuG,UAAUlL,OAChD,MAGJ,IAAK2E,EAAIuG,UAAUzE,SAASY,SAAS,qBAAsB,CACvD,IAAI+D,EAAUzG,EAAIuG,UAAUzE,SAASA,SAGrC,OAFA9B,EAAIuG,UAAUrI,QAAQ,yBACtB8B,EAAIuG,UAAYE,GAGpB,MAEJ,KAAK,GAED,GADAxE,EAAO4D,QAAQrI,EAAGwC,GACdA,EAAI8F,UAAY9F,EAAIuG,YAAcvG,EAAIuG,UAAUlL,OAChD,MAGJ,IAAIqL,EAAW1G,EAAIuG,UAAUhE,KAAK,mBAClC,GAAImE,EAAStH,OAASY,EAAIuG,UAAU7D,SAAS,wBAIzC,OAHA1C,EAAIuG,UAAY,KAChBG,EAASH,UAAY,UACrBG,EAAStH,MAAMlB,QAAQ,eAG3B,MAEJ,KAAK,GACL,KAAK,GACD,OAAI8B,EAAIuG,WAAavG,EAAIuG,UAAUnL,KAAK,2BAA2BC,YAC/D,IAEC2E,EAAIuG,WAAavG,EAAIuG,UAAUzE,UAAY9B,EAAIZ,OAC3C7D,SAAS,SAAWyE,EAAIzB,WAAW5C,SAAW,MAAQqE,EAAIzB,WAAWG,cAAgB,KAAmB,KAAdlB,EAAE6I,QAAiB,QAAU,UACvHnI,QAAQ,0BACbV,EAAE2E,kBAKV,KAAK,GAED,GADAF,EAAO4D,QAAQrI,EAAGwC,GACdA,EAAI8F,QAAS,CACb,GAAI9F,EAAIuG,YAAcvG,EAAIuG,UAAU3C,GAAG,oBAEnC,YADApG,EAAE2E,iBAGN,MAKJ,iBAH6B,IAAlBnC,EAAIuG,WAA+C,OAAlBvG,EAAIuG,WAC5CvG,EAAIuG,UAAUrI,QAAQ,YAI9B,KAAK,GACL,KAAK,GACL,KAAK,GAGD,YADA+D,EAAO4D,QAAQrI,EAAGwC,GAGtB,KAAK,GAKD,OAJAiC,EAAO4D,QAAQrI,EAAGwC,QACA,OAAdA,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMlB,QAAQ,qBAI1B,QACI,IAAIrE,EAAK8M,OAAOC,aAAapJ,EAAE6I,SAAUlM,cACzC,GAAI6F,EAAI6G,YAAc7G,EAAI6G,WAAWhN,GAGjC,YADAmG,EAAI6G,WAAWhN,GAAGmB,MAAMkD,QAAQ8B,EAAI6G,WAAWhN,GAAGuF,MAAQ,oBAAsB,WAO5F5B,EAAEuI,uBAC2B,IAAlB/F,EAAIuG,WAA+C,OAAlBvG,EAAIuG,WAC5CvG,EAAIuG,UAAUrI,QAAQV,KAI9BsJ,SAAU,SAAUtJ,GAChBA,EAAEuI,kBACF,IAAI/F,EAAMtG,EAAEuB,MAAMsH,KAAK,mBACnBsC,EAAOnL,EAAEuB,MAAMsH,KAAK,uBAGxB,GAAIvC,EAAIuG,UAAW,CACf,IAAIQ,EAAK/G,EAAIuG,WACbvG,EAAMA,EAAIuG,UAAUzE,SAASS,KAAK,oBAC9BgE,UAAYQ,EAQpB,IALA,IAAIpM,EAAYqF,EAAIZ,MAAM7D,WACtByL,EAAShH,EAAIuG,WAAcvG,EAAIuG,UAAUU,OAAO5L,OAA4B2E,EAAIuG,UAAUU,OAAjCtM,EAAUuM,OACnEC,EAASH,EAGNA,EAAMtE,SAASmC,EAAKtG,WAAW5C,WAAaqL,EAAMtE,SAASmC,EAAKtG,WAAWG,gBAAkBsI,EAAMpD,GAAG,YAMzG,IAJIoD,EADAA,EAAMC,OAAO5L,OACL2L,EAAMC,OAENtM,EAAUuM,QAEZtD,GAAGuD,GAET,OAKJnH,EAAIuG,WACJtE,EAAOmF,eAAezG,KAAKX,EAAIuG,UAAU9K,IAAI,GAAI+B,GAIrDyE,EAAOoF,eAAe1G,KAAKqG,EAAMvL,IAAI,GAAI+B,GAGzC,IAAI8J,EAASN,EAAM5L,KAAK,2BACpBkM,EAAOjM,QACPiM,EAAOC,SAIfC,SAAU,SAAUhK,GAChBA,EAAEuI,kBACF,IAAI/F,EAAMtG,EAAEuB,MAAMsH,KAAK,mBACnBsC,EAAOnL,EAAEuB,MAAMsH,KAAK,uBAGxB,GAAIvC,EAAIuG,UAAW,CACf,IAAIQ,EAAK/G,EAAIuG,WACbvG,EAAMA,EAAIuG,UAAUzE,SAASS,KAAK,oBAC9BgE,UAAYQ,EAQpB,IALA,IAAIpM,EAAYqF,EAAIZ,MAAM7D,WACtBkM,EAASzH,EAAIuG,WAAcvG,EAAIuG,UAAUmB,OAAOrM,OAA6B2E,EAAIuG,UAAUmB,OAAlC/M,EAAUa,QACnE2L,EAASM,EAGNA,EAAM/E,SAASmC,EAAKtG,WAAW5C,WAAa8L,EAAM/E,SAASmC,EAAKtG,WAAWG,gBAAkB+I,EAAM7D,GAAG,YAMzG,IAJI6D,EADAA,EAAMC,OAAOrM,OACLoM,EAAMC,OAEN/M,EAAUa,SAEZoI,GAAGuD,GAET,OAKJnH,EAAIuG,WACJtE,EAAOmF,eAAezG,KAAKX,EAAIuG,UAAU9K,IAAI,GAAI+B,GAIrDyE,EAAOoF,eAAe1G,KAAK8G,EAAMhM,IAAI,GAAI+B,GAGzC,IAAI8J,EAASG,EAAMrM,KAAK,2BACpBkM,EAAOjM,QACPiM,EAAOC,SAIfI,WAAY,WACR,IAAIrF,EAAQ5I,EAAEuB,MAAMmJ,QAAQ,sBACxB7B,EAAOD,EAAMC,OACbvC,EAAMuC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEhB/C,EAAK0B,UAAYvG,EAAIuG,UAAYjE,EACjCuC,EAAKiB,QAAU9F,EAAI8F,SAAU,GAGjC+B,UAAW,WACP,IACItF,EADQ7I,EAAEuB,MAAMmJ,QAAQ,sBACX7B,OACbvC,EAAMuC,EAAKqD,YACJrD,EAAKqF,gBAEX9B,QAAU9F,EAAI8F,SAAU,GAGjCgC,eAAgB,WACDpO,EAAEuB,MAAMsH,OAAOqF,gBACrBG,UAAW,GAGpBC,eAAgB,SAAUxK,GACtB,IAAIqH,EAAOnL,EAAEuB,MAAMsH,OAAOqF,gBACtB/C,EAAKI,QAAUJ,EAAKI,OAAOrB,GAAGpG,EAAE0G,iBAChCW,EAAKkD,UAAW,IAIxBV,eAAgB,SAAU7J,GACtB,IAAI8E,EAAQ5I,EAAEuB,MACVsH,EAAOD,EAAMC,OACbvC,EAAMuC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEhB/C,EAAKkD,UAAW,EAGZvK,GAAKqH,EAAKI,QAAUJ,EAAKI,OAAOrB,GAAGpG,EAAE0G,iBACrC1G,EAAE2E,iBACF3E,EAAE4E,6BAILpC,EAAIZ,MAAQY,EAAM6E,GAAMzF,MACpB7D,SAAS,IAAMsJ,EAAKtG,WAAWC,OAAON,QAAQ,oBAC9C3C,SAAS,UAAU2C,QAAQ,oBAE5BoE,EAAMI,SAASmC,EAAKtG,WAAW5C,WAAa2G,EAAMI,SAASmC,EAAKtG,WAAWG,eAC3EsB,EAAIuG,UAAY,KAKpBjE,EAAMpE,QAAQ,sBAGlBkJ,eAAgB,SAAU5J,GACtB,IAAI8E,EAAQ5I,EAAEuB,MACVsH,EAAOD,EAAMC,OACbvC,EAAMuC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEhB,GAAI/C,IAAS7E,GAAO6E,EAAKI,QAAUJ,EAAKI,OAAOrB,GAAGpG,EAAE0G,eAOhD,YAN8B,IAAnBW,EAAK0B,WAAgD,OAAnB1B,EAAK0B,WAC9C1B,EAAK0B,UAAUrI,QAAQ,oBAE3BV,EAAE2E,iBACF3E,EAAE4E,gCACFyC,EAAK0B,UAAYvG,EAAIuG,UAAYvG,EAAIhF,OAItCgF,GAAOA,EAAIZ,OAASY,EAAIZ,MAAMsD,SAAS,yBAI1CJ,EAAMpE,QAAQ,qBAGlB+J,UAAW,SAAUzK,GACjB,IAKI5B,EALA0G,EAAQ5I,EAAEuB,MACVsH,EAAOD,EAAMC,OACbvC,EAAMuC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBACZ5B,EAAMzD,EAAK2F,eAIf,MAAKlI,EAAItF,MAAMsL,IAAQ1D,EAAMsB,GAAG,IAAMiB,EAAKtG,WAAW5C,SAAW,+BAAiCkJ,EAAKtG,WAAWG,gBAAmB4D,EAAMsB,GAAG,2BAAuD,IAA3BiB,EAAKvG,mBAA/K,CAOA,GAHAd,EAAE2E,iBACF3E,EAAE4E,2BAEE1I,EAAE8J,WAAWxD,EAAImI,UAAUnC,KAASoC,OAAOC,UAAU9E,eAAe5C,KAAKX,EAAImI,UAAWnC,GAExFpK,EAAWoE,EAAImI,UAAUnC,OACtB,CAAA,IAAItM,EAAE8J,WAAWqB,EAAKjJ,UAKzB,OAHAA,EAAWiJ,EAAKjJ,UAO+B,IAA/CA,EAAS+E,KAAKkE,EAAK1B,SAAU6C,EAAKnB,EAAMrH,GACxCqH,EAAKzF,MAAMlB,QAAQ,oBACZ2G,EAAKzF,MAAM0C,SAASzG,QAC3B+H,EAAGkF,OAAO3H,KAAKkE,EAAK1B,SAAU0B,KAItC0D,WAAY,SAAU/K,GAClBA,EAAE4E,4BAGNoG,SAAU,SAAUhL,EAAG+E,GACnB,IAAIsC,EAAOnL,EAAEuB,MAAMsH,KAAK,mBACxBa,EAAGnC,KAAKN,KAAKkE,EAAK1B,SAAU0B,EAAMtC,GAAQA,EAAKkG,QAGnDC,UAAW,SAAUlL,GACjBA,EAAEuI,kBACF,IAAIzD,EAAQ5I,EAAEuB,MACVsH,EAAOD,EAAMC,OACbvC,EAAMuC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEZtF,EAAMI,SAASmC,EAAKtG,WAAW5C,WAAa2G,EAAMI,SAASmC,EAAKtG,WAAWG,iBAI/E4D,EACKqG,UAAU9D,EAAKtG,WAAWC,MAAOqG,EAAKtG,WAAWE,SAASmK,KAAK,MAE/D9G,SAAS1G,KAAK,sBAAsByN,IAAIvG,GACxCwG,YAAYjE,EAAKtG,WAAWE,SAC5BsK,OAAO,IAAMlE,EAAKtG,WAAWC,OAC7BN,QAAQ,oBAGb8B,EAAIuG,UAAY1B,EAAK0B,UAAYjE,EAG9BtC,GAAOA,EAAIhF,OAASgF,EAAIhF,MAAM0H,SAAS,yBACtC1C,EAAIhF,MAAM2N,SAAS9D,EAAKtG,WAAWC,OAInCwB,EAAIhF,OACJ6J,EAAKjE,gBAAgBD,KAAKX,EAAIhF,MAAOgF,EAAIZ,SAIjD4J,SAAU,SAAUxL,GAChBA,EAAEuI,kBACF,IAAIzD,EAAQ5I,EAAEuB,MACVsH,EAAOD,EAAMC,OACbvC,EAAMuC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEZ5H,EAAI7B,UACJmE,EAAMwG,YAAYjE,EAAKtG,WAAWE,SAEtC6D,EAAMwG,YAAYjE,EAAKtG,WAAWC,OAClCwB,EAAIuG,UAAY,OAIxBnD,GACIpC,KAAM,SAAUhB,EAAKC,EAAGC,GACpB,IAAIiD,EAAWzJ,EAAEuB,MACbqE,KASJ,GANA5F,EAAE,uBAAuBwE,QAAQ,aAGjC8B,EAAImD,SAAWA,GAG6B,IAAxCnD,EAAI7C,OAAO6D,KAAKL,KAAKwC,EAAUnD,GAAnC,CAYA,GANAoD,EAAGkF,OAAO3H,KAAKwC,EAAUnD,GAGzBA,EAAIX,SAASsB,KAAKwC,EAAUnD,EAAKC,EAAGC,GAGhCF,EAAIa,OAAQ,CACZ,IAAIoI,EAAmBjJ,EAAIa,OAED,mBAAfb,EAAIa,SACXoI,EAAmBjJ,EAAIa,OAAOF,KAAKwC,EAAUnD,IAEjDV,EAAIuB,OAASU,EAAO4B,GAAY8F,EAIpC7F,EAAG8F,MAAMvI,KAAKX,EAAIZ,MAAOY,EAAKV,EAAIuB,QAGlCb,EAAIZ,MAAMhE,KAAK,MAAMkE,IAAI,SAAUA,EAAIuB,OAAS,GAGhDb,EAAIZ,MAAME,IAAIA,GAAKU,EAAIc,UAAUE,MAAMhB,EAAIc,UAAUC,SAAU,WAC3DoC,EAASjF,QAAQ,yBAGrBiF,EACKZ,KAAK,cAAevC,GACpB2I,SAAS,uBAGdjP,EAAEkD,UAAU4H,IAAI,uBAAuBH,GAAG,sBAAuBpC,EAAO+D,KAEpEhG,EAAI7B,UAEJzE,EAAEkD,UAAUyH,GAAG,gCAAiC,SAAU7G,GAGtD,IAAI2L,EAAMhG,EAASzD,SACnByJ,EAAI3I,MAAQ2I,EAAIrJ,KAAOqD,EAASpD,aAChCoJ,EAAI9I,OAAS8I,EAAIvJ,IAAMuD,EAAStD,eAE5BG,EAAIiF,QAAWjF,EAAI+H,UAAevK,EAAE6D,OAAS8H,EAAIrJ,MAAQtC,EAAE6D,OAAS8H,EAAI3I,OAAYhD,EAAE8D,OAAS6H,EAAIvJ,KAAOpC,EAAE8D,OAAS6H,EAAI9I,QAEzHkE,WAAW,WACFvE,EAAI+H,UAA0B,OAAd/H,EAAIZ,YAAuC,IAAdY,EAAIZ,OAClDY,EAAIZ,MAAMlB,QAAQ,qBAEvB,WArDXT,EAAkB,MA0D1BwD,KAAM,SAAUjB,EAAKyI,GACjB,IAAItF,EAAWzJ,EAAEuB,MAMjB,GALK+E,IACDA,EAAMmD,EAASZ,KAAK,oBAInBkG,IAASzI,EAAI7C,SAAkD,IAAxC6C,EAAI7C,OAAO8D,KAAKN,KAAKwC,EAAUnD,GAA3D,CASA,GAJAmD,EACKY,WAAW,eACX+E,YAAY,uBAEb9I,EAAIiF,OAAQ,CAEZV,WAAW,SAAWU,GAClB,OAAO,WACHA,EAAO3H,UAFJ,CAIR0C,EAAIiF,QAAS,IAEhB,WACWjF,EAAIiF,OACb,MAAOzH,GACLwC,EAAIiF,OAAS,MAKrBxH,EAAkB,KAElBuC,EAAIZ,MAAMhE,KAAK,IAAM4E,EAAIzB,WAAWC,OAAON,QAAQ,oBACnD8B,EAAIuG,UAAY,KAEhBvG,EAAIZ,MAAMhE,KAAK,IAAM4E,EAAIzB,WAAWE,SAASqK,YAAY9I,EAAIzB,WAAWE,SAGxE/E,EAAEkD,UAAU4H,IAAI,wBAAwBA,IAAI,uBAExCxE,EAAIZ,OACJY,EAAIZ,MAAMY,EAAIc,UAAUG,MAAMjB,EAAIc,UAAUC,SAAU,WAE9Cf,EAAI2C,QACJ3C,EAAIZ,MAAM9B,SACV5D,EAAEmB,KAAKmF,EAAK,SAAUgG,GAClB,OAAQA,GACJ,IAAK,KACL,IAAK,WACL,IAAK,QACL,IAAK,UACD,OAAO,EAEX,QACIhG,EAAIgG,QAAOlK,EACX,WACWkE,EAAIgG,GACb,MAAOxI,IAET,OAAO,MAKvB+G,WAAW,WACPpB,EAASjF,QAAQ,uBAClB,QAIfmF,OAAQ,SAAUrD,EAAK6E,GAsBnB,SAASuE,EAAerO,GACpB,IAAIsO,EAAQ3P,EAAE,iBACd,GAAIqB,EAAKuO,WACDvO,EAAKwO,kBACLF,EAAMG,OAAO5M,SAAS6M,eAAe1O,EAAKwO,mBAE9C7P,EAAE,iBACGiP,SAAS,0BACTrN,KAAKP,EAAKuO,YACVrL,SAASoL,GACVtO,EAAK2O,iBACLL,EAAMG,OAAO5M,SAAS6M,eAAe1O,EAAK2O,uBAG9C,GAAI3O,EAAK4O,WAAY,CAEjB,QAA8B,IAAnB5O,EAAK6O,UACZ,MAAM,IAAI1G,MAAM,8FAEpBmG,EAAMhN,KAAKtB,EAAKP,WAEhB6O,EAAM/N,KAAKP,EAAKP,MAGxB,OAAO6O,OA7CS,IAATxE,IACPA,EAAO7E,GAIXA,EAAIZ,MAAQ1F,EAAE,uCAAuCiP,SAAS3I,EAAI6J,WAAa,IAAItH,MAC/EqD,YAAe5F,EACf4H,gBAAmB/C,IAGvBnL,EAAEmB,MAAM,YAAa,WAAY,UAAW,SAAUZ,EAAGJ,GACrDmG,EAAInG,MACCgL,EAAKhL,KACNgL,EAAKhL,SAIRgL,EAAKgC,aACNhC,EAAKgC,eA+BTnN,EAAEmB,KAAKmF,EAAItF,MAAO,SAAUsL,EAAKjL,GAC7B,IAAIyG,EAAK9H,EAAE,uCAAuCiP,SAAS5N,EAAK8O,WAAa,IACzEC,EAAS,KACTxC,EAAS,KAqBb,GAjBA9F,EAAG6C,GAAG,QAAS3K,EAAEwH,MAKG,iBAATnG,GAAmC,iBAAdA,EAAKiB,OACjCjB,GAAQiB,KAAM,iBAGlBjB,EAAKC,MAAQwG,EAAGe,MACZqD,YAAe5F,EACf4H,gBAAmB/C,EACnBqD,eAAkBlC,SAKQ,IAAnBjL,EAAK6O,UAEZ,IAAK,IAAWG,EADZC,EAAMrQ,EAAeoB,EAAK6O,WACrB3P,EAAI,EAAO8P,EAAKC,EAAI/P,GAAIA,IAC7B,IAAK4K,EAAKgC,WAAWkD,GAAK,CACtBlF,EAAKgC,WAAWkD,GAAMhP,EACtB,IAAIkP,EAAUlP,EAAKP,KAAK0P,MAAM,IAAIC,OAAO,UAAYJ,EAAK,SAAU,MAChEE,IACAlP,EAAKwO,iBAAmBU,EAAQ,GAChClP,EAAKuO,WAAaW,EAAQ,GAC1BlP,EAAK2O,gBAAkBO,EAAQ,IAEnC,MAKZ,GAAIlP,EAAKiB,MAAQ8B,EAAM/C,EAAKiB,MAExB8B,EAAM/C,EAAKiB,MAAM2E,KAAKa,EAAIzG,EAAMiF,EAAK6E,GAErCnL,EAAEmB,MAAMmF,EAAK6E,GAAO,SAAU5K,EAAGJ,GAC7BA,EAAEuQ,SAASpE,GAAOjL,GAGdrB,EAAE8J,WAAWzI,EAAKa,gBAA0C,IAArB/B,EAAEsO,UAAUnC,SAA4C,IAAbhG,EAAIhE,OACtFnC,EAAEsO,UAAUnC,GAAOjL,EAAKa,gBAG7B,CAsBH,OApBkB,iBAAdb,EAAKiB,KACLwF,EAAGmH,SAAS,0BAA4B9D,EAAKtG,WAAWG,eACnC,SAAd3D,EAAKiB,KACZwF,EAAGmH,SAAS,qBAAuB9D,EAAKtG,WAAWG,eAC9B,QAAd3D,EAAKiB,OAELjB,EAAKiB,MACZ8N,EAASpQ,EAAE,mBAAmBuE,SAASuD,GACvC4H,EAAerO,GAAMkD,SAAS6L,GAE9BtI,EAAGmH,SAAS,sBACZ3I,EAAIqK,UAAW,EACf3Q,EAAEmB,MAAMmF,EAAK6E,GAAO,SAAU5K,EAAGJ,GAC7BA,EAAEuQ,SAASpE,GAAOjL,EAClBlB,EAAEyQ,OAAOtE,GAAOjL,KAEbA,EAAKL,QACZK,EAAKiB,KAAO,QAGRjB,EAAKiB,MACT,IAAK,eACD,MAEJ,IAAK,OACDsL,EAAS5N,EAAE,2CACNgC,KAAK,OAAQ,sBAAwBsK,GACrCpM,IAAImB,EAAKoB,OAAS,IAClB8B,SAAS6L,GACd,MAEJ,IAAK,WACDxC,EAAS5N,EAAE,iCACNgC,KAAK,OAAQ,sBAAwBsK,GACrCpM,IAAImB,EAAKoB,OAAS,IAClB8B,SAAS6L,GAEV/O,EAAKwF,QACL+G,EAAO/G,OAAOxF,EAAKwF,QAEvB,MAEJ,IAAK,WACD+G,EAAS5N,EAAE,+CACNgC,KAAK,OAAQ,sBAAwBsK,GACrCpM,IAAImB,EAAKoB,OAAS,IAClB6F,KAAK,YAAajH,EAAKkB,UACvBsO,UAAUT,GACf,MAEJ,IAAK,QACDxC,EAAS5N,EAAE,4CACNgC,KAAK,OAAQ,sBAAwBX,EAAKmB,OAC1CtC,IAAImB,EAAKoB,OAAS,IAClB6F,KAAK,YAAajH,EAAKkB,UACvBsO,UAAUT,GACf,MAEJ,IAAK,SACDxC,EAAS5N,EAAE,6BACNgC,KAAK,OAAQ,sBAAwBsK,GACrC/H,SAAS6L,GACV/O,EAAKqB,UACL1C,EAAEmB,KAAKE,EAAKqB,QAAS,SAAUD,EAAOb,GAClC5B,EAAE,qBAAqBE,IAAIuC,GAAOb,KAAKA,GAAM2C,SAASqJ,KAE1DA,EAAO1N,IAAImB,EAAKkB,WAEpB,MAEJ,IAAK,MACDmN,EAAerO,GAAMkD,SAASuD,GAC9BzG,EAAKkD,SAAWlD,EAAKC,MACrBwG,EAAGe,KAAK,cAAexH,GAAM4N,SAAS,wBACtC5N,EAAKa,SAAW,KAKZ,mBAAsBb,EAAKL,MAAM8P,KAEjCpH,EAAGqH,gBAAgB1P,EAAM8J,EAAM9J,EAAKL,OAGpC0I,EAAGC,OAAOtI,EAAM8J,GAEpB,MAEJ,IAAK,OACDnL,EAAEqB,EAAKsB,MAAM4B,SAASuD,GACtB,MAEJ,QACI9H,EAAEmB,MAAMmF,EAAK6E,GAAO,SAAU5K,EAAGJ,GAC7BA,EAAEuQ,SAASpE,GAAOjL,GAGdrB,EAAE8J,WAAWzI,EAAKa,gBAA0C,IAArB/B,EAAEsO,UAAUnC,SAA4C,IAAbhG,EAAIhE,OACtFnC,EAAEsO,UAAUnC,GAAOjL,EAAKa,YAGhCwN,EAAerO,GAAMkD,SAASuD,GAKlCzG,EAAKiB,MAAsB,QAAdjB,EAAKiB,MAAgC,SAAdjB,EAAKiB,MAAiC,iBAAdjB,EAAKiB,OACjEsL,EACKjD,GAAG,QAASpC,EAAO0F,YACnBtD,GAAG,OAAQpC,EAAO4F,WAEnB9M,EAAKoC,QACLmK,EAAOjD,GAAGtJ,EAAKoC,OAAQ6C,IAK3BjF,EAAKgB,OACDrC,EAAE8J,WAAWzI,EAAKgB,MAClBhB,EAAK2P,MAAQ3P,EAAKgB,KAAK4E,KAAK1F,KAAMA,KAAMuG,EAAIwE,EAAKjL,GAEvB,iBAAfA,EAAS,MAAgD,QAA9BA,EAAKgB,KAAK4O,UAAU,EAAG,GAEzD5P,EAAK2P,MAAQ7F,EAAKtG,WAAWxC,KAAO,IAAM8I,EAAKtG,WAAWxC,KAAO,WAAahB,EAAKgB,KAEnFhB,EAAK2P,MAAQ7F,EAAKtG,WAAWxC,KAAO,IAAM8I,EAAKtG,WAAWxC,KAAO,IAAMhB,EAAKgB,KAGpFyF,EAAGmH,SAAS5N,EAAK2P,QAKzB3P,EAAKuM,OAASA,EACdvM,EAAK+O,OAASA,EAGdtI,EAAGvD,SAAS+B,EAAIZ,QAGXY,EAAIqK,UAAY3Q,EAAE6C,QAAQI,kBAI3B6E,EAAG6C,GAAG,gCAAiCpC,EAAOC,cAIjDlC,EAAIhF,OACLgF,EAAIZ,MAAME,IAAI,UAAW,QAAQqJ,SAAS,qBAE9C3I,EAAIZ,MAAMnB,SAAS+B,EAAI/B,UAAYrB,SAASgO,OAEhDC,OAAQ,SAAUzL,EAAO0L,GACrB,IAAIC,EAMJ3L,EAAME,KAAKD,SAAU,WAAY2L,QAAS,UAE1C5L,EAAMmD,KAAK,SACNwI,EAAU3L,EAAM3D,IAAI,IAAIwP,sBACrBtJ,KAAKuJ,KAAKH,EAAQE,wBAAwBvK,OAC1CtB,EAAMW,aAAe,GAE7BX,EAAME,KACFD,SAAU,SACV8L,SAAU,MACVC,SAAU,aAGdhM,EAAMhE,KAAK,aAAaP,KAAK,WACzBuI,EAAGyH,OAAOnR,EAAEuB,OAAO,KAIlB6P,GACD1L,EAAMhE,KAAK,MAAMiQ,UAAU/L,KACvBD,SAAU,GACV2L,QAAS,GACTG,SAAU,GACVC,SAAU,KACXrL,WAAW,WACV,OAAOrG,EAAEuB,MAAMsH,KAAK,YAIhC+F,OAAQ,SAAUtI,EAAK6E,GACnB,IAAI1B,EAAWlI,UACK,IAAT4J,IACPA,EAAO7E,EACPoD,EAAGyH,OAAO7K,EAAIZ,QAGlBY,EAAIZ,MAAM7D,WAAWV,KAAK,WACtB,IAII4D,EAJA6M,EAAQ5R,EAAEuB,MACV+K,EAAMsF,EAAM/I,KAAK,kBACjBxH,EAAOiF,EAAItF,MAAMsL,GACjBrK,EAAYjC,EAAE8J,WAAWzI,EAAKY,WAAaZ,EAAKY,SAASgF,KAAKwC,EAAU6C,EAAKnB,KAA4B,IAAlB9J,EAAKY,SAoBhG,GAjBI8C,EADA/E,EAAE8J,WAAWzI,EAAK0D,SACR1D,EAAK0D,QAAQkC,KAAKwC,EAAU6C,EAAKnB,QACZ,IAAjB9J,EAAK0D,UACQ,IAAjB1D,EAAK0D,QAInB6M,EAAM7M,EAAU,OAAS,UAGzB6M,EAAM3P,EAAW,WAAa,eAAekJ,EAAKtG,WAAW5C,UAEzDjC,EAAE8J,WAAWzI,EAAKgB,QAClBuP,EAAMxC,YAAY/N,EAAK2P,OACvB3P,EAAK2P,MAAQ3P,EAAKgB,KAAK4E,KAAK1F,KAAMkI,EAAUmI,EAAOtF,EAAKjL,GACxDuQ,EAAM3C,SAAS5N,EAAK2P,QAGpB3P,EAAKiB,KAKL,OAHAsP,EAAMlQ,KAAK,2BAA2B4G,KAAK,WAAYrG,GAG/CZ,EAAKiB,MACT,IAAK,OACL,IAAK,WACDjB,EAAKuM,OAAO1N,IAAImB,EAAKoB,OAAS,IAC9B,MAEJ,IAAK,WACL,IAAK,QACDpB,EAAKuM,OAAO1N,IAAImB,EAAKoB,OAAS,IAAI6F,KAAK,YAAajH,EAAKkB,UACzD,MAEJ,IAAK,SACDlB,EAAKuM,OAAO1N,KAAuB,IAAlBmB,EAAKkB,SAAiB,IAAMlB,EAAKkB,WAAa,IAKvElB,EAAKqE,OAELgE,EAAGkF,OAAO3H,KAAKwC,EAAUpI,EAAM8J,MAI3CqE,MAAO,SAAUlJ,EAAKa,GAGlB,IAAIoE,EAASjF,EAAIiF,OAASvL,EAAE,uCACvB4F,KACGiB,OAAQ5C,EAAK4C,SACbG,MAAO/C,EAAK+C,QACZsK,QAAS,QACT3L,SAAU,QACVkM,UAAW1K,EACXjB,IAAK,EACLE,KAAM,EACN0L,QAAS,EACTzC,OAAQ,mBACR0C,mBAAoB,SAEvBlJ,KAAK,kBAAmBvC,GACxB0L,aAAazQ,MACboJ,GAAG,cAAepC,EAAOC,YACzBmC,GAAG,YAAapC,EAAO0C,YAU5B,YAP4C,IAAjC/H,SAASgO,KAAKzE,MAAMiF,UAC3BnG,EAAO3F,KACHD,SAAY,WACZkB,OAAU7G,EAAEkD,UAAU2D,WAIvB0E,GAEXwF,gBAAiB,SAAUzK,EAAK6E,EAAM8G,GAclC,SAASC,EAAa5L,EAAK6E,EAAMgH,QAEJ,IAAdA,GACPA,GACI7I,OACIxI,KAAM,6BACNuB,KAAM,6CAGVU,OAAOsG,UACNA,QAAQC,OAASD,QAAQE,KAAKtC,KAAKoC,QAAS,yFAErB,iBAAd8I,IACdA,GAAa7I,OAAUxI,KAAMqR,KAEjCC,EAAqB9L,EAAK6E,EAAMgH,GAGpC,SAASC,EAAqB9L,EAAK6E,EAAMnK,QACX,IAAfmK,EAAKzF,OAA0ByF,EAAKzF,MAAMwE,GAAG,cAGxD5D,EAAIhF,MAAM8N,YAAYjE,EAAKtG,WAAWW,kBACtCc,EAAItF,MAAQA,EACZ0I,EAAGC,OAAOrD,EAAK6E,GAAM,GACrBzB,EAAGkF,OAAOtI,EAAK6E,GACfA,EAAKjE,gBAAgBD,KAAKX,EAAIhF,MAAOgF,EAAIZ,QAtC7CY,EAAIhF,MAAM2N,SAAS9D,EAAKtG,WAAWW,kBA2CnCyM,EAAQnB,KAzCR,SAA0BxK,EAAK6E,EAAMnK,QAGZ,IAAVA,GAEPkR,OAAa9P,GAEjBgQ,EAAqB9L,EAAK6E,EAAMnK,IAkCNqR,KAAK9Q,KAAM+E,EAAK6E,GAAO+G,EAAaG,KAAK9Q,KAAM+E,EAAK6E,MAoB9FnL,EAAEsS,GAAGpG,YAAc,SAAUqG,GACzB,IAAIzK,EAAKvG,KAAMiR,EAAKD,EACpB,GAAIhR,KAAKI,OAAS,EACd,QAAyB,IAAd4Q,EACPhR,KAAKO,QAAQ0C,QAAQ,oBAClB,QAA2B,IAAhB+N,EAAUhM,QAA4C,IAAhBgM,EAAU/L,EAC9DjF,KAAKO,QAAQ0C,QAAQxE,EAAEgK,MAAM,eACzBrC,MAAO4K,EAAUhM,EACjBqB,MAAO2K,EAAU/L,EACjBuC,YAAawJ,EAAUpI,eAExB,GAAkB,SAAdoI,EAAsB,CAC7B,IAAI7M,EAAQnE,KAAKO,QAAQ+G,KAAK,eAAiBtH,KAAKO,QAAQ+G,KAAK,eAAenD,MAAQ,KACpFA,GACAA,EAAMlB,QAAQ,wBAEG,YAAd+N,EACPvS,EAAEkM,YAAY,WAAYuG,QAASlR,OAC5BvB,EAAE0S,cAAcH,IACvBA,EAAUE,QAAUlR,KACpBvB,EAAEkM,YAAY,SAAUqG,IACjBA,EACPhR,KAAK6N,YAAY,yBACTmD,GACRhR,KAAK0N,SAAS,8BAGlBjP,EAAEmB,KAAKgD,EAAO,WACN5C,KAAK+C,WAAawD,EAAGxD,WACrBkO,EAAG3J,KAAOtH,KAEVvB,EAAEmJ,OAAOqJ,EAAG3J,MAAOrE,QAAS,cAIpC+D,EAAOI,YAAY1B,KAAKuL,EAAGtH,OAAQsH,GAGvC,OAAOjR,MAIXvB,EAAEkM,YAAc,SAAUqG,EAAW7P,GACR,iBAAd6P,IACP7P,EAAU6P,EACVA,EAAY,UAGO,iBAAZ7P,EACPA,GAAW4B,SAAU5B,QACK,IAAZA,IACdA,MAIJ,IAAIiQ,EAAI3S,EAAEmJ,QAAO,KAAU9E,EAAU3B,OACjC+H,EAAYzK,EAAEkD,UACd0P,EAAWnI,EACXoI,GAAc,EAWlB,OATKF,EAAEF,SAAYE,EAAEF,QAAQ9Q,QAIzBiR,EAAW5S,EAAE2S,EAAEF,SAAS3Q,QACxB6Q,EAAEF,QAAUG,EAAS7Q,IAAI,GACzB8Q,GAAe7S,EAAE2S,EAAEF,SAASvI,GAAGhH,WAL/ByP,EAAEF,QAAUvP,SAQRqP,GACJ,IAAK,SAED,IAAKI,EAAErO,SACH,MAAM,IAAIkF,MAAM,yBAGpB,GAAImJ,EAAErO,SAASkM,MAAM,yCACjB,MAAM,IAAIhH,MAAM,4BAA8BmJ,EAAErO,SAAW,yCAE/D,IAAKqO,EAAE1J,SAAW0J,EAAE3R,OAAShB,EAAEoJ,cAAcuJ,EAAE3R,QAC3C,MAAM,IAAIwI,MAAM,sBAcpB,GAZAtI,IACAyR,EAAEG,GAAK,eAAiB5R,EACnB2R,IACD3O,EAAWyO,EAAErO,UAAYqO,EAAEG,IAE/B3O,EAAMwO,EAAEG,IAAMH,EAGTA,EAAEnO,UACHmO,EAAEnO,QAAU,UAGXR,EAAa,CACd,IAAIuK,EAAiC,UAArBoE,EAAEI,eAA6B,oBAAsB,sBACjEC,GAGAC,gCAAiC1K,EAAOyG,UACxCkE,+BAAgC3K,EAAO+G,SACvC6D,0BAA2B5K,EAAOC,WAClC4K,yBAA0B7K,EAAOoF,eACjC0F,yBAA0B9K,EAAOmF,gBAErCsF,EAAmBzE,GAAahG,EAAOgG,UAEvC9D,EACKE,IACG2I,+BAAgC/K,EAAOuG,SACvCyE,0BAA2BhL,EAAO6E,SAClCoG,0BAA2BjL,EAAOuF,SAClCqF,0BAA2B5K,EAAOC,WAClC4K,yBAA0B7K,EAAO6F,eACjCiF,yBAA0B9K,EAAO+F,gBAClC,sBACF3D,GAAG,sBAAuB,sBAAuBpC,EAAOsG,YACxDlE,GAAGqI,EAAoB,sBAE5BhP,GAAc,EAclB,OAVA4O,EACKjI,GAAG,cAAgBgI,EAAEG,GAAIH,EAAErO,SAAUqO,EAAGpK,EAAOI,aAEhDkK,GAEAD,EAASjI,GAAG,SAAWgI,EAAEG,GAAI,WACzB9S,EAAEuB,MAAM2K,YAAY,aAIpByG,EAAEnO,SACN,IAAK,QACDoO,EACKjI,GAAG,aAAegI,EAAEG,GAAIH,EAAErO,SAAUqO,EAAGpK,EAAO+B,YAC9CK,GAAG,aAAegI,EAAEG,GAAIH,EAAErO,SAAUqO,EAAGpK,EAAOwC,YACnD,MAEJ,IAAK,OACD6H,EAASjI,GAAG,QAAUgI,EAAEG,GAAIH,EAAErO,SAAUqO,EAAGpK,EAAOpG,OAClD,MAChB,IAAK,aACWyQ,EAASjI,GAAG,aAAegI,EAAEG,GAAIH,EAAErO,SAAUqO,EAAGpK,EAAOpG,OAa1DwQ,EAAE1J,OACHS,EAAGC,OAAOgJ,GAEd,MAEJ,IAAK,UACD,IAAIc,EACJ,GAAIZ,EAAa,CAEb,IAAIJ,EAAUE,EAAEF,QAChBzS,EAAEmB,KAAKgD,EAAO,SAAU2O,EAAIH,GAExB,IAAKA,EACD,OAAO,EAIX,IAAK3S,EAAEyS,GAASvI,GAAGyI,EAAErO,UACjB,OAAO,GAGXmP,EAAezT,EAAE,sBAAsBqP,OAAO,aAC7B1N,QAAU8R,EAAa5K,OAAOqF,gBAAgBzE,SAASS,GAAGlK,EAAE2S,EAAEF,SAAS/Q,KAAKiR,EAAErO,YAC3FmP,EAAajP,QAAQ,oBAAqBuK,OAAO,IAGrD,IACQ5K,EAAMwO,EAAEG,IAAIpN,OACZvB,EAAMwO,EAAEG,IAAIpN,MAAM9B,gBAGfO,EAAMwO,EAAEG,IACjB,MAAOhP,GACLK,EAAMwO,EAAEG,IAAM,KAKlB,OAFA9S,EAAE2S,EAAEF,SAAS3H,IAAI6H,EAAEG,KAEZ,SAER,GAAKH,EAAErO,UAYP,GAAIJ,EAAWyO,EAAErO,UAAW,EAC/BmP,EAAezT,EAAE,sBAAsBqP,OAAO,aAC7B1N,QAAU8R,EAAa5K,OAAOqF,gBAAgBzE,SAASS,GAAGyI,EAAErO,WACzEmP,EAAajP,QAAQ,oBAAqBuK,OAAO,IAGrD,IACQ5K,EAAMD,EAAWyO,EAAErO,WAAWoB,OAC9BvB,EAAMD,EAAWyO,EAAErO,WAAWoB,MAAM9B,gBAGjCO,EAAMD,EAAWyO,EAAErO,WAC5B,MAAOR,GACLK,EAAMD,EAAWyO,EAAErO,WAAa,KAGpCmG,EAAUK,IAAI5G,EAAWyO,EAAErO,iBA3B3BmG,EAAUK,IAAI,qCACd9K,EAAEmB,KAAKgD,EAAO,SAAU2O,EAAIH,GACxB3S,EAAE2S,EAAEF,SAAS3H,IAAI6H,EAAEG,MAGvB5O,KACAC,KACAjD,EAAU,EACV8C,GAAc,EAEdhE,EAAE,2CAA2C4D,SAmBjD,MAEJ,IAAK,UAIK5D,EAAE6C,QAAQG,cAAgBhD,EAAE6C,QAAQC,cAAqC,kBAAZJ,GAAyBA,IACxF1C,EAAE,wBAAwBmB,KAAK,WACvBI,KAAKV,IACLb,EAAEkM,aACE5H,SAAU,gBAAkB/C,KAAKV,GAAK,IACtCG,MAAOhB,EAAEkM,YAAYwH,SAASnS,UAGvCqE,IAAI,UAAW,QAEtB,MAEJ,QACI,MAAM,IAAI4D,MAAM,sBAAwB+I,EAAY,KAG5D,OAAOhR,MAIXvB,EAAEkM,YAAYyH,eAAiB,SAAUrN,EAAKuC,QACtB,IAATA,IACPA,MAGJ7I,EAAEmB,KAAKmF,EAAIsK,OAAQ,SAAUtE,EAAKjL,GAC9B,OAAQA,EAAKiB,MACT,IAAK,OACL,IAAK,WACDjB,EAAKoB,MAAQoG,EAAKyD,IAAQ,GAC1B,MAEJ,IAAK,WACDjL,EAAKkB,WAAWsG,EAAKyD,GACrB,MAEJ,IAAK,QACDjL,EAAKkB,UAAYsG,EAAKxH,EAAKmB,QAAU,MAAQnB,EAAKoB,MAClD,MAEJ,IAAK,SACDpB,EAAKkB,SAAWsG,EAAKyD,IAAQ,OAO7CtM,EAAEkM,YAAY0H,eAAiB,SAAUtN,EAAKuC,GAyB1C,YAxBoB,IAATA,IACPA,MAGJ7I,EAAEmB,KAAKmF,EAAIsK,OAAQ,SAAUtE,EAAKjL,GAC9B,OAAQA,EAAKiB,MACT,IAAK,OACL,IAAK,WACL,IAAK,SACDuG,EAAKyD,GAAOjL,EAAKuM,OAAO1N,MACxB,MAEJ,IAAK,WACD2I,EAAKyD,GAAOjL,EAAKuM,OAAOtF,KAAK,WAC7B,MAEJ,IAAK,QACGjH,EAAKuM,OAAOtF,KAAK,aACjBO,EAAKxH,EAAKmB,OAASnB,EAAKoB,UAMjCoG,GAuLX7I,EAAEkM,YAAYwH,SAAW,SAAUG,GAC/B,IACI7S,KAIJ,OAFAD,EAAaC,EAHDhB,EAAE6T,GAGYhS,YAEnBb,GAIXhB,EAAEkM,YAAY7H,SAAWA,EACzBrE,EAAEkM,YAAY9H,MAAQA,EAEtBpE,EAAEkM,YAAY3D,OAASA,EACvBvI,EAAEkM,YAAYxC,GAAKA,EACnB1J,EAAEkM,YAAY/H,MAAQA", "file": "jquery.contextMenu.min.js", "sourcesContent": ["/**\r\n * jQuery contextMenu v@VERSION - Plugin for simple contextMenu handling\r\n *\r\n * Version: v@VERSION\r\n *\r\n * Authors: <AUTHORS>\n * Web: http://swisnl.github.io/jQuery-contextMenu/\r\n *\r\n * Copyright (c) 2011-@YEAR SWIS BV and contributors\r\n *\r\n * Licensed under\r\n *   MIT License http://www.opensource.org/licenses/mit-license\r\n *\r\n * Date: @DATE\r\n */\r\n\r\n// jscs:disable\r\n/* jshint ignore:start */\r\n(function (factory) {\r\n    if (typeof define === 'function' && define.amd) {\r\n        // AMD. Register as anonymous module.\r\n        define(['jquery'], factory);\r\n    } else if (typeof exports === 'object') {\r\n        // Node / CommonJS\r\n        factory(require('jquery'));\r\n    } else {\r\n        // Browser globals.\r\n        factory(jQuery);\r\n    }\r\n})(function ($) {\r\n\r\n    'use strict';\r\n\r\n    // TODO: -\r\n    // ARIA stuff: menuitem, menuitemcheckbox und menuitemradio\r\n    // create <menu> structure if $.support[htmlCommand || htmlMenuitem] and !opt.disableNative\r\n\r\n    // determine html5 compatibility\r\n    $.support.htmlMenuitem = ('HTMLMenuItemElement' in window);\r\n    $.support.htmlCommand = ('HTMLCommandElement' in window);\r\n    $.support.eventSelectstart = ('onselectstart' in document.documentElement);\r\n    /* // should the need arise, test for css user-select\r\n     $.support.cssUserSelect = (function(){\r\n     var t = false,\r\n     e = document.createElement('div');\r\n\r\n     $.each('Moz|Webkit|Khtml|O|ms|Icab|'.split('|'), function(i, prefix) {\r\n     var propCC = prefix + (prefix ? 'U' : 'u') + 'serSelect',\r\n     prop = (prefix ? ('-' + prefix.toLowerCase() + '-') : '') + 'user-select';\r\n\r\n     e.style.cssText = prop + ': text;';\r\n     if (e.style[propCC] == 'text') {\r\n     t = true;\r\n     return false;\r\n     }\r\n\r\n     return true;\r\n     });\r\n\r\n     return t;\r\n     })();\r\n     */\r\n\r\n\r\n    if (!$.ui || !$.widget) {\r\n        // duck punch $.cleanData like jQueryUI does to get that remove event\r\n        $.cleanData = (function (orig) {\r\n            return function (elems) {\r\n                var events, elem, i;\r\n                for (i = 0; elems[i] != null; i++) {\r\n                    elem = elems[i];\r\n                    try {\r\n                        // Only trigger remove when necessary to save time\r\n                        events = $._data(elem, 'events');\r\n                        if (events && events.remove) {\r\n                            $(elem).triggerHandler('remove');\r\n                        }\r\n\r\n                        // Http://bugs.jquery.com/ticket/8235\r\n                    } catch (e) {\r\n                    }\r\n                }\r\n                orig(elems);\r\n            };\r\n        })($.cleanData);\r\n    }\r\n    /* jshint ignore:end */\r\n    // jscs:enable\r\n\r\n    var // currently active contextMenu trigger\r\n        $currentTrigger = null,\r\n        // is contextMenu initialized with at least one menu?\r\n        initialized = false,\r\n        // window handle\r\n        $win = $(window),\r\n        // number of registered menus\r\n        counter = 0,\r\n        // mapping selector to namespace\r\n        namespaces = {},\r\n        // mapping namespace to options\r\n        menus = {},\r\n        // custom command type handlers\r\n        types = {},\r\n        // default values\r\n        defaults = {\r\n            // selector of contextMenu trigger\r\n            selector: null,\r\n            // where to append the menu to\r\n            appendTo: null,\r\n            // method to trigger context menu [\"right\", \"left\", \"hover\"]\r\n            trigger: 'right',\r\n            // hide menu when mouse leaves trigger / menu elements\r\n            autoHide: false,\r\n            // ms to wait before showing a hover-triggered context menu\r\n            delay: 200,\r\n            // flag denoting if a second trigger should simply move (true) or rebuild (false) an open menu\r\n            // as long as the trigger happened on one of the trigger-element's child nodes\r\n            reposition: true,\r\n\r\n            //ability to select submenu\r\n            selectableSubMenu: false,\r\n\r\n            // Default classname configuration to be able avoid conflicts in frameworks\r\n            classNames: {\r\n                hover: 'context-menu-hover', // Item hover\r\n                disabled: 'context-menu-disabled', // Item disabled\r\n                visible: 'context-menu-visible', // Item visible\r\n                notSelectable: 'context-menu-not-selectable', // Item not selectable\r\n\r\n                icon: 'context-menu-icon',\r\n                iconEdit: 'context-menu-icon-edit',\r\n                iconCut: 'context-menu-icon-cut',\r\n                iconCopy: 'context-menu-icon-copy',\r\n                iconPaste: 'context-menu-icon-paste',\r\n                iconDelete: 'context-menu-icon-delete',\r\n                iconAdd: 'context-menu-icon-add',\r\n                iconQuit: 'context-menu-icon-quit',\r\n                iconLoadingClass: 'context-menu-icon-loading'\r\n            },\r\n\r\n            // determine position to show menu at\r\n            determinePosition: function ($menu) {\r\n                // position to the lower middle of the trigger element\r\n                if ($.ui && $.ui.position) {\r\n                    // .position() is provided as a jQuery UI utility\r\n                    // (...and it won't work on hidden elements)\r\n                    $menu.css('display', 'block').position({\r\n                        my: 'center top',\r\n                        at: 'center bottom',\r\n                        of: this,\r\n                        offset: '0 5',\r\n                        collision: 'fit'\r\n                    }).css('display', 'none');\r\n                } else {\r\n                    // determine contextMenu position\r\n                    var offset = this.offset();\r\n                    offset.top += this.outerHeight();\r\n                    offset.left += this.outerWidth() / 2 - $menu.outerWidth() / 2;\r\n                    $menu.css(offset);\r\n                }\r\n            },\r\n            // position menu\r\n            position: function (opt, x, y) {\r\n                var offset;\r\n                // determine contextMenu position\r\n                if (!x && !y) {\r\n                    opt.determinePosition.call(this, opt.$menu);\r\n                    return;\r\n                } else if (x === 'maintain' && y === 'maintain') {\r\n                    // x and y must not be changed (after re-show on command click)\r\n                    offset = opt.$menu.position();\r\n                } else {\r\n                    // x and y are given (by mouse event)\r\n                    var offsetParentOffset = opt.$menu.offsetParent().offset();\r\n                    offset = {top: y - offsetParentOffset.top, left: x -offsetParentOffset.left};\r\n                }\r\n\r\n                // correct offset if viewport demands it\r\n                var bottom = $win.scrollTop() + $win.height(),\r\n                    right = $win.scrollLeft() + $win.width(),\r\n                    height = opt.$menu.outerHeight(),\r\n                    width = opt.$menu.outerWidth();\r\n\r\n                if (offset.top + height > bottom) {\r\n                    offset.top -= height;\r\n                }\r\n\r\n                if (offset.top < 0) {\r\n                    offset.top = 0;\r\n                }\r\n\r\n                if (offset.left + width > right) {\r\n                    offset.left -= width;\r\n                }\r\n\r\n                if (offset.left < 0) {\r\n                    offset.left = 0;\r\n                }\r\n\r\n                opt.$menu.css(offset);\r\n            },\r\n            // position the sub-menu\r\n            positionSubmenu: function ($menu) {\r\n                if (typeof $menu === 'undefined') {\r\n                    // When user hovers over item (which has sub items) handle.focusItem will call this.\r\n                    // but the submenu does not exist yet if opt.items is a promise. just return, will\r\n                    // call positionSubmenu after promise is completed.\r\n                    return;\r\n                }\r\n                if ($.ui && $.ui.position) {\r\n                    // .position() is provided as a jQuery UI utility\r\n                    // (...and it won't work on hidden elements)\r\n                    $menu.css('display', 'block').position({\r\n                        my: 'left top-5',\r\n                        at: 'right top',\r\n                        of: this,\r\n                        collision: 'flipfit fit'\r\n                    }).css('display', '');\r\n                } else {\r\n                    // determine contextMenu position\r\n                    var offset = {\r\n                        top: -9,\r\n                        left: this.outerWidth() - 5\r\n                    };\r\n                    $menu.css(offset);\r\n                }\r\n            },\r\n            // offset to add to zIndex\r\n            zIndex: 1,\r\n            // show hide animation settings\r\n            animation: {\r\n                duration: 50,\r\n                show: 'slideDown',\r\n                hide: 'slideUp'\r\n            },\r\n            // events\r\n            events: {\r\n                show: $.noop,\r\n                hide: $.noop\r\n            },\r\n            // default callback\r\n            callback: null,\r\n            // list of contextMenu items\r\n            items: {}\r\n        },\r\n        // mouse position for hover activation\r\n        hoveract = {\r\n            timer: null,\r\n            pageX: null,\r\n            pageY: null\r\n        },\r\n        // determine zIndex\r\n        zindex = function ($t) {\r\n            var zin = 0,\r\n                $tt = $t;\r\n\r\n            while (true) {\r\n                zin = Math.max(zin, parseInt($tt.css('z-index'), 10) || 0);\r\n                $tt = $tt.parent();\r\n                if (!$tt || !$tt.length || 'html body'.indexOf($tt.prop('nodeName').toLowerCase()) > -1) {\r\n                    break;\r\n                }\r\n            }\r\n            return zin;\r\n        },\r\n        // event handlers\r\n        handle = {\r\n            // abort anything\r\n            abortevent: function (e) {\r\n                e.preventDefault();\r\n                e.stopImmediatePropagation();\r\n            },\r\n            // contextmenu show dispatcher\r\n            contextmenu: function (e) {\r\n                var $this = $(this);\r\n\r\n                // disable actual context-menu if we are using the right mouse button as the trigger\r\n                if (e.data.trigger === 'right') {\r\n                    e.preventDefault();\r\n                    e.stopImmediatePropagation();\r\n                }\r\n\r\n                // abort native-triggered events unless we're triggering on right click\r\n                if ((e.data.trigger !== 'right' && e.data.trigger !== 'demand') && e.originalEvent) {\r\n                    return;\r\n                }\r\n\r\n                // Let the current contextmenu decide if it should show or not based on its own trigger settings\r\n                if (typeof e.mouseButton !== 'undefined' && e.data) {\r\n                    if (!(e.data.trigger === 'left' && e.mouseButton === 0) && !(e.data.trigger === 'right' && e.mouseButton === 2)) {\r\n                        // Mouse click is not valid.\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // abort event if menu is visible for this trigger\r\n                if ($this.hasClass('context-menu-active')) {\r\n                    return;\r\n                }\r\n\r\n                if (!$this.hasClass('context-menu-disabled')) {\r\n                    // theoretically need to fire a show event at <menu>\r\n                    // http://www.whatwg.org/specs/web-apps/current-work/multipage/interactive-elements.html#context-menus\r\n                    // var evt = jQuery.Event(\"show\", { data: data, pageX: e.pageX, pageY: e.pageY, relatedTarget: this });\r\n                    // e.data.$menu.trigger(evt);\r\n\r\n                    $currentTrigger = $this;\r\n                    if (e.data.build) {\r\n                        var built = e.data.build($currentTrigger, e);\r\n                        // abort if build() returned false\r\n                        if (built === false) {\r\n                            return;\r\n                        }\r\n\r\n                        // dynamically build menu on invocation\r\n                        e.data = $.extend(true, {}, defaults, e.data, built || {});\r\n\r\n                        // abort if there are no items to display\r\n                        if (!e.data.items || $.isEmptyObject(e.data.items)) {\r\n                            // Note: jQuery captures and ignores errors from event handlers\r\n                            if (window.console) {\r\n                                (console.error || console.log).call(console, 'No items specified to show in contextMenu');\r\n                            }\r\n\r\n                            throw new Error('No Items specified');\r\n                        }\r\n\r\n                        // backreference for custom command type creation\r\n                        e.data.$trigger = $currentTrigger;\r\n\r\n                        op.create(e.data);\r\n                    }\r\n                    var showMenu = false;\r\n                    for (var item in e.data.items) {\r\n                        if (e.data.items.hasOwnProperty(item)) {\r\n                            var visible;\r\n                            if ($.isFunction(e.data.items[item].visible)) {\r\n                                visible = e.data.items[item].visible.call($(e.currentTarget), item, e.data);\r\n                            } else if (typeof e.data.items[item] !== 'undefined' && e.data.items[item].visible) {\r\n                                visible = e.data.items[item].visible === true;\r\n                            } else {\r\n                                visible = true;\r\n                            }\r\n                            if (visible) {\r\n                                showMenu = true;\r\n                            }\r\n                        }\r\n                    }\r\n                    if (showMenu) {\r\n                        // show menu\r\n                        op.show.call($this, e.data, e.pageX, e.pageY);\r\n                    }\r\n                }\r\n            },\r\n            // contextMenu left-click trigger\r\n            click: function (e) {\r\n                e.preventDefault();\r\n                e.stopImmediatePropagation();\r\n                $(this).trigger($.Event('contextmenu', {data: e.data, pageX: e.pageX, pageY: e.pageY}));\r\n            },\r\n            // contextMenu right-click trigger\r\n            mousedown: function (e) {\r\n                // register mouse down\r\n                var $this = $(this);\r\n\r\n                // hide any previous menus\r\n                if ($currentTrigger && $currentTrigger.length && !$currentTrigger.is($this)) {\r\n                    $currentTrigger.data('contextMenu').$menu.trigger('contextmenu:hide');\r\n                }\r\n\r\n                // activate on right click\r\n                if (e.button === 2) {\r\n                    $currentTrigger = $this.data('contextMenuActive', true);\r\n                }\r\n            },\r\n            // contextMenu right-click trigger\r\n            mouseup: function (e) {\r\n                // show menu\r\n                var $this = $(this);\r\n                if ($this.data('contextMenuActive') && $currentTrigger && $currentTrigger.length && $currentTrigger.is($this) && !$this.hasClass('context-menu-disabled')) {\r\n                    e.preventDefault();\r\n                    e.stopImmediatePropagation();\r\n                    $currentTrigger = $this;\r\n                    $this.trigger($.Event('contextmenu', {data: e.data, pageX: e.pageX, pageY: e.pageY}));\r\n                }\r\n\r\n                $this.removeData('contextMenuActive');\r\n            },\r\n            // contextMenu hover trigger\r\n            mouseenter: function (e) {\r\n                var $this = $(this),\r\n                    $related = $(e.relatedTarget),\r\n                    $document = $(document);\r\n\r\n                // abort if we're coming from a menu\r\n                if ($related.is('.context-menu-list') || $related.closest('.context-menu-list').length) {\r\n                    return;\r\n                }\r\n\r\n                // abort if a menu is shown\r\n                if ($currentTrigger && $currentTrigger.length) {\r\n                    return;\r\n                }\r\n\r\n                hoveract.pageX = e.pageX;\r\n                hoveract.pageY = e.pageY;\r\n                hoveract.data = e.data;\r\n                $document.on('mousemove.contextMenuShow', handle.mousemove);\r\n                hoveract.timer = setTimeout(function () {\r\n                    hoveract.timer = null;\r\n                    $document.off('mousemove.contextMenuShow');\r\n                    $currentTrigger = $this;\r\n                    $this.trigger($.Event('contextmenu', {\r\n                        data: hoveract.data,\r\n                        pageX: hoveract.pageX,\r\n                        pageY: hoveract.pageY\r\n                    }));\r\n                }, e.data.delay);\r\n            },\r\n            // contextMenu hover trigger\r\n            mousemove: function (e) {\r\n                hoveract.pageX = e.pageX;\r\n                hoveract.pageY = e.pageY;\r\n            },\r\n            // contextMenu hover trigger\r\n            mouseleave: function (e) {\r\n                // abort if we're leaving for a menu\r\n                var $related = $(e.relatedTarget);\r\n                if ($related.is('.context-menu-list') || $related.closest('.context-menu-list').length) {\r\n                    return;\r\n                }\r\n\r\n                try {\r\n                    clearTimeout(hoveract.timer);\r\n                } catch (e) {\r\n                }\r\n\r\n                hoveract.timer = null;\r\n            },\r\n            // click on layer to hide contextMenu\r\n            layerClick: function (e) {\r\n                var $this = $(this),\r\n                    root = $this.data('contextMenuRoot'),\r\n                    button = e.button,\r\n                    x = e.pageX,\r\n                    y = e.pageY,\r\n                    target,\r\n                    offset;\r\n\r\n                e.preventDefault();\r\n\r\n                setTimeout(function () {\r\n                    var $window;\r\n                    var triggerAction = ((root.trigger === 'left' && button === 0) || (root.trigger === 'right' && button === 2));\r\n\r\n                    // find the element that would've been clicked, wasn't the layer in the way\r\n                    if (document.elementFromPoint && root.$layer) {\r\n                        root.$layer.hide();\r\n                        target = document.elementFromPoint(x - $win.scrollLeft(), y - $win.scrollTop());\r\n\r\n                        // also need to try and focus this element if we're in a contenteditable area,\r\n                        // as the layer will prevent the browser mouse action we want\r\n                        if (target.isContentEditable) {\r\n                            var range = document.createRange(),\r\n                                sel = window.getSelection();\r\n                            range.selectNode(target);\r\n                            range.collapse(true);\r\n                            sel.removeAllRanges();\r\n                            sel.addRange(range);\r\n                        }\r\n                        $(target).trigger(e);\r\n                        root.$layer.show();\r\n                    }\r\n\r\n                    if (root.reposition && triggerAction) {\r\n                        if (document.elementFromPoint) {\r\n                            if (root.$trigger.is(target)) {\r\n                                root.position.call(root.$trigger, root, x, y);\r\n                                return;\r\n                            }\r\n                        } else {\r\n                            offset = root.$trigger.offset();\r\n                            $window = $(window);\r\n                            // while this looks kinda awful, it's the best way to avoid\r\n                            // unnecessarily calculating any positions\r\n                            offset.top += $window.scrollTop();\r\n                            if (offset.top <= e.pageY) {\r\n                                offset.left += $window.scrollLeft();\r\n                                if (offset.left <= e.pageX) {\r\n                                    offset.bottom = offset.top + root.$trigger.outerHeight();\r\n                                    if (offset.bottom >= e.pageY) {\r\n                                        offset.right = offset.left + root.$trigger.outerWidth();\r\n                                        if (offset.right >= e.pageX) {\r\n                                            // reposition\r\n                                            root.position.call(root.$trigger, root, x, y);\r\n                                            return;\r\n                                        }\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (target && triggerAction) {\r\n                        root.$trigger.one('contextmenu:hidden', function () {\r\n                            $(target).contextMenu({x: x, y: y, button: button});\r\n                        });\r\n                    }\r\n\r\n                    if (root !== null && typeof root !== 'undefined' && root.$menu !== null  && typeof root.$menu !== 'undefined') {\r\n                        root.$menu.trigger('contextmenu:hide');\r\n                    }\r\n                }, 50);\r\n            },\r\n            // key handled :hover\r\n            keyStop: function (e, opt) {\r\n                if (!opt.isInput) {\r\n                    e.preventDefault();\r\n                }\r\n\r\n                e.stopPropagation();\r\n            },\r\n            key: function (e) {\r\n\r\n                var opt = {};\r\n\r\n                // Only get the data from $currentTrigger if it exists\r\n                if ($currentTrigger) {\r\n                    opt = $currentTrigger.data('contextMenu') || {};\r\n                }\r\n                // If the trigger happen on a element that are above the contextmenu do this\r\n                if (typeof opt.zIndex === 'undefined') {\r\n                    opt.zIndex = 0;\r\n                }\r\n                var targetZIndex = 0;\r\n                var getZIndexOfTriggerTarget = function (target) {\r\n                    if (target.style.zIndex !== '') {\r\n                        targetZIndex = target.style.zIndex;\r\n                    } else {\r\n                        if (target.offsetParent !== null && typeof target.offsetParent !== 'undefined') {\r\n                            getZIndexOfTriggerTarget(target.offsetParent);\r\n                        }\r\n                        else if (target.parentElement !== null && typeof target.parentElement !== 'undefined') {\r\n                            getZIndexOfTriggerTarget(target.parentElement);\r\n                        }\r\n                    }\r\n                };\r\n                getZIndexOfTriggerTarget(e.target);\r\n                // If targetZIndex is heigher then opt.zIndex dont progress any futher.\r\n                // This is used to make sure that if you are using a dialog with a input / textarea / contenteditable div\r\n                // and its above the contextmenu it wont steal keys events\r\n                if (opt.$menu && parseInt(targetZIndex,10) > parseInt(opt.$menu.css(\"zIndex\"),10)) {\r\n                    return;\r\n                }\r\n                switch (e.keyCode) {\r\n                    case 9:\r\n                    case 38: // up\r\n                        handle.keyStop(e, opt);\r\n                        // if keyCode is [38 (up)] or [9 (tab) with shift]\r\n                        if (opt.isInput) {\r\n                            if (e.keyCode === 9 && e.shiftKey) {\r\n                                e.preventDefault();\r\n                                if (opt.$selected) {\r\n                                    opt.$selected.find('input, textarea, select').blur();\r\n                                }\r\n                                if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                    opt.$menu.trigger('prevcommand');\r\n                                }\r\n                                return;\r\n                            } else if (e.keyCode === 38 && opt.$selected.find('input, textarea, select').prop('type') === 'checkbox') {\r\n                                // checkboxes don't capture this key\r\n                                e.preventDefault();\r\n                                return;\r\n                            }\r\n                        } else if (e.keyCode !== 9 || e.shiftKey) {\r\n                            if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                opt.$menu.trigger('prevcommand');\r\n                            }\r\n                            return;\r\n                        }\r\n                        break;\r\n                    // omitting break;\r\n                    // case 9: // tab - reached through omitted break;\r\n                    case 40: // down\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.isInput) {\r\n                            if (e.keyCode === 9) {\r\n                                e.preventDefault();\r\n                                if (opt.$selected) {\r\n                                    opt.$selected.find('input, textarea, select').blur();\r\n                                }\r\n                                if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                    opt.$menu.trigger('nextcommand');\r\n                                }\r\n                                return;\r\n                            } else if (e.keyCode === 40 && opt.$selected.find('input, textarea, select').prop('type') === 'checkbox') {\r\n                                // checkboxes don't capture this key\r\n                                e.preventDefault();\r\n                                return;\r\n                            }\r\n                        } else {\r\n                            if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                opt.$menu.trigger('nextcommand');\r\n                            }\r\n                            return;\r\n                        }\r\n                        break;\r\n\r\n                    case 37: // left\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.isInput || !opt.$selected || !opt.$selected.length) {\r\n                            break;\r\n                        }\r\n\r\n                        if (!opt.$selected.parent().hasClass('context-menu-root')) {\r\n                            var $parent = opt.$selected.parent().parent();\r\n                            opt.$selected.trigger('contextmenu:blur');\r\n                            opt.$selected = $parent;\r\n                            return;\r\n                        }\r\n                        break;\r\n\r\n                    case 39: // right\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.isInput || !opt.$selected || !opt.$selected.length) {\r\n                            break;\r\n                        }\r\n\r\n                        var itemdata = opt.$selected.data('contextMenu') || {};\r\n                        if (itemdata.$menu && opt.$selected.hasClass('context-menu-submenu')) {\r\n                            opt.$selected = null;\r\n                            itemdata.$selected = null;\r\n                            itemdata.$menu.trigger('nextcommand');\r\n                            return;\r\n                        }\r\n                        break;\r\n\r\n                    case 35: // end\r\n                    case 36: // home\r\n                        if (opt.$selected && opt.$selected.find('input, textarea, select').length) {\r\n                            return;\r\n                        } else {\r\n                            (opt.$selected && opt.$selected.parent() || opt.$menu)\r\n                                .children(':not(.' + opt.classNames.disabled + ', .' + opt.classNames.notSelectable + ')')[e.keyCode === 36 ? 'first' : 'last']()\r\n                                .trigger('contextmenu:focus');\r\n                            e.preventDefault();\r\n                            return;\r\n                        }\r\n                        break;\r\n\r\n                    case 13: // enter\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.isInput) {\r\n                            if (opt.$selected && !opt.$selected.is('textarea, select')) {\r\n                                e.preventDefault();\r\n                                return;\r\n                            }\r\n                            break;\r\n                        }\r\n                        if (typeof opt.$selected !== 'undefined' && opt.$selected !== null) {\r\n                            opt.$selected.trigger('mouseup');\r\n                        }\r\n                        return;\r\n\r\n                    case 32: // space\r\n                    case 33: // page up\r\n                    case 34: // page down\r\n                        // prevent browser from scrolling down while menu is visible\r\n                        handle.keyStop(e, opt);\r\n                        return;\r\n\r\n                    case 27: // esc\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                            opt.$menu.trigger('contextmenu:hide');\r\n                        }\r\n                        return;\r\n\r\n                    default: // 0-9, a-z\r\n                        var k = (String.fromCharCode(e.keyCode)).toUpperCase();\r\n                        if (opt.accesskeys && opt.accesskeys[k]) {\r\n                            // according to the specs accesskeys must be invoked immediately\r\n                            opt.accesskeys[k].$node.trigger(opt.accesskeys[k].$menu ? 'contextmenu:focus' : 'mouseup');\r\n                            return;\r\n                        }\r\n                        break;\r\n                }\r\n                // pass event to selected item,\r\n                // stop propagation to avoid endless recursion\r\n                e.stopPropagation();\r\n                if (typeof opt.$selected !== 'undefined' && opt.$selected !== null) {\r\n                    opt.$selected.trigger(e);\r\n                }\r\n            },\r\n            // select previous possible command in menu\r\n            prevItem: function (e) {\r\n                e.stopPropagation();\r\n                var opt = $(this).data('contextMenu') || {};\r\n                var root = $(this).data('contextMenuRoot') || {};\r\n\r\n                // obtain currently selected menu\r\n                if (opt.$selected) {\r\n                    var $s = opt.$selected;\r\n                    opt = opt.$selected.parent().data('contextMenu') || {};\r\n                    opt.$selected = $s;\r\n                }\r\n\r\n                var $children = opt.$menu.children(),\r\n                    $prev = !opt.$selected || !opt.$selected.prev().length ? $children.last() : opt.$selected.prev(),\r\n                    $round = $prev;\r\n\r\n                // skip disabled or hidden elements\r\n                while ($prev.hasClass(root.classNames.disabled) || $prev.hasClass(root.classNames.notSelectable) || $prev.is(':hidden')) {\r\n                    if ($prev.prev().length) {\r\n                        $prev = $prev.prev();\r\n                    } else {\r\n                        $prev = $children.last();\r\n                    }\r\n                    if ($prev.is($round)) {\r\n                        // break endless loop\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // leave current\r\n                if (opt.$selected) {\r\n                    handle.itemMouseleave.call(opt.$selected.get(0), e);\r\n                }\r\n\r\n                // activate next\r\n                handle.itemMouseenter.call($prev.get(0), e);\r\n\r\n                // focus input\r\n                var $input = $prev.find('input, textarea, select');\r\n                if ($input.length) {\r\n                    $input.focus();\r\n                }\r\n            },\r\n            // select next possible command in menu\r\n            nextItem: function (e) {\r\n                e.stopPropagation();\r\n                var opt = $(this).data('contextMenu') || {};\r\n                var root = $(this).data('contextMenuRoot') || {};\r\n\r\n                // obtain currently selected menu\r\n                if (opt.$selected) {\r\n                    var $s = opt.$selected;\r\n                    opt = opt.$selected.parent().data('contextMenu') || {};\r\n                    opt.$selected = $s;\r\n                }\r\n\r\n                var $children = opt.$menu.children(),\r\n                    $next = !opt.$selected || !opt.$selected.next().length ? $children.first() : opt.$selected.next(),\r\n                    $round = $next;\r\n\r\n                // skip disabled\r\n                while ($next.hasClass(root.classNames.disabled) || $next.hasClass(root.classNames.notSelectable) || $next.is(':hidden')) {\r\n                    if ($next.next().length) {\r\n                        $next = $next.next();\r\n                    } else {\r\n                        $next = $children.first();\r\n                    }\r\n                    if ($next.is($round)) {\r\n                        // break endless loop\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // leave current\r\n                if (opt.$selected) {\r\n                    handle.itemMouseleave.call(opt.$selected.get(0), e);\r\n                }\r\n\r\n                // activate next\r\n                handle.itemMouseenter.call($next.get(0), e);\r\n\r\n                // focus input\r\n                var $input = $next.find('input, textarea, select');\r\n                if ($input.length) {\r\n                    $input.focus();\r\n                }\r\n            },\r\n            // flag that we're inside an input so the key handler can act accordingly\r\n            focusInput: function () {\r\n                var $this = $(this).closest('.context-menu-item'),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                root.$selected = opt.$selected = $this;\r\n                root.isInput = opt.isInput = true;\r\n            },\r\n            // flag that we're inside an input so the key handler can act accordingly\r\n            blurInput: function () {\r\n                var $this = $(this).closest('.context-menu-item'),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                root.isInput = opt.isInput = false;\r\n            },\r\n            // :hover on menu\r\n            menuMouseenter: function () {\r\n                var root = $(this).data().contextMenuRoot;\r\n                root.hovering = true;\r\n            },\r\n            // :hover on menu\r\n            menuMouseleave: function (e) {\r\n                var root = $(this).data().contextMenuRoot;\r\n                if (root.$layer && root.$layer.is(e.relatedTarget)) {\r\n                    root.hovering = false;\r\n                }\r\n            },\r\n            // :hover done manually so key handling is possible\r\n            itemMouseenter: function (e) {\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                root.hovering = true;\r\n\r\n                // abort if we're re-entering\r\n                if (e && root.$layer && root.$layer.is(e.relatedTarget)) {\r\n                    e.preventDefault();\r\n                    e.stopImmediatePropagation();\r\n                }\r\n\r\n                // make sure only one item is selected\r\n                (opt.$menu ? opt : root).$menu\r\n                    .children('.' + root.classNames.hover).trigger('contextmenu:blur')\r\n                    .children('.hover').trigger('contextmenu:blur');\r\n\r\n                if ($this.hasClass(root.classNames.disabled) || $this.hasClass(root.classNames.notSelectable)) {\r\n                    opt.$selected = null;\r\n                    return;\r\n                }\r\n\r\n\r\n                $this.trigger('contextmenu:focus');\r\n            },\r\n            // :hover done manually so key handling is possible\r\n            itemMouseleave: function (e) {\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                if (root !== opt && root.$layer && root.$layer.is(e.relatedTarget)) {\r\n                    if (typeof root.$selected !== 'undefined' && root.$selected !== null) {\r\n                        root.$selected.trigger('contextmenu:blur');\r\n                    }\r\n                    e.preventDefault();\r\n                    e.stopImmediatePropagation();\r\n                    root.$selected = opt.$selected = opt.$node;\r\n                    return;\r\n                }\r\n\r\n                if(opt && opt.$menu && opt.$menu.hasClass('context-menu-visible')){\r\n                    return;\r\n                }\r\n\r\n                $this.trigger('contextmenu:blur');\r\n            },\r\n            // contextMenu item click\r\n            itemClick: function (e) {\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot,\r\n                    key = data.contextMenuKey,\r\n                    callback;\r\n\r\n                // abort if the key is unknown or disabled or is a menu\r\n                if (!opt.items[key] || $this.is('.' + root.classNames.disabled + ', .context-menu-separator, .' + root.classNames.notSelectable) || ($this.is('.context-menu-submenu') && root.selectableSubMenu === false )) {\r\n                    return;\r\n                }\r\n\r\n                e.preventDefault();\r\n                e.stopImmediatePropagation();\r\n\r\n                if ($.isFunction(opt.callbacks[key]) && Object.prototype.hasOwnProperty.call(opt.callbacks, key)) {\r\n                    // item-specific callback\r\n                    callback = opt.callbacks[key];\r\n                } else if ($.isFunction(root.callback)) {\r\n                    // default callback\r\n                    callback = root.callback;\r\n                } else {\r\n                    // no callback, no action\r\n                    return;\r\n                }\r\n\r\n                // hide menu if callback doesn't stop that\r\n                if (callback.call(root.$trigger, key, root, e) !== false) {\r\n                    root.$menu.trigger('contextmenu:hide');\r\n                } else if (root.$menu.parent().length) {\r\n                    op.update.call(root.$trigger, root);\r\n                }\r\n            },\r\n            // ignore click events on input elements\r\n            inputClick: function (e) {\r\n                e.stopImmediatePropagation();\r\n            },\r\n            // hide <menu>\r\n            hideMenu: function (e, data) {\r\n                var root = $(this).data('contextMenuRoot');\r\n                op.hide.call(root.$trigger, root, data && data.force);\r\n            },\r\n            // focus <command>\r\n            focusItem: function (e) {\r\n                e.stopPropagation();\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                if ($this.hasClass(root.classNames.disabled) || $this.hasClass(root.classNames.notSelectable)) {\r\n                    return;\r\n                }\r\n\r\n                $this\r\n                    .addClass([root.classNames.hover, root.classNames.visible].join(' '))\r\n                    // select other items and included items\r\n                    .parent().find('.context-menu-item').not($this)\r\n                    .removeClass(root.classNames.visible)\r\n                    .filter('.' + root.classNames.hover)\r\n                    .trigger('contextmenu:blur');\r\n\r\n                // remember selected\r\n                opt.$selected = root.$selected = $this;\r\n\r\n\r\n                if(opt && opt.$node && opt.$node.hasClass('context-menu-submenu')){\r\n                    opt.$node.addClass(root.classNames.hover);\r\n                }\r\n\r\n                // position sub-menu - do after show so dumb $.ui.position can keep up\r\n                if (opt.$node) {\r\n                    root.positionSubmenu.call(opt.$node, opt.$menu);\r\n                }\r\n            },\r\n            // blur <command>\r\n            blurItem: function (e) {\r\n                e.stopPropagation();\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                if (opt.autoHide) { // for tablets and touch screens this needs to remain\r\n                    $this.removeClass(root.classNames.visible);\r\n                }\r\n                $this.removeClass(root.classNames.hover);\r\n                opt.$selected = null;\r\n            }\r\n        },\r\n        // operations\r\n        op = {\r\n            show: function (opt, x, y) {\r\n                var $trigger = $(this),\r\n                    css = {};\r\n\r\n                // hide any open menus\r\n                $('#context-menu-layer').trigger('mousedown');\r\n\r\n                // backreference for callbacks\r\n                opt.$trigger = $trigger;\r\n\r\n                // show event\r\n                if (opt.events.show.call($trigger, opt) === false) {\r\n                    $currentTrigger = null;\r\n                    return;\r\n                }\r\n\r\n                // create or update context menu\r\n                op.update.call($trigger, opt);\r\n\r\n                // position menu\r\n                opt.position.call($trigger, opt, x, y);\r\n\r\n                // make sure we're in front\r\n                if (opt.zIndex) {\r\n                    var additionalZValue = opt.zIndex;\r\n                    // If opt.zIndex is a function, call the function to get the right zIndex.\r\n                    if (typeof opt.zIndex === 'function') {\r\n                        additionalZValue = opt.zIndex.call($trigger, opt);\r\n                    }\r\n                    css.zIndex = zindex($trigger) + additionalZValue;\r\n                }\r\n\r\n                // add layer\r\n                op.layer.call(opt.$menu, opt, css.zIndex);\r\n\r\n                // adjust sub-menu zIndexes\r\n                opt.$menu.find('ul').css('zIndex', css.zIndex + 1);\r\n\r\n                // position and show context menu\r\n                opt.$menu.css(css)[opt.animation.show](opt.animation.duration, function () {\r\n                    $trigger.trigger('contextmenu:visible');\r\n                });\r\n                // make options available and set state\r\n                $trigger\r\n                    .data('contextMenu', opt)\r\n                    .addClass('context-menu-active');\r\n\r\n                // register key handler\r\n                $(document).off('keydown.contextMenu').on('keydown.contextMenu', handle.key);\r\n                // register autoHide handler\r\n                if (opt.autoHide) {\r\n                    // mouse position handler\r\n                    $(document).on('mousemove.contextMenuAutoHide', function (e) {\r\n                        // need to capture the offset on mousemove,\r\n                        // since the page might've been scrolled since activation\r\n                        var pos = $trigger.offset();\r\n                        pos.right = pos.left + $trigger.outerWidth();\r\n                        pos.bottom = pos.top + $trigger.outerHeight();\r\n\r\n                        if (opt.$layer && !opt.hovering && (!(e.pageX >= pos.left && e.pageX <= pos.right) || !(e.pageY >= pos.top && e.pageY <= pos.bottom))) {\r\n                            /* Additional hover check after short time, you might just miss the edge of the menu */\r\n                            setTimeout(function () {\r\n                                if (!opt.hovering && opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                    opt.$menu.trigger('contextmenu:hide');\r\n                                }\r\n                            }, 50);\r\n                        }\r\n                    });\r\n                }\r\n            },\r\n            hide: function (opt, force) {\r\n                var $trigger = $(this);\r\n                if (!opt) {\r\n                    opt = $trigger.data('contextMenu') || {};\r\n                }\r\n\r\n                // hide event\r\n                if (!force && opt.events && opt.events.hide.call($trigger, opt) === false) {\r\n                    return;\r\n                }\r\n\r\n                // remove options and revert state\r\n                $trigger\r\n                    .removeData('contextMenu')\r\n                    .removeClass('context-menu-active');\r\n\r\n                if (opt.$layer) {\r\n                    // keep layer for a bit so the contextmenu event can be aborted properly by opera\r\n                    setTimeout((function ($layer) {\r\n                        return function () {\r\n                            $layer.remove();\r\n                        };\r\n                    })(opt.$layer), 10);\r\n\r\n                    try {\r\n                        delete opt.$layer;\r\n                    } catch (e) {\r\n                        opt.$layer = null;\r\n                    }\r\n                }\r\n\r\n                // remove handle\r\n                $currentTrigger = null;\r\n                // remove selected\r\n                opt.$menu.find('.' + opt.classNames.hover).trigger('contextmenu:blur');\r\n                opt.$selected = null;\r\n                // collapse all submenus\r\n                opt.$menu.find('.' + opt.classNames.visible).removeClass(opt.classNames.visible);\r\n                // unregister key and mouse handlers\r\n                // $(document).off('.contextMenuAutoHide keydown.contextMenu'); // http://bugs.jquery.com/ticket/10705\r\n                $(document).off('.contextMenuAutoHide').off('keydown.contextMenu');\r\n                // hide menu\r\n                if (opt.$menu) {\r\n                    opt.$menu[opt.animation.hide](opt.animation.duration, function () {\r\n                        // tear down dynamically built menu after animation is completed.\r\n                        if (opt.build) {\r\n                            opt.$menu.remove();\r\n                            $.each(opt, function (key) {\r\n                                switch (key) {\r\n                                    case 'ns':\r\n                                    case 'selector':\r\n                                    case 'build':\r\n                                    case 'trigger':\r\n                                        return true;\r\n\r\n                                    default:\r\n                                        opt[key] = undefined;\r\n                                        try {\r\n                                            delete opt[key];\r\n                                        } catch (e) {\r\n                                        }\r\n                                        return true;\r\n                                }\r\n                            });\r\n                        }\r\n\r\n                        setTimeout(function () {\r\n                            $trigger.trigger('contextmenu:hidden');\r\n                        }, 10);\r\n                    });\r\n                }\r\n            },\r\n            create: function (opt, root) {\r\n                if (typeof root === 'undefined') {\r\n                    root = opt;\r\n                }\r\n\r\n                // create contextMenu\r\n                opt.$menu = $('<ul class=\"context-menu-list\"></ul>').addClass(opt.className || '').data({\r\n                    'contextMenu': opt,\r\n                    'contextMenuRoot': root\r\n                });\r\n\r\n                $.each(['callbacks', 'commands', 'inputs'], function (i, k) {\r\n                    opt[k] = {};\r\n                    if (!root[k]) {\r\n                        root[k] = {};\r\n                    }\r\n                });\r\n\r\n                if (!root.accesskeys) {\r\n                    root.accesskeys = {};\r\n                }\r\n\r\n                function createNameNode(item) {\r\n                    var $name = $('<span></span>');\r\n                    if (item._accesskey) {\r\n                        if (item._beforeAccesskey) {\r\n                            $name.append(document.createTextNode(item._beforeAccesskey));\r\n                        }\r\n                        $('<span></span>')\r\n                            .addClass('context-menu-accesskey')\r\n                            .text(item._accesskey)\r\n                            .appendTo($name);\r\n                        if (item._afterAccesskey) {\r\n                            $name.append(document.createTextNode(item._afterAccesskey));\r\n                        }\r\n                    } else {\r\n                        if (item.isHtmlName) {\r\n                            // restrict use with access keys\r\n                            if (typeof item.accesskey !== 'undefined') {\r\n                                throw new Error('accesskeys are not compatible with HTML names and cannot be used together in the same item');\r\n                            }\r\n                            $name.html(item.name);\r\n                        } else {\r\n                            $name.text(item.name);\r\n                        }\r\n                    }\r\n                    return $name;\r\n                }\r\n\r\n                // create contextMenu items\r\n                $.each(opt.items, function (key, item) {\r\n                    var $t = $('<li class=\"context-menu-item\"></li>').addClass(item.className || ''),\r\n                        $label = null,\r\n                        $input = null;\r\n\r\n                    // iOS needs to see a click-event bound to an element to actually\r\n                    // have the TouchEvents infrastructure trigger the click event\r\n                    $t.on('click', $.noop);\r\n\r\n                    // Make old school string seperator a real item so checks wont be\r\n                    // akward later.\r\n                    // And normalize 'cm_separator' into 'cm_seperator'.\r\n                    if (typeof item === 'string' || item.type === 'cm_separator') {\r\n                        item = {type: 'cm_seperator'};\r\n                    }\r\n\r\n                    item.$node = $t.data({\r\n                        'contextMenu': opt,\r\n                        'contextMenuRoot': root,\r\n                        'contextMenuKey': key\r\n                    });\r\n\r\n                    // register accesskey\r\n                    // NOTE: the accesskey attribute should be applicable to any element, but Safari5 and Chrome13 still can't do that\r\n                    if (typeof item.accesskey !== 'undefined') {\r\n                        var aks = splitAccesskey(item.accesskey);\r\n                        for (var i = 0, ak; ak = aks[i]; i++) {\r\n                            if (!root.accesskeys[ak]) {\r\n                                root.accesskeys[ak] = item;\r\n                                var matched = item.name.match(new RegExp('^(.*?)(' + ak + ')(.*)$', 'i'));\r\n                                if (matched) {\r\n                                    item._beforeAccesskey = matched[1];\r\n                                    item._accesskey = matched[2];\r\n                                    item._afterAccesskey = matched[3];\r\n                                }\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (item.type && types[item.type]) {\r\n                        // run custom type handler\r\n                        types[item.type].call($t, item, opt, root);\r\n                        // register commands\r\n                        $.each([opt, root], function (i, k) {\r\n                            k.commands[key] = item;\r\n                            // Overwrite only if undefined or the item is appended to the root. This so it\r\n                            // doesn't overwrite callbacks of root elements if the name is the same.\r\n                            if ($.isFunction(item.callback) && (typeof k.callbacks[key] === 'undefined' || typeof opt.type === 'undefined')) {\r\n                                k.callbacks[key] = item.callback;\r\n                            }\r\n                        });\r\n                    } else {\r\n                        // add label for input\r\n                        if (item.type === 'cm_seperator') {\r\n                            $t.addClass('context-menu-separator ' + root.classNames.notSelectable);\r\n                        } else if (item.type === 'html') {\r\n                            $t.addClass('context-menu-html ' + root.classNames.notSelectable);\r\n                        } else if (item.type === 'sub') {\r\n                            // We don't want to execute the next else-if if it is a sub.\r\n                        } else if (item.type) {\r\n                            $label = $('<label></label>').appendTo($t);\r\n                            createNameNode(item).appendTo($label);\r\n\r\n                            $t.addClass('context-menu-input');\r\n                            opt.hasTypes = true;\r\n                            $.each([opt, root], function (i, k) {\r\n                                k.commands[key] = item;\r\n                                k.inputs[key] = item;\r\n                            });\r\n                        } else if (item.items) {\r\n                            item.type = 'sub';\r\n                        }\r\n\r\n                        switch (item.type) {\r\n                            case 'cm_seperator':\r\n                                break;\r\n\r\n                            case 'text':\r\n                                $input = $('<input type=\"text\" value=\"1\" name=\"\" />')\r\n                                    .attr('name', 'context-menu-input-' + key)\r\n                                    .val(item.value || '')\r\n                                    .appendTo($label);\r\n                                break;\r\n\r\n                            case 'textarea':\r\n                                $input = $('<textarea name=\"\"></textarea>')\r\n                                    .attr('name', 'context-menu-input-' + key)\r\n                                    .val(item.value || '')\r\n                                    .appendTo($label);\r\n\r\n                                if (item.height) {\r\n                                    $input.height(item.height);\r\n                                }\r\n                                break;\r\n\r\n                            case 'checkbox':\r\n                                $input = $('<input type=\"checkbox\" value=\"1\" name=\"\" />')\r\n                                    .attr('name', 'context-menu-input-' + key)\r\n                                    .val(item.value || '')\r\n                                    .prop('checked', !!item.selected)\r\n                                    .prependTo($label);\r\n                                break;\r\n\r\n                            case 'radio':\r\n                                $input = $('<input type=\"radio\" value=\"1\" name=\"\" />')\r\n                                    .attr('name', 'context-menu-input-' + item.radio)\r\n                                    .val(item.value || '')\r\n                                    .prop('checked', !!item.selected)\r\n                                    .prependTo($label);\r\n                                break;\r\n\r\n                            case 'select':\r\n                                $input = $('<select name=\"\"></select>')\r\n                                    .attr('name', 'context-menu-input-' + key)\r\n                                    .appendTo($label);\r\n                                if (item.options) {\r\n                                    $.each(item.options, function (value, text) {\r\n                                        $('<option></option>').val(value).text(text).appendTo($input);\r\n                                    });\r\n                                    $input.val(item.selected);\r\n                                }\r\n                                break;\r\n\r\n                            case 'sub':\r\n                                createNameNode(item).appendTo($t);\r\n                                item.appendTo = item.$node;\r\n                                $t.data('contextMenu', item).addClass('context-menu-submenu');\r\n                                item.callback = null;\r\n\r\n                                // If item contains items, and this is a promise, we should create it later\r\n                                // check if subitems is of type promise. If it is a promise we need to create\r\n                                // it later, after promise has been resolved.\r\n                                if ('function' === typeof item.items.then) {\r\n                                    // probably a promise, process it, when completed it will create the sub menu's.\r\n                                    op.processPromises(item, root, item.items);\r\n                                } else {\r\n                                    // normal submenu.\r\n                                    op.create(item, root);\r\n                                }\r\n                                break;\r\n\r\n                            case 'html':\r\n                                $(item.html).appendTo($t);\r\n                                break;\r\n\r\n                            default:\r\n                                $.each([opt, root], function (i, k) {\r\n                                    k.commands[key] = item;\r\n                                    // Overwrite only if undefined or the item is appended to the root. This so it\r\n                                    // doesn't overwrite callbacks of root elements if the name is the same.\r\n                                    if ($.isFunction(item.callback) && (typeof k.callbacks[key] === 'undefined' || typeof opt.type === 'undefined')) {\r\n                                        k.callbacks[key] = item.callback;\r\n                                    }\r\n                                });\r\n                                createNameNode(item).appendTo($t);\r\n                                break;\r\n                        }\r\n\r\n                        // disable key listener in <input>\r\n                        if (item.type && item.type !== 'sub' && item.type !== 'html' && item.type !== 'cm_seperator') {\r\n                            $input\r\n                                .on('focus', handle.focusInput)\r\n                                .on('blur', handle.blurInput);\r\n\r\n                            if (item.events) {\r\n                                $input.on(item.events, opt);\r\n                            }\r\n                        }\r\n\r\n                        // add icons\r\n                        if (item.icon) {\r\n                            if ($.isFunction(item.icon)) {\r\n                                item._icon = item.icon.call(this, this, $t, key, item);\r\n                            } else {\r\n                                if (typeof(item.icon) === 'string' && item.icon.substring(0, 3) === 'fa-') {\r\n                                    // to enable font awesome\r\n                                    item._icon = root.classNames.icon + ' ' + root.classNames.icon + '--fa fa ' + item.icon;\r\n                                } else {\r\n                                    item._icon = root.classNames.icon + ' ' + root.classNames.icon + '-' + item.icon;\r\n                                }\r\n                            }\r\n                            $t.addClass(item._icon);\r\n                        }\r\n                    }\r\n\r\n                    // cache contained elements\r\n                    item.$input = $input;\r\n                    item.$label = $label;\r\n\r\n                    // attach item to menu\r\n                    $t.appendTo(opt.$menu);\r\n\r\n                    // Disable text selection\r\n                    if (!opt.hasTypes && $.support.eventSelectstart) {\r\n                        // browsers support user-select: none,\r\n                        // IE has a special event for text-selection\r\n                        // browsers supporting neither will not be preventing text-selection\r\n                        $t.on('selectstart.disableTextSelect', handle.abortevent);\r\n                    }\r\n                });\r\n                // attach contextMenu to <body> (to bypass any possible overflow:hidden issues on parents of the trigger element)\r\n                if (!opt.$node) {\r\n                    opt.$menu.css('display', 'none').addClass('context-menu-root');\r\n                }\r\n                opt.$menu.appendTo(opt.appendTo || document.body);\r\n            },\r\n            resize: function ($menu, nested) {\r\n                var domMenu;\r\n                // determine widths of submenus, as CSS won't grow them automatically\r\n                // position:absolute within position:absolute; min-width:100; max-width:200; results in width: 100;\r\n                // kinda sucks hard...\r\n\r\n                // determine width of absolutely positioned element\r\n                $menu.css({position: 'absolute', display: 'block'});\r\n                // don't apply yet, because that would break nested elements' widths\r\n                $menu.data('width',\r\n                    (domMenu = $menu.get(0)).getBoundingClientRect ?\r\n                        Math.ceil(domMenu.getBoundingClientRect().width) :\r\n                        $menu.outerWidth() + 1); // outerWidth() returns rounded pixels\r\n                // reset styles so they allow nested elements to grow/shrink naturally\r\n                $menu.css({\r\n                    position: 'static',\r\n                    minWidth: '0px',\r\n                    maxWidth: '100000px'\r\n                });\r\n                // identify width of nested menus\r\n                $menu.find('> li > ul').each(function () {\r\n                    op.resize($(this), true);\r\n                });\r\n                // reset and apply changes in the end because nested\r\n                // elements' widths wouldn't be calculatable otherwise\r\n                if (!nested) {\r\n                    $menu.find('ul').addBack().css({\r\n                        position: '',\r\n                        display: '',\r\n                        minWidth: '',\r\n                        maxWidth: ''\r\n                    }).outerWidth(function () {\r\n                        return $(this).data('width');\r\n                    });\r\n                }\r\n            },\r\n            update: function (opt, root) {\r\n                var $trigger = this;\r\n                if (typeof root === 'undefined') {\r\n                    root = opt;\r\n                    op.resize(opt.$menu);\r\n                }\r\n                // re-check disabled for each item\r\n                opt.$menu.children().each(function () {\r\n                    var $item = $(this),\r\n                        key = $item.data('contextMenuKey'),\r\n                        item = opt.items[key],\r\n                        disabled = ($.isFunction(item.disabled) && item.disabled.call($trigger, key, root)) || item.disabled === true,\r\n                        visible;\r\n                    if ($.isFunction(item.visible)) {\r\n                        visible = item.visible.call($trigger, key, root);\r\n                    } else if (typeof item.visible !== 'undefined') {\r\n                        visible = item.visible === true;\r\n                    } else {\r\n                        visible = true;\r\n                    }\r\n                    $item[visible ? 'show' : 'hide']();\r\n\r\n                    // dis- / enable item\r\n                    $item[disabled ? 'addClass' : 'removeClass'](root.classNames.disabled);\r\n\r\n                    if ($.isFunction(item.icon)) {\r\n                        $item.removeClass(item._icon);\r\n                        item._icon = item.icon.call(this, $trigger, $item, key, item);\r\n                        $item.addClass(item._icon);\r\n                    }\r\n\r\n                    if (item.type) {\r\n                        // dis- / enable input elements\r\n                        $item.find('input, select, textarea').prop('disabled', disabled);\r\n\r\n                        // update input states\r\n                        switch (item.type) {\r\n                            case 'text':\r\n                            case 'textarea':\r\n                                item.$input.val(item.value || '');\r\n                                break;\r\n\r\n                            case 'checkbox':\r\n                            case 'radio':\r\n                                item.$input.val(item.value || '').prop('checked', !!item.selected);\r\n                                break;\r\n\r\n                            case 'select':\r\n                                item.$input.val((item.selected === 0 ? \"0\" : item.selected) || '');\r\n                                break;\r\n                        }\r\n                    }\r\n\r\n                    if (item.$menu) {\r\n                        // update sub-menu\r\n                        op.update.call($trigger, item, root);\r\n                    }\r\n                });\r\n            },\r\n            layer: function (opt, zIndex) {\r\n                // add transparent layer for click area\r\n                // filter and background for Internet Explorer, Issue #23\r\n                var $layer = opt.$layer = $('<div id=\"context-menu-layer\"></div>')\r\n                    .css({\r\n                        height: $win.height(),\r\n                        width: $win.width(),\r\n                        display: 'block',\r\n                        position: 'fixed',\r\n                        'z-index': zIndex,\r\n                        top: 0,\r\n                        left: 0,\r\n                        opacity: 0,\r\n                        filter: 'alpha(opacity=0)',\r\n                        'background-color': '#000'\r\n                    })\r\n                    .data('contextMenuRoot', opt)\r\n                    .insertBefore(this)\r\n                    .on('contextmenu', handle.abortevent)\r\n                    .on('mousedown', handle.layerClick);\r\n\r\n                // IE6 doesn't know position:fixed;\r\n                if (typeof document.body.style.maxWidth === 'undefined') { // IE6 doesn't support maxWidth\r\n                    $layer.css({\r\n                        'position': 'absolute',\r\n                        'height': $(document).height()\r\n                    });\r\n                }\r\n\r\n                return $layer;\r\n            },\r\n            processPromises: function (opt, root, promise) {\r\n                // Start\r\n                opt.$node.addClass(root.classNames.iconLoadingClass);\r\n\r\n                function completedPromise(opt, root, items) {\r\n                    // Completed promise (dev called promise.resolve). We now have a list of items which can\r\n                    // be used to create the rest of the context menu.\r\n                    if (typeof items === 'undefined') {\r\n                        // Null result, dev should have checked\r\n                        errorPromise(undefined);//own error object\r\n                    }\r\n                    finishPromiseProcess(opt, root, items);\r\n                }\r\n\r\n                function errorPromise(opt, root, errorItem) {\r\n                    // User called promise.reject() with an error item, if not, provide own error item.\r\n                    if (typeof errorItem === 'undefined') {\r\n                        errorItem = {\r\n                            \"error\": {\r\n                                name: \"No items and no error item\",\r\n                                icon: \"context-menu-icon context-menu-icon-quit\"\r\n                            }\r\n                        };\r\n                        if (window.console) {\r\n                            (console.error || console.log).call(console, 'When you reject a promise, provide an \"items\" object, equal to normal sub-menu items');\r\n                        }\r\n                    } else if (typeof errorItem === 'string') {\r\n                        errorItem = {\"error\": {name: errorItem}};\r\n                    }\r\n                    finishPromiseProcess(opt, root, errorItem);\r\n                }\r\n\r\n                function finishPromiseProcess(opt, root, items) {\r\n                    if (typeof root.$menu === 'undefined' || !root.$menu.is(':visible')) {\r\n                        return;\r\n                    }\r\n                    opt.$node.removeClass(root.classNames.iconLoadingClass);\r\n                    opt.items = items;\r\n                    op.create(opt, root, true); // Create submenu\r\n                    op.update(opt, root); // Correctly update position if user is already hovered over menu item\r\n                    root.positionSubmenu.call(opt.$node, opt.$menu); // positionSubmenu, will only do anything if user already hovered over menu item that just got new subitems.\r\n                }\r\n\r\n                // Wait for promise completion. .then(success, error, notify) (we don't track notify). Bind the opt\r\n                // and root to avoid scope problems\r\n                promise.then(completedPromise.bind(this, opt, root), errorPromise.bind(this, opt, root));\r\n            }\r\n        };\r\n\r\n    // split accesskey according to http://www.whatwg.org/specs/web-apps/current-work/multipage/editing.html#assigned-access-key\r\n    function splitAccesskey(val) {\r\n        var t = val.split(/\\s+/);\r\n        var keys = [];\r\n\r\n        for (var i = 0, k; k = t[i]; i++) {\r\n            k = k.charAt(0).toUpperCase(); // first character only\r\n            // theoretically non-accessible characters should be ignored, but different systems, different keyboard layouts, ... screw it.\r\n            // a map to look up already used access keys would be nice\r\n            keys.push(k);\r\n        }\r\n\r\n        return keys;\r\n    }\r\n\r\n// handle contextMenu triggers\r\n    $.fn.contextMenu = function (operation) {\r\n        var $t = this, $o = operation;\r\n        if (this.length > 0) {  // this is not a build on demand menu\r\n            if (typeof operation === 'undefined') {\r\n                this.first().trigger('contextmenu');\r\n            } else if (typeof operation.x !== 'undefined' && typeof operation.y !== 'undefined') {\r\n                this.first().trigger($.Event('contextmenu', {\r\n                    pageX: operation.x,\r\n                    pageY: operation.y,\r\n                    mouseButton: operation.button\r\n                }));\r\n            } else if (operation === 'hide') {\r\n                var $menu = this.first().data('contextMenu') ? this.first().data('contextMenu').$menu : null;\r\n                if ($menu) {\r\n                    $menu.trigger('contextmenu:hide');\r\n                }\r\n            } else if (operation === 'destroy') {\r\n                $.contextMenu('destroy', {context: this});\r\n            } else if ($.isPlainObject(operation)) {\r\n                operation.context = this;\r\n                $.contextMenu('create', operation);\r\n            } else if (operation) {\r\n                this.removeClass('context-menu-disabled');\r\n            } else if (!operation) {\r\n                this.addClass('context-menu-disabled');\r\n            }\r\n        } else {\r\n            $.each(menus, function () {\r\n                if (this.selector === $t.selector) {\r\n                    $o.data = this;\r\n\r\n                    $.extend($o.data, {trigger: 'demand'});\r\n                }\r\n            });\r\n\r\n            handle.contextmenu.call($o.target, $o);\r\n        }\r\n\r\n        return this;\r\n    };\r\n\r\n    // manage contextMenu instances\r\n    $.contextMenu = function (operation, options) {\r\n        if (typeof operation !== 'string') {\r\n            options = operation;\r\n            operation = 'create';\r\n        }\r\n\r\n        if (typeof options === 'string') {\r\n            options = {selector: options};\r\n        } else if (typeof options === 'undefined') {\r\n            options = {};\r\n        }\r\n\r\n        // merge with default options\r\n        var o = $.extend(true, {}, defaults, options || {});\r\n        var $document = $(document);\r\n        var $context = $document;\r\n        var _hasContext = false;\r\n\r\n        if (!o.context || !o.context.length) {\r\n            o.context = document;\r\n        } else {\r\n            // you never know what they throw at you...\r\n            $context = $(o.context).first();\r\n            o.context = $context.get(0);\r\n            _hasContext = !$(o.context).is(document);\r\n        }\r\n\r\n        switch (operation) {\r\n            case 'create':\r\n                // no selector no joy\r\n                if (!o.selector) {\r\n                    throw new Error('No selector specified');\r\n                }\r\n                // make sure internal classes are not bound to\r\n                if (o.selector.match(/.context-menu-(list|item|input)($|\\s)/)) {\r\n                    throw new Error('Cannot bind to selector \"' + o.selector + '\" as it contains a reserved className');\r\n                }\r\n                if (!o.build && (!o.items || $.isEmptyObject(o.items))) {\r\n                    throw new Error('No Items specified');\r\n                }\r\n                counter++;\r\n                o.ns = '.contextMenu' + counter;\r\n                if (!_hasContext) {\r\n                    namespaces[o.selector] = o.ns;\r\n                }\r\n                menus[o.ns] = o;\r\n\r\n                // default to right click\r\n                if (!o.trigger) {\r\n                    o.trigger = 'right';\r\n                }\r\n\r\n                if (!initialized) {\r\n                    var itemClick = o.itemClickEvent === 'click' ? 'click.contextMenu' : 'mouseup.contextMenu';\r\n                    var contextMenuItemObj = {\r\n                        // 'mouseup.contextMenu': handle.itemClick,\r\n                        // 'click.contextMenu': handle.itemClick,\r\n                        'contextmenu:focus.contextMenu': handle.focusItem,\r\n                        'contextmenu:blur.contextMenu': handle.blurItem,\r\n                        'contextmenu.contextMenu': handle.abortevent,\r\n                        'mouseenter.contextMenu': handle.itemMouseenter,\r\n                        'mouseleave.contextMenu': handle.itemMouseleave\r\n                    };\r\n                    contextMenuItemObj[itemClick] = handle.itemClick;\r\n                    // make sure item click is registered first\r\n                    $document\r\n                        .on({\r\n                            'contextmenu:hide.contextMenu': handle.hideMenu,\r\n                            'prevcommand.contextMenu': handle.prevItem,\r\n                            'nextcommand.contextMenu': handle.nextItem,\r\n                            'contextmenu.contextMenu': handle.abortevent,\r\n                            'mouseenter.contextMenu': handle.menuMouseenter,\r\n                            'mouseleave.contextMenu': handle.menuMouseleave\r\n                        }, '.context-menu-list')\r\n                        .on('mouseup.contextMenu', '.context-menu-input', handle.inputClick)\r\n                        .on(contextMenuItemObj, '.context-menu-item');\r\n\r\n                    initialized = true;\r\n                }\r\n\r\n                // engage native contextmenu event\r\n                $context\r\n                    .on('contextmenu' + o.ns, o.selector, o, handle.contextmenu);\r\n\r\n                if (_hasContext) {\r\n                    // add remove hook, just in case\r\n                    $context.on('remove' + o.ns, function () {\r\n                        $(this).contextMenu('destroy');\r\n                    });\r\n                }\r\n\r\n                switch (o.trigger) {\r\n                    case 'hover':\r\n                        $context\r\n                            .on('mouseenter' + o.ns, o.selector, o, handle.mouseenter)\r\n                            .on('mouseleave' + o.ns, o.selector, o, handle.mouseleave);\r\n                        break;\r\n\r\n                    case 'left':\r\n                        $context.on('click' + o.ns, o.selector, o, handle.click);\r\n                        break;\r\n\t\t\t\t    case 'touchstart':\r\n                        $context.on('touchstart' + o.ns, o.selector, o, handle.click);\r\n                        break;\r\n                    /*\r\n                     default:\r\n                     // http://www.quirksmode.org/dom/events/contextmenu.html\r\n                     $document\r\n                     .on('mousedown' + o.ns, o.selector, o, handle.mousedown)\r\n                     .on('mouseup' + o.ns, o.selector, o, handle.mouseup);\r\n                     break;\r\n                     */\r\n                }\r\n\r\n                // create menu\r\n                if (!o.build) {\r\n                    op.create(o);\r\n                }\r\n                break;\r\n\r\n            case 'destroy':\r\n                var $visibleMenu;\r\n                if (_hasContext) {\r\n                    // get proper options\r\n                    var context = o.context;\r\n                    $.each(menus, function (ns, o) {\r\n\r\n                        if (!o) {\r\n                            return true;\r\n                        }\r\n\r\n                        // Is this menu equest to the context called from\r\n                        if (!$(context).is(o.selector)) {\r\n                            return true;\r\n                        }\r\n\r\n                        $visibleMenu = $('.context-menu-list').filter(':visible');\r\n                        if ($visibleMenu.length && $visibleMenu.data().contextMenuRoot.$trigger.is($(o.context).find(o.selector))) {\r\n                            $visibleMenu.trigger('contextmenu:hide', {force: true});\r\n                        }\r\n\r\n                        try {\r\n                            if (menus[o.ns].$menu) {\r\n                                menus[o.ns].$menu.remove();\r\n                            }\r\n\r\n                            delete menus[o.ns];\r\n                        } catch (e) {\r\n                            menus[o.ns] = null;\r\n                        }\r\n\r\n                        $(o.context).off(o.ns);\r\n\r\n                        return true;\r\n                    });\r\n                } else if (!o.selector) {\r\n                    $document.off('.contextMenu .contextMenuAutoHide');\r\n                    $.each(menus, function (ns, o) {\r\n                        $(o.context).off(o.ns);\r\n                    });\r\n\r\n                    namespaces = {};\r\n                    menus = {};\r\n                    counter = 0;\r\n                    initialized = false;\r\n\r\n                    $('#context-menu-layer, .context-menu-list').remove();\r\n                } else if (namespaces[o.selector]) {\r\n                    $visibleMenu = $('.context-menu-list').filter(':visible');\r\n                    if ($visibleMenu.length && $visibleMenu.data().contextMenuRoot.$trigger.is(o.selector)) {\r\n                        $visibleMenu.trigger('contextmenu:hide', {force: true});\r\n                    }\r\n\r\n                    try {\r\n                        if (menus[namespaces[o.selector]].$menu) {\r\n                            menus[namespaces[o.selector]].$menu.remove();\r\n                        }\r\n\r\n                        delete menus[namespaces[o.selector]];\r\n                    } catch (e) {\r\n                        menus[namespaces[o.selector]] = null;\r\n                    }\r\n\r\n                    $document.off(namespaces[o.selector]);\r\n                }\r\n                break;\r\n\r\n            case 'html5':\r\n                // if <command> and <menuitem> are not handled by the browser,\r\n                // or options was a bool true,\r\n                // initialize $.contextMenu for them\r\n                if ((!$.support.htmlCommand && !$.support.htmlMenuitem) || (typeof options === 'boolean' && options)) {\r\n                    $('menu[type=\"context\"]').each(function () {\r\n                        if (this.id) {\r\n                            $.contextMenu({\r\n                                selector: '[contextmenu=' + this.id + ']',\r\n                                items: $.contextMenu.fromMenu(this)\r\n                            });\r\n                        }\r\n                    }).css('display', 'none');\r\n                }\r\n                break;\r\n\r\n            default:\r\n                throw new Error('Unknown operation \"' + operation + '\"');\r\n        }\r\n\r\n        return this;\r\n    };\r\n\r\n// import values into <input> commands\r\n    $.contextMenu.setInputValues = function (opt, data) {\r\n        if (typeof data === 'undefined') {\r\n            data = {};\r\n        }\r\n\r\n        $.each(opt.inputs, function (key, item) {\r\n            switch (item.type) {\r\n                case 'text':\r\n                case 'textarea':\r\n                    item.value = data[key] || '';\r\n                    break;\r\n\r\n                case 'checkbox':\r\n                    item.selected = data[key] ? true : false;\r\n                    break;\r\n\r\n                case 'radio':\r\n                    item.selected = (data[item.radio] || '') === item.value;\r\n                    break;\r\n\r\n                case 'select':\r\n                    item.selected = data[key] || '';\r\n                    break;\r\n            }\r\n        });\r\n    };\r\n\r\n// export values from <input> commands\r\n    $.contextMenu.getInputValues = function (opt, data) {\r\n        if (typeof data === 'undefined') {\r\n            data = {};\r\n        }\r\n\r\n        $.each(opt.inputs, function (key, item) {\r\n            switch (item.type) {\r\n                case 'text':\r\n                case 'textarea':\r\n                case 'select':\r\n                    data[key] = item.$input.val();\r\n                    break;\r\n\r\n                case 'checkbox':\r\n                    data[key] = item.$input.prop('checked');\r\n                    break;\r\n\r\n                case 'radio':\r\n                    if (item.$input.prop('checked')) {\r\n                        data[item.radio] = item.value;\r\n                    }\r\n                    break;\r\n            }\r\n        });\r\n\r\n        return data;\r\n    };\r\n\r\n// find <label for=\"xyz\">\r\n    function inputLabel(node) {\r\n        return (node.id && $('label[for=\"' + node.id + '\"]').val()) || node.name;\r\n    }\r\n\r\n// convert <menu> to items object\r\n    function menuChildren(items, $children, counter) {\r\n        if (!counter) {\r\n            counter = 0;\r\n        }\r\n\r\n        $children.each(function () {\r\n            var $node = $(this),\r\n                node = this,\r\n                nodeName = this.nodeName.toLowerCase(),\r\n                label,\r\n                item;\r\n\r\n            // extract <label><input>\r\n            if (nodeName === 'label' && $node.find('input, textarea, select').length) {\r\n                label = $node.text();\r\n                $node = $node.children().first();\r\n                node = $node.get(0);\r\n                nodeName = node.nodeName.toLowerCase();\r\n            }\r\n\r\n            /*\r\n             * <menu> accepts flow-content as children. that means <embed>, <canvas> and such are valid menu items.\r\n             * Not being the sadistic kind, $.contextMenu only accepts:\r\n             * <command>, <menuitem>, <hr>, <span>, <p> <input [text, radio, checkbox]>, <textarea>, <select> and of course <menu>.\r\n             * Everything else will be imported as an html node, which is not interfaced with contextMenu.\r\n             */\r\n\r\n            // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#concept-command\r\n            switch (nodeName) {\r\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/interactive-elements.html#the-menu-element\r\n                case 'menu':\r\n                    item = {name: $node.attr('label'), items: {}};\r\n                    counter = menuChildren(item.items, $node.children(), counter);\r\n                    break;\r\n\r\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#using-the-a-element-to-define-a-command\r\n                case 'a':\r\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#using-the-button-element-to-define-a-command\r\n                case 'button':\r\n                    item = {\r\n                        name: $node.text(),\r\n                        disabled: !!$node.attr('disabled'),\r\n                        callback: (function () {\r\n                            return function () {\r\n                                $node.get(0).click()\r\n                            };\r\n                        })()\r\n                    };\r\n                    break;\r\n\r\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#using-the-command-element-to-define-a-command\r\n                case 'menuitem':\r\n                case 'command':\r\n                    switch ($node.attr('type')) {\r\n                        case undefined:\r\n                        case 'command':\r\n                        case 'menuitem':\r\n                            item = {\r\n                                name: $node.attr('label'),\r\n                                disabled: !!$node.attr('disabled'),\r\n                                icon: $node.attr('icon'),\r\n                                callback: (function () {\r\n                                    return function () {\r\n                                        $node.get(0).click()\r\n                                    };\r\n                                })()\r\n                            };\r\n                            break;\r\n\r\n                        case 'checkbox':\r\n                            item = {\r\n                                type: 'checkbox',\r\n                                disabled: !!$node.attr('disabled'),\r\n                                name: $node.attr('label'),\r\n                                selected: !!$node.attr('checked')\r\n                            };\r\n                            break;\r\n                        case 'radio':\r\n                            item = {\r\n                                type: 'radio',\r\n                                disabled: !!$node.attr('disabled'),\r\n                                name: $node.attr('label'),\r\n                                radio: $node.attr('radiogroup'),\r\n                                value: $node.attr('id'),\r\n                                selected: !!$node.attr('checked')\r\n                            };\r\n                            break;\r\n\r\n                        default:\r\n                            item = undefined;\r\n                    }\r\n                    break;\r\n\r\n                case 'hr':\r\n                    item = '-------';\r\n                    break;\r\n\r\n                case 'input':\r\n                    switch ($node.attr('type')) {\r\n                        case 'text':\r\n                            item = {\r\n                                type: 'text',\r\n                                name: label || inputLabel(node),\r\n                                disabled: !!$node.attr('disabled'),\r\n                                value: $node.val()\r\n                            };\r\n                            break;\r\n\r\n                        case 'checkbox':\r\n                            item = {\r\n                                type: 'checkbox',\r\n                                name: label || inputLabel(node),\r\n                                disabled: !!$node.attr('disabled'),\r\n                                selected: !!$node.attr('checked')\r\n                            };\r\n                            break;\r\n\r\n                        case 'radio':\r\n                            item = {\r\n                                type: 'radio',\r\n                                name: label || inputLabel(node),\r\n                                disabled: !!$node.attr('disabled'),\r\n                                radio: !!$node.attr('name'),\r\n                                value: $node.val(),\r\n                                selected: !!$node.attr('checked')\r\n                            };\r\n                            break;\r\n\r\n                        default:\r\n                            item = undefined;\r\n                            break;\r\n                    }\r\n                    break;\r\n\r\n                case 'select':\r\n                    item = {\r\n                        type: 'select',\r\n                        name: label || inputLabel(node),\r\n                        disabled: !!$node.attr('disabled'),\r\n                        selected: $node.val(),\r\n                        options: {}\r\n                    };\r\n                    $node.children().each(function () {\r\n                        item.options[this.value] = $(this).text();\r\n                    });\r\n                    break;\r\n\r\n                case 'textarea':\r\n                    item = {\r\n                        type: 'textarea',\r\n                        name: label || inputLabel(node),\r\n                        disabled: !!$node.attr('disabled'),\r\n                        value: $node.val()\r\n                    };\r\n                    break;\r\n\r\n                case 'label':\r\n                    break;\r\n\r\n                default:\r\n                    item = {type: 'html', html: $node.clone(true)};\r\n                    break;\r\n            }\r\n\r\n            if (item) {\r\n                counter++;\r\n                items['key' + counter] = item;\r\n            }\r\n        });\r\n\r\n        return counter;\r\n    }\r\n\r\n// convert html5 menu\r\n    $.contextMenu.fromMenu = function (element) {\r\n        var $this = $(element),\r\n            items = {};\r\n\r\n        menuChildren(items, $this.children());\r\n\r\n        return items;\r\n    };\r\n\r\n// make defaults accessible\r\n    $.contextMenu.defaults = defaults;\r\n    $.contextMenu.types = types;\r\n// export internal functions - undocumented, for hacking only!\r\n    $.contextMenu.handle = handle;\r\n    $.contextMenu.op = op;\r\n    $.contextMenu.menus = menus;\r\n});\r\n"]}