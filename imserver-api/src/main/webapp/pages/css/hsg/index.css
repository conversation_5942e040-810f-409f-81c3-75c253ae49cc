/*公共样式*/
.header .layui-nav-child {
	z-index: 99999;
	top: 60px;
	left: auto;
	right: 0;
}

.seraph {
	font-size: 16px !important;
}

.main_body {
	min-width: 320px;
}

.layui-nav .layui-nav-item a {
	cursor: pointer;
}

.layui-nav .layui-nav-item>a {
	color: rgba(255,255,255,1);
	max-height: 60px;
}

.layui-layer-tab .layui-layer-title span {
	padding: 0 !important;
}

iframe {
	position: absolute;
	height: 100%;
	width: 100%;
	border: none;
}

.top_menu.layui-nav .layui-nav-child dd.layui-this a,.closeBox.layui-nav .layui-nav-child dd.layui-this a,.closeBox .layui-nav-child dd.layui-this,.top_menu .layui-nav-child dd.layui-this {
	background: none;
	color: #333;
}

.layui-nav .layui-nav-child a:hover,.layui-nav .layui-nav-child dd.layui-this a:hover {
	background-color: #5FB878;
	color: #fff;
}

/*模拟加载层图标样式*/
.layui-layer-dialog .layui-layer-content .layui-layer-ico16 {
	background-size: 100% 100% !important;
}
/*样式改变的过渡*/
.layui-body,.layui-footer,.layui-layout-admin .layui-side,.logo,.topLevelMenus li.layui-nav-item,.topLevelMenus li.layui-nav-item:hover {
	transition: all 0.3s ease-in-out;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-ms-transition: all 0.3s ease-in-out;
}
/*隐藏*/
*[mobile],.component .layui-select-title i.layui-edge {
	display: none;
}

/*打开页面动画*/
.layui-tab-item.layui-show {
	animation: moveTop 1s;
	-webkit-animation: moveTop 1s;
	animation-fill-mode: both;
	-webkit-animation-fill-mode: both;
	position: relative;
	height: 100%;
	-webkit-overflow-scrolling: touch;
	overflow: auto;
}

@keyframes moveTop {
	0% {
		opacity: 0;
		-webkit-transform: translateY(20px);
		-ms-transform: translateY(20px);
		transform: translateY(20px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateY(0);
		-ms-transform: translateY(0);
		transform: translateY(0);
	}
}

@-o-keyframes moveTop {
	0% {
		opacity: 0;
		-webkit-transform: translateY(20px);
		-ms-transform: translateY(20px);
		transform: translateY(20px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateY(0);
		-ms-transform: translateY(0);
		transform: translateY(0);
	}
}

@-moz-keyframes moveTop {
	0% {
		opacity: 0;
		-webkit-transform: translateY(20px);
		-ms-transform: translateY(20px);
		transform: translateY(20px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateY(0);
		-ms-transform: translateY(0);
		transform: translateY(0);
	}
}

@-webkit-keyframes moveTop {
	0% {
		opacity: 0;
		-webkit-transform: translateY(20px);
		-ms-transform: translateY(20px);
		transform: translateY(20px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateY(0);
		-ms-transform: translateY(0);
		transform: translateY(0);
	}
}
/*锁屏*/
.admin-header-lock {
	width: 320px;
	height: 170px;
	padding: 20px;
	position: relative;
	text-align: center;
}

.admin-header-lock-img {
	width: 70px;
	height: 70px;
	margin: 0 auto;
}

.admin-header-lock-img img {
	width: 70px;
	height: 70px;
	border-radius: 100%;
	box-shadow: 0 0 30px #44576b;
}

.admin-header-lock-name {
	color: #009688;
	margin: 8px 0 15px 0;
}

.input_btn {
	overflow: hidden;
	margin-bottom: 10px;
}

.admin-header-lock-input {
	width: 170px;
	color: #fff;
	background-color: #1E9FFF;
	float: left;
	margin: 0 10px 0 40px;
	border: none;
}

.admin-header-lock-input::-webkit-input-placeholder {
	color: #fff;
}

.admin-header-lock-input::-moz-placeholder {
	color: #fff;
}

.admin-header-lock-input:-ms-input-placeholder {
	color: #fff;
}

.admin-header-lock-input:-moz-placeholder {
	color: #fff;
}

#unlock {
	float: left;
}

#lock-box p {
	color: #e60000;
}
/*顶部*/
.header {
	z-index: 2000;
}

.logo {
	color: #fff;
	float: left;
	line-height: 60px;
	font-size: 20px;
	padding: 0 25px;
	text-align: center;
	width: auto;
}

.hideMenu {
	float: left;
	width: 20px;
	height: 20px;
	margin-top: 15px;
	font-size: 17px;
	line-height: 20px;
	text-align: center;
	padding: 5px 5px;
	color: #fff;
	background-color: #1AA094;
}

.hideMenu:hover {
	color: #fff;
}

.layui-nav cite {
	margin-left: 5px;
}
/*顶部右侧*/
.topLevelMenus {
	float: left;
}

.topLevelMenus li.layui-nav-item:hover {
	background-color: rgba(221,221,221,0.2);
}

.layui-nav .layui-this:after {
	bottom: -5px!important;
}

.header .layui-nav-bar {
	top: 60px !important;
}

.topLevelMenus .layui-nav-item.layui-this {
	background-color: rgba(0,0,0,0.5);
}

.top_menu.layui-nav .layui-this:after {
	width: 0px;
}

.top_menu .layui-nav-bar,.mobileTopLevelMenus .layui-nav-bar {
	background-color: rgba(0,0,0,0.7);
}

/*左侧导航*/
.layui-nav {
	background-color: inherit !important;
}

.showMenu.layui-layout-admin .layui-side {
	left: -200px;
}

.showMenu .layui-body,.showMenu .layui-footer {
	left: 0;
}
/*左侧用户头像*/
.top_menu {
	background-color: inherit !important;
	position: absolute;
	right: 0;
	top: 0;
}

.layui-layout-admin .layui-side {
	left: 0;
	overflow: hidden;
}

.user-photo {
	width: 200px;
	height: 120px;
	padding: 15px 0 5px;
}

.user-photo a.img {
	display: block;
	width: 80px;
	height: 80px;
	margin: 0 auto 10px;
}

.user-photo a.img img {
	display: block;
	border: none;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border: 4px solid #44576b;
	box-sizing: border-box;
}

.user-photo p {
	display: block;
	width: 100%;
	height: 25px;
	color: #ffffff;
	text-align: center;
	font-size: 12px;
	white-space: nowrap;
	line-height: 25px;
	overflow: hidden;
}
/*左侧导航重定义*/
.layui-nav-item.layui-nav-itemed {
	background-color: #2B2E37 !important;
}

.layui-nav-itemed:before {
	width: 5px;
	height: 100%;
	background-color: #1E9FFF;
	position: absolute;
	content: '';
	left: 0;
	top: 0;
}

.layui-nav-itemed .layui-nav-child a {
	padding-left: 40px;
}
/*左侧搜索框*/
.component {
	width: 180px;
	height: 30px;
	margin: 0 auto 5px;
	position: relative;
}

.component .layui-input {
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	border: none;
	transition: all 0.3s;
	background: rgba(255,255,255,0.05);
}

.component .layui-input:focus {
	background: #fff;
	color: #000;
}

.component .layui-form-select dl {
	top: 33px;
	background: #fff;
}

.component .layui-icon {
	position: absolute;
	right: 8px;
	top: 8px;
	color: #000;
}

.component dl dd {
	color: #000 !important;
}

.component dl dd.layui-this {
	color: #fff !important;
}

.component dl dd.layui-select-tips {
	color: #999 !important;
}

/*layui-body*/
.layui-body {
	overflow: hidden;
	border-top: 5px solid #1AA094;
	border-left: 2px solid #1AA094;
	background: #fff;
}

#top_tabs_box {
	padding-right: 138px;
	height: 40px;
	border-bottom: 1px solid #e2e2e2;
}

#top_tabs {
	position: absolute;
	border-bottom: none;
}

.layui-tab-title .layui-this {
	background-color: #1AA094;
	color: #fff;
}

.layui-tab-title .layui-this:after {
	border: none;
}

.layui-tab-title li cite {
	font-style: normal;
	padding-left: 5px;
}

.clildFrame.layui-tab-content {
	top: 41px;
	position: absolute;
	bottom: 0;
	width: 100%;
	padding: 0;
}
/*多窗口页面操作下拉*/
.closeBox {
	position: absolute;
	right: 0;
	background-color: #fff !important;
	color: #000;
	border-left: 1px solid #e2e2e2;
	border-bottom: 1px solid #e2e2e2;
}

.closeBox .layui-nav-item {
	line-height: 40px;
}

.closeBox .layui-nav-item>a,.closeBox .layui-nav-item>a:hover {
	color: #000;
}

.closeBox .layui-nav-child {
	top: 42px;
	left: -12px;
}

.closeBox .layui-nav-bar {
	display: none;
}

.closeBox a i.caozuo {
	font-size: 20px;
	position: absolute;
	top: 1px;
	left: 0;
}

.closeBox a span.layui-nav-more {
	border-color: #333 transparent transparent;
}

.closeBox a span.layui-nav-more.layui-nav-mored {
	border-color: transparent transparent #333;
}
/*功能设定*/
.functionSrtting_box {
	padding-top: 15px;
}

.functionSrtting_box .layui-form-label {
	width: 81px;
}

.functionSrtting_box .layui-word-aux {
	position: absolute;
	left: 60px;
	top: 9px;
	font-size: 12px;
}
/*换肤*/
.skins_box {
	padding: 10px 34px 0;
}

.skinBtn {
	text-align: center;
}
/*橙色*/
.orange .layui-layout-admin .layui-header {
	background-color: orange !important;
}

.orange .layui-bg-black {
	background-color: #e47214 !important;
}
/*蓝色*/
.blue .layui-layout-admin .layui-header {
	background-color: #3396d8 !important;
}

.blue .layui-bg-black,.blue .hideMenu {
	background-color: #146aa2 !important;
}
/*自定义*/
.skinCustom {
	visibility: hidden;
}

.skinCustom input {
	width: 48%;
	margin: 5px 2% 5px 0;
	float: left;
}

.orange .layui-nav-tree .layui-nav-child a,.blue .layui-nav-tree .layui-nav-child a {
	color: #fff;
}

.orange .top_menu.layui-nav .layui-nav-more,.blue .top_menu.layui-nav .layui-nav-more {
	border-color: #fff transparent transparent !important;
}

.orange .top_menu.layui-nav-itemed .layui-nav-more,.orange .top_menu.layui-nav .layui-nav-mored,.blue .top_menu.layui-nav-itemed .layui-nav-more,.blue .top_menu.layui-nav .layui-nav-mored {
	border-color: transparent transparent #fff !important;
}
/*底部*/
.footer {
	text-align: center;
	line-height: 44px;
	border-left: 2px solid #1AA094;
	z-index: 999;
}

/*响应式样式*/
@media screen and (max-width:1080px) {
	.mobileTopLevelMenus[mobile] {
		display: inline-block;
	}

	.site-mobile .site-tree-mobile,.topLevelMenus[pc] {
		display: none !important;
	}
}

@media screen and (max-width: 720px) {
	.hideMenu {
		display: none !important;
	}

	.mobileTopLevelMenus[mobile] {
		padding: 0;
	}

	.top_menu>li[pc] {
		display: none !important;
	}
	/*左侧导航*/
	.layui-layout-admin .layui-side {
		left: -260px;
	}

	.site-mobile .layui-side {
		left: 0;
		z-index: 1100;
	}

	.site-tree-mobile {
		display: block!important;
		position: fixed;
		z-index: 100000;
		bottom: 15px;
		left: 15px;
		width: 40px;
		height: 40px;
		line-height: 40px;
		border-radius: 2px;
		text-align: center;
		background-color: rgba(0,0,0,.7);
		color: #fff;
	}

	.site-mobile .site-mobile-shade {
		content: '';
		position: fixed;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: rgba(0,0,0,.8);
		z-index: 999;
	}

	.layui-body,.layui-layout-admin .layui-footer {
		left: -2px;
	}
}

@media screen and (max-width:480px) {
	.logo {
		width: 120px;
		font-size: 18px;
	}

	#userInfo>a {
		padding: 0 10px;
	}

	.mobileTopLevelMenus[mobile] li>a {
		padding: 0 17px 0 15px;
	}

	.logo,.layui-nav.top_menu {
		padding: 0 5px;
	}

	.adminName,.top_menu dd[pc] {
		display: none !important;
	}

	*[mobile],.top_menu .layui-nav-item.showNotice[pc] {
		display: inline-block !important;
	}
}

/*修改顶部高度*/
.header .layui-nav-child,.layui-body,.layui-layout-admin .layui-side,.header .layui-nav-bar {
	top: 50px !important;
}

.header .layui-nav .layui-nav-item,.header .layui-nav .layui-nav-item>a,.header,.logo {
	line-height: 50px !important;
	max-height: 50px;
	!important;
}

.mobileTopLevelMenus {
	float: left;
}

.hideMenu {
	margin-top: 10px;
}