<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>查看提现子后台</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta http-equiv="Access-Control-Allow-Origin" content="*">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="format-detection" content="telephone=no">
	<!--<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">-->
	<!--<link rel="icon" href="/pages/common/images/favicon.ico">-->
	
	<link rel="stylesheet" href="/pages/common/layui/css/layui.css" media="all" />
	<link rel="stylesheet" href="/pages/console/css/index.css" media="all" />
	
</head>
<body class="main_body" style="overflow-x: hidden;">
	<div class="layui-layout layui-layout-admin">
		<!-- 顶部 --><!--修改主题颜色位置-->
		<div class="layui-header header layui-bg-blue">
			<div id="autoplayWithWithdrawal"><audio  autoplay="autoplay" id="auto" src=""></audio></div>
			<div class="layui-main mag0" style="margin-left: 0;">
				<a href="#" class="logo"><span id="projectName"></span>查看提现子后台</a>
			    <!-- 顶部右侧菜单 -->
			    <ul class="layui-nav top_menu">
<!--					<li class="layui-nav-item" lay-filter="test"><a class="name" href="javascript:;"><i-->
<!--							class="layui-icon">&#xe629;</i>主题</a>-->
<!--						<dl class="layui-nav-child">-->
<!--							<dd data-skin="blue">-->
<!--								<a href="javascript:;">默认</a>-->
<!--							</dd>-->
<!--							<dd data-skin="orange">-->
<!--								<a href="javascript:;">橙色</a>-->
<!--							</dd>-->
<!--							<dd data-skin="red">-->
<!--								<a href="javascript:;">红色</a>-->
<!--							</dd>-->
<!--							<dd data-skin="green">-->
<!--								<a href="javascript:;">墨绿</a>-->
<!--							</dd>-->
<!--							<dd data-skin="cyan">-->
<!--								<a href="javascript:;">藏青</a>-->
<!--							</dd>-->
<!--						</dl>-->
<!--					</li>-->
					<li class="layui-nav-item" id="userInfo">
						<a href="javascript:;">
							欢迎你, <cite class="adminName"></cite>
						</a>
						<dl class="layui-nav-child">
						<!--	<dd>
								<a id="changeMyPasswd" class="signOut">
									<i class="seraph icon-tuichu"></i>
									<cite >修改密码</cite>
								</a>
							</dd>
							<dd id="bindDD" style="">
								<a id="bindGoogleValidation" class="signOut">
									<i class="seraph icon-tuichu"></i>
									<cite >绑定Google身份验证器</cite>
								</a>
							</dd>
							<dd id="unbindDD" style="display: none;">
								<a id="unbindGoogleValidation" class="signOut">
									<i class="seraph icon-tuichu"></i>
									<cite >解绑Google身份验证器</cite>
								</a>
							</dd>
							<dd style="display: none;">
								<a data-url="/pages/console/pressureTest.html" class="pressureTest">
									<i class="seraph icon-tuichu"></i>
									<cite id="pressureTest">压力测试</cite>
								</a>
							</dd>-->
							<dd>
								<a   onclick="quitLogin()" class="signOut">
									<i class="seraph icon-tuichu"></i>
									<cite>退出</cite>
								</a>
							</dd>
						</dl>
					</li>
				</ul>
			</div>
		</div>

		<!-- 左侧导航 -->
		<div class="layui-side layui-bg-black">
			<!--<div class="user-photo">-->
			<!--<a class="img" ><img id="projectLogo" src="/pages/common/images/logo.png" class="userAvatar"></a>-->
			<!--</div>-->
			<div class="navBar layui-side-scroll" id="navBar" style="height: 95%">
				<ul class="layui-nav layui-nav-tree" id="navBarUl">
					<li class="layui-nav-item layui-this">
						<!--<a href="javascript:;" class="layui-bg-blue" data-url="/pages/console/system_report.html"><i class="layui-icon" data-icon=""></i><cite>系统报表</cite></a>-->
					</li>
				</ul>
			</div>
		</div>
		<!-- 右侧内容 -->
		<div class="layui-body layui-form" style="background-color: #f2f2f2;height:94.5%">
			<div class="layui-tab mag0 bodyTab" lay-filter="bodyTab" id="top_tabs_box">
				<div class="layui-tab-content clildFrame">
					<div class="layui-tab-item layui-show">
						<iframe id="iframe" ></iframe>
						<!--<iframe id="iframe" src="/pages/console/system_report.html"></iframe>-->
					</div>
				</div>
			</div>
		</div>
		<!-- 底部 -->
		<!-- <div class="layui-footer footer" style="border-left:0">
			<p><span> 即时通讯管理系统 2018 &copy;</span></p>
		</div> -->
	</div>
	<!-- 移动导航 -->
	<div class="site-tree-mobile">
		<i class="layui-icon">&#xe602;</i>
	</div>
	<div class="site-mobile-shade"></div>
	<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
	<script>
		var role = localStorage.getItem("role");
        getAdminConfig();
        function getAdminConfig(){
            $.post("/console/adminConfig", function(data) {
                if (data.resultCode == 1) {
                    console.log("Login data:"+JSON.stringify(data))
                    localStorage.setItem("apiKey",data.data.apiKey);
                    localStorage.setItem("registerInviteCode",data.data.registerInviteCode); //系统邀请码模式
                    localStorage.setItem("apiAccessStatus",data.data.apiAccessStatus); //系统ip白名单
                    localStorage.setItem("nodeConfigStatus",data.data.nodeConfigStatus); //多节点状态
					localStorage.setItem("appDiscoverStatus",data.data.appDiscoverStatus);
					localStorage.setItem("appTabBarStatus",data.data.appTabBarStatus);
					localStorage.setItem("tlPayIsOpen",data.data.tlPayIsOpen);//是否启用通联支付
                }
            });
            if(role==6&&false){
				setTimeout(function () {
					Common.getWithdrawalMsg();
				}, 1000);
            }
        }
	</script>
	<script type="text/javascript" src="/pages/common/qrcode/qrcode.min.js"></script>
	<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="/pages/common/jquery/jquery.md5.js"></script>
	<script type="text/javascript" src="/pages/common/layui/layui.js"></script>
	<script type="text/javascript" src="/pages/console/js/common.js"></script>
	<script type="text/javascript" src="./js/index.js"></script>
</body>
</html>