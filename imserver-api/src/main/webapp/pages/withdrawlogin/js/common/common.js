/**
 * 公共的 js
 */
var ivKey=[1,2,3,4,5,6,7,8];
var iv=getStrFromBytes(ivKey);
var ConfigData=null;
function getStrFromBytes (arr) {
    var r = "";
    for(var i=0;i<arr.length;i++){
        r += String.fromCharCode(arr[i]);
    }
    return r;
}
function request(url){
	// localStorage.getItem("access_token");
	// localStorage.getItem("apiKey");
	// var myDate = new Date();//获取系统当前时间
	var time=new Date().getTime();
	/*console.log(time);
	console.log("userId:"+localStorage.getItem("userId"));
	console.log("apiKey:"+localStorage.getItem("apiKey"));*/
	/*console.log(localStorage.getItem("access_Token"));*/
/*	var obj=localStorage.getItem("apiKey")+time+localStorage.getItem("userId")+localStorage.getItem("access_Token");
	url=url+"?secret="+$.md5(obj)+"&time="+time;*/
    var obj=localStorage.getItem("apiKey")+time+localStorage.getItem("userId")+localStorage.getItem("access_Token");
    url=url+"?secret="+$.md5(obj)+"&time="+time+"&access_token="+localStorage.getItem("access_Token")+"&account="+localStorage.getItem("account");;
    return url;
}
//转化正整数
function positiveIntegerConversion(value){
    value = value.replace(/[^\d]/g,'');
    if(''!=value){
        value = parseInt(value);
    }
    return value;
}
function checkRequst(result){
    // 访问令牌过期或无效
    if(result.resultCode==1030102){
        layer.confirm(result.resultMsg,{icon:2, title:'提示消息',yes:function () {

                window.top.location.href="/pages/console/login.html";
            },btn2:function () {

                window.top.location.href="/pages/console/login.html";
            },cancel:function () {

                window.top.location.href="/pages/console/login.html";
            }});
    }else if(result.resultCode==0){
        if(!Common.isNil(result.resultMsg)){
            layer.msg(result.resultMsg,{icon: 2});
        }

    }else if(result.resultCode==1){
        if(!Common.isNil(result.resultMsg)){
            layer.msg(result.resultMsg,{icon: 1});
        }

    }
}
//转化浮点数
function positiveIFloatConversion(value){
    let reg = /^[1-9]+\d*(\.\d{0,2})?$|^0?\.\d{0,2}$/;
    let status = reg.test(value);
    if (status){
        return value;
    }
    if (value != ''){
        if (value.indexOf(".") == -1){
            value = value.replace(/[^\d]/g,'');
            return value;
        }
        let valLength = value.toString().split(".")[1].length;
        let val1 =  value.toString().split(".")[0];
        if (valLength > 2){
            let val2 =  value.toString().split(".")[1].substr(0,2);
            val1 = val1.replace(/[^\d]/g,'');
            val2 = val2.replace(/[^\d]/g,'');
            value =  val1+"."+val2;
            return value
        }
        let val2 =  value.toString().split(".")[1].substr(0,2);
        val1 = val1.replace(/[^\d]/g,'');
        val2 = val2.replace(/[^\d]/g,'');
        value =  val1+"."+val2
        return value;
    }
    return value;
}
var Config={
	getConfig:function(){
		if(ConfigData==null){
			$.ajax({
				type:'POST',
				url:'/config',
				data:{},
				async:false,
				success:function(result){
					ConfigData=result.data;
				}
			})
		}
		console.log(ConfigData);
		return ConfigData;
	}
}

var Common = {
	// layui table重载刷新 解决零界值删除不刷新到上页的问题(支持删除，多选删除)
	// 	currentCount ：当前table总数量, currentPageIndex ： 当前页数, checkLength ：选中的个数, tableId : table的ID){
	tableReload : function(currentCount,currentPageIndex,checkLength,tableId){
		var remainderIndex = (currentCount - checkLength) % Common.limit;
        console.log("currentCount : "+currentCount+"  checkLength : "+checkLength+"  Common.limit : "+Common.limit+"  currentPageIndex : "+currentPageIndex+"  remainderIndex : "+remainderIndex);
        if(0 == remainderIndex)
            currentPageIndex = currentPageIndex - 1;
        layui.table.reload(tableId,{
            page: {
                curr: currentPageIndex, //重新从当前页开始
            }
		})
	},
	getWithdrawalMsg : function(){
        $.ajax({
        		type:'POST',
        		url:'/console/getWithdrawalMsg',
        		data:{},
        		async:true,
        		success:function(result){
        		if(result.resultCode==1){
            		console.log("要响铃了")
            		$("#auto").attr("src","http://data.huiyi8.com/2017/gha/03/17/1702.mp3");
        		  }
            		setTimeout(function () {
                      Common.getWithdrawalMsg();
                    }, 60000);
        		}
        	})
	},
	// 分页公共参数
    limit:15,
    limits:[15,50,100,1000,10000],
    /** 调用接口通用方法  */
    invokeGood : function(obj){
        jQuery.support.cors = true;
        layer.load(1); //显示等待框

        var params = {
            type : "POST",
            url : obj.url,
            data : obj.data,
            contentType : 'application/x-www-form-urlencoded; charset=UTF-8',
            dataType : 'JSON',
            async:obj.async==false?false:true,
            success : function(result) {
                console.log(result)
                layer.closeAll('loading');
                if(1==result.resultCode){
                    obj.success(result);
                    if(obj.successMsg!=undefined&&obj.successMsg!=""&&obj.successMsg!=null){
                        layer.msg(obj.successMsg,{icon:1});
                    }
                }else if(-1==result.resultCode){
                    //缺少访问令牌
                    layer.msg("缺少访问令牌",{icon: 3});
                    window.location.href = "/pages/console/login.html";
                }else if(1030102==result.resultCode){// 访问令牌过期
                    checkRequst(result);
                }else{
                    if(obj.error!=undefined &&obj.error!=""&&obj.error!=null){
                        //obj.error(result);
                    }
                    if(!Common.isNil(result.resultMsg))
                        layer.msg(result.resultMsg,{icon: 2,time: 2000});
                    else
                        layer.msg(obj.errorMsg,{icon: 2,time: 2000});

                    // obj.error(result);
                }
                return;

            },
            error : function(result) {
                layer.closeAll('loading');
                if(!Common.isNil(result.resultMsg)){
                    layer.msg(result.resultMsg,{icon: 2});
                }else{
                    layer.msg(obj.errorMsg,{icon: 2});
                }
                // obj.error(result);//执行失败的回调函数
                return;
            },
            complete : function(result) {
                layer.closeAll('loading');
            }
        }
        params.data["access_token"] = localStorage.getItem("access_Token");
        $.ajax(params);
    },
	/** 调用接口通用方法  */
	invoke : function(obj){
		jQuery.support.cors = true;
		layer.load(1); //显示等待框
		var params = {
			type : "POST",
			url : obj.path,
			data : obj.data,
			contentType : 'application/x-www-form-urlencoded; charset=UTF-8',
			dataType : 'JSON',
			success : function(result) {
				layer.closeAll('loading');
				if(1==result.resultCode){
					if(obj.successMsg!=false)
						layer.msg(obj.successMsg,{icon: 1});
					obj.successCb(result);//执行成功的回调函数					
				}else if(-1==result.resultCode){
					//缺少访问令牌
					layer.msg(result.resultMsg == null ? "操作异常，请退出重新操作":result.resultMsg,{icon: 3});
					window.location.href = "/pages/console/login.html";
				}else{
					if(!Common.isNil(result.resultMsg))
						layer.msg(result.resultMsg,{icon: 2,time: 2000});
					else
						layer.msg(obj.errorMsg,{icon: 2,time: 2000});

					obj.errorCb(result);
				}
				return;
					
			},
			error : function(result) {
				layer.closeAll('loading');
				if(!Common.isNil(result.resultMsg)){
					layer.msg(result.resultMsg,{icon: 2});
				}else{
					layer.msg(obj.errorMsg,{icon: 2});
				}
				obj.errorCb(result);//执行失败的回调函数
				return;
			},
			complete : function(result) {
				layer.closeAll('loading');
			}
		}
		// params.data["access_token"] = myData.access_token;
		$.ajax(params);
	}
	,isNil : function(s) {
		return undefined == s || null == s || $.trim(s) == "" || $.trim(s) == "null";
	},
	formatDate : function (time,fmt,type) { //type : 类型 0:时间为秒  1:时间为毫秒
		var date = new Date((type==0?(time * 1000):time));
	    var o = {
	        "M+": date.getMonth() + 1, //月份 
	        "d+": date.getDate(), //日 
	        "h+": date.getHours(), //小时 
	        "m+": date.getMinutes(), //分 
	        "s+": date.getSeconds(), //秒 
	        "q+": Math.floor((date.getMonth() + 3) / 3), //季度 
	        "S": date.getMilliseconds() //毫秒 
	    };
	    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
	    for (var k in o)
	    	if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
	    return fmt;
	},
	// 消息类型
	msgType : function(type) {
        if(1 == type){
            return "文本消息";
        }else if(2 == type ){
            return "图片消息";
        }else if(3 == type){
            return "语音消息";
        }else if(4 == type){
            return "位置消息";
        }else if(5 == type){
            return "动画消息";
        }else if(6 == type){
            return "视频消息";
        }else if(7 == type){
            return "音频消息";
        }else if(8 == type){
            return "名片消息";
        }else if(9 == type){
            return "文件消息";
        }else if(10 == type){
            return "提醒消息";
        }else if (28 == type) {
			return "红包消息";
		}else if(83 == type){
            return "领取红包消息";
		}else if(86 == type){
            return "红包退回消息";
        }else if(29 == type){
            return "转账消息";
        }else if(80 == type){
            return "单条图文消息";
        }else if(81 == type){
            return "多条图文消息";
        }else if(84 == type){
            return "戳一戳消息";
        }else if(85 == type){
            return "聊天记录消息";
        }else if(88 == type){
            return "转账被领取消息";
        }else if(89 == type){
            return "转账已退回消息";
        }else if(90 == type){
            return "付款码已付款通知消息";
        }else if(91 == type){
            return "付款码已到账通知消息";
        }else if(92 == type){
            return "收款码已付款通知消息";
        }else if(93 == type){
            return "收款码已到账通知消息";
        }else if(201 == type){
            return "正在输入消息";
        }else if(202 == type){
            return "撤回消息";
        }else if(100 == type){
            return "发起语音通话消息";
        }else if(102 == type){
            return "接听语音通话消息";
        }else if(103 == type){
            return "拒绝语音通话消息";
        }else if(104 == type){
            return "结束语音通话消息";
        }else if(110 == type){
            return "发起视频通话消息";
        }else if(112 == type){
            return "接听视频通话消息";
        }else if(113 == type){
            return "拒绝视频通话消息";
        }else if(114 == type){
            return "结束视频通话消息";
        }else if(115 == type){
            return "会议邀请消息";
        }else if(901 == type){
            return "修改昵称消息";
        }else if(902 == type){
            return "修改房间名消息";
        }else if(903 == type){
            return "解散房间消息";
        }else if(904 == type){
            return "退出房间消息";
        }else if(905 == type){
            return "新公告消息";
        }else if(906 == type){
            return "禁言、取消禁言消息";
        }else if(907 == type){
            return "增加成员消息";
        }else if(913 == type){
            return "设置、取消管理员消息";
        }else if(915 == type){
            return "设置群已读消息";
        }else if(916 == type){
            return "群验证消息";
        }else if(917 == type){
            return "群组是否公开消息";
        }else if(918 == type){
            return "是否展示群成员列表消息";
        }else if(919 == type){
            return "允许发送名片消息";
        }else if(920 == type){
            return "全员禁言消息";
        }else if(921 == type){
            return "允许普通群成员邀请好友加群";
        }else if(922 == type){
            return "允许普通成员上传群共享";
        }else if(923 == type){
            return "允许普通成员发起会议";
        }else if(924 == type){
            return "允许普通成员发送讲课";
        }else if(925 == type){
            return "转让群组";
        }else if(930 == type){
            return "设置、取消隐身人，监控人";
        }else if(931 == type){
            return "群组被后台锁定/解锁";
        }
        else{
			return "其他消息类型";
		}
    },
	// 消息解密
    decryptMsg:function(content,msgId,timeSend) {
	    timeSend = parseInt(timeSend);
        console.log("content:"+content+"  msgId: "+msgId+"  timeSend: "+timeSend);
        var key=Common.getMsgKey(msgId,timeSend)
        var desContent = content.replace(" ", "");
        var content=Common.decryptDES(desContent,key);
        return content;
    },
    getMsgKey:function(msgId,timeSend){
        var key= localStorage.getItem("apiKey")+timeSend+msgId;
        return $.md5(key);
    },
    decryptDES:function(message,key){
        console.log("key1: "+key);
        //把私钥转换成16进制的字符串
        var keyHex = CryptoJS.enc.Utf8.parse(key);

        //把需要解密的数据从16进制字符串转换成字符byte数组
        var decrypted = CryptoJS.TripleDES.decrypt({
            ciphertext: CryptoJS.enc.Base64.parse(message)
        }, keyHex, {
            iv:CryptoJS.enc.Utf8.parse(iv),
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });
        //console.log("decryptDES   "+ decrypted);
        //以utf-8的形式输出解密过后内容
        var result = decrypted.toString(CryptoJS.enc.Utf8);
        console.log("decryptDES   "+ result);
        return result;
    },

}; /*End Common*/

var $,tab,layer;
layui.config({
    base : "/pages/console/js/"
}).extend({
    "bodyTab" : "bodyTab"
    , vip_nav: 'vip_nav'
    , vip_tab: 'vip_tab'
    , vip_table: 'vip_table'
})

layui.use(['bodyTab','form','element','layer','jquery'],function(){
    $ = layui.$;

    $(".layui-nav-child dd ").click(function(elem){
        // 修改skin
        if ($(this).attr('data-skin')) {
            localStorage.skin = $(this).attr('data-skin');
            skin();
        } else if($(this).attr('data-mp')) {
            //避开报错，暂时什么都不做
        }  else {
            // 添加tab方法
            window.addTab(elem);
        }
    })
    // 皮肤
    function skin() {
        var skin = localStorage.skin ? localStorage.skin : 'blue';
        var body = $('body');
        body.removeClass('orange');
        body.removeClass('blue');
        body.removeClass('red');
        body.removeClass('green');
        body.removeClass('cyan');
        body.addClass('' + skin);
        if(skin=='orange'&&window.frames['button-skin']!=undefined){
            removeColor();
            $(window.frames['button-skin'].document).find('body').addClass('orange');
        }else if(skin=='red'&&window.frames['button-skin']!=undefined){
            removeColor();
            $(window.frames['button-skin'].document).find('body').addClass('red');
        }else if(skin=='green'&&window.frames['button-skin']!=undefined){
            removeColor();
            $(window.frames['button-skin'].document).find('body').addClass('green');
        }else if(skin=='cyan'&&window.frames['button-skin']!=undefined){
            removeColor();
            $(window.frames['button-skin'].document).find('body').addClass('cyan');
        }else if(window.frames['button-skin']!=undefined){
            removeColor();
            $(window.frames['button-skin'].document).find('body').addClass('blue');
        }
    }
    function removeColor(){
        $(window.frames['button-skin'].document).find('body').removeClass('orange');
        $(window.frames['button-skin'].document).find('body').removeClass('red');
        $(window.frames['button-skin'].document).find('body').removeClass('green');
        $(window.frames['button-skin'].document).find('body').removeClass('cyan');
        $(window.frames['button-skin'].document).find('body').removeClass('blue');
    }
    skin();
})