layui.use(['form'], function() {
    $("#getImgSpan").click(function(){
        var account = $("#account").val();
        if(account.trim()==""){
            layer.msg("账号不能为空",{icon: 2});
            return;
        }
        getImgCode(account);
    })
    $("#account").blur(function(){
        var account = $("#account").val();
        if(account.trim()==""){
            layer.msg("账号不能为空",{icon: 2});
            return;
        }
        getImgCode(account);
    })
    var isGoogleSec = false;
    function getGoogleVal(account){
        $.post("/console/getGoogleQRValueByAccount",{ account: account }, function(result) {
            if (result.resultCode == 1) {
                if(result.data==true){
//                    $("#googleDiv").css("display","");
                    isGoogleSec = true;
                    getGooglePopup();
                }else{
//                    $("#googleDiv").css("display","none");
                    isGoogleSec = false;
                    loginForm();
                }
            } else {
                layer.msg(result.resultMsg,{icon: 2});
             }
        }, "json");
    }
    function getImgCode(account){
        var url = "/getImgCode?telephone=" + account+"&timestamp=" + new Date().getTime();
        $("#getImgCode").show();
        $("#getImgCode").attr("src", url);

    }
    var account = "";
    var password = "";
    var imgCode = "";
    var googleVal = "";
    $(document).keyup(function(event){
      if(event.keyCode ==13){
//      this.blur();
        $("#imgCode").blur();
       if(!isGoogleSec){
            submit();
        }
      }
    });
    $("#adminLogin").click(function(){
        submit();
    })
    function submit(){
        account = $("#account").val();
        if(account.trim()==""){
            layer.msg("账号不能为空",{icon: 2});
            return;
        }
        password = $("#password").val();
        if(password.trim()==""){
            layer.msg("密码不能为空",{icon: 2});
            return;
        }
        imgCode = $("#imgCode").val();
        if(imgCode.trim()==""){
            layer.msg("验证码不能为空",{icon: 2});
            return;
        }
        loginForm();
        // getGoogleVal(account);
        console.log("登录密码："+$.md5(password));
    }
    function getGooglePopup(){
        layui.layer.open({
            title:"输入Google验证码",
            type: 1,
            btn:["确定","取消"],
//            area: ['300px'],
            content: '<div id="loginGoogleVal" class="layui-form" style="margin:20px 40px 10px 40px;">'
            +   '<div class="layui-form-item">'
            +      '<div class="layui-input-block" style="margin: 0 auto;">'
            +        '<input type="text" required  lay-verify="required" placeholder="Google验证码" autocomplete="off" class="layui-input login_googleVal" style="height: 30px;"/>'
            +      '</div>'
            +    '</div>'
            +'</div>'
            ,yes: function(index, layero){ //确定按钮的回调
                googleVal = $("#loginGoogleVal .login_googleVal").val();
                 if(isGoogleSec){
                    if(googleVal.trim()==""){
                        layer.msg("google动态验证码不能为空",{icon: 2});
                        return;
                     }
                 }
                loginForm();
             return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
            }
        });
    }
    function loginForm(){
         layer.load(1);
         $.post("/withdrawlogin/login",{ account: account, password: $.md5(password),imgCode:imgCode,googleVal:googleVal,isGoogleSec:isGoogleSec}, function(data) {
         if (data.resultCode == 1) {
               layer.msg("登录成功",{icon: 1});
               console.log("Login data:"+JSON.stringify(data))
               localStorage.setItem("access_Token",data.data.access_Token);
               localStorage.setItem("role",data.data.role);
               localStorage.setItem("account",data.data.account);
               localStorage.setItem("adminId",data.data.adminId);
               localStorage.setItem("apiKey",data.data.apiKey);
               localStorage.setItem("nickname",data.data.nickname);
               localStorage.setItem("registerInviteCode",data.data.registerInviteCode); //系统邀请码模式
               localStorage.setItem("apiAccessStatus",data.data.apiAccessStatus); //系统ip白名单
               localStorage.setItem("nodeConfigStatus",data.data.nodeConfigStatus); //多节点状态

               localStorage.setItem("appDiscoverStatus",data.data.appDiscoverStatus); //多节点状态
               localStorage.setItem("appTabBarStatus",data.data.appTabBarStatus); //多节点状态
               localStorage.setItem("googleValue",data.data.googleValue); //多节点状态
               setTimeout(function() {
                   location.replace("/pages/withdrawlogin/index.html");
               }, 1000);

         } else if(data.resultCode == 0) {
                layer.closeAll('loading');
                     //layer.msg(data.resultMsg,{icon: 2});
                layer.msg(data.resultMsg,{icon: 2});

         }else if(data.resultCode == 1040216){
                layer.closeAll('loading');
                layer.msg(data.resultMsg,{icon: 2});
         }
      }, "json");

    }
    //表单输入效果
    $(".loginBody .input-item").click(function(e){
        e.stopPropagation();
        $(this).addClass("layui-input-focus").find(".layui-input").focus();
    })
    $(".loginBody .layui-form-item .layui-input").focus(function(){
        $(this).parent().addClass("layui-input-focus");
    })
    $(".loginBody .layui-form-item .layui-input").blur(function(){
        $(this).parent().removeClass("layui-input-focus");
        if($(this).val() != ''){
            $(this).parent().addClass("layui-input-active");
        }else{
            $(this).parent().removeClass("layui-input-active");
        }
    })

    $.post("/projectConfig", function(data) {
        console.log(data);
        if (data.resultCode == 1) {
            var link = document.createElement('link');
            link.rel = 'icon';
            link.href = data.data.projectIco;
            document.getElementsByTagName('head')[0].appendChild(link);
            $("#projectLogo").attr("src",data.data.projectLogo);
            $("#projectName").text(data.data.projectName==null?"":data.data.projectName+"●");
        }
    }, "json");

    var accountLogin = $("#account").val();
    if(accountLogin.trim()!=""){
        getImgCode(accountLogin);
    }
});