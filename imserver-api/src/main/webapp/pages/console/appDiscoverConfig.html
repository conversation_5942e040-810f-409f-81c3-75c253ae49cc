<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>app发现页管理</title>
	<style>
		.hide{display:none}
	</style>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">

<body>
<div class="layui-row">
		<div id="back" class="layui-col-md1">&nbsp;</div>
		<input id="pageCount" type="" name="" style="display: none">
		<!--<input type="" name="" id="save_roomId" style="display: none;">-->
		<div id="Discover_div" class="layui-col-md10">
			<div class="discover_btn_div" style="margin-top: 2%">
				<input type="text" name="" class="layui-input keyword" style="width: 15%;display: inline" placeholder="名称">
				<button class="layui-btn  search_discoverList">搜索</button>
				<button onclick="Discover.addDiscover()" class="layui-btn btn_addLive">新增发现页配置</button>
			</div>
			<div id="discoverList" class="layui-card" style="margin-top: 1%">
				<div class="layui-card-header">app发现页列表</div>
				<div class="layui-card-body">
					<table id="discover_table" lay-filter="discover_table"></table>
				</div>
			</div>
			<div id="updateDiscover" class="layui-col-md10" style="display: none;">
				<div class="layui-card" style="margin-top: 1%">
					<div class="layui-card-header">修改发现页配置</div>
					<div class="layui-card-body">
						<table cellspacing="0" cellpadding="0" border="0" class="layui-table">
							<tr>
								<td>图标</td>
								<td>
									<label class = "layui-input" placeholder="请上传PNG图像，最大不超过1M">
										<img src="" id="imgUpdate" width="350" class="hide" value="" style="height: 28px;width:28px;"/>
										<input id="discoverImgUpdate" type="file" name="" class="layui-input" style="display: none" onchange="Discover.pushImgUpdate()">
									</label>
									请上传PNG图像，最大不超过1M
								<td>
							</tr>
							<tr>
								<td>名称</td>
								<td><input id="discoverNameUpdate" type="text" name="" class="layui-input" placeholder="不超过8个字"></td>
							</tr>
							<tr>
								<td>链接地址</td>
								<td><input id="discoverLinkURLUpdate" type="text" name="" class="layui-input" placeholder="请输入链接"></td>
							</tr>
							<tr>
								<td>顺序</td>
								<td><input id="discoverNumUpdate" type="text" name="" class="layui-input" placeholder="请输入数字大于7" onchange="Discover.numChange()"></td>
							</tr>
						</table>
						<button onclick="Discover.commit_updateDiscover()" class="layui-btn">提交</button>
						<button onclick="Discover.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
					</div>
				</div>
			</div>
		</div>
		<div id="addDiscover" class="layui-col-md10" style="display: none;">
			<div class="layui-card" style="margin-top: 1%">
				<div class="layui-card-header">新增发现页配置</div>
				<div class="layui-card-body">
					<table cellspacing="0" cellpadding="0" border="0" class="layui-table">
						<tr>
							<td>图标</td>
							<td>
								<label class = "layui-input" placeholder="请上传PNG图像，最大不超过1M" >
									<img src="" id="img" width="350" class="hide" value="" style="height: 28px;width:28px;"/>
									<input id="discoverImg" type="file" name="" class="layui-input" style="display: none" onchange="Discover.pushImg()">
								</label>
                                请上传PNG图像，最大不超过1M
							<td>
						</tr>
						<tr>
							<td>名称</td>
							<td><input id="discoverName" type="text" name="" class="layui-input" placeholder="不超过8个字"></td>
						</tr>
						<tr>
							<td>链接地址</td>
							<td><input id="discoverLinkURL" type="text" name="" class="layui-input" placeholder="请输入链接"></td>
						</tr>
						<tr>
							<td>顺序</td>
							<td><input id="discoverNum" type="text" name="" class="layui-input" placeholder="请输入数字大于7" onchange="Discover.numChange()"></td>
						</tr>
					</table>
					<button onclick="Discover.commit_addDiscover()" class="layui-btn ">提交</button>
					<button onclick="Discover.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
				</div>
			</div>
		</div>
	</div>
	<!--操作-->
	<script type="text/html" id="discoverListBar">
		{{#  if(true){ }}
			{{# if(d.discoverStatus==1){ }}
				<a class="layui-btn layui-btn-primary layui-btn-xs status" lay-event="statusShow">显示</a>
			{{# }else{ }}
				<a class="layui-btn layui-btn-primary layui-btn-xs status" lay-event="statusHidden">隐藏</a>
			{{#  } }}
			<a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="delete">删除</a>
			<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="update">修改</a>
		{{#  }else{ }}
			{{# if(d.discoverStatus==1){ }}
				<a class="layui-btn layui-btn-primary layui-btn-xs status" lay-event="statusShow">显示</a>
			{{# }else{ }}
				<a class="layui-btn layui-btn-primary layui-btn-xs status" lay-event="statusHidden">隐藏</a>
			{{#  } }}
			<a class="layui-btn layui-btn-disabled layui-btn-xs deleteSystem" lay-event="deleteSystem">删除</a>
			<a class="layui-btn layui-btn-disabled layui-btn-xs updateSystem" lay-event="updateSystem">修改</a>
		{{#  } }}
	</script>
	<script type="text/html" id="imgDisplay">
		<img src ="{{d.discoverImg}}"  id="imgDisplayId" style="height: 28px;width: 28px;"/>
	</script>
	<!--<script type="text/html" id="statusDisplay">-->
		<!--<input type="checkbox" name="lock" value="{{d.discoverStatus}}" class="status" title="隐藏" lay-filter="lockDemo">-->
	<!--</script>-->
<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/common/jquery/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="text/javascript" src="./js/common.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="/pages/console/js/appDiscoverConfig.js"></script>
<!--<script type="text/javascript" src="/pages/js/console_init.js"></script>-->
</body>
</html>