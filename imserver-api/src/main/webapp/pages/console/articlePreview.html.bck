<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>文章预览</title>
</head>
<body>
<div id="previewArticleOuter" style="padding-top: 3%;padding-left: 8%;">
    <div id="previewArticleInner" style="width: 90%; border: 1px solid grey">
        <p class="articleFirstName" style="text-align: center;font-size: xx-large">1</p>
        <p class="articleSecondName" style="text-align: center;font-size: x-large">
            <input type="hidden" value="${aid}" id="stuName"/>
        </p>
        <div class="articleContent"></div>
        <div>
            <span class="articleCreatetime" style="display: inline-block; float: left;">创建时间：2018-10-30 11:55</span>
            <span class="articleReadCount" style="display: inline-block; float: right;">阅读数：0</span>
        </div>
    </div>
</div>
<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript">
    //预览文章
    var PA = {
        previewArticle : function(obj) {
            var objectId = PA.getRequestUrlArg();

            //加载当前点击的文章数据，成功后处理回显
            $.ajax({
                type:'POST',
                url : '/mp/articleList',
                dataType:"json",
                //加上这句话
                xhrFields: {
                    withCredentials: true
                },
                data:{
                    objectId:objectId
                },
                crossDomain: true,
                async: false,
                success:function(result){
                    //这里处理查询出来的数据放到预览位置
                    var articleObj = result.data[0];
                    $(".articleFirstName").empty().append(articleObj.articleFirstName);
                    $(".articleSecondName").empty().append(articleObj.articleSecondName);
                    $(".articleContent").empty().append(articleObj.articleContent);
                    $(".articleReadCount").empty().append("阅读数：" + articleObj.pageView);
                    $(".articleCreatetime").empty().append("创建时间：" + PA.getLocalTime(articleObj.createTime));
                }
            });
        },
        //当下这个url只有一个参数，直接写返回
        getRequestUrlArg : function () {
            var url = location.search.split('&')[0]; //获取url中"?"符后的字串
            var theRequest = '';
            if (url.indexOf("?") != -1) {
                theRequest = url.substr(5);
            }
            return theRequest;
        },
        getLocalTime:function(time){
            var date = new Date(time);
            var Y = date.getFullYear() + '-';
            var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            var D = date.getDate() + ' ';
            var h = date.getHours() + ':';
            var m = (date.getMinutes()<10?'0'+(date.getMinutes()):date.getMinutes()) + ':';
            var s = (date.getSeconds()<10?'0'+(date.getSeconds()):date.getSeconds());
            return Y+M+D+h+m+s;
        },
    }
    PA.previewArticle();
</script>
</body>
</html>