<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>IP白名单</title>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet" media="all">
<body>
<div class="layui-row">
    <div class="layui-col-md1">&nbsp;</div>
    <input id="pageCount" type="" name="" style="display: none">
    <div id="ipWhiteList" class="layui-col-md10">
        <form class="layui-form" action="" style="margin-top: 2%">
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label" style="width: 200px;margin-left: 20px;padding: 0px;text-align: left;font-size: 18px;"><strong>接口API调用IP白名单</strong></label>
            </div>
            <div class="layui-form-item layui-form-text">
                <div class="layui-input-block" style="margin-left: 48px;">
                    <textarea id="ipWhiteListText" name="desc" placeholder="请输入白名单IP" class="layui-textarea" style= "overflow:hidden;resize:none;width: 50%;min-height: 350px;" readOnly></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 48px;">
                    <div id="updateDiv">
                        <button type="button" style="width: 80px;" onclick="updateButton()" class="layui-btn layui-btn-sm ">修改</button>
                    </div>
                    <div id="commitDiv" hidden="hidden">
                        <button type="button" style="width: 80px;" onclick="commonButton()" class="layui-btn layui-btn-sm ">提交</button>
                        <button type="button" style="width: 80px;cursor:pointer;" onclick="backButton()" class="layui-btn layui-btn-sm layui-btn-disabled">返回</button>
                    </div>
                </div>
            </div>
            <div style="margin-left: 20px;">
                <p>说明：</p>
                <p style="text-indent:2em">1.默认为空，禁止所有的IP访问特定的接口。</p>
                <p style="text-indent:2em">2.调用API接口时需要将调用服务器IP加入白名单中，否则将会报错。</p>
                <p style="text-indent:2em">3.可以填写多个IP,多个IP请使用半角英文逗号【,】或换行格式完成</p>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/core.js"></script>
<script type="text/javascript" src="./js/tripledes.js"></script>
<script type="text/javascript" src="/pages/console/js/userList.js"></script>
<!-- <script type="text/javascript" src="/pages/js/console_init.js"></script> -->
<script>
    getIpWhite();
    function getIpWhite(){
        $.post("/console/getIpWhiteList", function(data) {
            if (data.resultCode == 1){
                $("#ipWhiteListText").val(data.resultMsg);
            }
        });
    }

    function commonButton(){
        let ipWhiteListText = $("#ipWhiteListText").val();
        $.post("/console/setIpWhiteList",{ipWhiteStr:ipWhiteListText}, function(data) {
            if (data.resultCode == 1){
                $("#ipWhiteListText").val(data.resultMsg);
                $("#ipWhiteListText").attr("readonly","readonly");
                $("#commitDiv").hide();
                $("#updateDiv").show();
            }else {
                let layer = layui.layer;
                layer.msg('设置接口API IP白名单失败', {
                    time: 2000
                },function () {
                    $("#ipWhiteListText").attr("readonly","readonly");
                    $("#commitDiv").hide();
                    $("#updateDiv").show();
                });
            }
        });
    }

    let  ipWhiteListText = $("#ipWhiteListText").val();
    function updateButton() {
        $("#ipWhiteListText").removeAttr("readonly");
        $("#commitDiv").show();
        $("#updateDiv").hide();
    }
    function backButton() {
        $("#ipWhiteListText").val(ipWhiteListText);
        $("#ipWhiteListText").attr("readonly","readonly");
        $("#commitDiv").hide();
        $("#updateDiv").show();
    }
</script>
</body>
</html>