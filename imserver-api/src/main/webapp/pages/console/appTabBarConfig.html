<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>app Tab Bar 管理</title>
	<style>
		.hide{display:none}
		.hide1{display:none}
	</style>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">

<body>
<div class="layui-row">
		<div id="back" class="layui-col-md1">&nbsp;</div>
		<input id="pageCount" type="" name="" style="display: none">
		<!--<input type="" name="" id="save_roomId" style="display: none;">-->
		<div id="tabBar_div" class="layui-col-md10">
			<div class="tabBar_btn_div" style="margin-top: 2%">
				<input type="text" name="" class="layui-input keyword" style="width: 15%;display: inline" placeholder="名称">
				<button class="layui-btn  search_tabBarList">搜索</button>
				<button onclick="TabBar.addTabBar()" class="layui-btn btn_addLive">新增TabBar配置</button>
			</div>
			<div id="tabBarList" class="layui-card" style="margin-top: 1%">
				<div class="layui-card-header">appTabBar列表</div>
				<div class="layui-card-body">
					<table id="tabBar_table" lay-filter="tabBar_table"></table>
				</div>
			</div>
			<div id="updateTabBar" class="layui-col-md10" style="display: none;">
				<div class="layui-card" style="margin-top: 1%">
					<div class="layui-card-header">修改TabBar配置</div>
					<div class="layui-card-body">
						<table cellspacing="0" cellpadding="0" border="0" class="layui-table">
							<tr>
								<td>点前图标</td>
								<td>
									<label class = "layui-input" placeholder="请上传PNG图像，最大不超过1M" >
										<img src="" id="imgUpdate" width="350" class="hide" value="" style="height: 28px;width:28px;"/>
										<input id="tabBarImgUpdate" type="file" name="" class="layui-input" style="display: none" onchange="TabBar.pushImgUpdate()">
									</label>
									请上传PNG图像，最大不超过1M
								<td>
							</tr>
							<tr>
								<td>点后图标</td>
								<td>
									<label class = "layui-input" placeholder="请上传PNG图像，最大不超过1M" >
										<img src="" id="imgUpdate1" width="350" class="hide1" value="" style="height: 28px;width:28px;"/>
										<input id="tabBarImgUpdate1" type="file" name="" class="layui-input" style="display: none" onchange="TabBar.pushImgUpdate1()">
									</label>
									请上传PNG图像，最大不超过1M
								<td>
							</tr>
							<tr>
								<td>名称</td>
								<td><input id="tabBarNameUpdate" type="text" name="" class="layui-input" placeholder="不超过8个字"></td>
							</tr>
							<tr>
								<td>链接地址</td>
								<td><input id="tabBarLinkURLUpdate" type="text" name="" class="layui-input" placeholder="请输入链接"></td>
							</tr>
							<tr>
								<td>顺序</td>
								<td><input id="tabBarNumUpdate" type="text" name="" class="layui-input" placeholder="请输入数字" ></td>
							</tr>
						</table>
						<button onclick="TabBar.commit_updateTabBar()" class="layui-btn">提交</button>
						<button onclick="TabBar.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
					</div>
				</div>
			</div>
		</div>
		<div id="addTabBar" class="layui-col-md10" style="display: none;">
			<div class="layui-card" style="margin-top: 1%">
				<div class="layui-card-header">新增TabBar配置</div>
				<div class="layui-card-body">
					<table cellspacing="0" cellpadding="0" border="0" class="layui-table">
						<tr>
							<td>点前图标</td>
							<td>
								<label class = "layui-input" placeholder="请上传PNG图像，最大不超过1M" >
									<img src="" id="img" width="350" class="hide" value="" style="height: 28px;width:28px;"/>
									<input id="tabBarImg" type="file" name="" class="layui-input" style="display: none" onchange="TabBar.pushImg()">
								</label>
                                请上传PNG图像，最大不超过1M
							<td>
						</tr>
						<tr>
							<td>点后图标</td>
							<td>
								<label class = "layui-input" placeholder="请上传PNG图像，最大不超过1M" >
									<img src="" id="img1" width="350" class="hide1" value="" style="height: 28px;width:28px;"/>
									<input id="tabBarImg1" type="file" name="" class="layui-input" style="display: none" onchange="TabBar.pushImg1()">
								</label>
								请上传PNG图像，最大不超过1M
							<td>
						</tr>
						<tr>
							<td>名称</td>
							<td><input id="tabBarName" type="text" name="" class="layui-input" placeholder="不超过8个字"></td>
						</tr>
						<tr>
							<td>链接地址</td>
							<td><input id="tabBarLinkURL" type="text" name="" class="layui-input" placeholder="请输入链接"></td>
						</tr>
						<tr>
							<td>顺序</td>
							<td><input id="tabBarNum" type="text" name="" class="layui-input" placeholder="请输入数字" ></td>
						</tr>
					</table>
					<button onclick="TabBar.commit_addTabBar()" class="layui-btn ">提交</button>
					<button onclick="TabBar.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
				</div>
			</div>
		</div>
	</div>
	<!--操作-->
	<script type="text/html" id="tabBarListBar">
			{{# if(d.tabBarStatus==1){ }}
				<a class="layui-btn layui-btn-primary layui-btn-xs status" lay-event="statusShow">显示</a>
			{{# }else{ }}
				<a class="layui-btn layui-btn-primary layui-btn-xs status" lay-event="statusHidden">隐藏</a>
			{{#  } }}
			<a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="delete">删除</a>
			<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="update">修改</a>
	</script>
	<script type="text/html" id="imgDisplay">
		<img src ="{{d.tabBarImg}}"  id="imgDisplayId" style="height: 28px;width: 28px;"/>
	</script>
	<script type="text/html" id="imgDisplay1">
		<img src ="{{d.tabBarImg1}}"  id="imgDisplayId1" style="height: 28px;width: 28px;"/>
	</script>
<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/common/jquery/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="text/javascript" src="./js/common.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="/pages/console/js/appTabBarConfig.js"></script>
<!--<script type="text/javascript" src="/pages/js/console_init.js"></script>-->
</body>
</html>