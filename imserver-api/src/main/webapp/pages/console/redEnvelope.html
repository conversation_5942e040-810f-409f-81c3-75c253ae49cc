<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>红包列表</title>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">
<!--<script type="text/javascript" src="/pages/js/console_init.js"></script>-->
<body>
<div class="layui-row">
	<div id="back" class="layui-col-md1">&nbsp;</div>
	<input id="pageCount" type="" name="" style="display: none">
	<div id="redPage_div" class="layui-col-md10">
		<div class="redPage_btn_div" style="margin-top: 2%">
			<input id="toUserName" type="text" name="" class="layui-input" style="width: 15%;display: inline" placeholder="请输入发送人用户ID或昵称">
			<button class="layui-btn search_live">搜索</button>
		</div>
		<div id="redEnvelope" class="layui-card" style="margin-top: 1%">
			<div class="layui-card-header">红包列表</div>
			<div class="layui-card-body">
				<table id="redEnvelope_table" lay-filter="redEnvelope_table"></table>
			</div>
		</div>

		<!-- 红包流水 -->
		<div id="receiveWater" class="layui-card" style="margin-top: 1%;display: none;">
			<div class="layui-card-header">红包领取详情</div>
			<div class="layui-card-body">
				<table id="receiveWater_table" lay-filter="receiveWater_table">

				</table>
				<button onclick="Red.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
			</div>
		</div>
		<!--操作-->
		<script type="text/html" id="redPageListBar">
			<a class="layui-btn layui-btn-primary layui-btn-xs receiveWaterInfo" lay-event="receiveWaterInfo">领取详情</a>
		</script>
	</div>

</div>

<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/common/jquery/jquery.md5.js"></script>
<script type="text/javascript" src="./js/common.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="./js/redEnvelope.js"></script>
<!--<script type="text/javascript" src="/pages/js/console_init.js"></script>-->
</body>
</html>