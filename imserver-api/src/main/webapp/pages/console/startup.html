<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/pages/common/layui/css/layui.css" media="all"/>
    <link rel="stylesheet" href="./css/public.css" media="all"/>

    <title>启动引导</title>
</head>
<body>
<div class="layui-tab layui-tab-brief" lay-filter="demo">

    <div class="layui-tab-content layui-row">
        <div id="back" class="layui-col-md1">&nbsp;</div>

        <div id="aaa" class="layui-tab-item layui-tab-card layui-show layui-col-md10">
            <div id="startupList">
                <table id="startup_list" lay-filter="startup_list">

                </table>
                <div class="magt10 layui-center">
                    <div class="layui-input-block" style="margin-left: 0px;margin-bottom: 13px;">
                        <button onclick="Clu.addStartUP()" class="layui-btn save" lay-submit=""
                                lay-filter="systemConfig">添加
                        </button>
                    </div>
                </div>
            </div>
            <div id="startup_area" style="display: none;">
                <div class="layui-card-header">新增引导</div>
                <div class="layui-card-body">
                    <table cellspacing="0" cellpadding="0" border="0" class="layui-table">
                        <tbody>
                        <tr>
                            <td>标题</td>
                            <td>
                                <input type="hidden" name="" id="media_id" style="display: none;">
                                <input type="text" class="layui-input media_subject" lay-verify="required"
                                       placeholder="请输入标题">
                            </td>
                        </tr>
                        <tr>
                            <td>引导图片</td>
                            <td>
                                <input id="media_addr" type="hidden" />
                                <button class="layui-btn" onclick="Clu.selectCover()">选择图片</button>
                                <span id="imageCover"></span>
                                <form id="uploadImage" name="uploadImage" action="" method="post"
                                      enctype="multipart/form-data" style="display: none;">
                                    <input id="uploadCover" type="file" name="file" onchange="Clu.uploadCover()">
                                </form>
                                1242*1876（内容尽量靠近图片中间，边缘会在适配部分机型时被切割）
                            </td>
                        </tr>
                        <tr>
                            <td>描述</td>
                            <td>
                                <input type="text" class="layui-input media_remark" lay-verify="required"
                                       placeholder="请输入描述">
                            </td>
                        </tr>
                        </tbody>
                    </table>

                    <button lay-submit="" lay-filter="systemConfig" class="layui-btn" type="button"
                            onclick="Clu.commit_startUP()">保存
                    </button>
                    <button class="layui-btn layui-btn-primary" type="button" onclick="Clu.back()">返回</button>

                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/html" id="startUPList_toolbar">
    <a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="delete">删除</a>
    <!--	<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="update">修改</a>-->
</script>

<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="text/javascript" src="./js/common.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="/pages/console/js/startup.js"></script>

<script type="text/javascript">
</script>
</body>
</html>