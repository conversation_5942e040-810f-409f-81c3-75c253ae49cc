<!DOCTYPE  html>
<html>
<head>
	<meta charset="utf-8">
	<title>封禁信息</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">

<body class="childrenBody">
	<div class="layui-row">
		<div class="layui-col-md1">&nbsp;</div>
		<div id="deviceList" class="layui-col-md10">
			<div class="layui-card-header"><p></p></div>
			<div class="layui-tab">
				<ul class="layui-tab-title">
					<li class="layui-this">用户</li>
					<li>登录设备</li>
					<li>登录IP</li>
				</ul>
				<div class="layui-tab-content">
					<div class="layui-tab-item layui-show">
						<div class="user_btn_div" style="margin-top: 2%">
							<input type="text" name="" id="search_key_user" class="layui-input keyWordUsers" style="width: 50%;display: inline;margin-right: 10px;" placeholder="请输入用户ID/昵称/手机号/用户名">
							<button class="layui-btn search_users">搜索</button>
							<button onclick="Forbid.UnforbidUsers('')" class="layui-btn btn_unforbid">解封</button>
						</div>
						<div id="forbid_user_table" class="layui-card" style="margin-top: 1%">
							<div class="layui-card-body">
								<table id="forbid_user_list" lay-filter="forbid_user_list" style="table-layout:fixed;word-break:break-all;" >

								</table>
							</div>
						</div>
					</div>
					<div class="layui-tab-item">
						<div class="device_btn_div" style="margin-top: 2%">
							<input type="text" name="" id="search_key_device" class="layui-input keyWordDevice" style="width: 50%;display: inline;margin-right: 10px;" placeholder="请输入设备编号/用户ID/用户名">
							<button class="layui-btn search_device">搜索</button>
							<button onclick="Forbid.unforbidDevice('')" class="layui-btn btn_device">解封</button>
						</div>
						<div id="forbid_device_table" class="layui-card" style="margin-top: 1%">
							<div class="layui-card-body">
								<table id="forbid_device_history" lay-filter="forbid_device_history" style="table-layout:fixed;word-break:break-all;" >

								</table>
							</div>
						</div>
					</div>
					<div class="layui-tab-item">
						<div class="ip_btn_div" style="margin-top: 2%">
							<input type="text" name="" id="search_key_ip" class="layui-input keyWordIp" style="width: 50%;display: inline;margin-right: 10px;" placeholder="请输入登录IP/用户ID/用户名">
							<button class="layui-btn search_ip">搜索</button>
							<button onclick="Forbid.unforbidIp('')" class="layui-btn btn_ip">解封</button>
						</div>
						<div id="forbid_ip_Table" class="layui-card" style="margin-top: 1%">
							<div class="layui-card-body">
								<table id="forbid_ip_history" lay-filter="forbid_ip_history" style="table-layout:fixed;word-break:break-all;" >

								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script type="text/html" id="forbidUserBar">
		<a class="layui-btn layui-btn-primary layui-btn-xs reOpenUser" lay-event="reOpenUser">解封</a>
		<a class="layui-btn layui-btn-primary layui-btn-xs loginHistory" lay-event="loginHistory">登录历史</a>
	</script>
	<script type="text/html" id="forbidDeviceBar">
		<a class="layui-btn layui-btn-primary layui-btn-xs reOpenDevice" lay-event="reOpenDevice">解封</a>
		<a class="layui-btn layui-btn-primary layui-btn-xs loginDeviceUsers" lay-event="loginDeviceUsers">常登用户</a>
	</script>
	<script type="text/html" id="forbidIPBar">
		<a class="layui-btn layui-btn-primary layui-btn-xs reOpenIP" lay-event="reOpenIP">解封</a>
		<a class="layui-btn layui-btn-primary layui-btn-xs loginIPUsers" lay-event="loginIPUsers">常登用户</a>
	</script>
<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="/pages/console/js/roomList.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript">
var page=0;
var sum=0;
var lock=0;
var updateId="";
var currentPageIndex;// 当前页码数
var currentCount;// 当前总数
var unforbidUserIds = new Array();
var unforbidUserDevices = new Array();
var unforbidUserIPs = new Array();
layui.use(['form','layer','laydate','table','laytpl'],function(){
	var form = layui.form,
		layer = parent.layer === undefined ? layui.layer : top.layer,
		$ = layui.jquery,
		laydate = layui.laydate,
		laytpl = layui.laytpl,
		table = layui.table;
    var userIds = new Array();
    var devices = new Array();
    var ips = new Array();
    console.log(' currentUserId '+localStorage.getItem("currentUserId"));

    var tableInsUser = layui.table.render({
        elem: '#forbid_user_list'
        ,url:request("/console/getAllForbidUsers")
        ,id: 'forbid_user_list'
        ,page: true
        ,curr: 0
        ,limit:10
        ,limits:[10,15,20]
        ,groups: 7
        ,cols: [[ //表头
        	 {type:'checkbox',fixed:'left'}
        	,{field: 'userId', title: '用户ID',sort:'true', width:120}
            ,{field: 'nickname', title: '昵称',sort:'true', width:100}
            ,{field: 'account', title: '用户名',sort:'true', width:100}
            ,{field: 'phone', title: '手机号',sort:'true', width:150}
            ,{field: 'serial', title: '设备编号',sort:'true', width:150}
            ,{field: 'loginIp', title: '登录IP',sort:'true', width:150}
            ,{field: 'forbidUsersTime', title: '封禁时间',sort:'true', width:170,templet: function(d){
                    return UI.getLocalTime(d.forbidUsersTime);
                }}
            , {fixed: 'right', width: 250, title: "操作", align: 'left', toolbar: '#forbidUserBar'}
        ]]
        ,done:function(res, curr, count){
        	 if (count == 0 && lock == 1) {
                layer.msg("暂无数据", {"icon": 2});
                renderTable();
            }
            var pageIndex = tableInsUser.config.page.curr;//获取当前页码
            var resCount = res.count;// 获取table总条数
            currentCount = resCount;
            currentPageIndex = pageIndex;
        }
    });
    var tableInsDevice = layui.table.render({
        elem: '#forbid_device_history'
        ,url:request("/console/getAllForbidDevice")
        ,id: 'forbid_device_history'
        ,page: true
        ,curr: 0
        ,limit:10
        ,limits:[10,15,20]
        ,groups: 7
        ,cols: [[ //表头
        	 {type:'checkbox',fixed:'left'}
        	,{field: 'device', title: '登录设备类型',sort:'true', width:120}
            ,{field: 'forbidDevice', title: '设备编号',sort:'true', width:150}
            ,{field: 'time', title: '封禁时间',sort:'true', width:170,templet: function(d){
                    return UI.getLocalTime(d.time);
                }}
            , {fixed: 'right', width: 250, title: "操作", align: 'left', toolbar: '#forbidDeviceBar'}
        ]]
        ,done:function(res, curr, count){
         	if (count == 0 && lock == 1) {
                layer.msg("暂无数据", {"icon": 2});
                renderTable();
            }
			var pageIndex = tableInsDevice.config.page.curr;//获取当前页码
            var resCount = res.count;// 获取table总条数
            var currentCount = resCount;
            var currentPageIndex = pageIndex;
        }

    });
     var tableInsIP = layui.table.render({
        elem: '#forbid_ip_history'
        ,url:request("/console/getAllForbidIp")
        ,id: 'forbid_ip_history'
        ,page: true
        ,curr: 0
        ,limit:10
        ,limits:[10,15,20]
        ,groups: 7
        ,cols: [[ //表头
        	 {type:'checkbox',fixed:'left'}
            ,{field: 'forbidIp', title: '登录IP',sort:'true', width:150}
            ,{field: 'time', title: '封禁时间',sort:'true', width:170,templet: function(d){
                    return UI.getLocalTime(d.time);
                }}
            , {fixed: 'right', width: 250, title: "操作", align: 'left', toolbar: '#forbidIPBar'}
        ]]
        ,done:function(res, curr, count){
         	if (count == 0 && lock == 1) {
                layer.msg("暂无数据", {"icon": 2});
                renderTable();
            }
			var pageIndex = tableInsIP.config.page.curr;//获取当前页码
            var resCount = res.count;// 获取table总条数
            var currentCount = resCount;
            var currentPageIndex = pageIndex;
        }

    });
     table.on('tool(forbid_user_list)', function (obj) {
        var layEvent = obj.event, data = obj.data;
        if (layEvent === 'reOpenUser') { //解封用户
            if (data.userId <= 10000) {
                return;
            }
            Forbid.UnforbidUsers(data.userId);
        }else if(layEvent === 'loginHistory'){
            if (data.userId <= 10000) {
                return;
            }
            Forbid.LoginHistory(data.userId);
        }
     });
     table.on('tool(forbid_device_history)', function (obj) {
        var layEvent = obj.event, data = obj.data;
        if (layEvent === 'reOpenDevice') { //解封设备
            if (data.userId <= 10000) {
                return;
            }
            Forbid.unforbidDevice(data.forbidDevice);
        }else if(layEvent === 'loginDeviceUsers'){
            if (data.userId <= 10000) {
                return;
            }
            Forbid.userLoginHistory(data.forbidDevice,0);
        }
     });
     table.on('tool(forbid_ip_history)', function (obj) {
        var layEvent = obj.event, data = obj.data;
        if (layEvent === 'reOpenIP') { //解封IP
            if (data.userId <= 10000) {
                return;
            }
            Forbid.unforbidIp(data.forbidIp);
        }else if(layEvent === 'loginIPUsers'){
            if (data.userId <= 10000) {
                return;
            }
            Forbid.userLoginHistory(data.forbidIp,1);
        }
     });
         //搜索锁定用户
    $(".search_users").on("click", function () {
        // 关闭超出宽度的弹窗
        $(".layui-layer-content").remove();
        table.reload("forbid_user_list", {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: {
                keyWord: $(".keyWordUsers").val()  //搜索的关键字
            }
        })
        lock = 1;
    });
             //搜索锁定设备
    $(".search_device").on("click", function () {
        // 关闭超出宽度的弹窗
        $(".layui-layer-content").remove();
        table.reload("forbid_device_history", {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: {
                keyWord: $(".keyWordDevice").val()  //搜索的关键字
            }
        })
        lock = 1;
    });
             //搜索锁定IP
    $(".search_ip").on("click", function () {
        // 关闭超出宽度的弹窗
        $(".layui-layer-content").remove();
        table.reload("forbid_ip_history", {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: {
                keyWord: $(".keyWordIp").val()  //搜索的关键字
            }
        })
        lock = 1;
    });
});
var Forbid = {
	UnforbidUsers:function (userId){
		if(userId==""){
			// 多选操作
			var checkStatus = layui.table.checkStatus('forbid_user_list');
			for (var i = 0; i < checkStatus.data.length; i++){
				unforbidUserIds.push(checkStatus.data[i].userId);
			}
			console.log(unforbidUserIds);
			if(0 == checkStatus.data.length){
				layer.msg("请勾选要删除的行");
				return;
			}
			userId = unforbidUserIds.join(",");
		}
	    layer.confirm('确定解封用户', {
        icon: 3, title: '提示消息', yes: function () {
          myFn.invoke({
               url: request('/console/unforbidUsers'),
               data: {
                   userIds: userId
               },
               success: function (result) {
               		if (result.resultCode == 1) {
                          layer.msg("解封成功", {"icon": 1});
                          Common.tableReload(currentCount, currentPageIndex, 1, "forbid_user_list");
                    }else{
                         layer.alert(result.resultMsg);
                     }
                     layui.form.render();
               },
               error: function (result) {
                     if (result.resultCode == 0) {
                          layer.alert(result.resultMsg);
                     }
               }
           });
          }, btn2: function () {
                userIds = [];
          }, cancel: function () {
                userIds = [];
          }
        });
	},
	LoginHistory:function(userId){
		localStorage.setItem("currentUserId", userId);
	        layer.open({
				title: "",
				type: 2,
				skin: 'layui-layer-rim', //加上边框
				area: ['1050px', '750px'], //宽高
				content: 'getAllDevice.html'
				, success: function (index, layero) {
				}
			});
	},
	unforbidDevice:function (device){
		if(device==""){
			// 多选操作
			var checkStatus = layui.table.checkStatus('forbid_device_history');
			for (var i = 0; i < checkStatus.data.length; i++){
				unforbidUserDevices.push(checkStatus.data[i].forbidDevice);
			}
			console.log(unforbidUserDevices);
			if(0 == checkStatus.data.length){
				layer.msg("请勾选要删除的行");
				return;
			}
			device = unforbidUserDevices.join(",");
		}
	    layer.confirm('确定解封设备', {
        icon: 3, title: '提示消息', yes: function () {
          myFn.invoke({
               url: request('/console/unforbidDevices'),
               data: {
                   devices: device
               },
               success: function (result) {
               		if (result.resultCode == 1) {
                          layer.msg("解封成功", {"icon": 1});
                          Common.tableReload(currentCount, currentPageIndex, 1, "forbid_device_history");
                    }else{
                         layer.alert(result.resultMsg);
                     }
                     layui.form.render();
               },
               error: function (result) {
                     if (result.resultCode == 0) {
                          layer.alert(result.resultMsg);
                     }
               }
           });
          }, btn2: function () {
                devices = [];
          }, cancel: function () {
                devices = [];
          }
        });
	},
	unforbidIp:function (ip){
		if(ip==""){
			// 多选操作
			var checkStatus = layui.table.checkStatus('forbid_ip_history');
			for (var i = 0; i < checkStatus.data.length; i++){
				unforbidUserIPs.push(checkStatus.data[i].forbidIp);
			}
			console.log(unforbidUserIPs);
			if(0 == checkStatus.data.length){
				layer.msg("请勾选要删除的行");
				return;
			}
			ip = unforbidUserIPs.join(",");
		}
	    layer.confirm('确定解封IP', {
        icon: 3, title: '提示消息', yes: function () {
          myFn.invoke({
               url: request('/console/unforbidIps'),
               data: {
                   ips: ip
               },
               success: function (result) {
               		if (result.resultCode == 1) {
                          layer.msg("解封成功", {"icon": 1});
                          Common.tableReload(currentCount, currentPageIndex, 1, "forbid_ip_history");
                    }else{
                         layer.alert(result.resultMsg);
                     }
                     layui.form.render();
               },
               error: function (result) {
                     if (result.resultCode == 0) {
                          layer.alert(result.resultMsg);
                     }
               }
           });
          }, btn2: function () {
                ips = [];
          }, cancel: function () {
                ips = [];
          }
        });
	},
	userLoginHistory:function(deviceMsg,status){
        localStorage.setItem("deviceMsg", deviceMsg);
        localStorage.setItem("status", status);
        layer.open({
            title: "",
            type: 2,
            skin: 'layui-layer-rim', //加上边框
            area: ['800px', '700px'], //宽高
            content: 'deviceUserDetailsPopup.html'
            , success: function (index, layero) {

            }
        });
	}
}
</script>

</body>
</html>