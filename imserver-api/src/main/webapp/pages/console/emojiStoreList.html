<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>表情商店列表</title>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">

<body>
<div class="layui-row">
    <div class="layui-col-md1">&nbsp;</div>
    <input id="pageCount" type="" name="" style="display: none">
    <div id="emojiList" class="layui-col-md10">
        <div class="admin_btn_div" style="margin-top: 2%">
            <input type="text" name="" class="layui-input admin_keyword" style="width: 15%;display: inline" placeholder="名称">
            <button class="layui-btn  search_emojiList">搜索</button>
            <button class="layui-btn  btn_addEmojiList">新增表情包</button>

        </div>

        <div class="layui-card emoji_table" style="margin-top: 1%">
            <div class="layui-card-header">表情包列表</div>
            <div class="layui-card-body">
                <table id="emojiStore_list" lay-filter="emojiStore_list" style="table-layout:fixed;word-break:break-all;" >

                </table>
            </div>
        </div>
        <div id="add_emojiList" class="layui-col-md10" style="display: none;">
            <div class="layui-card" style="margin-top: 1%">
                <div class="layui-card-header">新增表情包</div>
                <div class="layui-card-body">
                    <table cellspacing="0" cellpadding="0" border="0" class="layui-table">
                        <tr>
                            <td>名称</td>
                            <td>
                                <input type="text"  placeholder="输入表情包名称" autocomplete="off" class="layui-input emojiName">
                            <td>
                        </tr>
                        <tr>
                            <td>zip路径</td>
                            <td>
                                <label class = "layui-btn" onchange="EmojiStore.changeZipPath()">
                                    请上传zip路径
                                    <input type="file" name="file" id="zipId" value='zip' style="display: none"/>
                                </label>
                                <span id="zipSpanPath"></span>
                            <td>
                        </tr>
                        <tr>
                            <td>简介</td>
                            <td>
                                <input type="text"  placeholder="输入表情包简介" autocomplete="off" class="layui-input emojiProfile">
                            </td>
                        </tr>
                        <tr>
                            <td>表情名称</td>
                            <td>
                                <input type="text"  placeholder="输入表情包里的每个表情的名称并用 / 隔开" autocomplete="off" class="layui-input emojiEveryName">
                            <td>
                        </tr>
                    </table>
                    <button onclick="EmojiStore.commit_addEmoji()" class="layui-btn ">提交</button>
                    <button onclick="EmojiStore.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
                </div>
            </div>
        </div>
    </div>


    </div>
</div>

<!--操作-->
<script type="text/html" id="emojiStoreBar">
    <a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="delete">删除</a>
    <!--<a class="layui-btn layui-btn-primary layui-btn-xs locking" lay-event="locking">禁用</a>-->
</script>

<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="/pages/console/js/emojiStoreList.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>


</body>
</html>