<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/pages/common/layui/css/layui.css" media="all" />
    <link rel="stylesheet" href="./css/public.css" media="all" />
    <link href="./css/scrollbar.css" rel="stylesheet">
</head>
<body class="scrollbar_zdy" style="margin-left: 15px;margin-right: 15px;">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 24px;">
        <legend>短信配置</legend>
    </fieldset>
    <div class="" style="margin-bottom: 10px;padding: 5px;">
        <form class="layui-form layui-form-pane">
            <table class="layui-table mag0">
                <colgroup>
                    <col width="25%">
                    <col width="45%">
                    <col>
                </colgroup>
                <thead>
                <tr>
                    <th>参数说明</th>
                    <th>参数值</th>
                    <th>变量名</th>
                    <th>参数说明</th>
                </tr>
                </thead>
                <tbody>
                <!--<tr>-->
                    <!--<td>是否开启短信</td>-->
                    <!--<td>-->
                        <!--<select class="layui-input-inline isOpenSMS">-->
                            <!--<option value="1">开启</option>-->
                            <!--<option value="0">关闭</option>-->
                        <!--</select>-->
                    <!--</td>-->
                    <!--<td pc>isOpenSMS</td>-->
                    <!--<td></td>-->
                <!--</tr>-->
                <tr>
                    <td>短信服务支持</td>
                    <td>
                        <select class="layui-input-inline SMSType">
                            <option value="">请选择短信类型</option>
                            <option value="aliyun">阿里云短信</option>
                            <option value="ttgj">天天国际短信</option>
                            <option value="yunpian">云片国际短信</option>
                            <option value="mandao">漫道短信</option>
                            <option value="juhe">聚合短信</option>
                            <option value="chuanglang">创蓝短信</option>
                        </select>
                    </td>
                    <td>
                        SMSType
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td style="border: 0"></td>
                    <td style="border: 0;text-align: center;">天天国际:</td>
                </tr>
                <tr>
                    <tr>
                        <td>用户名：</td>
                        <td>
                            <div><input type="text" class="layui-input ttSmsName" placeholder="请输入用户名"></div>
                        </td>
                        <td>ttSmsName</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>密码：</td>
                        <td><input type="text"  class="layui-input ttSmsPassword" placeholder="请输入密码"></td>
                        <td>ttSmsPassword</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>中文短信模板：</td>
                        <td><input type="text"  class="layui-input ttSmsChTemplate" placeholder="请输入中文短信模板"></td>
                        <td pc>ttSmsChTemplate</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>英文短信模板：</td>
                        <td><input type="text"  class="layui-input ttSmsEnTemplate" placeholder="请输入英文短信模板"></td>
                        <td pc>ttSmsEnTemplate</td>
                        <td></td>
                    </tr>
                </tr>
                <tr>
                    <td style="border: 0"></td>
                    <td style="border: 0;text-align: center;">阿里云:</td>
                </tr>
                <tr>
                    <tr>
                        <td>accessKeyId：</td>
                        <td>
                            <div><input type="text" class="layui-input accessKeyId" placeholder="请输入accessKeyId"></div>
                        </td>
                        <td>accessKeyId</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>accessKeySecret：</td>
                        <td><input type="text"  class="layui-input accessKeySecret" placeholder="请输入accessKeySecret"></td>
                        <td>accessKeySecret</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>签名：</td>
                        <td><input type="text"  class="layui-input aliSMSSign" placeholder="请输入中文签名"></td>
                        <td pc>aliSMSSign</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>中文模板code：</td>
                        <td><input type="text"  class="layui-input aliSMSChTemCode" placeholder="请输入中文模板code"></td>
                        <td pc>aliSMSChTemCode</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>英文模板code：</td>
                        <td><input type="text"  class="layui-input aliSMSEnTemCode" placeholder="请输入英文模板code"></td>
                        <td pc>aliSMSEnTemCode</td>
                        <td></td>
                    </tr>
                </tr>
                <tr>
                    <td style="border: 0"></td>
                    <td style="border: 0;text-align: center;">云片:</td>
                </tr>
                <tr>
                    <tr>
                        <td>appKey：</td>
                        <td>
                            <div><input type="text" class="layui-input yunPianAppKey" placeholder="请输入appKey"></div>
                        </td>
                        <td>yunPianAppKey</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>中文签名：</td>
                        <td><input type="text"  class="layui-input yunPianSMSChSign" placeholder="请输入中文签名"></td>
                        <td pc>yunPianSMSChSign</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>英文签名：</td>
                        <td><input type="text"  class="layui-input yunPianSMSEnSign" placeholder="请输入英文签名"></td>
                        <td pc>yunPianSMSEnSign</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>中文模板code：</td>
                        <td><input type="text"  class="layui-input yunPianSMSChTemCode" placeholder="请输入中文模板code"></td>
                        <td pc>yunPianSMSChTemCode</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>英文模板code：</td>
                        <td><input type="text"  class="layui-input yunPianSMSEnTemCode" placeholder="请输入英文模板code"></td>
                        <td pc>yunPianSMSEnTemCode</td>
                        <td></td>
                    </tr>
                </tr>
                <tr>
                    <td style="border: 0"></td>
                    <td style="border: 0;text-align: center;">
                        漫道：使用前请先进行序列号注册 ：<a href="http://sdk.entinfo.cn:8060/webservice.asmx?op=Register" style="color: #0000cc">点击这里进行注册</a>
                    </td>
                </tr>
                <tr>
                    <tr>
                        <td>国内短信序列号：</td>
                        <td>
                            <div><input type="text" class="layui-input mdSN" placeholder="请输入序列号"></div>
                        </td>
                        <td>mdSN</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>国内短信序列号密码：</td>
                        <td>
                            <div><input type="text" class="layui-input mdPassword" placeholder="请输入序列号密码"></div>
                        </td>
                        <td>mdPassword</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>国内短信短信签名：</td>
                        <td><input type="text"  class="layui-input mdSignName" placeholder="请输入短信签名"></td>
                        <td pc>mdSignName</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>国内短信短信模板：</td>
                        <td><input type="text"  class="layui-input mdSMSChTemCode" placeholder="请输入短信签名"></td>
                        <td pc>mdSMSChTemCode</td>
                        <td>模板中必须包含：#code#</td>
                    </tr>
                    <tr>
                        <td>国际短信序列号：</td>
                        <td>
                            <div><input type="text" class="layui-input mdEnSN" placeholder="请输入序列号"></div>
                        </td>
                        <td>mdSN</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>国际短信序列号密码：</td>
                        <td>
                            <div><input type="text" class="layui-input mdEnPassword" placeholder="请输入序列号密码"></div>
                        </td>
                        <td>mdPassword</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>国际短信短信签名：</td>
                        <td><input type="text"  class="layui-input mdEnSignName" placeholder="请输入短信签名"></td>
                        <td pc>mdSignName</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>国际短信短信模板：</td>
                        <td><input type="text"  class="layui-input mdSMSEnTemCode" placeholder="请输入短信签名"></td>
                        <td pc>mdSMSChTemCode</td>
                        <td>模板中必须包含：#code#</td>
                    </tr>
                </tr>
                <tr>
                    <td style="border: 0"></td>
                    <td style="border: 0;text-align: center;">聚合:</td>
                </tr>
                <tr>
                <tr>
                    <td>短信模板ID：</td>
                    <td>
                        <div><input type="text" class="layui-input juheAccessKeyId" placeholder="请输入短信模板ID"></div>
                    </td>
                    <td>juheAccessKeyId</td>
                    <td></td>
                </tr>
                <tr>
                    <td>应用APPKEY：</td>
                    <td><input type="text"  class="layui-input juheAccessKey" placeholder="请输入应用APPKEY"></td>
                    <td>juheAccessKey</td>
                    <td></td>
                </tr>
                </tr>
                <tr>
                    <td style="border: 0"></td>
                    <td style="border: 0;text-align: center;">创蓝:</td>
                </tr>
                <tr>
                <tr>
                    <td>应用appkey：</td>
                    <td><input type="text"  class="layui-input clappkey" placeholder="请输入应用appkey"></td>
                    <td>clappkey</td>
                    <td></td>
                </tr>
                <tr>
                    <td>应用secretkey：</td>
                    <td><input type="text"  class="layui-input clsecretkey" placeholder="请输入应用secretkey"></td>
                    <td>clsecretkey</td>
                    <td></td>
                </tr>
                </tr>
                </tbody>
            </table>
        </form>

        <div class="magt10 layui-center">
            <div class="layui-input-block" style="margin-left: 0px">
                <input type="hidden" id="rateId">
                <button class="layui-btn save" lay-submit="" lay-filter="rateConfig">保存</button>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="/pages/common/layui/layui.js"></script>
    <script type="text/javascript" src="/pages/common/jquery/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
    <script type="text/javascript" src="./js/common.js"></script>
    <script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
    <script type="text/javascript" src="./js/system_sms_config.js"></script>
</body>
</html>