<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>内容页</title>
</head>
<style>
* {
    margin: 0;
    padding: 0;
}

html,
body {
    height: 100%;

    display: flex;
    flex-direction: column;
}
section {flex: 1;overflow: auto;padding: 0 12px;}
header{flex-shrink: 0;margin: 0 12px;}
header{display: flex; align-items: center;justify-content: center;position: relative;height: 60px;font-size: 18px;color: #333;font-weight: 550;}
header .back{position: absolute;left: 0px; display: block;height: 25px;width: 25px;}
section h3{color:#090909;font-size: 18px;line-height: 30px;}
section .articleCreatetime{font-size: 14px;color:#969696;height: 30px;line-height: 30px;}
section .redunm{color:#969696;font-size: 14px;height: 60px; line-height: 60px;}
section .detail{color: #444; font-size: 14px;line-height: 30px;}
</style>
<body>
<!--<header>-->
<!--    <img class="back" src="./img/back.png" alt="">-->
<!--    <div>-->
<!--        内容页-->
<!--    </div>-->
<!--</header>-->
<section>
    <div class="item">
        <h3 style="text-align:center" class="articleFirstName">这里是标题</h3>
        <h5 style="text-align:center" class="articleSecondName">这里是副标题</h5>
        <p class="articleCreatetime">2018-09-12 10:24</p>
        <div class="detail">
            <p></p>
        </div>
        <div class="redunm">
            <span>阅读数</span>
            <span class="articleReadCount"></span>
        </div>
    </div>
</section>
</body>
<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript">
    //预览文章
    var PA = {
        previewArticle : function(obj) {
            var objectId = PA.getRequestUrlArg();

            //加载当前点击的文章数据，成功后处理回显
            $.ajax({
                type:'POST',
                url : '/mp/articleList',
                dataType:"json",
                //加上这句话
                xhrFields: {
                    withCredentials: true
                },
                data:{
                    objectId:objectId
                },
                crossDomain: true,
                async: false,
                success:function(result){
                    //这里处理查询出来的数据放到预览位置
                    var articleObj = result.data[0];
                    $(".articleFirstName").empty().append(articleObj.articleFirstName);
                    $(".articleSecondName").empty().append(articleObj.articleSecondName);
                    $(".detail p").empty().append(articleObj.articleContent);
                    $(".articleReadCount").empty().append(articleObj.pageView);
                    $(".articleCreatetime").empty().append(PA.getLocalTime(articleObj.createTime));
                }
            });
        },
        //当下这个url只有一个参数，直接写返回
        getRequestUrlArg : function () {
            var url = location.search.split('&')[0]; //获取url中"?"符后的字串
            var theRequest = '';
            if (url.indexOf("?") != -1) {
                theRequest = url.substr(5);
            }
            return theRequest;
        },
        getLocalTime:function(time){
            var date = new Date(time);
            var Y = date.getFullYear() + '-';
            var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            var D = date.getDate() + ' ';
            var h = date.getHours() + ':';
            var m = (date.getMinutes()<10?'0'+(date.getMinutes()):date.getMinutes()) + ':';
            var s = (date.getSeconds()<10?'0'+(date.getSeconds()):date.getSeconds());
            return Y+M+D+h+m+s;
        },
    }
    PA.previewArticle();
</script>
</html>