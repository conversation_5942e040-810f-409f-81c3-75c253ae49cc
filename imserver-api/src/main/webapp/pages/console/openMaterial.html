<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="format-detection" content="telephone=no">
	<link rel="stylesheet" href="/pages/common/layui/css/layui.css" media="all" />
	<link rel="stylesheet" href="./css/public.css" media="all" />

	<title>开放平台素材管理</title>
</head>
<body>
<div class="layui-tab layui-tab-brief" lay-filter="demo">
	<div class="layui-tab-content layui-row">
		<div id="back" class="layui-col-md1">&nbsp;</div>
		<div id="open" class="layui-tab-item layui-tab-card layui-show layui-col-md10">
			<div id="openList">
				<table id="open_list" lay-filter="open_list">
				</table>
				<div class="magt10 layui-center">
					<div class="layui-input-block" style="margin-left: 0px;margin-bottom: 13px;">
						<button onclick="openMaterial.addOpen()" class="layui-btn save" lay-submit="" lay-filter="systemConfig">添加文档类型</button>
					</div>
				</div>
			</div>
			<div id="addOpen" style="display: none;">
				<form class="layui-form">
					<table class="layui-table mag0">
						<colgroup>
							<col width="25%">
							<col width="45%">
							<col>
						</colgroup>
						<tbody>
						<input type="hidden" name="" id="open_id">
						<tr>
							<td>序号</td>
							<td>
								<input type="text" id="addNumber" class="layui-input addNumber" lay-verify="required" placeholder="请输入序号">
							</td>
						</tr>
						<tr>
							<td>类型</td>
							<td>
								<input type="text" id="addStyleType" class="layui-input addStyleType" lay-verify="required" placeholder="请输入类型">
							</td>
						</tr>
						<tr>
							<td>文档名称</td>
							<td>
								<input type="text" id="addDocName" class="layui-input addDocName" lay-verify="required" placeholder="请输入文档名称">
							</td>
						</tr>
						<tr>
							<td>文档说明</td>
							<td>
								<input type="text" id="addDocExplain" class="layui-input addDocExplain" lay-verify="required" placeholder="请输入文档说明">
							</td>
						</tr>
						<tr>
							<td>更新内容</td>
							<td>
								<textarea name="addUpdateContent" id="addUpdateContent" class="layui-textarea addUpdateContent" lay-verify="required" placeholder="请输入更新内容" ></textarea>
							</td>
						</tr>
						<tr>
							<td>更新文档</td>
							<td>
								<button type="button" class="layui-btn" onclick="projectOpenClick()">请上传文档</button>
								<span id="open_doc_update"></span>
							</td>
						</tr>
						</tbody>
					</table>
					<div class="magt10 layui-center">
						<div class="layui-input-block" style="margin-left: 0px;padding-bottom: 13px;">
							<button lay-submit="" lay-filter="systemConfig" class="layui-btn" type="button" onclick="openMaterial.commit_open()">保存</button>
							<button class="layui-btn layui-btn-primary" type="button" onclick="openMaterial.back()">返回</button>
						</div>
					</div>
				</form>
			</div>
			<div id="modifyOpen" style="display: none;">
				<form class="layui-form">
					<table class="layui-table mag0">
						<colgroup>
							<col width="25%">
							<col width="45%">
							<col>
						</colgroup>
						<tbody>
						<tr>
							<td>序号</td>
							<td>
								<input type="text" id="modifyNumber" class="layui-input modifyNumber" lay-verify="required" placeholder="请输入序号">
							</td>
						</tr>
						<tr>
							<td>类型</td>
							<td>
								<input type="text" id="modifyStyleType" class="layui-input modifyStyleType" lay-verify="required" placeholder="请输入类型">
							</td>
						</tr>
						<tr>
							<td>文档名称</td>
							<td>
								<input type="text" id="modifyDocName" class="layui-input modifyDocName" lay-verify="required" placeholder="请输入文档名称">
							</td>
						</tr>
						<tr>
							<td>文档说明</td>
							<td>
								<input type="text" id="modifyDocExplain" class="layui-input modifyDocExplain" lay-verify="required" placeholder="请输入文档说明">
							</td>
						</tr>
						<tr>
							<td>更新内容</td>
							<td>
								<textarea name="modifyUpdateContent" id="modifyUpdateContent" class="layui-textarea modifyUpdateContent" lay-verify="required" placeholder="请输入更新内容" ></textarea>
							</td>
						</tr>
						<tr>
							<td>更新文档</td>
							<td>
								<button type="button" class="layui-btn" onclick="projectOpenModifyClick()">请上传文档</button>
								<span id="open_doc_update_modify"></span>
							</td>
						</tr>
						</tbody>
					</table>
					<div class="magt10 layui-center">
						<div class="layui-input-block" style="margin-left: 0px;padding-bottom: 13px;">
							<button lay-submit="" lay-filter="systemConfig" class="layui-btn" type="button" onclick="openMaterial.commit_open_modify()">保存</button>
							<button class="layui-btn layui-btn-primary" type="button" onclick="openMaterial.back()">返回</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<input type="hidden" id="openId" value="">
<script type="text/html" id="openList_toolbar">
	<a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="deleteInfo">删除</a>
	<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="updateInfo">修改</a>
</script>
<form id="open_doc_path_update" name="openDocPath_update" action="" method="post" enctype="multipart/form-data" style="display: none;">
	<input id="open_doc_display_update" type="file" name="file" onchange="openDocDisplay()" >
</form>
<form id="open_doc_path_update_modify" name="openDocPath_update_modify" action="" method="post" enctype="multipart/form-data" style="display: none;">
	<input id="open_doc_display_update_modify" type="file" name="file" onchange="openDocDisplayModify()" >
</form>
<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="/pages/console/js/openMaterial.js"></script>
<script type="text/javascript">
</script>
</body>
</html>