<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>密保问题管理列表</title>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">

<body>
<div class="layui-row">
    <div class="layui-col-md1">&nbsp;</div>
    <input id="pageCount" type="" name="" style="display: none">
    <div id="emojiList" class="layui-col-md10">
        <div class="admin_btn_div" style="margin-top: 2%">
            <input type="text" name="" class="layui-input admin_keyword" style="width: 15%;display: inline" placeholder="问题描述">
            <button class="layui-btn  search_question">搜索</button>
            <button class="layui-btn  btn_addQuestion">新增密保问题</button>

        </div>

        <div class="layui-card question_table" style="margin-top: 1%">
            <div class="layui-card-header">密保问题列表</div>
            <div class="layui-card-body">
                <table id="question_list" lay-filter="question_list" style="table-layout:fixed;word-break:break-all;" >

                </table>
            </div>
        </div>
        <div id="add_question" class="layui-col-md10" style="display: none;">
            <div class="layui-card" style="margin-top: 1%">
                <div class="layui-card-header">新增密保问题</div>
                <div class="layui-card-body">
                    <table cellspacing="0" cellpadding="0" border="0" class="layui-table">
                        <tr>
                            <td>密保问题</td>
                            <td>
                                <input type="text"  placeholder="输入密保问题" autocomplete="off" id="questionTxt1" class="layui-input questionTxt">
                            <td>
                            <td>
                                <input type="text"  placeholder="输入密保问题" autocomplete="off" id="questionTxt2" class="layui-input questionTxt">
                            <td>
                            <td>
                                <input type="text"  placeholder="输入密保问题" autocomplete="off" id="questionTxt3" class="layui-input questionTxt">
                            <td>
                        </tr>
                    </table>
                    <button onclick="questionStore.commit_addQuestion()" class="layui-btn ">提交</button>
                    <button onclick="questionStore.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
                </div>
            </div>
        </div>
        <div id="add_question1" class="layui-col-md10" style="display: none;">
            <div class="layui-card" style="margin-top: 1%">
                <div class="layui-card-header">新增密保问题</div>
                <div class="layui-card-body">
                    <table cellspacing="0" cellpadding="0" border="0" class="layui-table">
                        <tr>
                            <td>密保问题</td>
                            <td>
                                <input type="text"  placeholder="输入密保问题" autocomplete="off" id="questionTxt" class="layui-input questionTxt">
                            <td>
                        </tr>
                    </table>
                    <button onclick="questionStore.commit_addQuestion()" class="layui-btn ">提交</button>
                    <button onclick="questionStore.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
                </div>
            </div>
        </div>
    </div>


    </div>
</div>

<!--操作-->
<script type="text/html" id="questionBar">
    <a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="delete">删除</a>
    <!--<a class="layui-btn layui-btn-primary layui-btn-xs locking" lay-event="locking">禁用</a>-->
</script>

<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="/pages/console/js/console_ui.js"></script>
<script type="text/javascript" src="/pages/console/js/questionList.js"></script>

</body>
</html>