<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>朋友圈管理</title>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">

<body>
<div class="layui-row">
		<div id="back" class="layui-col-md1">&nbsp;</div>
		<input id="pageCount" type="" name="" style="display: none">
		<input type="" name="" id="save_roomId" style="display: none;">
		<div id="friendMsg_div" class="layui-col-md10">
			<div class="msg_btn_div" style="margin-top: 2%">
				<input id="userId" type="text" name="" class="layui-input" style="width: 15%;display: inline" placeholder="发送人Id">
				<input id="nickName" type="text" name="" class="layui-input" style="width: 15%;display: inline" placeholder="发送人昵称">
				<button class="layui-btn search_live">搜索</button>
			</div>
			<div id="friendMsgList" class="layui-card" style="margin-top: 1%">
				<div class="layui-card-header">朋友圈列表</div>
				<div class="layui-card-body">
					<table id="msg_table" lay-filter="msg_table" lay-data="{id:'msg_table'}"></table>
				</div>
			</div>
			<!-- 评论详情 -->
			<div id="commonMsg" class="layui-card" style="margin-top: 1%;display: none;">
				<div class="layui-card-header">评论详情</div>
				<div class="layui-card-body">
					<table id="commentMsg_table" lay-filter="commentMsg_table">
							
					</table>
					<input id="msg_pageCount" type="" name="" style="display: none">
						
					<button onclick="Msg.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
				</div>
			</div>
			<!-- 点赞详情 -->
			<div id="praiseMsg" class="layui-card" style="margin-top: 1%;display: none;">
				<div class="layui-card-header">点赞详情</div>
				<div class="layui-card-body">
					<table id="praiseMsg_table" lay-filter="praiseMsg_table">
						
					</table>
					<input id="member_pageCount" type="" name="" style="display: none">
					
					<button onclick="Msg.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
				</div>
			</div>
		</div>

	</div>

	<!--操作-->
	<script type="text/html" id="msgListBar">
		<a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="delete">删除</a>
		<a class="layui-btn layui-btn-primary layui-btn-xs praiseMsg" lay-event="praiseMsg">点赞列表</a>
		<a class="layui-btn layui-btn-primary layui-btn-xs commonMsg" lay-event="commonMsg">评论列表</a>
		{{#  if(d.state == 0){ }}
		<a class="layui-btn layui-btn-primary layui-btn-xs shutup" lay-event="shutup">锁定</a>
		{{#  }else{  }}
		<a class="layui-btn layui-btn-primary layui-btn-xs cancelShutup" lay-event="cancelShutup">解锁</a>
		{{#  } }}
		{{#  if(d.userStatus == 1){ }}
		<a class="layui-btn layui-btn-primary layui-btn-xs shutup" lay-event="locking">锁定该用户</a>
		{{#  }else{  }}
		<a class="layui-btn layui-btn-primary layui-btn-xs cancelShutup" lay-event="unlock">解锁该用户</a>
		{{#  } }}
	</script>

	<script type="text/html" id="deleteCommonMsg">
		<a class="layui-btn layui-btn-danger layui-btn-xs deleteCommon" lay-event="deleteCommon">删除评论</a>
	</script>

	<script type="text/html" id="toolbarDemo">
		<div class="layui-btn-container">
			<button class="layui-btn layui-btn-sm checkDelete" lay-event="deleteMsg" onclick="Msg.checkDeleteMsg()">多选删除</button>
		</div>
	</script>

	<script type="text/html" id="toolbardeleteComment">
		<div class="layui-btn-container">
			<button class="layui-btn layui-btn-sm checkDeleteComment" lay-event="checkDeleteComment" onclick="Msg.checkDeleteComment()">多选删除</button>
		</div>
	</script>

<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/common/jquery/jquery.md5.js"></script>
<script type="text/javascript" src="./js/common.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="./js/msg.js"></script>
<!--<script type="text/javascript" src="/pages/js/console_init.js"></script>-->


</body>
</html>