/*节点配置js*/
var currentPageIndex;// 当前页码数
var currentCount;// 当前总数
//重新渲染表单
function renderForm(){
    layui.use('form', function(){
        var form = layui.form;//高版本建议把括号去掉，有的低版本，需要加()
        form.render();
    });
}


layui.use(['form','layer','laydate','table','laytpl'],function(){
    var form = layui.form,
        layer = parent.layer === undefined ? layui.layer : top.layer,
        $ = layui.jquery,
        laydate = layui.laydate,
        laytpl = layui.laytpl,
        table = layui.table;
    var tableVersion = table.render({
        elem: '#version_list'
        ,url:request("/console/versionInfoList")
        ,id: 'version_list'
        ,page: true
        ,curr: 0
        ,limit:10
        ,limits:[10,20,30,40,50]
        ,groups: 7
        ,cols: [[ //表头
            {field: 'device', title: '设备类型',width:200,templet(d){
                if(null != d.device){
                   return d.device == 0?"安卓":d.device == 1?"IOS":d.device == 2?"AppStore":"其他";
                 }
                   return "其他";
             }}
            ,{field: 'projectName', title: 'APP名称',width:200}
            ,{field: 'versionName', title: '版本名称',width:200}
            ,{field: 'versionNum', title: '版本号',width:200}
            ,{field: 'thirdLoadURL', title: '三方下载链接',width:200}
            ,{field: 'versionStatus', title: '版本状态',width:200,templet(d){
                    if(null != d.versionStatus){
                        return d.versionStatus == 1?"正常":"禁用";
                    }
                    return "状态错误";
                }}
            ,{fixed: 'right',title:"操作", align:'left', toolbar: '#versionList_toolbar'}
        ]]
        ,done:function(res, curr, count){
            if(res.resultCode == -1 && res.resultMsg =="用户未登录或登录失效"){
                layer.msg("用户未登录或登录失效,请重新登录", {"icon": 2},function(){
                    window.location.href = "/pages/console/login.html";
                });
                return;
            }
            if (count == 0 && lock == 1) {
                layer.msg("暂无数据", {"icon": 2});
                renderTable();
            }
            var pageIndex = tableVersion.config.page.curr;//获取当前页码
            var resCount = res.count;// 获取table总条数
            currentCount = resCount;
            currentPageIndex = pageIndex;
            console.log("结束");
        }
    });
    //列表操作
    table.on('tool(version_list)', function(obj){
        var layEvent = obj.event, data = obj.data;
        console.log(data);
        if(layEvent === 'deleteInfo'){ //删除
            layer.confirm('确定删除该节点？',{icon:3, title:'提示信息'},function(index){
                versionManage.deleteVersion(data.id);
                obj.del();
                layer.closeAll();
            })

        } else if(layEvent === 'updateInfo'){ //修改
            versionManage.updateVersion(data);
        } else if(layEvent === 'updateStatusNo'){// 禁用
            layer.confirm('确定禁用该版本？',{icon:3, title:'提示信息'},function(){
                versionManage.updateStatus(data.id,0);
                layer.closeAll();
            })
        } else if(layEvent === 'updateStatusYes'){// 启用
            versionManage.updateStatus(data.id,1);
            layer.closeAll();
        } else if (layEvent == "androidVersion") {
            $("#appVersionId").val(data.id);
            $("#appProjectName").val(data.projectName)
            $("#appVersionNum").val(data.versionNum)
            $("#appVersionName").val(data.versionName);
            $("#appThirdLoadURL").val(data.thirdLoadURL);
            $("#versionStatus").val(data.versionStatus == 1 ? "正常" : "禁用");
            $("#appUpdateContent").val(data.updateContent);
            $("#uploadApkPath_update").attr("action",Config.getConfig().uploadUrl+"/upload/UploadServlet");
            $("#appType").val("androidVersion");
            if(data.androidMsg == null){
                $("#apkPath_update").text("");
                $("#androidInfoForceStatus").val(0);
            }else {
                $("#apkPath_update").text(data.androidMsg.apkLoadUrl);
                $("#androidInfoForceStatus").val(data.androidMsg.forceStatus);
                $("#apkDownloadUrl").val(data.androidMsg.downloadUrl);
            }
            $("#versionList").hide();
            $("#appVersion").show();
            $("#androidInfo").show();
            $("#appStoreInfo").hide();
            $("#IosInfo").hide();
        }else if (layEvent == "appStoreVersion") {
            $("#appVersionId").val(data.id);
            $("#appProjectName").val(data.projectName)
            $("#appVersionNum").val(data.versionNum)
            $("#appVersionName").val(data.versionName);
            $("#appThirdLoadURL").val(data.thirdLoadURL);
            $("#versionStatus").val(data.versionStatus == 1 ? "正常" : "禁用");
            $("#appUpdateContent").val(data.updateContent);
            if(data.appStoreMsg == null){
                $("#appStoreLoadUrl").val("");
                $("#appStoreInfoForceStatus").val(0);
            }else {
                $("#appStoreLoadUrl").val(data.appStoreMsg.appStoreLoadUrl);
                $("#appStoreInfoForceStatus").val(data.appStoreMsg.forceStatus);
            }
            $("#versionList").hide();
            $("#appVersion").show();
            $("#androidInfo").hide();
            $("#appStoreInfo").show();
            $("#IosInfo").hide();
            $("#appType").val("appStoreVersion");
        }else if (layEvent == "iosVersion") {
            $("#appVersionId").val(data.id);
            $("#appProjectName").val(data.projectName)
            $("#appVersionNum").val(data.versionNum)
            $("#appVersionName").val(data.versionName);
            $("#appThirdLoadURL").val(data.thirdLoadURL);
            $("#versionStatus").val(data.versionStatus == 1 ? "正常" : "禁用");
            $("#appUpdateContent").val(data.updateContent);
            $("#uploadFullPath_update").attr("action",Config.getConfig().uploadUrl+"/upload/UploadServlet");
            $("#uploadIosDisplayPath_update").attr("action",Config.getConfig().uploadUrl+"/upload/UploadServlet");
            $("#uploadIosIpaPath_update").attr("action",Config.getConfig().uploadUrl+"/upload/UploadServlet");
            if(data.iosMsg == null){
                $("#iosIpaPath_update").text("");
                $("#bundleId").text("");
                $("#iosDisplayPath_update").text("");
                $("#iosFullPath_update").text("");
                $("#IosInfoForceStatus").val(0);
            }else {
                $("#iosIpaPath_update").text(data.iosMsg.ipaLoadUrl);
                $("#iosDownloadUrl").val(data.iosMsg.downloadUrl);
                $("#bundleId").val(data.iosMsg.bundleId);
                $("#iosDisplayPath_update").text(data.iosMsg.displayImg);
                $("#iosFullPath_update").text(data.iosMsg.fullImg);
                $("#IosInfoForceStatus").val(data.iosMsg.forceStatus);
            }
            $("#versionList").hide();
            $("#appVersion").show();
            $("#androidInfo").hide();
            $("#appStoreInfo").hide();
            $("#IosInfo").show();
            $("#appType").val("iosVersion");
        }
        renderForm();
    });
});

var versionManage={
    addVersion:function(){
        if (currentCount > 0){
            myFn.invoke({
                url:request('/console/newVersionInfo'),
                success:function(result){
                    console.log(result);
                    $("#version_id").val("");
                    $("#projectName").val("")
                    $("#versionNum").val("")
//                    $("#oldVersionName").val(result.data.versionName);
//                    $("#oldVersionNum").val(result.data.versionNum);
                    $("#thirdLoadURL").val("");
                    $("#versionName").val("");
                    $("#updateContent").val("");
                    $(".versionStatus").val(1);
//                    $("#oldVersionNameTr").show();
//                    $("#oldVersionNumTr").show();
                    $("#versionList").hide();
                    $("#addVersion").show();
                }
            });
        } else {
            $("#version_id").val("");
            $("#projectName").val("")
            $("#versionName").val("");
            $("#versionNum").val("");
            $("#updateContent").val("");
            $("#thirdLoadURL").val("");
//            $("#oldVersionName").val("无");
//            $("#oldVersionNum").val("无");
            $(".versionStatus").val(1);
//            $("#oldVersionNameTr").show();
//            $("#oldVersionNumTr").show();
            $("#versionList").hide();
            $("#addVersion").show();
            renderForm();
        }
    },
    // 修改版本信息
    updateVersion:function(data){
        $("#version_id").val(data.id);
        $("#projectName").val(data.projectName)
        $("#versionName").val(data.versionName)
        $("#versionNum").val(data.versionNum)
        $("#thirdLoadURL").val(data.thirdLoadURL);
        $(".versionStatus").val(data.versionStatus);
        $("#updateContent").val(data.updateContent);
//        $("#oldVersionNameTr").hide();
//        $("#oldVersionNumTr").hide();
        $("#versionList").hide();
        $("#addVersion").show();
        renderForm();
    },
    // 提交添加版本
    commit_version:function(){
        var versionName = $("#versionName").val();
        if (versionName == null || versionName == ""){
            return;
        }
        var updateContent = $("#updateContent").val();
        if (updateContent == null || updateContent == ""){
            return;
        }
        if($("#version_id").val()!=""){
            myFn.invoke({
                url:request('/console/addVersionInfo'),
                data:{
                    id:$("#version_id").val(),
                    projectName:$("#projectName").val(),
                    versionName:$("#versionName").val(),
                    versionNum:$("#versionNum").val(),
                    updateContent:$("#updateContent").val(),
                    thirdLoadURL:$("#thirdLoadURL").val(),
                    versionStatus:$(".versionStatus").val(),
                    device:$(".device").val()
                },
                success:function(result){
                    console.log(result);
                    renderForm();
                }
            })
        }else{
            myFn.invoke({
                url:request('/console/addVersionInfo'),
                data:{
                    projectName:$("#projectName").val(),
                    versionName:$("#versionName").val(),
                    versionNum:$("#versionNum").val(),
                    updateContent:$("#updateContent").val(),
                    thirdLoadURL:$("#thirdLoadURL").val(),
                    versionStatus:$(".versionStatus").val(),
                    device:$(".device").val()
                },
                success:function(result){
                    console.log(result);
                    renderForm();
                }
            })
        }
        $("#versionList").show();
        $("#addVersion").hide();
        layui.table.reload("version_list");
    },
    updateStatus(id,status){
        myFn.invoke({
            url:request("/console/updateVersionInfo"),
            data:{
                id:id,
                versionStatus:status
            },
            success:function(){
                if (status ==1){
                    console.log("=====版本启用成功=====");
                }
                if (status ==0){
                    console.log("=====版本金庸成功=====");
                }
                layui.table.reload("version_list",{})
            }
        })
    },
    // 删除地区配置
    deleteVersion:function(id){
        myFn.invoke({
            url:request("/console/deleteVersionInfo"),
            data:{
                id:id
            },
            success:function(){
                console.log("=====删除版本成功=====");
            }
        })
    },
    selectUpdateApk:function(){
        $("#uploadApk_update").click();
    },
    updateUploadApk:function(){
        var file=$("#uploadApk_update")[0].files[0];
        console.log(file)
        versionManage.uploading("安卓APP安装包上传中");
        $("#uploadApkPath_update").ajaxSubmit(function(data){
            var jsonObj = eval('(' + data + ')');
            console.log(jsonObj.data.others[0]);
            $("#apkPath_update").html(jsonObj.data.others[0].oUrl);
            versionManage.uploadClose();
        });
    },
    selectUpdateIosIpa:function(){
        $("#uploadIosIpa_update").click();
    },
    updateUploadIosIpa:function(){
        var file=$("#uploadIosIpa_update")[0].files[0];
        console.log(file)
        versionManage.uploading("IOS IPA包上传中");
        $("#uploadIosIpaPath_update").ajaxSubmit(function(data){
            var jsonObj = eval('(' + data + ')');
            console.log(jsonObj.data.others[0]);
            $("#iosIpaPath_update").html(jsonObj.data.others[0].oUrl);
            versionManage.uploadClose();
        });
    },
    selectUpdateIosDisplay:function(){
        $("#uploadIosDisplay_update").click();
    },
    updateUploadIosDisplay:function(){
        var file=$("#uploadIosDisplay_update")[0].files[0];
        console.log(file)
        versionManage.uploading("IOS 安装LOGO上传中");
        $("#uploadIosDisplayPath_update").ajaxSubmit(function(data){
            var jsonObj = eval('(' + data + ')');
            console.log(jsonObj.data.images[0]);
            $("#iosDisplayPath_update").html(jsonObj.data.images[0].oUrl);
            versionManage.uploadClose();
        });
    },
    selectUpdateIosFull:function(){
        $("#uploadIosFull_update").click();
    },
    updateUploadIosFull:function(){
        var file=$("#uploadIosFull_update")[0].files[0];
        console.log(file)
        versionManage.uploading("IOS iTunes LOGO上传中");
        $("#uploadFullPath_update").ajaxSubmit(function(data){
            var jsonObj = eval('(' + data + ')');
            console.log(jsonObj.data.images[0]);
            $("#iosFullPath_update").html(jsonObj.data.images[0].oUrl);
            versionManage.uploadClose();
        });
    },
    uploading:function(content){
        layui.use(['layer','jquery'],function(){
            var layer = layui.layer, $ = layui.jquery;
            layer.load(1, {
                content: content,
                shade: [0.4, '#393D49'],
                // time: 10 * 1000,
                success: function(uploadLayer) {
                    uploadLayer.css('padding-left', '30px');
                    uploadLayer.find('.layui-layer-content').css({
                        'padding-top': '40px',
                        'width': "161px",
                        'color':'white',
                        'background-position-x': '16px'
                    });
                }
            })
        })
    },
    uploadClose:function(){
        layui.use(['layer','jquery'],function(){
            layer.closeAll();
        })
    },
    // 提交添加版本
    commit_app_version:function(){
        if($("#appType").val()=="androidVersion"){
            myFn.invoke({
                url:request('/console/addAndroidVersionInfo'),
                data:{
                    versionId:$("#appVersionId").val(),
                    apkLoadUrl:$("#apkPath_update").text(),
                    forceStatus:$("#androidInfo .forceStatus").val() == null ? 0 : $("#androidInfo .forceStatus").val(),
                    downloadUrl:$("#apkDownloadUrl").val()
                },
                success:function(result){
                    console.log(result);
                    renderForm();
                }
            })
        }else if($("#appType").val()=="iosVersion"){
            myFn.invoke({
                url:request('/console/addIosVersionInfo'),
                data:{
                    versionId:$("#appVersionId").val(),
                    bundleId:$("#bundleId").val() == null,
                    ipaLoadUrl:$("#iosIpaPath_update").text(),
                    displayImg:$("#iosDisplayPath_update").text(),
                    fullImg:$("#iosFullPath_update").text(),
                    forceStatus:$("#IosInfo .forceStatus").val() == null ? 0 : $("#IosInfo .forceStatus").val(),
                    downloadUrl:$("#iosDownloadUrl").val()
                },
                success:function(result){
                    console.log(result);
                    renderForm();
                }
            })
        }else if($("#appType").val()=="appStoreVersion"){
            myFn.invoke({
                url:request('/console/addAppStoreVersionInfo'),
                data:{
                    versionId:$("#appVersionId").val(),
                    appStoreLoadUrl:$("#appStoreLoadUrl").val() == null?"":$("#appStoreLoadUrl").val(),
                    forceStatus:$("#appStoreInfo .forceStatus").val() == null ? 0 : $("#appStoreInfo .forceStatus").val()
                },
                success:function(result){
                    console.log(result);
                    renderForm();
                }
            })
        }
        $("#versionList").show();
        $("#appVersion").hide();
        layui.table.reload("version_list");
    },
    // 返回
    back:function(){
        $("#versionList").show();
        $("#addVersion").hide();
    },
    // 返回
    appBack:function(){
        $("#versionList").show();
        $("#appVersion").hide();
    }
}