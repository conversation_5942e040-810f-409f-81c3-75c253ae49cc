/**
 * 系统配置页面相关的js
 */
var obj;
function num(){
	obj = $(".giftRatio").val();
	var reg = new RegExp("^[0-9]+(.[0-9]{0,2})?$", "g");
	(!reg.test(obj)?layer.alert("直播礼物分成比率请保持小数点后两位有效数字"):"");
}

$(function () {
    $("#project_logo_path_update").attr("action",Config.getConfig().uploadUrl+"/upload/UploadServlet");
    $("#project_ico_path_update").attr("action",Config.getConfig().uploadUrl+"/upload/UploadServlet");
    //非超级管理员登录屏蔽操作按钮
    if(localStorage.getItem("role")==1 || localStorage.getItem("role")==5){
        $(".save").remove();
    }
})

//填充数据方法
function fillParameter(data){
    //判断字段数据是否存在
    function nullData(data){
        if(data == '' || data == "undefined" || data==null){
            return "";
        }else{
            return data;
        }
    }

    //数据回显
    $(".XMPPHost").val(nullData(data.XMPPHost));  
    $(".XMPPDomain").val(nullData(data.XMPPDomain));
    $(".PCXMPPHost").val(nullData(data.PCXMPPHost));
    $(".PCXMPPDomain").val(nullData(data.PCXMPPDomain));
    $(".XMPPTimeout").val(nullData(data.XMPPTimeout));      
    $(".apiUrl").val(nullData(data.apiUrl));        
    $(".downloadAvatarUrl").val(nullData(data.downloadAvatarUrl));    
    $(".downloadUrl").val(nullData(data.downloadUrl));        
    $(".uploadUrl").val(nullData(data.uploadUrl));

    $(".jitsiServer").val(nullData(data.jitsiServer));    
    $(".liveUrl").val(nullData(data.liveUrl));
    // 直播分成比率
    $(".giftRatio").val(nullData(data.giftRatio));
    $(".promotionUrl").val(nullData(data.promotionUrl));
    $(".defaultTelephones").val(nullData(data.defaultTelephones));
    // $(".displayRedPacket").val(data.displayRedPacket);
    $(".fileValidTime").val(nullData(data.fileValidTime));
    $(".chatRecordTimeOut").val(data.chatRecordTimeOut);
    $(".telephoneSearchUser").val(data.telephoneSearchUser);
    $(".telephoneLogin").val(data.isTelephoneLogin);
    $(".isOpenReceipt").val(data.isOpenReceipt);//是否开启消息回执
    $(".isOpenSMSCode").val(data.isOpenSMSCode);//是否开启短信验证码
    $(".userIdLogin").val(data.isUserIdLogin);
    $(".regeditPhoneOrName").val(data.regeditPhoneOrName); //使用手机号或者用户名注册
    $(".registerInviteCode").val(data.registerInviteCode);  //注册邀请码
    $(".isQuestionOpen").val(data.isQuestionOpen);//密保问题
    $(".nicknameSearchUser").val(data.nicknameSearchUser);
    // $(".showContactsUser").val(data.showContactsUser);

    $(".isKeyWord").val(data.isKeyWord);// 关键词
    $(".isUrlWhite").val(data.isUrlWhite);// URL白名单
    $(".isSaveMsg").val(data.isSaveMsg);// 保存单聊消息
    $(".isSaveMucMsg").val(data.isSaveMucMsg);// 保存群聊消息
    $(".isMsgSendTime").val(data.isMsgSendTime);// 强制同步消息发送时间
    $(".roamingTime").val(data.roamingTime);// 默认漫游时长
    $(".outTimeDestroy").val(data.outTimeDestroy);// 默认过期销毁时长
    $(".language").val(data.language);// 客户端默认语种
    $(".authApi").val(data.isAuthApi);
    $(".isFriendsVerify").val(data.isFriendsVerify);// 是否需要好友验证
    $(".isEncrypt").val(data.isEncrypt);// 是否加密传输
    $(".isMultiLogin").val(data.isMultiLogin);// 是否支持多点登录
    $(".isVibration").val(data.isVibration);// 是否振动
    $(".isTyping").val(data.isTyping); // 让对方知道我正在输入
    $(".isUseGoogleMap").val(data.isUseGoogleMap);// 使用Google地图
    $(".phoneSearch").val(data.phoneSearch);// 使用Google地图
    $(".nameSearch").val(data.nameSearch);// 使用Google地图
    $(".isKeepalive").val(data.isKeepalive);// 使用Google地图
    $(".showLastLoginTime").val(data.showLastLoginTime);// 使用Google地图
    $(".showTelephone").val(data.showTelephone);// 使用Google地图
    /** 建立群组默认参数设置 **/
    $(".maxUserSize").val(data.maxUserSize);
    $(".isLook").val(data.isLook);
    $(".isAttritionNotice").val(data.isAttritionNotice);// 群组减员发送通知
    $(".showRead").val(data.showRead);
    $(".isNeedVerify").val(data.isNeedVerify);
    $(".showMember").val(data.showMember);
    $(".allowSendCard").val(data.allowSendCard);
    $(".allowInviteFriend").val(data.allowInviteFriend);
    $(".allowUploadFile").val(data.allowUploadFile);
    $(".allowConference").val(data.allowConference);
    $(".allowSpeakCourse").val(data.allowSpeakCourse);
    $(".isStrongNotice").val(data.isStrongNotice);
    $(".allowCreateRoom").val(data.allowCreateRoom);
    $(".allowAddFirend").val(data.allowAddFirend);

    $(".SMSType").val(data.sMSType);// 短信服务支持
    $(".iosPushServer").val(data.iosPushServer);// 短信服务支持
    $(".isAutoAddressBook").val(data.isAutoAddressBook);// 通讯录自动添加好友
    $(".isSaveRequestLogs").val(data.isSaveRequestLogs);// 通讯录自动添加好友
    $(".isOpenCluster").val(data.isOpenCluster);
    $(".isOpenVoip").val(data.isOpenVoip);// 是否开启voip推送
    $(".isOpenGoogleFCM").val(data.isOpenGoogleFCM);// 是否开启Google推送
    //钱包参数配置
    $(".isUserSignRedPacket").val(data.isUserSignRedPacket);// 是否开放用户签到红包
    $(".maxSignRedPacket").val(data.maxSignRedPacket);// 签到红包最大金额
    $(".minSignRedPacket").val(data.minSignRedPacket);// 签到红包最小金额
    $(".exchangeVipScore").val(data.exchangeVipScore);// 兑换积分
    $(".firstShareScoreRate").val(data.firstShareScoreRate);// 兑换积分
    $(".secondShareScoreRate").val(data.secondShareScoreRate);// 兑换积分
    $(".thirdShareScoreRate").val(data.thirdShareScoreRate);// 兑换积分
    if (data.isUserSignRedPacket == 1){
        $("#maxSignRedPacketTr").show();
        $("#minSignRedPacketTr").show();
    }
    if (data.isUserSignRedPacket == 0){
        $("#maxSignRedPacketTr").hide();
        $("#minSignRedPacketTr").hide();
    }
    //OBS参数设置
    $(".isOpenOSStatus").val(data.isOpenOSStatus) //是否启用对象存储
    $(".osType").val(data.osType) //对象存储类型
    $(".accessKeyId").val(data.accessKeyId)//对象存储授权KEY
    $(".accessSecretKey").val(data.accessSecretKey)//对象存储授权KEY
    $(".bucketName").val(data.bucketName)//华为云授权KEY
    $(".osAppId").val(data.osAppId)//华为云授权KEY
    if (data.isOpenOSStatus== 1){
        if (data.osType == 1){
            $(".obsEndPointSelect").val(data.location)//对象存储OBS大区
            $("#osAppIdTr").hide();
            $("#obsEndPointSelectTr").show();
            $("#cosEndPointSelectTr").hide();
            $("#AccessKeyText").text("华为云授权SK信息，请在华为云后台查询");
            $("#AccessSecretKeyText").text("华为云授权SK信息，请在华为云后台查询");
        }else if (data.osType == 2){
            $(".cosEndPointSelect").val(data.location)//对象存储COS大区
            $("#osAppIdTr").show();
            $("#obsEndPointSelectTr").hide();
            $("#cosEndPointSelectTr").show();
            $("#AccessKeyText").text("腾讯云COS授权AccessKey信息，请在腾讯云后台查询");
            $("#AccessSecretKeyText").text("腾讯云COS授权AccessSecret信息，请在腾讯云后台查询");
        }
        $("#accessKeyIdTr").show();
        $("#accessSecretKeyTr").show();
        $("#bucketNameTr").show();
        $("#osTypeTr").show();
    }
    if (data.isOpenOSStatus == 0){
        $("#osAppIdTr").hide();
        $("#accessKeyIdTr").hide();
        $("#accessSecretKeyTr").hide();
        $("#bucketNameTr").hide();
        $("#osTypeTr").hide();
        $("#obsEndPointSelectTr").hide();
        $("#cosEndPointSelectTr").hide();
    }
    //项目参数配置
    $("#projectName").val(data.projectName),
    $("#project_logo_update").html(data.projectLogo);
    $("#project_ico_update").html(data.projectIco);
    //默认群组
    $(".defaultRooms").val(nullData(data.defaultRooms));
    //重新渲染
    layui.form.render();
    // layui.form.render('checkbox','appCheckbox');
}

layui.use(['form','jquery',"layer"],function() {
    var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
    //用户签到红包开关
    form.on('select(isUserSignRedPacket)',function(data){
        if (data.value == 1){
            $("#maxSignRedPacketTr").show();
            $("#minSignRedPacketTr").show();
        }
        if (data.value == 0){
            $("#maxSignRedPacketTr").hide();
            $("#minSignRedPacketTr").hide();
        }
    });
    //对象存储开关
    form.on('select(isOpenOSStatus)',function(data){
        if (data.value == 1){
            var osType = $(".osType").val();
            if (osType == 1){
                $("#osAppIdTr").hide();
                $("#cosEndPointSelectTr").hide();
                $("#obsEndPointSelectTr").show();
                $("#AccessKeyText").text("华为云授权SK信息，请在华为云后台查询");
                $("#AccessSecretKeyText").text("华为云授权SK信息，请在华为云后台查询");
            }else if (osType == 2){
                $("#osAppIdTr").show();
                $("#cosEndPointSelectTr").show();
                $("#obsEndPointSelectTr").hide();
                $("#AccessKeyText").text("腾讯云COS授权AccessKey信息，请在腾讯云后台查询");
                $("#AccessSecretKeyText").text("腾讯云COS授权AccessSecret信息，请在腾讯云后台查询");
            }
            $("#accessKeyIdTr").show();
            $("#accessSecretKeyTr").show();
            $("#bucketNameTr").show();
            $("#osTypeTr").show();
        }
        if (data.value == 0){
            $("#osAppIdTr").hide();
            $("#accessKeyIdTr").hide();
            $("#accessSecretKeyTr").hide();
            $("#bucketNameTr").hide();
            $("#osTypeTr").hide();
            $("#obsEndPointSelectTr").hide();
            $("#cosEndPointSelectTr").hide();
        }
    });
    //对象存储类型选择
    form.on('select(osType)',function(data){
        if (data.value == 1){
            $("#osAppIdTr").hide();
            $("#cosEndPointSelectTr").hide();
            $("#obsEndPointSelectTr").show();
            $("#AccessKeyText").text("华为云授权SK信息，请在华为云后台查询");
            $("#AccessSecretKeyText").text("华为云授权SK信息，请在华为云后台查询");
        }else if (data.value == 2){
            $("#osAppIdTr").show();
            $("#cosEndPointSelectTr").show();
            $("#obsEndPointSelectTr").hide();
            $("#AccessKeyText").text("腾讯云COS授权AccessKey信息，请在腾讯云后台查询");
            $("#AccessSecretKeyText").text("腾讯云COS授权AccessSecret信息，请在腾讯云后台查询");
        }
        $(".accessKeyId").val(null);
        $(".accessSecretKey").val(null);
        $(".bucketName").val(null);
    });
    //获取当前系统配置
    if(window.sessionStorage.getItem("systemConfig")){
        var systemConfig = JSON.parse(window.sessionStorage.getItem("systemConfig"));
        fillParameter(systemConfig);
    }else{
        /*$.ajax({
            url : "../json/systemParameter.json",
            type : "get",
            dataType : "json",
            success : function(data){
                fillParameter(data);
            }
        })*/

        Common.invoke({
            path : request('/console/config'),
            data : {},
            successMsg : false,
            errorMsg : "获取数据失败,请检查网络",
            successCb : function(result) {
                fillParameter(result.data);
            },
            errorCb : function(result) {
            }
        });
    }
    //非管理员登录屏蔽操作按钮
    if(localStorage.getItem("IS_ADMIN")==0){
      $(".save").remove();
    }

    //提交保存配置
    form.on("submit(systemConfig)",function(data){
       var systemConfig = {
       };
        systemConfig.id = 10000;
        systemConfig.distance = 0;
        systemConfig.XMPPHost = $(".XMPPHost").val();  
        systemConfig.XMPPDomain = $(".XMPPDomain").val();
        systemConfig.PCXMPPHost = $(".PCXMPPHost").val();
        systemConfig.PCXMPPDomain = $(".PCXMPPDomain").val();
        systemConfig.XMPPTimeout = $(".XMPPTimeout").val();    
        systemConfig.apiUrl = $(".apiUrl").val();        
        systemConfig.downloadAvatarUrl = $(".downloadAvatarUrl").val();    
        systemConfig.downloadUrl = $(".downloadUrl").val();        
        systemConfig.uploadUrl = $(".uploadUrl").val();  

        systemConfig.jitsiServer = $(".jitsiServer").val();    
        systemConfig.liveUrl = $(".liveUrl").val();
        //直播分成比例
        systemConfig.giftRatio = $(".giftRatio").val();
        if($(".giftRatio").val() == ""){
        	layer.alert("直播礼物分成比率不能为空");
        	return;
        }else if(obj > 1){
        	layer.alert("直播礼物分成比率要小于等于1");
        	return;
        }else if(obj == 0){
        	layer.alert("直播礼物分成比率不能为0");
        	return;
        }
        systemConfig.promotionUrl = $(".promotionUrl").val();
        
        systemConfig.defaultTelephones = $(".defaultTelephones").val();

        systemConfig.fileValidTime = $(".fileValidTime").val();
        
        systemConfig.chatRecordTimeOut = $(".chatRecordTimeOut").val();

        systemConfig.telephoneSearchUser = $(".telephoneSearchUser").val();

        systemConfig.nicknameSearchUser = $(".nicknameSearchUser").val();

        systemConfig.isTelephoneLogin = $(".telephoneLogin").val();

        systemConfig.isUserIdLogin = $(".userIdLogin").val();
        
        systemConfig.regeditPhoneOrName = $(".regeditPhoneOrName").val();

        systemConfig.registerInviteCode = $(".registerInviteCode").val();

        systemConfig.isQuestionOpen = $(".isQuestionOpen").val();
        
        systemConfig.isAuthApi = $(".authApi").val();

        systemConfig.isOpenCluster = $(".isOpenCluster").val();

        systemConfig.isOpenVoip = $(".isOpenVoip").val();

        systemConfig.isOpenGoogleFCM = $(".isOpenGoogleFCM").val();

        if($(".androidVersion").val()=="" || $(".androidVersion").val() == null){
            systemConfig.androidVersion = 0;
        }else {
            systemConfig.androidVersion = $(".androidVersion").val();
        }
        
        systemConfig.androidAppUrl = $(".androidAppUrl").val();
        systemConfig.androidExplain = $(".androidExplain").val();

        if($(".iosVersion").val()=="" || $(".iosVersion").val() == null){
            systemConfig.iosVersion = 0;
        }else {
            systemConfig.iosVersion = $(".iosVersion").val();
        }
        systemConfig.iosAppUrl = $(".iosAppUrl").val();
        systemConfig.iosExplain = $(".iosExplain").val();
        systemConfig.isKeyWord = $(".isKeyWord").val();
        systemConfig.isUrlWhite = $(".isUrlWhite").val();
        systemConfig.isSaveMsg = $(".isSaveMsg").val();
        systemConfig.isSaveMucMsg = $(".isSaveMucMsg").val();
        systemConfig.isMsgSendTime = $(".isMsgSendTime").val();
        systemConfig.roamingTime = $(".roamingTime").val();
        systemConfig.outTimeDestroy = $(".outTimeDestroy").val();
        systemConfig.language = $(".language").val();
        systemConfig.isFriendsVerify = $(".isFriendsVerify").val();
        systemConfig.isEncrypt = $(".isEncrypt").val();
        systemConfig.isMultiLogin = $(".isMultiLogin").val();
        systemConfig.isVibration = $(".isVibration").val();
        systemConfig.isTyping = $(".isTyping").val();
        systemConfig.isUseGoogleMap = $(".isUseGoogleMap").val();
        systemConfig.phoneSearch = $(".phoneSearch").val();
        systemConfig.nameSearch = $(".nameSearch").val();
        systemConfig.isKeepalive = $(".isKeepalive").val();
        systemConfig.showLastLoginTime = $(".showLastLoginTime").val();
        systemConfig.showTelephone = $(".showTelephone").val();

        /** 建立群组默认参数设置 **/
        systemConfig.maxUserSize = $(".maxUserSize").val();
        systemConfig.isLook = $(".isLook").val();
        systemConfig.showRead = $(".showRead").val();
        systemConfig.isNeedVerify = $(".isNeedVerify").val();
        systemConfig.isAttritionNotice = $(".isAttritionNotice").val();
        systemConfig.showMember = $(".showMember").val();
        systemConfig.allowSendCard = $(".allowSendCard").val();
        systemConfig.allowInviteFriend = $(".allowInviteFriend").val();
        systemConfig.allowUploadFile = $(".allowUploadFile").val();
        systemConfig.allowConference = $(".allowConference").val();
        systemConfig.allowSpeakCourse = $(".allowSpeakCourse").val();

        systemConfig.isOpenSMSCode = $(".isOpenSMSCode").val();
        systemConfig.isOpenReceipt = $(".isOpenReceipt").val();
        // systemConfig.isOpenReadReceipt = $(".isOpenReadReceipt").val();
        systemConfig.SMSType = $(".SMSType").val();
        
        systemConfig.iosPushServer = $(".iosPushServer").val();
        systemConfig.isAutoAddressBook = $(".isAutoAddressBook").val();
        systemConfig.isSaveRequestLogs = $(".isSaveRequestLogs").val();
        systemConfig.allowCreateRoom = $(".allowCreateRoom").val();
        systemConfig.allowAddFirend = $(".allowAddFirend").val();
        systemConfig.isUserSignRedPacket = $(".isUserSignRedPacket").val();
        systemConfig.maxSignRedPacket = $(".maxSignRedPacket").val();
        systemConfig.minSignRedPacket = $(".minSignRedPacket").val();
        systemConfig.isStrongNotice = $(".isStrongNotice").val();
        systemConfig.exchangeVipScore = $(".exchangeVipScore").val();
        systemConfig.firstShareScoreRate = $(".firstShareScoreRate").val();
        systemConfig.secondShareScoreRate = $(".secondShareScoreRate").val();
        systemConfig.thirdShareScoreRate = $(".thirdShareScoreRate").val();
        var isUserSignRedPacket = $(".isUserSignRedPacket").val();
        if (isUserSignRedPacket == 1){
            if($(".maxSignRedPacket").val() == ""){
                layer.alert("签到红包最大金额不能为空");
                return;
            }else if($(".minSignRedPacket").val() == 0){
                layer.alert("签到红包最大金额不能为0");
                return;
            }
            if($(".minSignRedPacket").val() == ""){
                layer.alert("签到红包最小金额不能为空");
                return;
            }else if($(".minSignRedPacket").val() == 0){
                layer.alert("签到红包最小金额不能为0");
                return;
            }
        }
        systemConfig.isOpenOSStatus = $(".isOpenOSStatus").val(); //是否启用对象存储
        if (systemConfig.isOpenOSStatus == 1){
            systemConfig.accessKeyId = $(".accessKeyId").val()//对象存储授权KEY
            systemConfig.accessSecretKey = $(".accessSecretKey").val()//对象存储授权KEY
            // systemConfig.obsEndPointSelect = $(".obsEndPointSelect").val()//OBS对象存储大区
            // systemConfig.cosEndPointSelect = $(".cosEndPointSelect").val()//OBS对象存储大区
            systemConfig.bucketName = $(".bucketName").val()//对象存储空间名称
            systemConfig.osType = $(".osType").val();
            var osType = $(".osType").val();
            if (osType == 1){
                systemConfig.endPoint = $(".obsEndPointSelect option:selected").attr("value-data");
                systemConfig.location = $(".obsEndPointSelect").val();
                if($(".obsEndPointSelect").val() == ""){
                    layer.alert("请选择"+text+"大区");
                    return;
                }
            }
            var text = "华为云OBS"
            if (osType == 2){
                systemConfig.osAppId = $(".osAppId").val();
                if($(".osAppId").val() == ""){
                    layer.alert("腾讯云COS对象存储APPID不能为空");
                    return;
                }
                text="腾讯云COS"
                systemConfig.endPoint = $(".cosEndPointSelect option:selected").attr("value-data");
                systemConfig.location = $(".cosEndPointSelect").val();
                if($(".cosEndPointSelect").val() == ""){
                    layer.alert("请选择"+text+"大区");
                    return;
                }
            }
            if($(".accessKeyId").val() == ""){
                layer.alert(text+"授权Key不能为空");
                return;
            }
            if($(".accessSecretKey").val() == ""){
                layer.alert(text+"授权Secret不能为空");
                return;
            }
            if($(".bucketName").val() == ""){
                layer.alert(text+"存储空间名称不能为空");
                return;
            }
        }
        systemConfig.projectName = $(".projectName").val();
        systemConfig.projectLogo = $("#project_logo_update").text();
        systemConfig.projectIco = $("#project_ico_update").text();
        //默认群组
        systemConfig.defaultRooms = $(".defaultRooms").val();
        //弹出loading
        //var index = top.layer.msg('数据提交中，请稍候',{icon: 16,time:false,shade:0.8});
        Common.invoke({
            path : request('/config/set'),
            data : systemConfig,
            successMsg : "系统配置修改成功",
            errorMsg : "修改系统配置失败,请检查网络",
            successCb : function(result) {
                localStorage.setItem("registerInviteCode",systemConfig.registerInviteCode); //更新系统邀请码模式
            },
            errorCb : function(result) {
            }
        });
        return false;
    });
});

function projectLogoClick(){
    $("#project_logo_display_update").click();
}
function updateProjectLogoDisplay(){
    var file=$("#project_logo_display_update")[0].files[0];
    console.log(file)
    uploading("项目LOGO图片上传中");
    $("#project_logo_path_update").ajaxSubmit(function(data){
        var jsonObj = eval('(' + data + ')');
        console.log(jsonObj.data.images[0]);
        $("#project_logo_update").html(jsonObj.data.images[0].oUrl);
        uploadClose();
    });
}
function projectIcoClick(){
    $("#project_ico_display_update").click();
}
function projectIcoDisplay(){
    var file=$("#project_ico_display_update")[0].files[0];
    console.log(file)
    uploading("项目ICO上传中");
    $("#project_ico_path_update").ajaxSubmit(function(data){
        var jsonObj = eval('(' + data + ')');
        console.log(jsonObj.data.images[0]);
        $("#project_ico_update").html(jsonObj.data.others[0].oUrl);
        uploadClose();
    });
}
function uploading(content){
    layui.use(['layer','jquery'],function(){
        var layer = layui.layer, $ = layui.jquery;
        layer.load(1, {
            content: content,
            shade: [0.4, '#393D49'],
            // time: 10 * 1000,
            success: function(uploadLayer) {
                uploadLayer.css('padding-left', '30px');
                uploadLayer.find('.layui-layer-content').css({
                    'padding-top': '40px',
                    'width': "161px",
                    'color':'white',
                    'background-position-x': '16px'
                });
            }
        })
    })
}
function uploadClose(){
    layui.use(['layer','jquery'],function(){
        layer.closeAll();
    })
}



