var currentPageIndex;// 当前页码数
var currentCount;// 当前总数
var $,table,layer;
layui.use(['form','layer','laydate','table','laytpl'],function(){

        layer = parent.layer === undefined ? layui.layer : top.layer,
        $ = layui.jquery,
        table = layui.table;

    //管理员列表
    var tableIns = table.render({

        elem: '#emojiStore_list'
        ,url:request("/console/emojiStoreList/page")
        ,id: 'emojiStore_list'
        ,page: true
        ,curr: 0
        ,limit:Common.limit
        ,limits:Common.limits
        ,groups: 6
        ,cols: [[ //表头
            {field: 'emoPackName', title: '名称', width:150}
            ,{field: 'emoPackImageUrl', title: 'zip路径', width:150}
            ,{field: 'emoPackProfile', title: '简介', width:150}
            ,{field: 'emoPackCreateTime', title: '创建时间',sort:'true', width:200,templet: function(d){
                    return UI.getLocalTime(d.emoPackCreateTime);
                }}
            ,{fixed: 'right', width: 250,title:"操作", align:'left', toolbar: '#emojiStoreBar'}
        ]]
        ,done:function(res, curr, count){
            if(res.resultCode == -1 && res.resultMsg =="用户未登录或登录失效"){
                layer.msg("用户未登录或登录失效,请重新登录", {"icon": 2},function(){
                    window.location.href = "/pages/console/login.html";
                });
                return;
            }
            if (count == 0 && lock == 1) {
                layer.msg("暂无数据", {"icon": 2});
                renderTable();
            }
            var pageIndex = tableIns.config.page.curr;//获取当前页码
            var resCount = res.count;// 获取table总条数
            currentCount = resCount;
            currentPageIndex = pageIndex;
        }
    });

    //列表操作
    table.on('tool(emojiStore_list)', function(obj){
        var layEvent = obj.event, data = obj.data;
        console.log("delete:"+JSON.stringify(data));
        if(layEvent === 'delete'){ //移出
            layer.confirm('确定删除该表情包？',{icon:3, title:'提示信息'},function(index){

                Common.invoke({
                    path : '/console/emojiStoreList/delete',
                    data : {
                        "emoPackId" :data.emoPackId
                    },
                    successMsg : "移出表情包成功",
                    errorMsg :  "移出表情包失败，请稍后重试",
                    successCb : function(result) {
                        layer.close(index); //关闭弹框
                        // obj.del();
                        Common.tableReload(currentCount,currentPageIndex,1,"emojiStore_list");
                    },
                    errorCb : function(result) {

                    }
                });
            })

        }
    });

    //搜索
    $(".search_emojiList").on("click",function(){
        // 关闭超出宽度的弹窗
        $(".layui-layer-content").remove();
        console.log(" ======>>>>>>> search Admin Test "+$(".admin_keyword").val());

        table.reload("emojiStore_list",{
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: {
                keyWorld : $(".admin_keyword").val()  //搜索的关键字
            }
        })
        $(".admin_keyword").val("");
    });

    $(".btn_addEmojiList").on("click",function(){
        $(".admin_btn_div").hide();
        $(".emoji_table").hide();
        $("#add_emojiList").show();
    });
})
var EmojiStore={
    uploadZip:function(formData,callbackEmoji){
        layer.load(1);
        $.ajax({
            url : '/console/emojiStoreList/uploadZip',
            method : 'POST',
//            async: false,
            processData: false,
            contentType:false,
            data : formData,
            success : function(result) {
                callbackEmoji(result)
            },
            error : function(result) {
                layer.closeAll('loading');
            }
        })
    },
    commit_addEmoji:function(){
        var zipUpdate = $("#zipId")[0].files[0];
        var formData = new FormData();
        formData.append("zipFile",zipUpdate);
        EmojiStore.uploadZip(formData,callbackEmoji)
    },
    btn_back:function(){
        $(".admin_btn_div").show();
        $(".emoji_table").show();
        $("#add_emojiList").hide();
    },
    changeZipPath:function(){
        $("#zipSpanPath").html($("#zipId").val());
    },
}
function callbackEmoji(result){
    if(result.resultCode==1){
        var emojiName = $(".emojiName").val();
        // var emojiPath = $(".emojiPath").val();
        var emojiProfile = $(".emojiProfile").val();
        var emojiEveryName = $(".emojiEveryName").val();
        var emojiPath = JSON.stringify(result.data);
        Common.invoke({
            path : '/console/emojiStoreList/add',
            data : {
                'zipName':emojiName,
                'zipPath':emojiPath,
                'zipProfile': emojiProfile,
                'nameStr' :emojiEveryName
            },
            successMsg : "表情包加入成功",
            errorMsg : "表情包加入失败,请稍后重试",
            successCb : function(result) {
                layer.closeAll('loading');
                if (1 == result.resultCode){
                    $(".emojiName").val("");
                    $(".emojiPath").val("");
                    $(".emojiProfile").val("");
                    $(".emojiEveryName").val("");
                    layui.layer.alert("表情包加入成功");
                    layui.table.reload("emojiStore_list");
                    EmojiStore.btn_back()
                }
            },
            errorCb : function(result) {
                layer.closeAll('loading');
            }
        });
    }
}