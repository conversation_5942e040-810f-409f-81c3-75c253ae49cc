layui.use(['form','jquery',"layer"],function() {
    var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
    $.post("/console/smsConfig/get", function(result) {
        console.log(result);
        if (result.resultCode == 1) {
            fillParameter(result.data);
        }
    }, "json");
    //提交保存配置
    form.on("submit(rateConfig)",function(data){
        var systemSmsConfig = {
        };
        systemSmsConfig.isOpenSMS = $(".isOpenSMS").val();
        systemSmsConfig.SMSType = $(".SMSType").val();
        systemSmsConfig.ttSmsName = $(".ttSmsName").val();
        systemSmsConfig.ttSmsPassword = $(".ttSmsPassword").val();
        systemSmsConfig.ttSmsChTemplate = $(".ttSmsChTemplate").val();
        systemSmsConfig.ttSmsEnTemplate = $(".ttSmsEnTemplate").val();
        systemSmsConfig.accessKeyId = $(".accessKeyId").val();
        systemSmsConfig.accessKeySecret = $(".accessKeySecret").val();
        systemSmsConfig.aliSMSSign = $(".aliSMSSign").val();
        systemSmsConfig.aliSMSChTemCode = $(".aliSMSChTemCode").val();
        systemSmsConfig.aliSMSEnTemCode = $(".aliSMSEnTemCode").val();
        systemSmsConfig.yunPianAppKey = $(".yunPianAppKey").val();
        systemSmsConfig.yunPianSMSChSign = $(".yunPianSMSChSign").val();
        systemSmsConfig.yunPianSMSEnSign = $(".yunPianSMSEnSign").val();
        systemSmsConfig.yunPianSMSChTemCode = $(".yunPianSMSChTemCode").val();
        systemSmsConfig.yunPianSMSEnTemCode = $(".yunPianSMSEnTemCode").val();
        systemSmsConfig.mdPassword = $(".mdPassword").val();
        systemSmsConfig.mdSN = $(".mdSN").val();
        systemSmsConfig.mdSignName = $(".mdSignName").val();
        systemSmsConfig.mdSMSChTemCode = $(".mdSMSChTemCode").val();
        systemSmsConfig.mdEnPassword = $(".mdEnPassword").val();
        systemSmsConfig.mdEnSN = $(".mdEnSN").val();
        systemSmsConfig.mdEnSignName = $(".mdEnSignName").val();
        systemSmsConfig.mdSMSEnTemCode = $(".mdSMSEnTemCode").val();
        systemSmsConfig.juheAccessKeyId = $(".juheAccessKeyId").val();
        systemSmsConfig.juheAccessKey = $(".juheAccessKey").val();
        systemSmsConfig.clappkey = $(".clappkey").val();
        systemSmsConfig.clsecretkey = $(".clsecretkey").val();
        Common.invoke({
            path : request('/console/smsConfig/set'),
            data : systemSmsConfig,
            successMsg : "系统配置修改成功",
            errorMsg : "修改系统配置失败,请检查网络",
            successCb : function(result) {
                layui.layer.alert("修改成功");
            },
            errorCb : function(result) {
            }
        });
        return false;
    });
});
function fillParameter(data){
    //数据回显
    if(data!=undefined&&data!=null){
        $(".isOpenSMS").val(data.isOpenSMS);
        $(".SMSType").val(data.sMSType);
        $(".ttSmsName").val(data.ttSmsName);
        $(".ttSmsPassword").val(data.ttSmsPassword);
        $(".ttSmsChTemplate").val(data.ttSmsChTemplate);
        $(".ttSmsEnTemplate").val(data.ttSmsEnTemplate);
        $(".accessKeyId").val(data.accessKeyId);
        $(".accessKeySecret").val(data.accessKeySecret);
        $(".aliSMSSign").val(data.aliSMSSign);
        $(".aliSMSChTemCode").val(data.aliSMSChTemCode);
        $(".aliSMSEnTemCode").val(data.aliSMSEnTemCode);
        $(".yunPianAppKey").val(data.yunPianAppKey);
        $(".yunPianSMSChSign").val(data.yunPianSMSChSign);
        $(".yunPianSMSEnSign").val(data.yunPianSMSEnSign);
        $(".yunPianSMSChTemCode").val(data.yunPianSMSChTemCode);
        $(".yunPianSMSEnTemCode").val(data.yunPianSMSEnTemCode);
        $(".mdPassword").val(data.mdPassword);
        $(".mdSN").val(data.mdSN);
        $(".mdSignName").val(data.mdSignName);
        $(".mdSMSChTemCode").val(data.mdSMSChTemCode);
        $(".mdEnPassword").val(data.mdEnPassword);
        $(".mdEnSN").val(data.mdEnSN);
        $(".mdEnSignName").val(data.mdEnSignName);
        $(".mdSMSEnTemCode").val(data.mdSMSEnTemCode);
        $(".juheAccessKeyId").val(data.juheAccessKeyId);
        $(".juheAccessKey").val(data.juheAccessKey);
        $(".clappkey").val(data.clappkey);
        $(".clsecretkey").val(data.clsecretkey);
    }
    //重新渲染
    layui.form.render();
}