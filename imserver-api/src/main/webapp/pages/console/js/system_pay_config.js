    layui.use('form', function(){
        let form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    if (localStorage.getItem("tlPayIsOpen") == null || localStorage.getItem("tlPayIsOpen") == "false"){
        $("#tlFrom").hide();
    }
    //填充数据方法
    function fillParameter(data){
        //判断字段数据是否存在
        function nullData(data){
            if(data == '' || data == "undefined" || data==null){
                return "";
            }else{
                return data;
            }
        }
        //数据回显
        $("#rateId").val(nullData(data.id));
        $(".transferRate").val(data.transferRate == 0 ? 0 : nullData(data.transferRate));
        $(".minTransferAmount").val(data.minTransferAmount == 0 ? 0 : nullData(data.minTransferAmount));
        $(".maxSendRedPagesAmount").val(data.maxSendRedPagesAmount == 0 ? 0 : nullData(data.maxSendRedPagesAmount));
        $(".displayRedPacket").val(data.displayRedPacket);
        var isWithdrawToAdmin = data.isWithdrawToAdmin;
         if (isWithdrawToAdmin == 0) {
            $("#withdrawToAdmin").hide();
         }
         if (isWithdrawToAdmin == 1) {
             $("#withdrawToAdmin").show();
         }
        $(".isWithdrawToAdmin").val(data.isWithdrawToAdmin);// 是否提现到后台管理
        var isWeiBaoStatus = data.isWeiBaoStatus;
        $(".isWeiBaoStatus").val(isWeiBaoStatus);// 是否提现到后台管理
        if (isWeiBaoStatus == 0) {
            $("#weiFrom").hide();
            $("#weiBaoTransferRate").hide();
            $("#weiBaoMinTransferAmount").hide();
            $("#weiBaoMaxTransferAmount").hide();
            $("#weiBaoMaxRedPacketAmount").hide();
        }
        if (isWeiBaoStatus == 1) {
            $("#weiFrom").show();
            $("#weiBaoTransferRate").show();
            $("#weiBaoMinTransferAmount").show();
            $("#weiBaoMaxTransferAmount").show();
            $("#weiBaoMaxRedPacketAmount").show();
        }
        $(".isWeiBaoStatus").val(data.isWeiBaoStatus);
        $(".weiBaoTransferRate").val(nullData(data.weiBaoTransferRate));
        $(".weiBaoMinTransferAmount").val(nullData(data.weiBaoMinTransferAmount));
        $(".weiBaoMaxTransferAmount").val(nullData(data.weiBaoMaxTransferAmount));
        $(".weiBaoMaxRedPacketAmount").val(nullData(data.weiBaoMaxRedPacketAmount));
        var withdrawWayList = data.withdrawWayList;
        if(null!=withdrawWayList){
            if(withdrawWayList.length>2){
                $("#withdrawToAdmin1 table tbody tr").slice(2,withdrawWayList.length).remove();
            }
            for (var i=0;i<withdrawWayList.length;i++) {
                if(i==0){
                    $("#isWithdrawToAlipayName").val(withdrawWayList[i].withdrawWayName);
                    $("#isWithdrawToAlipay").val(withdrawWayList[i].withdrawWayStatus);
                    $("#isWithdrawToAlipayId").val(withdrawWayList[i].withdrawWayKeyId);
                }else if(i==1){
                    $("#isWithdrawToBankCardName").val(withdrawWayList[i].withdrawWayName);
                    $("#isWithdrawToBankCard").val(withdrawWayList[i].withdrawWayStatus);
                    $("#isWithdrawToBankCardId").val(withdrawWayList[i].withdrawWayKeyId);
                }else{
//                    $("#table tr").length();
                    if(withdrawWayList[i].withdrawWayStatus==1){
                        $("#withdrawToAdmin1 table tbody").append('<tr><td id="'+withdrawWayList[i].withdrawWayName+i+'">'+withdrawWayList[i].withdrawWayName+'</td><td><select class="layui-input-inline" lay-filter="withdrawWay" ><option value="1" selected>开启</option><option value="0">关闭</option></select></td><td><a href="" style="color: blue;font-size: 15px;" class="editPlatform">详情</a>&nbsp;&nbsp;&nbsp;<a href="" style="color: blue;font-size: 15px;" class="deletePlatform">删除</a></td><td id="'+withdrawWayList[i].withdrawWayKeyId+'"></td></tr>');
                    }else{
                        $("#withdrawToAdmin1 table tbody").append('<tr><td id="'+withdrawWayList[i].withdrawWayName+i+'">'+withdrawWayList[i].withdrawWayName+'</td><td><select class="layui-input-inline" lay-filter="withdrawWay" ><option value="1">开启</option><option value="0" selected>关闭</option></select></td><td><a href="" style="color: blue;font-size: 15px;" class="editPlatform">详情</a>&nbsp;&nbsp;&nbsp;<a href="" style="color: blue;font-size: 15px;" class="deletePlatform">删除</a></td><td id="'+withdrawWayList[i].withdrawWayKeyId+'"></td></tr>');
                    }
                    $("#"+withdrawWayList[i].withdrawWayName+i).val(withdrawWayList[i].withdrawWayName);
                    $("#"+withdrawWayList[i].withdrawWayKeyId).val(withdrawWayList[i].withdrawWayKeyId);
                }
            }
        }i
        //重新渲染
        layui.form.render();
        // layui.form.render('checkbox','appCheckbox');
    }

    layui.use(['form','jquery',"layer"],function() {
        var form = layui.form, $ = layui.jquery, layer = layui.layer;
        form.on('select(withdrawWay)', function(data){
            var id = $(this).parent().parent().parent().parent().children('td').eq(3).val();
            var status=data.value
            var isAllClosed = true;
             var trList = $("#withdrawToAdmin1").children("table").children("tbody").children("tr")
             for(var i = 0; i <trList.length;i++){
                 var tdArr = trList.eq(i).find("td");
                  if(tdArr.eq(1).find('select').val()==1){
                      isAllClosed = false;
                      break;
                  }
             }
             if(isAllClosed){
                 layui.layer.msg("请开启至少一种提现通道", {"icon": 2});
                 $(this).parent().parent().parent().parent().children('td').eq(1).find('select').val(1);
                 layui.form.render();
                 return false;
             }
            updateWithdrawWayStatus(id , status);
         });
        $(document).on('click', '#deleteWithdrawKey', function () {
            $("#addWithdrawWay").on('click', '.input', function () {
                 var withdrawId = $(this).parent().parent().children('td').attr("id");
                 $(this).closest('tr').remove();
                 if(withdrawId!=""){
                   Common.invoke({
                      path: '/console/deleteWithdrawKey',
                      data: {
                           "withdrawId": withdrawId
                       },
                      successMsg: "删除成功",
                      errorMsg: "删除失败，请稍后重试",
                      successCb: function (result) {
                             return false;
                          },
                           errorCb: function (result) {

                          }
                      });
                  return false;
                 }
            });
            return false;
        });
        $(document).on('click', '.editPlatform', function () {
            var withdrawWayKeyId = $(this).parent().parent().children('td').eq(3).val();
              Common.invoke({
                 path: '/console/getWithdrawWay',
                 data: {
                      "withdrawWayKeyId": withdrawWayKeyId
                  },
                 successMsg: "",
                 errorMsg: "",
                 successCb: function (result) {
                    layui.layer.open({
                         title: "提现通道详情",
                         type: 1,
                         btn: ["关闭"],
                         area : ['60%', '60%'],
                         content: '<div id="addWithdrawWay" class="layui-form" style="margin:20px 40px 10px 40px;;">'
//                               +'<div style="color: red;">注意:请将提现所需的重要必填字段如账号、姓名等填在前两个字段</div>'
                               + '<table cellspacing="0" cellpadding="0" border="0" class="layui-table">'
                               +'<tr><td>平台名称</td><td><input  readonly type="text" name="kouLing" class="layui-input" id="platformName" placeholder="请输入平台名称" value="'+result.data.withdrawWayName+'"></td><td></td><td></td></tr>'
                               + '</table>'
//                               + '<a href="" id="addKey" style="color: blue;font-size: 5px;">添加字段</a>'
                             + '</div>'

                         , yes: function (index, layero) { //确定按钮的回调
//                                 var linkDetailsArr = new Array();
//                         		var trList = $("#addWithdrawWay").children("table").children("tbody").children("tr")
//                         		var platformName = $("#platformName").val();
//                         		if(platformName==null||platformName==""){
//                                     layui.layer.msg("平台名称不能为空", {"icon": 2});
//                                     return false;
//                                }
//                                if(trList.length==1){
//                                       layui.layer.msg("请添加至少一个字段", {"icon": 2});
//                                       return false;
//                                }
//                                var isExistInput = false;
//                         		for (var i=1;i<trList.length;i++) {
//                         			var linkDetailsArrOne = new Array();
//                         			var tdArr = trList.eq(i).find("td");
//                         			var withdrawKayName = tdArr.eq(1).find('input').val();
//                         			var withdrawKayStatus = tdArr.eq(2).find('input');
//                         			if(withdrawKayName==null||withdrawKayName==""){
//                                         layui.layer.msg("字段不能为空", {"icon": 2});
//                                         return false;
//                         			}
//                         			linkDetailsArrOne[0] = withdrawKayName;
//                         			if((withdrawKayStatus.is(":checked"))){
//                         			    linkDetailsArrOne[1] = 1;
//                         			    isExistInput = true;
//                         			}else{
//                         			    linkDetailsArrOne[1] = 0;
//                         			}
//                         			linkDetailsArr[i] = linkDetailsArrOne;
//                         		}
//                         		if(!isExistInput){
//                                         layui.layer.msg("请勾选至少一个必填字段", {"icon": 2});
//                                         return false;
//                         		}
//                                  var platformName = $("#platformName").val();
//                                  var jsonLinkDetails = JSON.stringify(linkDetailsArr);
//                                 Common.invoke({
//                                     path: '/console/updateWithdrawWay',
//                                     data: {
//                                         "withdrawWayKeyId": withdrawWayKeyId,
//                                         "withdrawKeyDetails": jsonLinkDetails,
//                                         "platformName":$("#platformName").val()
//                                     },
//                                     successMsg: "更新成功",
//                                     errorMsg: "更新失败，请稍后重试",
//                                     successCb: function (result) {
//                                            layui.layer.close(index); //关闭弹框
//                                            var trList = $("#withdrawToAdmin1").children("table").children("tbody").children("tr")
//                                            for(var i = 0; i <trList.length;i++){
//                                                var tdArr = trList.eq(i).find("td");
//                                                if(tdArr.eq(3).val()==withdrawWayKeyId){
//                                                    var id = tdArr.eq(0).attr("id");
//                                                    $("#"+id).val(platformName);
//                                                    $("#"+id).html(platformName);
//                                                }
//                                            }
////                                          Common.invoke({
////                                                 path : request('/console/getTransferConfig'),
////                                                 data : {},
////                                                 successMsg : false,
////                                                 errorMsg : "获取数据失败,请检查网络",
////                                                 successCb : function(result) {
////                                                            fillParameter(result.data);
////                                                 },
////                                                 errorCb : function(result) {
////                                                }
////                                           });
////                                          return false;
//                                     },
//                                     errorCb: function (result) {
//
//                                     }
//                                 });
                                layui.layer.close(index); //关闭弹框
                             }
                         });
                            for(var i=0 ;i<result.data.withdrawKeyDetails.length;i++){
                                if(result.data.withdrawKeyDetails[i].withdrawStatus==1){
                                    $("#addWithdrawWay table").append('<tr><td id="'+result.data.withdrawKeyDetails[i].withdrawId +'">字段</td><td><input  readonly type="text" name="kouLing" class="layui-input" placeholder="请输入字段" value="'+result.data.withdrawKeyDetails[i].withdrawName+'"/></td><td>必填</td><td></td></tr>');
                                }else{
                                    $("#addWithdrawWay table").append('<tr><td id="'+result.data.withdrawKeyDetails[i].withdrawId +'">字段</td><td><input  readonly type="text" name="kouLing" class="layui-input" placeholder="请输入字段" value="'+result.data.withdrawKeyDetails[i].withdrawName+'"/></td><td>非必填</td><td></td></tr>');
                                }
                           }
                     },
                      errorCb: function (result) {
                     }
                 });
                 return false;
        });
        $(document).on('click', '.deletePlatform', function () {
            var isAllClosed = true;
            var withdrawWayKeyId = $(this).parent().parent().children('td').eq(3).val();
            var trList = $("#withdrawToAdmin1").children("table").children("tbody").children("tr")
            for(var i = 0; i <trList.length;i++){
                var tdArr = trList.eq(i).find("td");
                if(tdArr.eq(3).val()!=withdrawWayKeyId){
                  if(tdArr.eq(1).find('select').val()==1){
                      isAllClosed = false;
                      break;
                  }
                }
            }
            if(isAllClosed){
                layui.layer.msg("请开启至少一种提现通道,再进行删除", {"icon": 2});
                return false;
            }
             $(this).closest('tr').remove();
              Common.invoke({
                 path: '/console/deleteWithdrawWay',
                 data: {
                      "withdrawWayKeyId": withdrawWayKeyId
                  },
                 successMsg: "删除成功",
                 errorMsg: "删除失败，请稍后重试",
                 successCb: function (result) {
                        form.render();
                     },
                      errorCb: function (result) {

                     }
                 });
             return false;
         });
        $(document).on('click','#addKey',function(){
            if($("#addWithdrawWay table tr").length>5){
                 layui.layer.msg("最多只能添加5个字段", {"icon": 2});
                 return false;
            }else{
                $("#addWithdrawWay table").append('<tr><td id = "">字段</td><td><input  type="text" name="kouLing" class="layui-input" placeholder="请输入字段" /></td><td><input type="checkbox" style="display: block;">必填</input></td><td><input  type="button" name="" id="deleteWithdrawKey" class="input" value="删除" /></td></tr>');
            }
            return false;
        });
         $(document).on('click','#addNewWithdraw',function(){
                layui.layer.open({
                        title: "新增提现通道",
                        type: 1,
                        btn: ["保存", "取消"],
                        area : ['60%', '60%'],
                        content: '<div id="addWithdrawWay" class="layui-form" style="margin:20px 40px 10px 40px;;">'
                              +'<div style="color: red;">注意:请将提现所需的重要必填字段如账号、姓名等填在前两个字段</div>'
                              + '<table cellspacing="0" cellpadding="0" border="0" class="layui-table">'
                                 +'<tr><td>平台名称</td><td><input  type="text" name="kouLing" class="layui-input" id="platformName" placeholder="请输入平台名称" ></td><td></td><td></td></tr>'
                              + '</table>'
                              + '<a href="" id="addKey" style="color: blue;font-size: 5px;">添加字段</a>'
                            + '</div>'

                        , yes: function (index, layero) { //确定按钮的回调
                                var linkDetailsArr = new Array();
                        		var trList = $("#addWithdrawWay").children("table").children("tbody").children("tr")
                                var platformName = $("#platformName").val();
                                if(platformName==null||platformName==""){
                                   layui.layer.msg("平台名称不能为空", {"icon": 2});
                                   return false;
                                }
                                if(trList.length==1){
                                    layui.layer.msg("请添加至少一个字段", {"icon": 2});
                                    return false;
                                }
                                var isExistInput = false;
                        		for (var i=1;i<trList.length;i++) {
                        			var linkDetailsArrOne = new Array();
                        			var tdArr = trList.eq(i).find("td");
                        			var withdrawKayName = tdArr.eq(1).find('input').val();
                        			var withdrawKayStatus = tdArr.eq(2).find('input');
                        			if(withdrawKayName==null||withdrawKayName==""){
                                        layui.layer.msg("字段不能为空", {"icon": 2});
                                        return false;
                        			}
                        			linkDetailsArrOne[0] = withdrawKayName;
                        			if((withdrawKayStatus.is(":checked"))){
                        			    linkDetailsArrOne[1] = 1;
                        			    isExistInput = true;
                        			}else{
                        			    linkDetailsArrOne[1] = 0;
                        			}
                        			linkDetailsArr[i] = linkDetailsArrOne;
                        		}
                        		if(!isExistInput){
                                     layui.layer.msg("请勾选至少一个必填字段", {"icon": 2});
                                     return false;
                        		}
                                 var jsonLinkDetails = JSON.stringify(linkDetailsArr);
                                Common.invoke({
                                    path: '/console/addWithdrawWay',
                                    data: {
                                        "withdrawWayName": platformName,
                                        "withdrawKeyDetails": jsonLinkDetails
                                    },
                                    successMsg: "新增成功",
                                    errorMsg: "新增失败，请稍后重试",
                                    successCb: function (result) {
                                        layui.layer.close(index); //关闭弹框
                                         $("#withdrawToAdmin1 table tbody").append('<tr><td id="'+result.data.withdrawWayName+0+'">'+result.data.withdrawWayName+'</td><td><select class="layui-input-inline" lay-filter="withdrawWay"><option value="1" selected>开启</option><option value="0">关闭</option></select></td><td><a href="" style="color: blue;font-size: 15px;" class="editPlatform">详情</a>&nbsp;&nbsp;&nbsp;<a href="" style="color: blue;font-size: 15px;" class="deletePlatform">删除</a></td><td id="'+result.data.withdrawWayKeyId+'"></td></tr>');
                                         $("#"+result.data.withdrawWayName+0).val(result.data.withdrawWayName);
                                         $("#"+result.data.withdrawWayKeyId).val(result.data.withdrawWayKeyId);
                                         form.render();
                                         return false;
                                    },
                                    errorCb: function (result) {

                                    }
                                });
                            }
                        });
                      return false;
         });
        Common.invoke({
            path : request('/console/getTransferConfig'),
            data : {},
            successMsg : false,
            errorMsg : "获取数据失败,请检查网络",
            successCb : function(result) {
                fillParameter(result.data);
            },
            errorCb : function(result) {
            }
        });

        form.on('select(isWithdrawToAdmin)',function(data){
             if (data.value == 1){
                 $("#withdrawToAdmin").show();
             }
            if (data.value == 0){
                $("#withdrawToAdmin").hide();
            }
        });

        //提交保存配置
        form.on("submit(rateConfig)",function(){
            setTransferConfig();
            return false;
        });
        form.on('select(isWeiBaoStatus)',function(data){
            if (data.value == 0) {
                $("#weiFrom").hide();
                $("#weiBaoTransferRate").hide();
                $("#weiBaoMinTransferAmount").hide();
                $("#weiBaoMaxTransferAmount").hide();
                $("#weiBaoMaxRedPacketAmount").hide();
            }
            if (data.value == 1) {
                $("#weiFrom").show();
                $("#weiBaoTransferRate").show();
                $("#weiBaoMinTransferAmount").show();
                $("#weiBaoMaxTransferAmount").show();
                $("#weiBaoMaxRedPacketAmount").show();
            }
        });
    });

    function getFormData(obj) {
        var unindexed_array = obj.serializeArray();
        var indexed_array = {};
        $.map(unindexed_array, function (n) {
            if(n['value']!=null&&n['value']!=''){
                indexed_array[n['name']] = n['value'];
            }
        });
        return indexed_array;
    }

     function updateWithdrawWayStatus(id,status){
        Common.invoke({
              path: '/console/updateWithdrawWayStatus',
              data: {
                     "withdrawWayKeyId": id,
                      "status": status
              },
              successMsg: "修改成功",
              errorMsg: "修改失败，请稍后重试",
              successCb: function (result) {
                       return false;
              },
              errorCb: function (result) {

              }
          });
     }
    //提交配置表单
    function setTransferConfig(){
        var rateConfig = {
        };
        var maxSendRedPagesAmount = $(".maxSendRedPagesAmount").val();
        rateConfig.id = $("#rateId").val();
        rateConfig.transferRate = $(".transferRate").val();
        rateConfig.rechargeRate = $(".rechargeRate").val();
        rateConfig.minTransferAmount = $(".minTransferAmount").val();
        rateConfig.maxSendRedPagesAmount = maxSendRedPagesAmount;
        rateConfig.displayRedPacket = $(".displayRedPacket").val();
        rateConfig.isWithdrawToAdmin = $(".isWithdrawToAdmin").val();
        rateConfig.isWeiBaoStatus = $(".isWeiBaoStatus").val();
        rateConfig.weiBaoTransferRate = $(".weiBaoTransferRate").val();
        rateConfig.weiBaoMinTransferAmount = $(".weiBaoMinTransferAmount").val();
        rateConfig.weiBaoMaxTransferAmount = $(".weiBaoMaxTransferAmount").val();
        rateConfig.weiBaoMaxRedPacketAmount = $(".weiBaoMaxRedPacketAmount").val();
        //弹出loading
        //var index = top.layer.msg('数据提交中，请稍候',{icon: 16,time:false,shade:0.8});
        var newWithdrawDetailsArr = new Array();
        var trList = $("#withdrawToAdmin1").children("table").children("tbody").children("tr")
        for (var i=0;i<trList.length;i++) {
            var newWithdrawDetailsArrOne = new Array();
            var tdArr = trList.eq(i).find("td");
            var withdrawName = tdArr.eq(0).val();
            var withdrawStatus = tdArr.eq(1).find('select').val();
            var withdrawKey = tdArr.eq(3).val();
            newWithdrawDetailsArrOne[0] = withdrawName;
            newWithdrawDetailsArrOne[1] = withdrawStatus;
            newWithdrawDetailsArrOne[2] = withdrawKey;
            newWithdrawDetailsArr[i] = newWithdrawDetailsArrOne;
        }
        var isAllClosed = true;
        if($(".isWithdrawToAdmin").val()==1){
            for(var i = 0; i < newWithdrawDetailsArr.length;i++){
                if(newWithdrawDetailsArr[i][1]==1){
                    isAllClosed = false;
                    break;
                }
            }
            if(isAllClosed){
                layui.layer.msg("请开启至少一种提现通道", {"icon": 2});
                return false;
            }
        }

        if (maxSendRedPagesAmount<=0 || maxSendRedPagesAmount>99999){
            layui.layer.msg("发送红包最大金额请输入【0~100000】之间的数组", {"icon": 2});
            return false;
        }
        var jsonNewWithdrawDetails = JSON.stringify(newWithdrawDetailsArr);
        Common.invoke({
        path : request('/console/setTransferConfig'),
        data : {
            "id":$("#rateId").val(),
            "transferRate":$(".transferRate").val(),
            "rechargeRate":$(".rechargeRate").val(),
            "minTransferAmount":$(".minTransferAmount").val(),
            "maxSendRedPagesAmount":maxSendRedPagesAmount,
            "displayRedPacket":$(".displayRedPacket").val(),
            "isWithdrawToAdmin":$(".isWithdrawToAdmin").val(),
            "isWeiBaoStatus":$(".isWeiBaoStatus").val(),
            "weiBaoTransferRate":$(".weiBaoTransferRate").val(),
            "weiBaoMinTransferAmount":$(".weiBaoMinTransferAmount").val(),
            "weiBaoMaxTransferAmount":$(".weiBaoMaxTransferAmount").val(),
            "weiBaoMaxRedPacketAmount":$(".weiBaoMaxRedPacketAmount").val(),
            "newWithdrawDetails":jsonNewWithdrawDetails
        },
        successMsg : "提现配置修改成功",
        errorMsg : "修改提现配置失败,请检查网络",
        successCb : function(result) {},
        errorCb : function(result) {}
        });
        return false;
    }
    function savePayConfig(obj){
        var payConfig = getFormData(obj);
        Common.invoke({
            path : request('/console/savePayConfig'),
            data : payConfig,
            successMsg : false,
            errorMsg : "获取数据失败,请检查网络",
            successCb : function(result) {
                layui.use('layer', function() { //独立版的layer无需执行这一句
                    layer.msg(result.resultMsg,{time:2000});
                    if (result.resultCode == 1){
                        getAliPayConfig();
                        getWechatPayConfig();
                        getTlPayConfig();
                        // getTlWithPayConfig();
                        getYunPayConfig();
                        // getJzPayConfig();
                        getHmPayConfig();
                        getWeiPayConfig();
                    }
                });
            },
            errorCb : function(result) {
            }
        });
    }
    getAliPayConfig();
    getWechatPayConfig();
    getTlPayConfig();
    // getTlWithPayConfig();
    getYunPayConfig();
    // getJzPayConfig();
    getHmPayConfig();
    getWeiPayConfig();
    function getAliPayConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getPayConfig'),
                data : {type:1},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#aliId").val(result.data.id);
                        $("#appIdAli").val(result.data.appId);
                        $("#mchIdAli").val(result.data.mchId);
                        $("#payCallBackUrlAli").val(result.data.payCallBackUrl);
                        $("#privateKeyAli").val(result.data.privateKey);
                        $("#publicKeyAli").val(result.data.publicKey);
                        $("#transCallBackUrlAli").val(result.data.transCallBackUrl);
                        $("#aliType").val(result.data.type);
                        let aliStatus = result.data.status;
                        if (aliStatus == 1){
                            $("#isAlipayFalse").attr("checked",false);
                            $("#isAlipayTrue").attr("checked","checked");
                        }
                        if (aliStatus == 2){
                            $("#isAlipayTrue").attr("checked",false);
                            $("#isAlipayFalse").attr("checked","checked");
                        }
                        let withdrawStatus = result.data.withdrawStatus;
                        if (withdrawStatus == 1){
                            $("#isWithdrawAlipayFalse").attr("checked",false);
                            $("#isWithdrawAlipayTrue").attr("checked","checked");
                        }
                        if (withdrawStatus == 2){
                            $("#isWithdrawAlipayTrue").attr("checked",false);
                            $("#isWithdrawAlipayFalse").attr("checked","checked");
                        }
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }

    function getWechatPayConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getPayConfig'),
                data : {type:2},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#wechatId").val(result.data.id);
                        $("#appIdWechat").val(result.data.appId);
                        $("#appKeyWechat").val(result.data.appKey);
                        $("#mchIdWechat").val(result.data.mchId);
                        $("#payCallBackUrlWechat").val(result.data.payCallBackUrl);
                        $("#wechatType").val(result.data.type);
                        $("#apiKeyWechat").val(result.data.apiKey);
                        let wechatStatus = result.data.status;
                        if (wechatStatus == 1){
                            $("#isWechatFalse").attr("checked",false);
                            $("#isWechatTrue").attr("checked","checked");
                        }
                        if (wechatStatus == 2){
                            $("#isWechatTrue").attr("checked",false);
                            $("#isWechatFalse").attr("checked","checked");
                        }
                        let withdrawStatus = result.data.withdrawStatus;
                        if (withdrawStatus == 1){
                            $("#isWithdrawWechatFalse").attr("checked",false);
                            $("#isWithdrawWechatTrue").attr("checked","checked");
                        }
                        if (withdrawStatus == 2){
                            $("#isWithdrawWechatTrue").attr("checked",false);
                            $("#isWithdrawWechatFalse").attr("checked","checked");
                        }
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }

    function getTlPayConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getPayConfig'),
                data : {type:5},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#tlId").val(result.data.id);
                        $("#appIdTl").val(result.data.appId);
                        $("#appKeyTl").val(result.data.appKey);
                        $("#mchIdTl").val(result.data.mchId);
                        $("#payCallBackUrlTl").val(result.data.payCallBackUrl);
                        $("#tlType").val(result.data.type);
                        $("#privateKeyTl").val(result.data.privateKey);
                        $("#publicKeyTl").val(result.data.publicKey);
                        $("#withdrawMchIdTl").val(result.data.withdrawMchId);
                        $("#withdrawAppIdTl").val(result.data.withdrawAppId);
                        $("#withdrawAppKeyTl").val(result.data.withdrawAppKey);
                        let tlStatus = result.data.status;
                        if (tlStatus == 1){
                            $("#isTlPayFalse").attr("checked",false);
                            $("#isWechatTrue").attr("checked","checked");
                        }
                        if (tlStatus == 2){
                            $("#isTlPayTrue").attr("checked",false);
                            $("#isTlPayFalse").attr("checked","checked");
                        }
                        let withdrawStatus = result.data.withdrawStatus;
                        if (withdrawStatus == 1){
                            $("#isTlWithdrawFalse").attr("checked",false);
                            $("#isTlWithdrawTrue").attr("checked","checked");
                        }
                        if (withdrawStatus == 2){
                            $("#isTlWithdrawTrue").attr("checked",false);
                            $("#isTlWithdrawFalse").attr("checked","checked");
                        }
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }

    // function getTlWithPayConfig() {
    //     layui.use(['form','jquery',"layer"],function() {
    //         var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
    //         Common.invoke({
    //             path : request('/console/getPayConfig'),
    //             data : {type:6},
    //             successMsg : false,
    //             errorMsg : "获取数据失败,请检查网络",
    //             successCb : function(result) {
    //                 if (result.data!=null){
    //                     $("#tlWithdrawId").val(result.data.id);
    //                     $("#appIdTlWithdraw").val(result.data.appId);
    //                     $("#appKeyTlWithdraw").val(result.data.appKey);
    //                     $("#mchIdTlWithdraw").val(result.data.mchId);
    //                     $("#privateKeyTlWithdraw").val(result.data.privateKey);
    //                     $("#publicKeyTlWithdraw").val(result.data.publicKey);
    //                     $("#tlWithdrawType").val(result.data.type);
    //                     let tlStatus = $("#tlStatus").val();
    //                     if (tlStatus == 1){
    //                         $("#isTlWithdrawFalse").attr("checked",false);
    //                         $("#isTlWithdrawTrue").attr("checked","checked");
    //                     }
    //                     if (tlStatus == 2){
    //                         $("#isTlWithdrawTrue").attr("checked",false);
    //                         $("#isTlWithdrawFalse").attr("checked","checked");
    //                     }
    //                     form.render();
    //                 }
    //             },
    //             errorCb : function(result) {
    //             }
    //         });
    //     });
    // }
    function getYunPayConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getPayConfig'),
                data : {type:6},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#yunId").val(result.data.id);
                        $("#appIdYun").val(result.data.appId);
                        $("#appKeyYun").val(result.data.appKey);
                        $("#payApiUrlYun").val(result.data.payApiUrl);
                        $("#payCallBackUrlYun").val(result.data.payCallBackUrl);
                        $("#yunType").val(result.data.type);
                        $("#privateKeyYun").val(result.data.privateKey);
                        $("#publicKeyYun").val(result.data.publicKey);
                        let yunStatus = result.data.status;
                        if (yunStatus == 1){
                            $("#isYunPayFalse").attr("checked",false);
                            $("#isYunPayTrue").attr("checked","checked");
                        }
                        if (yunStatus == 2){
                            $("#isYunPayTrue").attr("checked",false);
                            $("#isYunPayFalse").attr("checked","checked");
                        }
                        /*let withdrawStatus = result.data.withdrawStatus;
                        if (withdrawStatus == 1){
                            $("#isYunWithdrawFalse").attr("checked",false);
                            $("#isYunWithdrawTrue").attr("checked","checked");
                        }
                        if (withdrawStatus == 2){
                            $("#isYunWithdrawTrue").attr("checked",false);
                            $("#isYunWithdrawFalse").attr("checked","checked");
                        }*/
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }

    function getJzPayConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getPayConfig'),
                data : {type:7},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#jzId").val(result.data.id);
                        $("#appIdJz").val(result.data.appId);
                        $("#apiKeyJz").val(result.data.apiKey);
                        $("#payCallBackUrlJz").val(result.data.payCallBackUrl);
                        $("#jzType").val(result.data.type);
                        let jzStatus = result.data.status;
                        if (jzStatus == 1){
                            $("#isJzPayFalse").attr("checked",false);
                            $("#isJzPayTrue").attr("checked","checked");
                        }
                        if (jzStatus == 2){
                            $("#isJzPayTrue").attr("checked",false);
                            $("#isJzPayFalse").attr("checked","checked");
                        }
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }
    function getHmPayConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getPayConfig'),
                data : {type:8},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#hmId").val(result.data.id);
                        $("#appIdHm").val(result.data.appId);
                        $("#apiKeyHm").val(result.data.apiKey);
                        $("#payCallBackUrlHm").val(result.data.payCallBackUrl);
                        $("#hmType").val(result.data.type);
                        let hmStatus = result.data.status;
                        if (hmStatus == 1){
                            $("#isHmPayFalse").attr("checked",false);
                            $("#isHmPayTrue").attr("checked","checked");
                        }
                        if (hmStatus == 2){
                            $("#isHmPayTrue").attr("checked",false);
                            $("#isHmPayFalse").attr("checked","checked");
                        }
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }

    function getWeiPayConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getPayConfig'),
                data : {type:9},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#weiId").val(result.data.id);
                        $("#appIdWei").val(result.data.appId);
                        $("#payCallBackUrlWei").val(result.data.payCallBackUrl);
                        $("#weiType").val(result.data.type);
                        let weiStatus = result.data.status;
                        if (weiStatus == 1){
                            $("#isWeiPayFalse").attr("checked",false);
                            $("#isWeiPayTrue").attr("checked","checked");
                        }
                        if (weiStatus == 2){
                            $("#isWeiPayTrue").attr("checked",false);
                            $("#isWeiPayFalse").attr("checked","checked");
                        }
                        let withdrawStatus = result.data.withdrawStatus;
                        if (withdrawStatus == 1){
                            $("#isWeiWithdrawFalse").attr("checked",false);
                            $("#isWeiWithdrawTrue").attr("checked","checked");
                        }
                        if (withdrawStatus == 2){
                            $("#isWeiWithdrawTrue").attr("checked",false);
                            $("#isWeiWithdrawFalse").attr("checked","checked");
                        }
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }