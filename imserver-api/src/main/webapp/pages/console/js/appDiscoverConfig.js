var page=0;
var sum=0;
var lock=0;
var updateId="";
var currentPageIndex;// 当前页码数
var currentCount;// 当前总数
layui.use(['form','layer','laydate','table','laytpl'],function(){
	var form = layui.form,
		layer = parent.layer === undefined ? layui.layer : top.layer,
		$ = layui.jquery,
		laydate = layui.laydate,
		laytpl = layui.laytpl,
		table = layui.table;

	//发现页配置列表
	var tableIns = table.render({
		elem: '#discover_table'
		,url:request("/console/appDiscoverList")+ "&user=0"
		,id: 'discover_table'
		,page: true
		,curr: 0
		,limit:Common.limit
		,limits:Common.limits
		,groups: 7
		,cols: [[ //表头
			{field: 'discoverImg', title: '图标',width:150, templet: '#imgDisplay', unresize: true}
			,{field: 'discoverName', title: '名称',sort: true,width:150}
			,{field: 'discoverLinkURL', title: '链接地址',sort: true,width:300}
			,{field: 'discoverNum', title: '顺序',sort: true, width:100}
			,{field: 'discoverUpdateTime', title: '更新时间',sort:'true', width:170,templet: function(d){
					return UI.getLocalTime(d.discoverUpdateTime);
				}}
			,{fixed: 'right', width: 250,title:"操作", align:'left', toolbar: '#discoverListBar'}
		]]
		,done:function(res, curr, count){
            if(res.resultCode == -1 && res.resultMsg =="用户未登录或登录失效"){
                layer.msg("用户未登录或登录失效,请重新登录", {"icon": 2},function(){
                    window.location.href = "/pages/console/login.html";
                });
                return;
            }
            if (count == 0 && lock == 1) {
                layer.msg("暂无数据", {"icon": 2});
                renderTable();
            }
			// renderTable();
			var pageIndex = tableIns.config.page.curr;//获取当前页码
			var resCount = res.count;// 获取table总条数
			currentCount = resCount;
			currentPageIndex = pageIndex;
		}
	});
	//列表操作
	table.on('tool(discover_table)', function(obj){
		var layEvent = obj.event,
			data = obj.data;
		console.log(data);
		if(layEvent === 'delete'){// 删除发现页配置
			layer.confirm('确定删除？',{icon:3, title:'提示信息'},function(index) {
				layer.close(index);
				Discover.deleteDiscover(data.discoverId);
			})
		}else if(layEvent === 'update'){// 修改发现页配置
			Discover.updateDiscover(data);
		}else if(layEvent === 'statusShow'){// 更新发现页配置显示
			Discover.statusDiscover(data.discoverId,1);
		}else if(layEvent === 'statusHidden'){// 更新发现页配置隐藏
			Discover.statusDiscover(data.discoverId,0);
		}
	});

	// 搜索
	$(".search_discoverList").on("click",function(){
	        table.reload("discover_table",{
	            where: {
					keyWord : $(".keyword").val()  //搜索的关键字
	            },
	            page: {
	                curr: 1 //重新从第 1 页开始
	            }
	        })
	    $(".keyword").val("");
	});
});

var Discover={
	numChange:function(){
		var num = $("#discoverNumUpdate").val();
		if(num==""){
			num = $("#discoverNum").val();
			if(num!=""&&num<8){
				layui.layer.alert("请输入比7大的数");
				return;
			}else if(num==""){
				layui.layer.alert("顺序不能为空");
				return;
			}
		}else if(num<8){
			layui.layer.alert("请输入比7大的数");
			return;
		}
	},
	addDiscover:function(){
		$("#Discover_div").hide();
		$("#addDiscover").show();
	},
	commit_addDiscover:function(){
		if($("#img")[0].src.trim()==""){
			layui.layer.alert("图标不能为空");
			return;
		}
		if($("#discoverName").val().trim()==""){
			layui.layer.alert("名称不能为空");
			return;
		}
		if($("#discoverName").val().trim().length>8){
			layui.layer.alert("名称不能超过8个字");
			return;
		}
		if($("#discoverLinkURL").val().trim()==""){
			layui.layer.alert("链接地址不能为空");
			return;
		}
		if($("#discoverNum").val().trim()==""){
			layui.layer.alert("顺序不能为空");
			return;
		}
		var img = $("#discoverImg")[0].files[0];
		var formData = new FormData();
		formData.append("file",img);
		$.ajax({
			url : '/console/pushImg',
			method : 'POST',
			processData: false,
			contentType:false,
			data : formData,
			success : function(result) {
				if (result.resultCode == 1) {
					myFn.invoke({
						url:request('/console/addAppDiscover'),
						data:{
							discoverImg:result.resultMsg,
							discoverName:$("#discoverName").val(),
							discoverNum:$("#discoverNum").val(),
							discoverLinkURL:$("#discoverLinkURL").val(),
							discoverType:1
						},
						success:function(result){
							if(result.resultCode==1){
								$("#discoverImg").val("");
								$("#img")[0].src="";
								$("#img").addClass("hide");
								$("#img").val("");
								$("#discoverImg").val("");
								$("#discoverName").val("");
								$("#discoverLinkURL").val("");
								$("#discoverNum").val("");
								$("#Discover_div").show();
								$("#addDiscover").hide();
								layui.layer.alert("新增成功");
								layui.table.reload("discover_table");
							}
						}
					})
				}
			}
		})
	},
	// 删除配置
	deleteDiscover:function(id){
		myFn.invoke({
			url:request('/console/deleteDiscoverConfig'),
			data:{
				discoverId:id
			},
			success:function(result){
				if(result.resultCode==1){
					layui.layer.alert("删除成功");
					layui.table.reload("discover_table");
				}
			}
		})
	},
	// 修改配置
	updateDiscover:function(data){
		$("#discoverList").hide();
		$("#updateDiscover").show();
		$("#imgUpdate").val(data.discoverImg);
		$("#imgUpdate").attr("src", data.discoverImg);
		$("#discoverNameUpdate").val(data.discoverName);
		$("#discoverLinkURLUpdate").val(data.discoverLinkURL);
		$("#discoverNumUpdate").val(data.discoverNum);
		// $("#discoverImgUpdate").val(data.discoverImgForm);
		$("#imgUpdate").removeClass("hide");
		updateId=data.discoverId;
	},

	// 更新配置状态
	statusDiscover:function(discoverId,status){
		myFn.invoke({
			url:request('/console/updateDiscoverStatus'),
			data:{
				discoverId:discoverId,
				status:status
			},
			success:function(result){
				if(result.resultCode==1){
					layui.layer.alert("更新成功");
					layui.table.reload("discover_table");
					// Common.tableReload(currentCount,currentPageIndex,checkLength,"user_list");
				}else{
					layui.layer.alert("更新失败，用户自定义显示最多10个");
				}
			}
		})
	},

	// 提交修改发现页配置
	commit_updateDiscover:function(){
		if($("#imgUpdate").val().trim()==""){
			layui.layer.alert("图标不能为空");
			return;
		}
		if($("#discoverNameUpdate").val().trim()==""){
			layui.layer.alert("名称不能为空");
			return;
		}
		if($("#discoverLinkURLUpdate").val().trim()==""){
			layui.layer.alert("链接地址不能为空");
			return;
		}
		if($("#discoverNumUpdate").val().trim()==""){
			layui.layer.alert("顺序不能为空");
			return;
		}
		if($("#discoverImgUpdate")[0].files.length==0){
			myFn.invoke({
				url:request('/console/updateDiscoverConfig'),
				data:{
					discoverImg:$("#imgUpdate").val(),
					discoverName:$("#discoverNameUpdate").val(),
					discoverNum:$("#discoverNumUpdate").val(),
					discoverLinkURL:$("#discoverLinkURLUpdate").val(),
					discoverId:updateId
				},
				success:function(result){
					if(result.resultCode==1){
						layui.layer.alert("修改成功");
						$("#discoverList").show();
						$("#updateDiscover").hide();
						layui.table.reload("discover_table");
					}
				}
			})

		}else{
			var imgUpdate = $("#discoverImgUpdate")[0].files[0];
			var formData = new FormData();
			formData.append("file",imgUpdate);
			$.ajax({
				url : '/console/pushImg',
				method : 'POST',
				processData: false,
				contentType:false,
				data : formData,
				success : function(result) {
					if (result.resultCode == 1) {
						$("#discoverImg").val("");
						$("#img")[0].src="";
						$("#img").addClass("hide");
						myFn.invoke({
							url:request('/console/updateDiscoverConfig'),
							data:{
								discoverImg:result.resultMsg,
								discoverId:updateId,
								discoverLinkURL:$("#discoverLinkURLUpdate").val(),
								discoverName:$("#discoverNameUpdate").val(),
								discoverNum:$("#discoverNumUpdate").val(),
							},
							success:function(result){
								if(result.resultCode==1){
									layui.layer.alert("修改成功");
									$("#discoverList").show();
									$("#updateDiscover").hide();
									layui.table.reload("discover_table");
								}
							}
						})
					}
				}
			})
		}
	},
	btn_back:function(){
		$("#discoverList").show();
		$("#discover_btn_div").show();
		$("#Discover_div").show();
		$("#addDiscover").hide();
		$("#updateDiscover").hide();
	},
	pushImg:function(){
		var file = $("#discoverImg")[0].files[0];
		var size = $("#discoverImg")[0].files[0].size;
		var nameImg =  $("#discoverImg").val();
		var sfIndexImg=nameImg.lastIndexOf(".");//后缀位置的.的位置
		var extImg=nameImg.substring(sfIndexImg,nameImg.length).toUpperCase();//截取后缀
		if(extImg !='.PNG' ){
			layui.layer.alert("文件类型错误,请上传正确图片类型");
			return false;
		}
		if(size/1024/1024>1){
			layui.layer.alert("图片大小不能大于1M");
			return false;
		}
		var objUrl = Discover.getObjectURL(file);
		console.log("size = "+size) ;
		console.log("objUrl = "+objUrl) ;
		if (objUrl)
		{
			$("#img").attr("src", objUrl);
			$("#img").attr("value", file);
			console.log("objUrl = "+objUrl) ;
			$("#img").removeClass("hide");
		}
	},
	pushImgUpdate:function(){
		var file = $("#discoverImgUpdate")[0].files[0];
		var size = $("#discoverImgUpdate")[0].files[0].size;
		var nameImg =  $("#discoverImgUpdate").val();
		var sfIndexImg=nameImg.lastIndexOf(".");//后缀位置的.的位置
		var extImg=nameImg.substring(sfIndexImg,nameImg.length).toUpperCase();//截取后缀
		if(extImg !='.PNG' ){
			layui.layer.alert("文件类型错误,请上传正确图片类型");
			return false;
		}
		if(size/1024/1024>1){
			layui.layer.alert("图片大小不能大于1M");
			return false;
		}
		var objUrl = Discover.getObjectURL(file);
		console.log("size = "+size) ;
		console.log("objUrl = "+objUrl) ;
		if (objUrl)
		{
			$("#imgUpdate").attr("src", objUrl);
			$("#imgUpdate").attr("value", file);
			console.log("objUrl = "+objUrl) ;
			$("#imgUpdate").removeClass("hide");
		}
	},
	getObjectURL:function(file){
		var url = null ;
		if (window.createObjectURL!=undefined)
		{ // basic
			url = window.createObjectURL(file) ;
		}
		else if (window.URL!=undefined)
		{
			// mozilla(firefox)
			url = window.URL.createObjectURL(file) ;
		}
		else if (window.webkitURL!=undefined) {
			// webkit or chrome
			url = window.webkitURL.createObjectURL(file) ;
		}
		return url ;

	},
}
//重新渲染表单
function renderTable(){
	layui.use('table', function(){
		var table = layui.table;//高版本建议把括号去掉，有的低版本，需要加()
		// table.reload("user_list");
		table.reload("discover_table",{
			page: {
				curr: 1 //重新从第 1 页开始
			},
			where: {
				keyWord : $(".keyword").val()  //搜索的关键字
			}
		})
	});
}