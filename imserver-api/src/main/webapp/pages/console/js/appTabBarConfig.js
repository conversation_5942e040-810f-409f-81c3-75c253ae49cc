var page=0;
var sum=0;
var lock=0;
var updateId="";
var currentPageIndex;// 当前页码数
var currentCount;// 当前总数
layui.use(['form','layer','laydate','table','laytpl'],function(){
	var form = layui.form,
		layer = parent.layer === undefined ? layui.layer : top.layer,
		$ = layui.jquery,
		laydate = layui.laydate,
		laytpl = layui.laytpl,
		table = layui.table;

	//发现页配置列表
	var tableIns = table.render({
		elem: '#tabBar_table'
		,url:request("/console/appTabBarList")+ "&user=0"
		,id: 'tabBar_table'
		,page: true
		,curr: 0
		,limit:Common.limit
		,limits:Common.limits
		,groups: 7
		,cols: [[ //表头
			{field: 'tabBarImg', title: '点前图标',width:150, templet: '#imgDisplay', unresize: true},
			{field: 'tabBarImg1', title: '点后图标',width:150, templet: '#imgDisplay1', unresize: true}
			,{field: 'tabBarName', title: '名称',sort: true,width:150}
			,{field: 'tabBarLinkUrl', title: '链接地址',sort: true,width:300}
			,{field: 'tabBarNum', title: '顺序',sort: true, width:100}
			,{field: 'tabBarUpdateTime', title: '更新时间',sort:'true', width:170,templet: function(d){
					return UI.getLocalTime(d.tabBarUpdateTime);
				}}
			,{fixed: 'right', width: 250,title:"操作", align:'left', toolbar: '#tabBarListBar'}
		]]
		,done:function(res, curr, count){
            if(res.resultCode == -1 && res.resultMsg =="用户未登录或登录失效"){
                layer.msg("用户未登录或登录失效,请重新登录", {"icon": 2},function(){
                    window.location.href = "/pages/console/login.html";
                });
                return;
            }
            if (count == 0 && lock == 1) {
                layer.msg("暂无数据", {"icon": 2});
                renderTable();
            }
			var pageIndex = tableIns.config.page.curr;//获取当前页码
			var resCount = res.count;// 获取table总条数
			currentCount = resCount;
			currentPageIndex = pageIndex;
		}
	});
	//列表操作
	table.on('tool(tabBar_table)', function(obj){
		var layEvent = obj.event,
			data = obj.data;
		console.log(data);
		if(layEvent === 'delete'){// 删除发现页配置
			layer.confirm('确定删除？',{icon:3, title:'提示信息'},function(index) {
				layer.close(index);
				TabBar.deleteTabBar(data.tabBarId);
			})
		}else if(layEvent === 'update'){// 修改发现页配置
			TabBar.updateTabBar(data);
		}else if(layEvent === 'statusShow'){// 更新发现页配置显示
			TabBar.statusTabBar(data.tabBarId,1);
		}else if(layEvent === 'statusHidden'){// 更新发现页配置隐藏
			TabBar.statusTabBar(data.tabBarId,0);
		}
	});

	// 搜索
	$(".search_tabBarList").on("click",function(){
	        table.reload("tabBar_table",{
	            where: {
					keyWord : $(".keyword").val()  //搜索的关键字
	            },
	            page: {
	                curr: 1 //重新从第 1 页开始
	            }
	        })
	    $(".keyword").val("");
	});
});

var TabBar={
	addTabBar:function(){
		$("#tabBar_div").hide();
		$("#addTabBar").show();
	},
	commit_addTabBar:function(){
		if($("#img").attr("src").trim()==""){
			layui.layer.alert("图标不能为空");
			return;
		}
		if($("#tabBarName").val().trim()==""){
			layui.layer.alert("名称不能为空");
			return;
		}
		if($("#tabBarName").val().trim().length>8){
			layui.layer.alert("名称不能超过8个字");
			return;
		}
		if($("#tabBarLinkURL").val().trim()==""){
			layui.layer.alert("链接地址不能为空");
			return;
		}
		if($("#tabBarNum").val().trim()==""){
			layui.layer.alert("顺序不能为空");
			return;
		}
		$("#tabBarLinkURL").val(encodeURIComponent($("#tabBarLinkURL").val()));
		if($("#tabBarImg1")[0].files[0]==0){
			var img = $("#tabBarImg")[0].files[0];
			var formData = new FormData();
			formData.append("file",img);
			var result = TabBar.uploadImg(formData,callbackTabbar,0);
		}else{
			var img = $("#tabBarImg")[0].files[0];
			var formData = new FormData();
			formData.append("file",img);
			var result = TabBar.uploadImg(formData,callbackTabbar,1);
		}
	},
	// 删除配置
	deleteTabBar:function(id){
		myFn.invoke({
			url:request('/console/deleteTabBarConfig'),
			data:{
				tabBarId:id
			},
			success:function(result){
				if(result.resultCode==1){
					layui.layer.alert("删除成功");
					layui.table.reload("tabBar_table");
				}
			}
		})
	},
	// 修改配置
	updateTabBar:function(data){
		$("#tabBarList").hide();
		$("#updateTabBar").show();
		$("#imgUpdate").val(data.tabBarImg);
		$("#imgUpdate1").val(data.tabBarImg1);
		$("#imgUpdate").attr("src", data.tabBarImg);
		$("#imgUpdate1").attr("src", data.tabBarImg1);
		$("#tabBarNameUpdate").val(data.tabBarName);
		$("#tabBarLinkURLUpdate").val(data.tabBarLinkUrl);
		$("#tabBarNumUpdate").val(data.tabBarNum);
		// $("#tabBarImgUpdate").val(data.discoverImgForm);
		$("#imgUpdate").removeClass("hide");
		$("#imgUpdate1").removeClass("hide1");
		updateId=data.tabBarId;
	},

	// 更新配置状态
	statusTabBar:function(tabBarId,status){
		myFn.invoke({
			url:request('/console/updateTabBarStatus'),
			data:{
				tabBarId:tabBarId,
				status:status
			},
			success:function(result){
				if(result.resultCode==1){
					layui.layer.alert("更新成功");
					layui.table.reload("tabBar_table");
					// Common.tableReload(currentCount,currentPageIndex,checkLength,"user_list");
				}
			}
		})
	},
	uploadImg:function(formData,callbackTabbar,step){
		$.ajax({
			url : '/console/pushImg',
			method : 'POST',
			async: false,
			processData: false,
			contentType:false,
			data : formData,
			success : function(result) {
				callbackTabbar(result,step)
			}
		})
	},
	// 提交修改发现页配置
	commit_updateTabBar:function(){
		if($("#imgUpdate").attr("src").trim()==""){
			layui.layer.alert("图标不能为空");
			return;
		}
		if($("#tabBarNameUpdate").val().trim()==""){
			layui.layer.alert("名称不能为空");
			return;
		}
		if($("#tabBarLinkURLUpdate").val().trim()==""){
			layui.layer.alert("链接地址不能为空");
			return;
		}
		$("#tabBarLinkURLUpdate").val(encodeURIComponent($("#tabBarLinkURLUpdate").val()));
		if($("#tabBarNumUpdate").val().trim()==""){
			layui.layer.alert("顺序不能为空");
			return;
		}
		if($("#tabBarImgUpdate")[0].files.length==0&&$("#tabBarImgUpdate1")[0].files.length==0){
			myFn.invoke({
				url:request('/console/updateTabBarConfig'),
				data:{
					tabBarImg:$("#imgUpdate").val(),
					tabBarImg1:$("#imgUpdate1").val(),
					tabBarName:$("#tabBarNameUpdate").val(),
					tabBarNum:$("#tabBarNumUpdate").val(),
					tabBarLinkUrl:$("#tabBarLinkURLUpdate").val(),
					tabBarId:updateId
				},
				success:function(result){
					if(result.resultCode==1){
						layui.layer.alert("修改成功");
						$("#tabBarList").show();
						$("#updateTabBar").hide();
						layui.table.reload("tabBar_table");
						$("#tabBarImgUpdate").val("");
						$("#imgUpdate")[0].src="";
						$("#imgUpdate").addClass("hide");
						$("#tabBarImgUpdate1").val("");
						$("#imgUpdate1")[0].src="";
						$("#imgUpdate1").addClass("hide1");

					}
				}
			})

		}else if($("#tabBarImgUpdate")[0].files.length!=0&&$("#tabBarImgUpdate1")[0].files.length==0) {
			var imgUpdate = $("#tabBarImgUpdate")[0].files[0];
			var formData = new FormData();
			formData.append("file",imgUpdate);
			TabBar.uploadImg(formData,callbackTabbar,2);
		}else if($("#tabBarImgUpdate")[0].files.length==0&&$("#tabBarImgUpdate1")[0].files.length!=0){
			var imgUpdate1 = $("#tabBarImgUpdate1")[0].files[0];
			var formData1 = new FormData();
			formData1.append("file",imgUpdate1);
			// var result1 = TabBar.uploadImg(formData1);
			TabBar.uploadImg(formData1,callbackTabbar,3);
		}else{
			var imgUpdate = $("#tabBarImgUpdate")[0].files[0];
			var formData = new FormData();
			formData.append("file",imgUpdate);
			// var result = TabBar.uploadImg(formData);
			TabBar.uploadImg(formData,callbackTabbar,4);


		}
	},
	btn_back:function(){
		$("#tabBarList").show();
		$("#tabBar_btn_div").show();
		$("#tabBar_div").show();
		$("#addTabBar").hide();
		$("#updateTabBar").hide();
	},
	pushImg:function(){
		var file = $("#tabBarImg")[0].files[0];
		var size = $("#tabBarImg")[0].files[0].size;
		var nameImg =  $("#tabBarImg").val();
		var sfIndexImg=nameImg.lastIndexOf(".");//后缀位置的.的位置
		var extImg=nameImg.substring(sfIndexImg,nameImg.length).toUpperCase();//截取后缀
		if(extImg !='.PNG' ){
			layui.layer.alert("文件类型错误,请上传正确图片类型");
			return false;
		}
		if(size/1024/1024>1){
			layui.layer.alert("图片大小不能大于1M");
			return false;
		}
		var objUrl = TabBar.getObjectURL(file);
		console.log("size = "+size) ;
		console.log("objUrl = "+objUrl) ;
		if (objUrl)
		{
			$("#img").attr("src", objUrl);
			$("#img").attr("value", file);
			console.log("objUrl = "+objUrl) ;
			$("#img").removeClass("hide");
		}
	},
	pushImg1:function(){
		var file = $("#tabBarImg1")[0].files[0];
		var size = $("#tabBarImg1")[0].files[0].size;
		var nameImg =  $("#tabBarImg1").val();
		var sfIndexImg=nameImg.lastIndexOf(".");//后缀位置的.的位置
		var extImg=nameImg.substring(sfIndexImg,nameImg.length).toUpperCase();//截取后缀
		if(extImg !='.PNG' ){
			layui.layer.alert("文件类型错误,请上传正确图片类型");
			return false;
		}
		if(size/1024/1024>1){
			layui.layer.alert("图片大小不能大于1M");
			return false;
		}
		var objUrl = TabBar.getObjectURL(file);
		console.log("size = "+size) ;
		console.log("objUrl = "+objUrl) ;
		if (objUrl)
		{
			$("#img1").attr("src", objUrl);
			$("#img1").attr("value", file);
			console.log("objUrl = "+objUrl) ;
			$("#img1").removeClass("hide1");
		}
	},
	pushImgUpdate:function(){
		var file = $("#tabBarImgUpdate")[0].files[0];
		var size = $("#tabBarImgUpdate")[0].files[0].size;
		var nameImg =  $("#tabBarImgUpdate").val();
		var sfIndexImg=nameImg.lastIndexOf(".");//后缀位置的.的位置
		var extImg=nameImg.substring(sfIndexImg,nameImg.length).toUpperCase();//截取后缀
		if(extImg !='.PNG' ){
			layui.layer.alert("文件类型错误,请上传正确图片类型");
			return false;
		}
		if(size/1024/1024>1){
			layui.layer.alert("图片大小不能大于1M");
			return false;
		}
		var objUrl = TabBar.getObjectURL(file);
		console.log("size = "+size) ;
		console.log("objUrl = "+objUrl) ;
		if (objUrl)
		{
			$("#imgUpdate").attr("src", objUrl);
			$("#imgUpdate").attr("value", file);
			console.log("objUrl = "+objUrl) ;
			$("#imgUpdate").removeClass("hide");
		}
	},
	pushImgUpdate1:function(){
		var file = $("#tabBarImgUpdate1")[0].files[0];
		var size = $("#tabBarImgUpdate1")[0].files[0].size;
		var nameImg =  $("#tabBarImgUpdate1").val();
		var sfIndexImg=nameImg.lastIndexOf(".");//后缀位置的.的位置
		var extImg=nameImg.substring(sfIndexImg,nameImg.length).toUpperCase();//截取后缀
		if(extImg !='.PNG' ){
			layui.layer.alert("文件类型错误,请上传正确图片类型");
			return false;
		}
		if(size/1024/1024>1){
			layui.layer.alert("图片大小不能大于1M");
			return false;
		}
		var objUrl = TabBar.getObjectURL(file);
		console.log("size = "+size) ;
		console.log("objUrl = "+objUrl) ;
		if (objUrl)
		{
			$("#imgUpdate1").attr("src", objUrl);
			$("#imgUpdate1").attr("value", file);
			console.log("objUrl = "+objUrl) ;
			$("#imgUpdate1").removeClass("hide1");
		}
	},
	getObjectURL:function(file){
		var url = null ;
		if (window.createObjectURL!=undefined)
		{ // basic
			url = window.createObjectURL(file) ;
		}
		else if (window.URL!=undefined)
		{
			// mozilla(firefox)
			url = window.URL.createObjectURL(file) ;
		}
		else if (window.webkitURL!=undefined) {
			// webkit or chrome
			url = window.webkitURL.createObjectURL(file) ;
		}
		return url ;

	},
}
function callbackTabbar(result,step){
	if(step==0&&result.resultCode==1){
		myFn.invoke({
			url:request('/console/addAppTabBar'),
			data:{
				tabBarImg:result.resultMsg,
				tabBarName:$("#tabBarName").val(),
				tabBarNum:$("#tabBarNum").val(),
				tabBarLinkUrl:$("#tabBarLinkURL").val()
			},
			success:function(result){
				if(result.resultCode==1){
					$("#tabBarImg").val("");
					$("#img")[0].src="";
					$("#img").addClass("hide");
					$("#img").val("");
					$("#tabBarImg").val("");
					$("#tabBarImg1").val("");
					$("#img1")[0].src="";
					$("#img1").addClass("hide1");
					$("#img1").val("");
					$("#tabBarImg1").val("");
					$("#tabBarName").val("");
					$("#tabBarLinkURL").val("");
					$("#tabBarNum").val("");
					$("#tabBar_div").show();
					$("#addTabBar").hide();
					layui.layer.alert("新增成功");
					layui.table.reload("tabBar_table");
				}
			}
		})
	}else if(step==1&&result.resultCode == 1){
		var img1 = $("#tabBarImg1")[0].files[0];
		var formData1 = new FormData();
		formData1.append("file",img1);
		$.ajax({
			url : '/console/pushImg',
			method : 'POST',
			async: false,
			processData: false,
			contentType:false,
			data : formData1,
			success : function(result1) {
				if(result1.resultCode == 1){
					myFn.invoke({
						url:request('/console/addAppTabBar'),
						data:{
							tabBarImg:result.resultMsg,
							tabBarImg1:result1.resultMsg,
							tabBarName:$("#tabBarName").val(),
							tabBarNum:$("#tabBarNum").val(),
							tabBarLinkUrl:$("#tabBarLinkURL").val()
						},
						success:function(result){
							if(result.resultCode==1){
								$("#tabBarImg").val("");
								$("#img")[0].src="";
								$("#img").addClass("hide");
								$("#img").val("");
								$("#tabBarImg").val("");
								$("#tabBarImg1").val("");
								$("#img1")[0].src="";
								$("#img1").addClass("hide1");
								$("#img1").val("");
								$("#tabBarImg1").val("");
								$("#tabBarName").val("");
								$("#tabBarLinkURL").val("");
								$("#tabBarNum").val("");
								$("#tabBar_div").show();
								$("#addTabBar").hide();
								layui.layer.alert("新增成功");
								layui.table.reload("tabBar_table");
							}
						}
					})
				}
			}
		})
	}else if(step==2&&result.resultCode == 1){
			myFn.invoke({
				url:request('/console/updateTabBarConfig'),
				data:{
					tabBarImg:result.resultMsg,
					tabBarImg1:$("#imgUpdate1").val(),
					tabBarId:updateId,
					tabBarLinkUrl:$("#tabBarLinkURLUpdate").val(),
					tabBarName:$("#tabBarNameUpdate").val(),
					tabBarNum:$("#tabBarNumUpdate").val()
				},
				success:function(result){
					if(result.resultCode==1){
						layui.layer.alert("修改成功");
						$("#tabBarList").show();
						$("#updateTabBar").hide();
						layui.table.reload("tabBar_table");
                        $("#tabBarImgUpdate").val("");
						$("#imgUpdate")[0].src="";
						$("#imgUpdate").addClass("hide");
						$("#tabBarImgUpdate1").val("");
						$("#imgUpdate1")[0].src="";
						$("#imgUpdate1").addClass("hide1");
					}
				}
			})
	}else if(step==3&&result.resultCode == 1){
			myFn.invoke({
				url:request('/console/updateTabBarConfig'),
				data:{
					tabBarImg:$("#imgUpdate").val(),
					tabBarImg1:result.resultMsg,
					tabBarId:updateId,
					tabBarLinkUrl:$("#tabBarLinkURLUpdate").val(),
					tabBarName:$("#tabBarNameUpdate").val(),
					tabBarNum:$("#tabBarNumUpdate").val()
				},
				success:function(result){
					if(result.resultCode==1){
						layui.layer.alert("修改成功");
						$("#tabBarList").show();
						$("#updateTabBar").hide();
						layui.table.reload("tabBar_table");
                        $("#tabBarImgUpdate").val("");
						$("#imgUpdate")[0].src="";
						$("#imgUpdate").addClass("hide");
						$("#tabBarImgUpdate1").val("");
						$("#imgUpdate1")[0].src="";
						$("#imgUpdate1").addClass("hide1");
					}
				}
			})
	}else if(step==4&&result.resultCode == 1){
			var imgUpdate1 = $("#tabBarImgUpdate1")[0].files[0];
			var formData1 = new FormData();
			formData1.append("file",imgUpdate1);
			$.ajax({
				url: '/console/pushImg',
				method: 'POST',
				async: false,
				processData: false,
				contentType: false,
				data: formData1,
				success: function (result1) {
					if (result1.resultCode == 1) {
							myFn.invoke({
								url:request('/console/updateTabBarConfig'),
								data:{
									tabBarImg:result.resultMsg,
									tabBarImg1:result1.resultMsg,
									tabBarId:updateId,
									tabBarLinkUrl:$("#tabBarLinkURLUpdate").val(),
									tabBarName:$("#tabBarNameUpdate").val(),
									tabBarNum:$("#tabBarNumUpdate").val()
								},
								success:function(result){
									if(result.resultCode==1){
										layui.layer.alert("修改成功");
										$("#tabBarList").show();
										$("#updateTabBar").hide();
										layui.table.reload("tabBar_table");
                                        $("#tabBarImgUpdate").val("");
                                        $("#imgUpdate")[0].src="";
                                        $("#imgUpdate").addClass("hide");
                                        $("#tabBarImgUpdate1").val("");
                                        $("#imgUpdate1")[0].src="";
                                        $("#imgUpdate1").addClass("hide1");
									}
								}
							})
					}
				}
			})
	}
}
