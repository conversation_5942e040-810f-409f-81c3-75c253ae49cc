var page=0;
var lock=0;
var messageIds = new Array();
var userIds = new Array();
var roomId;
var roomName;
var currentPageIndex;// 群聊天记录中的当前页码数
var currentCount;// 群聊天记录中的当前总数
layui.use(['form','layer','laydate','table','laytpl'],function(){
    var form = layui.form,
        layer = parent.layer === undefined ? layui.layer : top.layer,
        $ = layui.jquery,
        laydate = layui.laydate,
        laytpl = layui.laytpl,
        table = layui.table;

	//群组列表
    var tableInRoom = table.render({
      elem: '#room_table'
      ,url:request("/console/vipConfigList")
      ,id: 'room_table'
      ,page: true
      ,curr: 0
      ,limit:Common.limit
	  ,limits:Common.limits
      ,groups: 7
      ,cols: [[ //表头
          // {field: 'id', title: '配置id',width:250}
           {field: 'level', title: 'VIP等级',width:250}
            ,{field:'levelName', title: 'VIP等级名称',width:250}
          ,{field: 'number', title: '有效期',width:250}
          ,{field: 'price', title: '价格',width:250}
          ,{fixed: 'right', width: 300,title:"操作", align:'left', toolbar: '#roomListBar'}
        ]]
		,done:function(res, curr, count){
            if(res.resultCode == -1 && res.resultMsg =="用户未登录或登录失效") {
                layer.msg("用户未登录或登录失效,请重新登录", {"icon": 2}, function () {
                    window.location.href = "/pages/console/login.html";
                });
                return;

                if (count == 0 && lock == 1) {
                    layer.msg("暂无数据", {"icon": 2});
                    renderTable();
                }
                lock = 0;
                if (localStorage.getItem("role") == 1 || localStorage.getItem("role") == 4) {
                    $(".btn_addRoom").hide();
                    $(".member").hide();
                    $(".randUser").hide();
                    $(".modifyConf").hide();
                    $(".msgCount").hide();
                    $(".sendMsg").hide();
                    $(".del").hide();
                    $(".deleteMonthLogs").hide();
                    $(".deleteThousandAgoLogs").hide();
                    $(".locking").hide();
                    $(".cancelLocking").hide();
                }
                if (localStorage.getItem("role") == 1) {
                    $(".chatRecord").hide();
                }
                var pageIndex = tableInRoom.config.page.curr;//获取当前页码
                var resCount = res.count;// 获取table总条数
                currentCount = resCount;
                currentPageIndex = pageIndex;
            }
		}
    });


	$("#room_table_div").show();
	$("#updateRoom").hide();
	$("#addRoom").hide();

	table.on('rowDouble(room_table)', function(obj){
	  console.log(obj);
	});

    //列表操作
    table.on('tool(room_table)', function(obj){
        var layEvent = obj.event,
        data = obj.data;
        if(layEvent === 'modifyConf'){ //修改配置
        	RoomConfig.updateRoom(data.id,data.number,data.level,data.price,data.upgradePrice);
        }else if(layEvent === 'del'){ //删除
            RoomConfig.deleteRoom(data.id);
        }
    });

})

//重新渲染表单
function renderTable(){
  layui.use('table', function(){
   var table = layui.table;//高版本建议把括号去掉，有的低版本，需要加()
   // table.reload("user_list");
    table.reload("room_table",{
        page: {
            curr: 1 //重新从第 1 页开始
        },
        where: {
            keyWorld : $(".group_name").val(),  //搜索的关键字
            leastNumbers : $(".leastNumbers").val()  //搜索的关键字
        }
    })
  });
 }
	
var button='<button onclick="Room.button_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top: 35px;margin-left: 50px;"><<返回</button>';


var RoomConfig={
    // 新增群组
    addRoom:function(){
        $("#roomList").hide();
        $("#addRoom").show();
    },
    // 提交新增群组
    commit_addRoom:function(){
        if($("#add_number").val()==""){
            layer.alert("请输入人数");
            return ;
        }else if($("#add_price").val()==""){
            layer.alert("请输入价格");
            return;
        }else if($("#add_level").val()==""){
            layer.alert("请输入等级");
            return;
        }
        myFn.invoke({
            url:request('/console/addVipConfig'),
            data:{
                level:$("#add_level").val(),
                number:$("#add_number").val(),
                price:$("#add_price").val(),
            },
            success:function(result){
                if(result.resultCode==1){
                    layer.msg("新增成功",{"icon":1});
                    $("#roomList").show();
                    $("#addRoom").hide();
                    $("#updateRoom").hide();
                    $("#add_level").val("");
                    $("#add_number").val("");
                    $("#add_price").val("");
                    $("#add_upgradePrice").val("");
                    // UI.roomList(0);
                    layui.table.reload("room_table",{
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: {

                        }
                    })
                }
            }
        });
    },
    // 提交新增群组
    commit_update:function(){
        if($("#update_number").val()==""){
            layer.alert("请输入有效期");
            return ;
        }else if($("#update_price").val()==""){
            layer.alert("请输入价格");
            return;
        }else if($("#update_level").val()==""){
            layer.alert("请输入VIP等级");
            return;
        }
        myFn.invoke({
            url:request('/console/updateVipConfig'),
            data:{
                id:$("#update_roomId").val(),
                number:$("#update_number").val(),
                level:$("#update_level").val(),
                price:$("#update_price").val(),
            },
            success:function(result){
                if(result.resultCode==1){
                    layer.msg("修改成功",{"icon":1});
                    $("#roomList").show();
                    $("#addRoom").hide();
                    $("#add_number").val("");
                    $("#add_price").val("");
                    $("#add_level").val("");
                    $("#add_upgradePrice").val("");
                    $("#updateRoom").hide();
                    $("#update_number").val("");
                    $("#update_level").val("");
                    $("#update_price").val("");
                    $("#update_upgradePrice").val("");
                    // UI.roomList(0);
                    layui.table.reload("room_table",{
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: {

                        }
                    })
                }
            }
        });
    },
     // 删除群组
    deleteRoom:function(id){
        layer.confirm('确定删除该VIP收费配置？',{icon:3, title:'提示信息'},function(index){
            $.ajax({
                type:'POST',
                url:request('/console/deleteVipConfig'),
                data:{
                    id:id,
                },
                async:false,
                success : function(result){
                    if(result.resultCode==1){
                        layer.alert("删除成功");
                        // layui.table.reload("room_table");
                        Common.tableReload(currentCount,currentPageIndex,1,"room_table");
                    }
                    if(result.resultCode==0){
                        layer.alert(result.resultMsg);
                    }
                },

            })
        });
    },

	// 刷新table
    reloadTable:function(){
		// 刷新父级页面
        parent.layui.table.reload("room_user")
    },


		// 修改群配置
		updateRoom:function(id,number,level,price,upgradePrice){
            $("#update_roomId").val(id);
            $("#update_number").val(number);
            $("#update_level").val(level);
            $("#update_price").val(price);
            $("#roomList").hide();
            $("#addRoom").hide();
            $("#updateRoom").show();
		},


	// 返回
	button_back:function(){
		$("#room_table_div").show();
		$("#roomList").show();
		$("#addRoom").hide();
		$("#updateRoom").hide();
		$("#back").empty();
		layui.table.reload("room_table");
	},

}