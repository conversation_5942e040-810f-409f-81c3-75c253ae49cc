var page=0;
var sum=0;
layui.use(['form','layer','laydate','table','laytpl'],function(){
    var form = layui.form,
        layer = parent.layer === undefined ? layui.layer : top.layer,
        $ = layui.jquery,
        laydate = layui.laydate,
        laytpl = layui.laytpl,
        table = layui.table;


    // 用户提现列表
    var tableIns = table.render({

        elem: '#raiseCash_table'
        ,url:request("/console/systemRecharge")+"&type="+15
        ,id: 'raiseCash_table'
        ,page: true
        ,curr: 0
        ,limit:Common.limit
        ,limits:Common.limits
        ,groups: 7
        ,cols: [[ //表头
            {field: 'id', title: '提现记录Id',sort: true,width:150}
            ,{field: 'tradeNo', title: '交易单号',sort: true,width:180}
            ,{field: 'userId', title: '用户Id',sort: true, width:120}
            ,{field: 'userName', title: '用户昵称',sort: true, width:120}
            ,{field: 'money', title: '提现总金额',sort: true, width:120}
            ,{field: 'fee', title: '提现手续费',sort: true, width:120}
            ,{field: 'realFee', title: '实际到账金额',sort: true, width:120}
            ,{field: 'desc', title: '备注',sort: true, width:120}
            ,{field: 'payType', title: '支付方式',sort: true, width:120,templet : function (d) {
                var payTypeMsg;
                (d.payType == 1 ? payTypeMsg = "支付宝支付" : (d.payType == 2) ? payTypeMsg = "微信支付" : (d.payType == 3) ? payTypeMsg = "余额支付" : (d.payType == 5) ? payTypeMsg = "通联支付" : payTypeMsg = "其他方式支付")
                return payTypeMsg;
             }}
            ,{field: 'contextType', title: '提现到',sort: true, width:120,templet : function (d) {
                var payTypeMsg;
                (d.contextType == 1 ? payTypeMsg = "支付宝" : (d.contextType == 2) ? payTypeMsg = "微信" : (d.contextType == 5) ? payTypeMsg = "银行卡" : (d.nameOtherWay!=null)? payTypeMsg = d.nameOtherWay : "其他" )
                return payTypeMsg;
             }}
            ,{field: 'contextName', title: '支付宝姓名/开户名',sort: false, width:150}
            ,{field: 'contextNo', title: '支付宝账号/银行卡号',sort: false, width:200}
            ,{field: 'contextBankName', title: '银行名称',sort: false, width:120}
            ,{field: 'contextOtherWay', title: '其他提现通道信息',sort: false, width:200}
            ,{field: 'status', title: '交易状态',sort: true, width:120,templet : function (d) {
                    var statusMsg;
                    (d.status == 2 ? statusMsg = "待审核" : (d.status == 1) ? statusMsg = "已通过" : (d.status == 3) ? statusMsg = "已拒绝" : statusMsg = "关闭")
                    return statusMsg;
                }}
           /* ,{field: 'type', title: '类型',sort: true, width:150, templet : function (d) {
					var statusMsg;
            		(d.type == 15 ? statusMsg = "用户提现审核" : "其他方式提现")
					return statusMsg;
                }}*/
            ,{field: 'time',title:'提现时间',width:195,templet: function(d){
                    return UI.getLocalTime(d.time);
                }}
            ,{fixed: 'right', width: 200,title:"操作", align:'left', toolbar: '#raiseCashBar'}
        ]]
        ,done:function(res, curr, count){
            if(res.resultCode == -1 && res.resultMsg =="用户未登录或登录失效"){
                layer.msg("用户未登录或登录失效,请重新登录", {"icon": 2},function(){
                    window.location.href = "/pages/console/login.html";
                });
                return;
            }
            if (count == 0) {
            // if (count == 0 && lock == 1) {
                layer.msg("暂无数据", {"icon": 2});
                renderTable();
            }

            // 初始化时间控件
            ///layui.form.render('select');
            //日期范围
            layui.laydate.render({
                elem: '#raiseCashMsgDate'
                ,range: "~"
                ,done: function(value, date, endDate){  // choose end
                    //console.log("date callBack====>>>"+value); //得到日期生成的值，如：2017-08-18
                    var startDate = value.split("~")[0];
                    var endDate = value.split("~")[1];
                    // Count.loadGroupMsgCount(roomJId,startDate,endDate,timeUnit);
                    table.reload("raiseCash_table",{
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: {
                            // userId : data.userId,  //搜索的关键字
                            startDate : startDate,
                            endDate : endDate
                        }
                    })
                }
                ,max: 0
            });

            $(".currentCreate_total").empty().text((0==res.totalCreate ? 0:res.totalCreate));
            $(".currentDone_total").empty().text((0==res.totalDone ? 0:res.totalDone));
            $(".current_total").empty().text((0==res.total ? 0:res.total));
            $(".currentPassBank_total").empty().text((0==res.totalPassBank ? 0:res.totalPassBank));
            $(".currentPassAilpay_total").empty().text((0==res.totalPassAilpay ? 0:res.totalPassAilpay));
            if(localStorage.getItem("IS_ADMIN")==0){
                $(".btn_addLive").hide();
                $(".delete").hide();
                $(".chatMsg").hide();
                $(".member").hide();
            }
        }
    });
    // 列表操作
    table.on('tool(raiseCash_table)', function(obj){
        var page = tableIns.config.page.curr;//获取当前页码
        var layEvent = obj.event, data = obj.data;
        console.log(data);
        if(layEvent === 'agreeRaise'){// 同意提现
            raiseCashAdmin.updateRaiseStatus(data.id,1,page);
        }else if(layEvent === 'refuseRaise'){// 拒绝提现
            raiseCashAdmin.updateRaiseStatus(data.id,3,page);
        }
    });

    //首页搜索
    $(".search_live").on("click",function(){
        // 关闭超出宽度的弹窗
        $(".layui-layer-content").remove();
        table.reload("raiseCash_table",{
            where: {
                userId : $("#userId").val(), //搜索的关键字
                // type :$("#complaint_select").val(),
            },
            page: {
                curr: 1 //重新从第 1 页开始
            }
        })
        $("#userId").val("");
        // $("#complaint_select").val(0);
    });


});

var raiseCashAdmin={
    // 删除账单记录
    btn_back:function(){
        $("#redEnvelope").show();
        $("#receiveWater").hide();

    },
    updateRaiseStatus:function(raiseId,status,page){
        if (status == 1){
            updataStatus(raiseId,status,page)
        }
        if (status == 3){
            layer.prompt({title: '请输入拒绝理由', formType: 0, value: ''}, function (remark, index) {
                Common.invoke({
                    path: request('/console/updataRaiseStatus'),
                    data: {
                        remark: remark,
                        raiseId :raiseId,
                        status :status
                    },
                    successMsg: "操作成功",
                    errorMsg: "操作失败，请稍后重试",
                    successCb: function (result) {
                        layer.close(index); //关闭弹框
                        if(result.resultCode==1){
                            layer.msg("审核成功",{"icon":1});
                            messageIds = [];
                            layui.use(['table'],function() {
                                layui.table.reload("raiseCash_table", {
                                    where: {
                                        userId: $("#userId").val(), //搜索的关键字
                                    },
                                    page: {
                                        curr: page //重新从第 1 页开始
                                    }
                                })
                            })
                        }if(result.resultCode!=1){
                            layer.msg("审核失败",{"icon":1});
                            messageIds = [];
                        }
                    },
                    errorCb: function (result) {
                    }
                });
            });

       /*     console.log(page)
            myFn.invoke({
                url:request('/console/updataRaiseStatus'),
                data:{
                    raiseId :raiseId,
                    status :status
                },
                success:function(result){
                    if(result.resultCode==1){
                        layer.msg("审核成功",{"icon":1});
                        messageIds = [];
                        layui.use(['table'],function() {
                            layui.table.reload("raiseCash_table", {
                                where: {
                                    userId: $("#userId").val(), //搜索的关键字
                                },
                                page: {
                                    curr: page //重新从第 1 页开始
                                }
                            })
                        })
                    }if(result.resultCode!=1){
                        layer.msg("审核失败",{"icon":1});
                        messageIds = [];
                    }
                }
            })
        /!*    layer.confirm('确定拒绝用户此次提现申请么?',{icon:3, title:'提示消息',yes:function () {
                updataStatus(raiseId,status,page)*!/
            },btn2:function () {
                messageIds = [];
            },cancel:function () {
                messageIds = [];
            }});*/
        }
    }

}

function updataStatus(raiseId,status,page) {
    console.log(page)
    myFn.invoke({
        url:request('/console/updataRaiseStatus'),
        data:{
            raiseId :raiseId,
            status :status
        },
        success:function(result){
            if(result.resultCode==1){
                layer.msg("审核成功",{"icon":1});
                messageIds = [];
                layui.use(['table'],function() {
                    layui.table.reload("raiseCash_table", {
                        where: {
                            userId: $("#userId").val(), //搜索的关键字
                        },
                        page: {
                            curr: page //重新从第 1 页开始
                        }
                    })
                })
            }if(result.resultCode!=1){
                layer.msg("审核失败",{"icon":1});
                messageIds = [];
            }
        }
    })
}