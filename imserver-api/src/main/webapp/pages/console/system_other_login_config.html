<!DOCTYPE html>
<html>
<head> <link rel="shortcut icon" href="favicon.ico" type="image/x-icon" />
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>第三方登录</title>
    <link rel="stylesheet" href="/pages/common/layui/css/layui.css" />
    <link rel="stylesheet" href="./css/public.css" />
    <style>
        .scrollbar_zdy::-webkit-scrollbar {/*滚动条整体样式*/
            width: 10px;     /*高宽分别对应横竖滚动条的尺寸*/
            height: 1px;
        }
        .scrollbar_zdy::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
            border-radius: 10px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            background: #EEEEEE;
        }
        .scrollbar_zdy::-webkit-scrollbar-track {/*滚动条里面轨道*/
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 10px;
            background: #FFFEFE;
        }
    </style>
</head>
<body class="scrollbar_zdy" style="margin-left: 15px;margin-right: 15px;">
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 24px;">
    <legend>第三方登录配置</legend>
</fieldset>
<div class="layui-tab layui-tab-card">
    <ul class="layui-tab-title">
        <li class="layui-this">第三方登录</li>
    </ul>
    <div class="layui-tab-content" style="height: 100%;background-color: #fefefe;">
        <div class="layui-tab-item layui-show" style="margin-bottom: 10px;">
            <div class="layui-row layui-col-space15">
                <form id="qqLoginForm" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                    <div class="layui-col-md6" style="padding: 3px">
                        <div class="layui-card" style="border: 1px solid #d0cbcb;">
                            <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>Q&nbsp;&nbsp;&nbsp;Q</strong></span></div>
                            <div class="layui-card-body" style="height:195px">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width: 207px;">是否启用</label>
                                    <div class="layui-input-block" style="margin-left: 240px;" id="isQQLogin">
                                        <input type="radio" name="status" id="isQQLoginTrue" value="1" title="是" checked="checked">
                                        <input type="radio" name="status" id="isQQLoginFalse" value="2" title="否" >
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline" style="width: 378px;">
                                        <label class="layui-form-label" style="width: 136px;">应用APP ID</label>
                                        <div class="layui-input-inline" style="width: 233px;">
                                            <input type="text" name="appId" id="appIdQQ" value="" placeholder="请输入QQ APP ID" class="layui-input" >
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item" style="padding-top: 20px;">
                                    <input type="hidden" name="id" id="qqId" value="">
                                    <input type="hidden" name="type" id="qqType" value="1">
                                    <input type="hidden" name="name" id="qqName" value="">
                                    <input class="layui-btn" onclick="saveLoginConfig($('#qqLoginForm'))" type="button" style="width:200px;" value="更新QQ登录配置">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <form id="wechatForm" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                    <div class="layui-col-md6" style="padding: 3px">
                        <div class="layui-card" style="border: 1px solid #d0cbcb;">
                            <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>微&nbsp;&nbsp;&nbsp;信</strong></span></div>
                            <div class="layui-card-body" style="height:195px;">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width: 207px;">是否启用</label>
                                    <div class="layui-input-block" style="margin-left: 240px;" id="isWechat">
                                        <input type="radio" name="status" id="isWechatTrue" value="1" title="是" checked="checked">
                                        <input type="radio" name="status" id="isWechatFalse" value="2" title="否" >
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline" style="width: 378px;">
                                        <label class="layui-form-label" style="width: 136px;">应用APP ID</label>
                                        <div class="layui-input-inline" style="width: 233px;">
                                            <input type="text" name="appId" id="appIdWechat" value="" placeholder="请输入微信APP ID" class="layui-input" >
                                        </div>
                                    </div>
                                    <div class="layui-inline" style="width: 378px;">
                                        <label class="layui-form-label" style="width: 136px;">应用Secret</label>
                                        <div class="layui-input-inline" style="width: 233px;">
                                            <input type="text" name="appKey" id="appKeyWechat" value="" placeholder="请输入微信Secret" class="layui-input" >
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item" style="padding-top: 20px;">
                                    <input type="hidden" name="id" id="wechatId" value="">
                                    <input type="hidden" name="type" id="wechatType" value="2">
                                    <input type="hidden" name="name" id="wechatName" value="">
                                    <input class="layui-btn" onclick="saveLoginConfig($('#wechatForm'))" type="button" style="width:200px;" value="更新微信登录配置">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <form id="wechatH5Form" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                    <div class="layui-col-md6" style="padding: 3px">
                        <div class="layui-card" style="border: 1px solid #d0cbcb;">
                            <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>微&nbsp;&nbsp;信&nbsp;&nbsp;公&nbsp;&nbsp;众&nbsp;&nbsp;号</strong></span></div>
                            <div class="layui-card-body" style="height:195px;">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width: 207px;">是否启用</label>
                                    <div class="layui-input-block" style="margin-left: 240px;" id="isWechatH5">
                                        <input type="radio" name="status" id="isWechatH5True" value="1" title="是" checked="checked">
                                        <input type="radio" name="status" id="isWechatH5False" value="2" title="否" >
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline" style="width: 378px;">
                                        <label class="layui-form-label" style="width: 136px;">应用APP ID</label>
                                        <div class="layui-input-inline" style="width: 233px;">
                                            <input type="text" name="appId" id="appIdWechatH5" value="" placeholder="请输入微信公众号APP ID" class="layui-input" >
                                        </div>
                                    </div>
                                    <div class="layui-inline" style="width: 378px;">
                                        <label class="layui-form-label" style="width: 136px;">应用Secret</label>
                                        <div class="layui-input-inline" style="width: 233px;">
                                            <input type="text" name="appKey" id="appKeyWechatH5" value="" placeholder="请输入微信公众号Secret" class="layui-input" >
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item" style="padding-top: 20px;">
                                    <input type="hidden" name="id" id="wechatH5Id" value="">
                                    <input type="hidden" name="type" id="wechatH5Type" value="4">
                                    <input type="hidden" name="name" id="wechatH5Name" value="">
                                    <input class="layui-btn" onclick="saveLoginConfig($('#wechatH5Form'))" type="button" style="width:200px;" value="更新微信公众号登录配置">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="/pages/common/layui/layui.js"></script>
<script type="text/javascript" src="/pages/common/jquery/jquery-1.11.3.min.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="./js/common.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="application/javascript">
    layui.use('form', function(){
        let form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
</script>
<script type="application/javascript">
    function getFormData(obj) {
        var unindexed_array = obj.serializeArray();
        var indexed_array = {};
        $.map(unindexed_array, function (n) {
            if(n['value']!=null&&n['value']!=''){
                indexed_array[n['name']] = n['value'];
            }
        });
        return indexed_array;
    }
    //提交配置表单
    function saveLoginConfig(obj){
        var payConfig = getFormData(obj);
        Common.invoke({
            path : request('/console/saveLoginConfig'),
            data : payConfig,
            successMsg : false,
            errorMsg : "获取数据失败,请检查网络",
            successCb : function(result) {
                layui.use('layer', function() { //独立版的layer无需执行这一句
                    layer.msg(result.resultMsg,{time:2000});
                    if (result.resultCode == 1){
                        getQQLoginConfig();
                        getWechatLoginConfig();
                        getWechatH5LoginConfig();
                    }
                });
            },
            errorCb : function(result) {
            }
        });
    }
    getQQLoginConfig();
    getWechatLoginConfig();
    getWechatH5LoginConfig();
    function getQQLoginConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getLoginConfig'),
                data : {type:1},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#qqId").val(result.data.id);
                        $("#appIdQQ").val(result.data.appId);
                        $("#qqType").val(result.data.type);
                        $("#qqName").val(result.data.name);
                        let qqStatus = result.data.status;
                        if (qqStatus == 1){
                            $("#isQQLoginFalse").attr("checked",false);
                            $("#isQQLoginTrue").attr("checked","checked");
                        }
                        if (qqStatus == 2){
                            $("#isQQLoginTrue").attr("checked",false);
                            $("#isQQLoginFalse").attr("checked","checked");
                        }
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }

    function getWechatLoginConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getLoginConfig'),
                data : {type:2},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#wechatId").val(result.data.id);
                        $("#appIdWechat").val(result.data.appId);
                        $("#appKeyWechat").val(result.data.appKey);
                        $("#wechatType").val(result.data.type);
                        $("#wechatName").val(result.data.name);
                        let wechatStatus = result.data.status;
                        if (wechatStatus == 1){
                            $("#isWechatFalse").attr("checked",false);
                            $("#isWechatTrue").attr("checked","checked");
                        }
                        if (wechatStatus == 2){
                            $("#isWechatTrue").attr("checked",false);
                            $("#isWechatFalse").attr("checked","checked");
                        }
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }
    function getWechatH5LoginConfig() {
        layui.use(['form','jquery',"layer"],function() {
            var form = layui.form, $ = layui.jquery, layer = parent.layer === undefined ? layui.layer : top.layer;
            Common.invoke({
                path : request('/console/getLoginConfig'),
                data : {type:4},
                successMsg : false,
                errorMsg : "获取数据失败,请检查网络",
                successCb : function(result) {
                    if (result.data!=null){
                        $("#wechatH5Id").val(result.data.id);
                        $("#appIdWechatH5").val(result.data.appId);
                        $("#appKeyWechatH5").val(result.data.appKey);
                        $("#wechatH5Type").val(result.data.type);
                        $("#wechatH5Name").val(result.data.name);
                        let wechatStatus = result.data.status;
                        if (wechatStatus == 1){
                            $("#isWechatH5False").attr("checked",false);
                            $("#isWechatH5True").attr("checked","checked");
                        }
                        if (wechatStatus == 2){
                            $("#isWechatH5True").attr("checked",false);
                            $("#isWechatH5False").attr("checked","checked");
                        }
                        form.render();
                    }
                },
                errorCb : function(result) {
                }
            });
        });
    }
</script>
</html>