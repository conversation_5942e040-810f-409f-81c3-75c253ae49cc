<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <link href="/pages/common/layui/css/layui.css" rel="stylesheet" media="all">
    <script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="/pages/common/layui/layui.js"></script>
    <script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
    <script type="text/javascript" src="/pages/console/js/common.js"></script>
    <script type="text/javascript" src="/pages/console/js/console_ui.js"></script>
    <script type="text/javascript" src="/pages/console/js/core.js"></script>
    <script type="text/javascript" src="/pages/console/js/tripledes.js"></script>
</head>
<body>
<div class="layui-row">
    <div class="layui-col-md1">&nbsp;</div>
    <div id="roomList" class="layui-col-md10" style="">
        <form class="layui-form" style="margin-top: 2%;">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" name="typeName" placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline" style="width: 100px;">
                        <button class="layui-btn" lay-submit lay-filter="search">查询</button>
                    </div>
                </div>
            </div>
        </form>
        <div class="layui-card admin_table" style="margin-top: 1%">
            <!--            <div class="layui-card-header">列表</div>-->
            <div class="layui-card-body">
                <table id="configs" lay-filter="configs">
                </table>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="bar">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="select">选择</a>
</script>
<script type="text/javascript">
    var table;
    var form;
    var layer;
    layui.use(['table', 'form', 'layer'], function () {
        table = layui.table;
        form = layui.form;
        layer = layui.layer;
        form.on('submit(search)', function (data) {
            operate.renderTable(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
            return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });
        operate.renderTable();
        table.on('tool(configs)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data; //获得当前行数据
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）

            if (layEvent === 'select') { //select
                layer.confirm('是否确认选择', function (index) {
                    if (!data.id) {
                        return;
                    }
                    operate.doSelect(data.id, data.typeName);
                });
            }
        });
    })
    const operate = {
        doSelect: (id, name) => {
            parent.setWindowObject(id, name);
            var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
            parent.layer.close(index); //再执行关闭
            parent.layer.msg("选择成功");
        }, renderTable: (data) => {
            var options = {
                elem: '#configs',
                page: true
                , curr: 0
                , limit: Common.limit
                , limits: Common.limits
                , id: 'id'
                , data: data
                , where: data
                , cellMinWidth: 150
                , cols: [[
                    // {field: 'id', title: '选择', sort: true, type: 'radio'}
                    {field: 'id', title: 'id', sort: true}
                    , {field: 'typeName', title: '名称', sort: true}
                    , {field: 'sort', title: '排序', sort: true}
                    , {fixed: 'right', title: '操作', toolbar: '#bar'}
                ]], parseData: function (res) {
                    checkRequst(res);
                    return {
                        "code": 0, //解析接口状态
                        "msg": res.message, //解析提示文本
                        "data": res.data,//解析数据列表
                        "count": res.count
                    };
                }, done: function (res, curr, count) {
                }
            };
            options.url = request('/user/goodsType/page');
            configTable = table.render(options);
        }
    };
</script>
</body>
</html>