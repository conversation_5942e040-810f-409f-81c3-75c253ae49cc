<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <link href="/pages/common/layui/css/layui.css" rel="stylesheet" media="all">
    <script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="/pages/common/layui/layui.js"></script>
    <script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
    <script type="text/javascript" src="/pages/console/js/common.js"></script>
    <script type="text/javascript" src="/pages/console/js/console_ui.js"></script>
    <script type="text/javascript" src="/pages/console/js/core.js"></script>
    <script type="text/javascript" src="/pages/console/js/tripledes.js"></script>
</head>
<body>
<div class="layui-row">
    <div class="layui-col-md1">&nbsp;</div>
    <div id="roomList" class="layui-col-md10" style="">
<!--        <button class="layui-btn" onclick="operate.edit()">新增</button>-->
        <form class="layui-form" style="margin-top: 2%;">
            <div class="layui-form-item">
                <!--                <div class="layui-inline">-->
                <!--                    <select id="state" name="state" class="layui-select">-->
                <!--                        <option value="0" selected>未审核</option>-->
                <!--                        <option value="1">已处理</option>-->
                <!--                        <option value="-1">忽略</option>-->
                <!--                    </select>-->
                <!--                </div>-->
                <div class="layui-inline">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" name="typeName" placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline" style="width: 100px;">
                        <button class="layui-btn" lay-submit lay-filter="search">查询</button>
                    </div>
                </div>
            </div>
        </form>
        <div class="layui-card admin_table" style="margin-top: 1%">
            <!--            <div class="layui-card-header">列表</div>-->
            <div class="layui-card-body">
                <table id="configs" lay-filter="configs">
                </table>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="bar">
    <!-- <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="edit">编辑</a> -->
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="audit1">退回</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="audit2">发货</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="audit3">已送达</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<div id="tpl-edit" hidden>
    <div style="padding: 20px 30px 0 0;">
        <form class="layui-form" action="" lay-filter="configForm">
            <input type="hidden" name="id">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">用户</label>
                    <div class="layui-input-inline">
                        <input type="text" name="userId" lay-verify="required" autocomplete="off" placeholder="" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-inline">
                        <input type="number" name="sort" lay-verify="required" autocomplete="off" placeholder="" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn" lay-submit="" lay-filter="editConfig">提交</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    var table;
    var form;
    var layer;
    layui.use(['table', 'form', 'layer'], function () {
        table = layui.table;
        form = layui.form;
        layer = layui.layer;
        form.on('submit(editConfig)', function (data) {
            operate.doEdit(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
            return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });
        form.on('submit(levelEditConfig)', function (data) {
            operate.dolevelEdit(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
            return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });
        form.on('submit(search)', function (data) {
            operate.renderTable(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
            return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });
        operate.renderTable();
        table.on('tool(configs)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data; //获得当前行数据
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）

            if (layEvent === 'del') { //删除
                layer.confirm('是否确认删除', function (index) {
                    obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                    layer.close(index);
                    if (!data.id) {
                        return;
                    }
                    operate.remove(data.id);
                });
            } else if (layEvent === 'edit') {
                operate.edit(data.id);
            } else if (layEvent === 'audit1') {
                operate.audit(data.id, -1);
            } else if (layEvent === 'audit2') {
                operate.audit(data.id, 1);
            } else if (layEvent === 'audit3') {
                operate.audit(data.id, 2);
            }
        });
    })
    const operate = {
        audit: (id, state) => {
            layer.confirm('是否确认删除', function (index) {
                Common.invoke({
                    path: request('/user/buyOrder/audit'),
                    data: {
                        id: id,
                        state: state
                    }, success: function () {
                        layer.msg('审核成功');
                        window.location.reload();
                    }
                })
            });
        },
        defaultEdit: () => {
            operate.editIndex = layer.open({
                area: '80%',
                type: 1,
                title: "设置默认获取活跃信息",
                closeBtn: 1,
                content: $("#level-edit"),
                success: function () {
                    Common.invoke({
                        path: request("/user/buyOrder/get"),
                        data: {id: 1000},
                        success: function (res) {
                            if (res.data != null) {
                                form.val("configForm", res.data);
                            }
                        }
                    })
                }
            });
        },
        dolevelEdit: (data) => {
            Common.invoke({
                path: request('/user/buyOrder/edit'),
                type: "post",
                data: data,
                success: function (res) {
                    operate.renderTable();
                    layer.msg('保存成功');
                    layer.close(operate.editIndex);
                    window.location.reload();
                }
            })
        },
        edit: (id) => {
            operate.editIndex = layer.open({
                area: '80%',
                type: 1,
                title: "编辑",
                closeBtn: 1,
                content: $("#tpl-edit"),
                success: function () {
                    if (id != null) {
                        Common.invoke({
                            path: request("/user/buyOrder/get"),
                            data: {id: id},
                            success: function (res) {
                                if (res.data != null) {
                                    form.val("configForm", res.data);
                                }
                                // form.render();
                            }
                        })
                    }
                }
                , end: function () {
                    window.location.reload();
                }
            });
        }, remove: (id) => {
            Common.invoke({
                path: request('/user/buyOrder/delete'),
                data: {
                    id: id
                }, success: function () {
                    layer.msg('删除成功');
                }
            })
        },
        doEdit: (data) => {
            if (data.id == null || data.id == '') {
                delete data.id;
            }
            Common.invoke({
                path: request('/user/buyOrder/edit'),
                type: "post",
                data: data,
                success: function (res) {
                    operate.renderTable();
                    layer.msg('保存成功');
                    layer.close(operate.editIndex);
                    window.location.reload();
                }
            })
        }, renderTable: (data) => {
            if (data == null){
                data = {isAdmin:1}
            }else {
                data.isAdmin = 1;
            }
            var options = {
                elem: '#configs',
                page: true
                , curr: 0
                , limit: Common.limit
                , limits: Common.limits
                , id: 'id'
                , data: data
                , where: data
                // , cellMinWidth: 150
                , cols: [[
                    {field: 'id', title: 'id', sort: true}
                    , {field: 'userId', title: '用户id', sort: true}
                    , {field: 'userName', title: '用户姓名', sort: true}
                    , {field: 'userPhone', title: '电话', sort: true}
                    , {field: 'userAddress', title: '地址', sort: true}
                    , {field: 'goodsName', title: '商品名称', sort: true}
                    , {field: 'goodsNum', title: '商品编号', sort: true}
                    , {field: 'goodsMoney', title: '商品金额', sort: true}
                    , {field: 'buyNum', title: '数量', sort: true}
                    , {
                        field: 'state', title: '状态', sort: true, templet(d) {
                            switch (d.status) {
                                case -1:
                                    return "退回";
                                case 0:
                                    return "未审核";
                                case 1:
                                    return "发货";
                                case 2:
                                    return "已送达";
                            }
                        }
                    }
                    , {fixed: 'right', width: '200' , title: '操作', toolbar: '#bar'}
                ]], parseData: function (res) {
                    checkRequst(res);
                    return {
                        "code": 0, //解析接口状态
                        "msg": res.message, //解析提示文本
                        "data": res.data,//解析数据列表
                        "count": res.count
                    };
                }, done: function (res, curr, count) {
                    // if (userId != 1) {//此处test为你的条件值
                    //     $("[data-field='lotteryCount']").css('display', 'none'); //关键代码
                    // }
                }
            };
            options.url = request('/user/buyOrder/page');
            configTable = table.render(options);
        }
    };
</script>
</body>
</html>