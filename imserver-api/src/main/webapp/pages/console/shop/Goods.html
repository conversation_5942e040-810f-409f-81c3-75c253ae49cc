<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <link href="/pages/common/layui/css/layui.css" rel="stylesheet" media="all">
    <script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<!--    <script type="text/javascript" src="/pages/common/layui/layui.js"></script>-->
    <script type="text/javascript" src="/pages/common/layui-v2.5.6/layui.js"></script>
    <script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
    <script type="text/javascript" src="/pages/console/js/common.js"></script>
    <script type="text/javascript" src="/pages/console/js/console_ui.js"></script>
    <script type="text/javascript" src="/pages/console/js/core.js"></script>
    <script type="text/javascript" src="/pages/console/js/tripledes.js"></script>
</head>
<body>
<div class="layui-row">
    <div class="layui-col-md1">&nbsp;</div>
    <div id="roomList" class="layui-col-md10" style="">
        <button class="layui-btn" onclick="operate.edit()">新增</button>
        <form class="layui-form" style="margin-top: 2%;">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <select id="isShow" name="isShow" class="layui-select">
                        <option selected="selected" value="-1" selected>所有</option>
                        <option value="0">不显示</option>
                        <option value="1">显示</option>
                    </select>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" name="typeName" placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline" style="width: 100px;">
                        <button class="layui-btn" lay-submit lay-filter="search">查询</button>
                    </div>
                </div>
            </div>
        </form>
        <div class="layui-card admin_table" style="margin-top: 1%">
            <!--            <div class="layui-card-header">列表</div>-->
            <div class="layui-card-body">
                <table id="configs" lay-filter="configs">

                </table>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="bar">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<div id="tpl-edit" hidden>
    <div style="padding: 20px 30px 0 0;">
        <form class="layui-form" action="" lay-filter="configForm">
            <input type="hidden" name="id">
            <div class="layui-form-item">
                <label class="layui-form-label ">主图</label>
                <div class="layui-input-block" style="width: auto">
                    <div id="imgUrl0"></div>
                    <input type="text" name="mainImg" id="photoUrl0" placeholder="" autocomplete="off" class="layui-input" lay-verify="required">
                    <button type="button" class="layui-btn" id="uploadPhotoUrl0">
                        <i class="layui-icon">&#xe67c;</i>上传
                    </button>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label ">展示图</label>
                <div class="layui-input-block">
                    <input type="text" name="detailsImg" id="photoUrl1" placeholder="" autocomplete="off" class="layui-input">
                    <button type="button" class="layui-btn" id="uploadPhotoUrl1">
                        <i class="layui-icon">&#xe67c;</i>上传
                    </button>
                </div>
                <div class="layui-input-block">
                    <div class="layui-input-inline">
                        展示图：
                    </div>
                </div>
                <div class="layui-input-block" id="addFacePreview"></div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-block">
                    <input type="text" name="goodsName" lay-verify="required" autocomplete="off" placeholder="" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">商品信息介绍</label>
                <div class="layui-input-block">
                    <input type="text" name="goodsInfo" lay-verify="required" autocomplete="off" placeholder="" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">分类</label>
                <div class="layui-input-inline">
                    <input type="text" id="typeId" name="typeId" lay-verify="required" autocomplete="off" placeholder="" class="layui-input"
                           style="display: none">
                    <input type="text" id="typeName" name="typeName" autocomplete="off" placeholder="" class="layui-input"
                           onclick="operate.select()">
                </div>
                <label class="layui-form-label">价格</label>
                <div class="layui-input-inline">
                    <input type="number" name="goodsMoney" lay-verify="required" autocomplete="off" placeholder="" class="layui-input">
                </div>

<!--                <label class="layui-form-label">详情</label>-->
<!--                <div class="layui-input-inline">-->
<!--                    <input type="text" name="detailsIntroduce" lay-verify="required" autocomplete="off" placeholder="" class="layui-input">-->
<!--                </div>-->
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">编号</label>
                <div class="layui-input-inline">
                    <input type="text" name="goodsNum" lay-verify="required" autocomplete="off" placeholder="" class="layui-input">
                </div>

                <label class="layui-form-label">排序</label>
                <div class="layui-input-inline">
                    <input type="number" name="sort" lay-verify="required" autocomplete="off" placeholder="" class="layui-input">
                </div>

                <label class="layui-form-label">显示</label>
                <div class="layui-input-inline">
                    <select id="type" name="isShow" class="layui-select">
                        <option value="0">不显示</option>
                        <option value="1">显示</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">产品详情介绍</label>
                <!--                <div class="layui-input-inline">-->
                <!--                    <input type="text" name="detailsIntroduce" lay-verify="required" autocomplete="off" placeholder="" class="layui-input">-->
                <!--                </div>-->
                <div class="layui-input-block">
                    <input type="text" name="detailsIntroduce" id="photoUrl2" placeholder="" autocomplete="off" class="layui-input">
                    <button type="button" class="layui-btn" id="uploadPhotoUrl2">
                        <i class="layui-icon">&#xe67c;</i>上传
                    </button>
                </div>
                <div class="layui-input-block">
                    <div class="layui-input-inline">
                        详情图：
                    </div>
                </div>
                <div class="layui-input-block" id="addFacePreview2"></div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn" lay-submit="" lay-filter="editConfig">提交</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
    const uploadUrl = Config.getConfig().uploadUrl + "/upload/UploadServlet";
    $("#uploadPath0").attr("action", uploadUrl);
    var table;
    var form;
    var layer;
    var upload;
    var faceUrls = [];
    var faceUrls2 = [];
    layui.use(['table', 'form', 'layer', 'upload'], function () {
        table = layui.table;
        form = layui.form;
        layer = layui.layer;
        upload = layui.upload;
        form.on('submit(editConfig)', function (data) {
            operate.doEdit(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
            return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });
        form.on('submit(levelEditConfig)', function (data) {
            operate.dolevelEdit(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
            return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });
        form.on('submit(search)', function (data) {
            operate.renderTable(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
            return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });
        operate.renderTable({isShow: -1});
        //执行实例
        var uploadInst0 = upload.render({
            elem: '#uploadPhotoUrl0' //绑定元素
            , accept: 'file'
            , field: 'files'
            , multiple: false
            , number: 1
            , url: uploadUrl //上传接口
            , done: function (res) {
                if (res != null && res != '') {
                    // var obj = eval("(" + res + ")");
                    console.log(res);
                    var image = res.data.images[0].oUrl;
                    refreshAddPreview(image, 0);
                }
            }
            , error: function () {
                layer.error("上传出错！" + uploadUrl);
            }
        });

        var uploadInst1 = upload.render({
            elem: '#uploadPhotoUrl1' //绑定元素
            , accept: 'file'
            , field: 'files'
            , multiple: true
            , size: 0
            , number: 0
            // , drag: true
            , url: uploadUrl //上传接口
            , choose: function (obj) {
                faceUrls = [];
            }
            , done: function (res) {
                if (res != null && res !== '') {
                    // faceUrls.push(res[0]);
                    var image = res.data.images[0].oUrl;
                    faceUrls.push(image);
                    refreshAddFacePreview();

                   /* faceUrls.push(res.data.oUrl);
                    refreshAddFacePreview();*/
                }
                $("#photoUrl1").val(faceUrls);
            }, error: function () {
                layer.error("上传出错！" + uploadUrl);
            }
        });

        var uploadInst2 = upload.render({
            elem: '#uploadPhotoUrl2' //绑定元素
            , accept: 'file'
            , field: 'files'
            , multiple: true
            , size: 0
            , number: 0
            // , drag: true
            , url: uploadUrl //上传接口
            , choose: function (obj) {
                faceUrls2 = [];
            }
            , done: function (res) {
                if (res != null && res != '') {
                    // faceUrls2.push(res[0]);
                   /* faceUrls2.push(res.data.oUrl);
                    refreshAddFacePreview2();*/

                    var image = res.data.images[0].oUrl;
                    faceUrls2.push(image);
                    refreshAddFacePreview2();

                }
                $("#photoUrl2").val(faceUrls2);
            }, error: function () {
                layer.error("上传出错！" + uploadUrl);
            }
        });

        table.on('tool(configs)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data; //获得当前行数据
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
            if (layEvent === 'del') { //删除
                layer.confirm('是否确认删除', function (index) {
                    obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                    layer.close(index);
                    if (!data.id) {
                        return;
                    }
                    operate.remove(data.id);
                });
            } else if (layEvent === 'edit') {
                operate.edit(data.id);
            }
        });
    })

    const operate = {
        select: () => {
            operate.probIndex = layer.open({
                id: 'insert-form',
                area: ['80%', '80%'],
                type: 2,
                title: "选择类型",
                shadeClose: true,
                maxmin: true, //开启最大化最小化按钮
                content: '/pages/console/shop/GoodsTypeSelect.html',
                success: function () {

                },
                end: function () {
                    window.windowObject = null;
                }
            });
        },
        defaultEdit: () => {
            operate.editIndex = layer.open({
                area: '80%',
                type: 1,
                title: "设置默认获取活跃信息",
                closeBtn: 1,
                content: $("#level-edit"),
                success: function () {
                    Common.invoke({
                        url: request("/user/goods/get"),
                        data: {id: 1000},
                        success: function (res) {
                            if (res.data != null) {
                                form.val("configForm", res.data);
                            }
                        }
                    })
                }
            });
        },
        dolevelEdit: (data) => {
            Common.invokeGood({
                url: request('/user/goods/edit'),
                type: "post",
                data: data,
                success: function (res) {
                    operate.renderTable();
                    layer.msg('保存成功');
                    layer.close(operate.editIndex);
                    window.location.reload();
                }
            })
        },
        edit: (id) => {
            faceImages = [];
            index = -1
            operate.editIndex = layer.open({
                area: '80%',
                type: 1,
                title: "编辑",
                closeBtn: 1,
                content: $("#tpl-edit"),
                success: function () {
                    if (id != null) {
                        Common.invokeGood({
                            url : request('/user/goods/get'),
                            data: {id: id},
                            success: function (res) {
                                console.log("res="+res)
                                console.log("res.data="+res.data)
                                if (res.data != null) {
                                    form.val("configForm", res.data);
                                    refreshAddPreview(res.data.mainImg, 0);
                                    if (res.data.detailsImg != null) {
                                        faceUrls = res.data.detailsImg;
                                    }
                                    if (res.data.detailsIntroduce != null) {
                                        faceUrls2 = res.data.detailsIntroduce;
                                    }
                                    refreshFacePreview();
                                    refreshFacePreview2();
                                }
                                form.render();
                            }
                        })
                    }
                }
                , end: function () {
                    window.location.reload();
                }
            });
        }, remove: (id) => {
            Common.invoke({
                url: request('/user/goods/delete'),
                data: {
                    id: id
                }, success: function () {
                    layer.msg('删除成功');
                }
            })
        },
        doEdit: (data) => {
            if (data.id == null || data.id == '') {
                delete data.id;
            }
            console.log(faceUrls);
            // console.log(JSON.stringify(faceUrls));
            var list = "";
            if (faceUrls != null) {
                faceUrls.forEach(function (url) {
                    list += url + ","
                })
                list = list.slice(0, list.length - 1);
            }
            console.log(list);
            data.detailsImg = list;
            // data.detailsImg = faceUrls;
            Common.invokeGood({
                url: request('/user/goods/edit'),
                type: "post",
                data: data,
                success: function (res) {
                    operate.renderTable();
                    layer.msg('保存成功');
                    layer.close(operate.editIndex);
                    window.location.reload();
                }
            })
        }, renderTable: (data) => {
            var options = {
                elem: '#configs',
                page: true
                , curr: 0
                , limit: Common.limit
                , limits: Common.limits
                , id: 'id'
                , data: data
                , where: data
                , cellMinWidth: 150
                , cols: [[
                    {field: 'id', title: 'id', sort: true}
                    , {field: 'typeName', title: '类型名称', sort: true}
                    // , {field: 'imgUrl', title: '图标', sort: true, templet(d) {
                    //         if (d.imgUrl) {
                    //             return "<img onclick=\"imgOpen('" + d.imgUrl + "')\" class='bubbleImg' src='" + d.imgUrl + "' />";
                    //         }
                    //         return "未设置";
                    //     }
                    // }
                    , {field: 'goodsName', title: '商品名称', sort: true}
                    , {field: 'goodsMoney', title: '价格', sort: true}
                    , {field: 'goodsNum', title: '编号', sort: true}
                    , {field: 'sort', title: '排序', sort: true}
                    , {
                        field: 'isShow', title: '显示', sort: true, templet(d) {
                            if (d.isShow == 0) {
                                return "不显示";
                            } else if (d.isShow == 1) {
                                return "显示";
                            }
                            return "未设置";
                        }
                    }
                    , {fixed: 'right', title: '操作', toolbar: '#bar'}
                ]], parseData: function (res) {
                    checkRequst(res);
                    return {
                        "code": 0, //解析接口状态
                        "msg": res.message, //解析提示文本
                        "data": res.data,//解析数据列表
                        "count": res.count
                    };
                }, done: function (res, curr, count) {
                    // if (userId != 1) {//此处test为你的条件值
                    //     $("[data-field='lotteryCount']").css('display', 'none'); //关键代码
                    // }
                }
            };
            options.url = request('/user/goods/page');
            configTable = table.render(options);
        }
    };

    function setWindowObject(id, name) {
        $("#typeId").val(id);
        $("#typeName").val(name);
    }

    function imgOpen(url) {
        layer.open({
            area: ['1000px', '800px'],
            type: 1,
            title: "预览",
            closeBtn: 1,
            content: "<img style=\"width: auto;height: auto\" class='faceImg' src=\'" + url + "\' >",
            success: function () {
            }
        })
    }

    function refreshAddPreview(imgUrls, id) {
        if (!imgUrls || imgUrls.length == 0) return;
        $("#imgUrl" + id).html("");
        var imgHtml = "<a><img class='faceImg' style='width: 100px' src=\'" + imgUrls + "\' ></a>";
        $("#imgUrl" + id).html(imgHtml);
        $("#photoUrl" + id).val(imgUrls);
    }

    function refreshFacePreview() {
        if (!faceUrls || faceUrls.length == 0) return;
        var faceHtml = "";
        $("#addFacePreview").html = "";

        faceUrls.forEach(function (url) {
            // faceHtml += "<li><a><img style='width: 50px' class='faceImg' src=\'" + url + "\' ></a></li>";
            faceHtml += "<div class=\"layui-input-inline\"><img style=\"width: 100px\" class=\"faceImg\" src=\'" + url + "\'></div>";
        });
        $("#addFacePreview").html(faceHtml);
    }

    function refreshAddFacePreview() {
        if (!faceUrls || faceUrls.length == 0) return;
        var faceHtml = "";
        $("#addFacePreview").html = "";

        faceUrls.forEach(function (url) {
            // faceHtml += "<li><a><img style='width: 50px' class='faceImg' src=\'" + url + "\' ></a></li>";
            faceHtml += "<div class=\"layui-input-inline\"><img style=\"width: 100px\" class=\"faceImg\" src=\'" + url + "\'></div>";
        });
        $("#addFacePreview").html(faceHtml);
    }

    function refreshFacePreview2() {
        if (!faceUrls2 || faceUrls2.length == 0) return;
        var faceHtml = "";
        $("#addFacePreview2").html = "";
        faceUrls2.forEach(function (url) {
            faceHtml += "<div class=\"layui-input-inline\"><img style=\"width: 100px\" class=\"faceImg\" src=\'" + url + "\'></div>";
        });
        $("#addFacePreview2").html(faceHtml);
    }

    function refreshAddFacePreview2() {
        if (!faceUrls2 || faceUrls2.length == 0) return;
        var faceHtml = "";
        $("#addFacePreview2").html = "";
        faceUrls2.forEach(function (url) {
            faceHtml += "<div class=\"layui-input-inline\"><img style=\"width: 100px\" class=\"faceImg\" src=\'" + url + "\'></div>";
        });
        $("#addFacePreview2").html(faceHtml);
    }
</script>

</body>
</html>