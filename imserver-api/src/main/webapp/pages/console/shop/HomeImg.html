<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>轮播图管理</title>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">

<body>
<div class="layui-row">
    <div id="back" class="layui-col-md1">&nbsp;</div>
    <input id="pageCount" type="" name="" style="display: none">
    <input type="" name="" id="save_roomId" style="display: none;">
    <div id="music_div" class="layui-col-md10">
        <div class="liveRoom_btn_div" style="margin-top: 2%">
<!--            <input id="inviteCode" type="text" name="typeName" class="layui-input" style="width: 15%;display: inline" placeholder="商品名称">-->
<!--            <button class="layui-btn search_live">搜索</button>-->
            <button onclick="Music.addMusic()" class="layui-btn btn_addLive">新增轮播图</button>
        </div>
        <div id="musicList" class="layui-card" style="margin-top: 1%">
            <div class="layui-card-header">轮播图管理</div>
            <div class="layui-card-body">
                <table id="music_table" lay-filter="music_table"></table>
            </div>
        </div>
        <div id="updateMusic" class="layui-col-md10" style="display: none;">
            <div class="layui-card" style="margin-top: 1%">
                <div class="layui-card-header">修改轮播图</div>
                <div class="layui-card-body">
                    <table cellspacing="0" cellpadding="0" border="0" class="layui-table">
                        <tr>
                            <td>地址</td>
                            <td><input id="inviteCode_update" type="text" name="typeName" required class="layui-input" placeholder="地址"></td>
                        </tr>
                        <tr>
                            <td>封面</td>
                            <td>
                                <button class="layui-btn" onclick="Music.selectUpdateCover()">选择封面</button>
                                <span id="musicCover_update"></span>
                                <form id="uploadMusicCover_update" name="uploadMusicCover_update" action="" method="post" enctype="multipart/form-data" style="display: none;">
                                    <input id="uploadCover_update" type="file" name="file" onchange="Music.updateUploadCover()">
                                </form>
                            </td>
                        </tr>
                        <tr>
                            <td>排序</td>
                            <td><input id = "remark_update" type="number" name="sort" required autocomplete="off" placeholder="" class="layui-input"></td>
                        </tr>
                    </table>
                    <button onclick="Music.commit_updateMusic()" class="layui-btn addLiveRoombtn">提交</button>
                    <button onclick="Music.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
                </div>
            </div>
        </div>
    </div>
    <div id="addMusic" class="layui-col-md10" style="display: none;">
        <div class="layui-card" style="margin-top: 1%">
            <div class="layui-card-header">新增轮播图</div>
            <div class="layui-card-body">
                <table cellspacing="0" cellpadding="0" border="0" class="layui-table">
                    <tr>
                        <td>地址</td>
                        <td><input id="inviteCode_add" type="text" name="" required class="layui-input" placeholder="地址"></td>
                    </tr>
                    <tr>
                        <td>封面</td>
                        <td>
                            <button class="layui-btn" onclick="Music.selectCover()">选择封面</button>
                            <span id="musicCover"></span>
                            <form id="uploadMusicCover" name="uploadMusicCover" action="" method="post" enctype="multipart/form-data" style="display: none;">
                                <input id="uploadCover" type="file" name="file" onchange="Music.uploadAddCover()">
                            </form>
                        </td>
                    </tr>
                    <tr>
                        <td>排序</td>
                        <td><input id="remark_add"  type="number" name="sort" required autocomplete="off" placeholder="" class="layui-input"></td>
                    </tr>
                </table>
                <button onclick="Music.commit_add()" class="layui-btn addLiveRoombtn">提交</button>
                <button onclick="Music.btn_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
            </div>
        </div>
    </div>
</div>

<!--操作-->
<script type="text/html" id="musicListBar">
    <a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="delete">删除</a>
    <a class="layui-btn layui-btn-primary layui-btn-xs delete" lay-event="update">修改</a>
</script>


<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/common/jquery/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="/pages/console/js/console_ui.js"></script>
<script type="text/javascript" src="/pages/console/js/HomeImg.js"></script>
<!--<script type="text/javascript" src="/pages/js/console_init.js"></script>-->
</body>
</html>