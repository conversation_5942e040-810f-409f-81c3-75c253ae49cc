<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/pages/common/layui/css/layui.css" media="all"/>
    <link rel="stylesheet" href="./css/public.css" media="all"/>

    <title>匿名管理</title>
</head>
<body>
<div class="layui-tab layui-tab-brief" lay-filter="demo">

    <div class="layui-tab-content layui-row">
        <div id="back" class="layui-col-md1">&nbsp;</div>

        <div id="aaa" class="layui-tab-item layui-tab-card layui-show layui-col-md10">
            <div class="admin_btn_div" style="margin-top: 2%">
                <input type="text" name="" class="layui-input userID_keyword" style="width: 15%;display: inline" placeholder="用户ID">
                <button class="layui-btn  search_anonymousList">搜索</button>
                <button class="layui-btn save" lay-submit="" onclick="AnonClu.addAnonymous()">添加</button>
            </div>
            <br/>
            <div id="anonymousList">
                <table id="anonymous_list" lay-filter="anonymous_list">

                </table>
<!--                <div class="magt10 layui-center">-->
<!--                    <div class="layui-input-block" style="margin-left: 0px">-->
<!--                        <button onclick="AnonClu.addAnonymous()" class="layui-btn save" lay-submit=""-->
<!--                                lay-filter="systemConfig">添加-->
<!--                        </button>-->
<!--                    </div>-->
<!--                </div>-->
            </div>
            <div id="anonymous_area" style="display: none;">
                <div class="layui-card-header">新增记录</div>
                <div class="layui-card-body">
                    <table cellspacing="0" cellpadding="0" border="0" class="layui-table">
                        <tbody>
                        <tr>
                            <td>用户id</td>
                            <td>
                                <input type="hidden" name="" id="media_id" style="display: none;">
                                <input type="text" class="layui-input userId" lay-verify="required"
                                       placeholder="请输入用户ID">
                            </td>
                        </tr>
                        <tr>
                            <td>描述</td>
                            <td>
                                <input type="text" class="layui-input remark" lay-verify="required"
                                       placeholder="请输入描述">
                            </td>
                        </tr>
                        </tbody>
                    </table>

                    <button lay-submit="" lay-filter="systemConfig" class="layui-btn" type="button"
                            onclick="AnonClu.commitAnonymous()">保存
                    </button>
                    <button class="layui-btn layui-btn-primary" type="button" onclick="AnonClu.back()">返回</button>

                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/html" id="anonymousList_toolbar">
    <a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="delete">删除</a>
    <!--	<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="update">修改</a>-->
</script>

<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="text/javascript" src="./js/common.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="/pages/console/js/anonymous.js"></script>

<script type="text/javascript">
</script>
</body>
</html>