<!DOCTYPE  html>
<html>
<head>
	<meta charset="utf-8">
	<title>封禁用户信息</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">

<body class="childrenBody">
	<div class="layui-row">
		<div class="layui-col-md1">&nbsp;</div>
		<div id="deviceList" class="layui-col-md10">
			<div class="layui-card-header"><p>常登用户</p></div>
			<div class="layui-tab">
				<div class="layui-tab-content">
					<div class="layui-tab-item layui-show">
<!--						<div class="user_btn_div" style="margin-top: 2%">-->
<!--							<input type="text" name="" id="search_key_user" class="layui-input keyWordUsers" style="width: 50%;display: inline;margin-right: 10px;" placeholder="请输入用户ID/昵称/手机号/用户名">-->
<!--							<button class="layui-btn search_users">搜索</button>-->
<!--							<button onclick="Forbid.UnforbidUsers('')" class="layui-btn btn_unforbid">解封</button>-->
<!--						</div>-->
						<div id="forbid_user_table" class="layui-card" style="margin-top: 1%">
							<div class="layui-card-body">
								<table id="forbid_user_list" lay-filter="forbid_user_list" style="table-layout:fixed;word-break:break-all;" >

								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="/pages/console/js/roomList.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript">
var page=0;
var sum=0;
var lock=0;
var updateId="";
var currentPageIndex;// 当前页码数
var currentCount;// 当前总数
layui.use(['form','layer','laydate','table','laytpl'],function(){
	var form = layui.form,
		layer = parent.layer === undefined ? layui.layer : top.layer,
		$ = layui.jquery,
		laydate = layui.laydate,
		laytpl = layui.laytpl,
		table = layui.table;
    var deviceMsg = localStorage.getItem("deviceMsg");
    var status = localStorage.getItem("status");
    console.log(' deviceMsg '+localStorage.getItem("deviceMsg"));
	console.log(' status '+localStorage.getItem("status"));
    var tableInsUser = layui.table.render({
        elem: '#forbid_user_list'
        ,url:request("/console/getAllForbidUsersPopup") + "&status="+localStorage.getItem("status") + "&deviceMsg="+localStorage.getItem("deviceMsg")
        ,id: 'forbid_user_list'
        ,page: true
        ,curr: 0
        ,limit:10
        ,limits:[10,15,20]
        ,groups: 7
        ,cols: [[ //表头
        	 {field: 'userId', title: '用户ID',sort:'true', width:120}
            ,{field: 'nickname', title: '昵称',sort:'true', width:100}
            ,{field: 'account', title: '用户名',sort:'true', width:100}
            ,{field: 'phone', title: '手机号',sort:'true', width:150}
            ,{field: 'phoneToLocation', title: '手机归属地',sort:'true', width:150}
<!--            ,{field: 'loginIp', title: '登录IP',sort:'true', width:150}-->
<!--            ,{field: 'showLastLoginTime', title: '最后登录时间',sort:'true', width:170,templet: function(d){-->
<!--                    return UI.getLocalTime(d.showLastLoginTime);-->
<!--                }}-->
			,{
                field: 'loginLog', title: '最后登录时间', sort: 'true', width: 170, templet: function (d) {
                    if (d.loginLog == undefined) {
                        return "";
                    } else {
                        if (d.loginLog.loginTime == 0) {
                            return "";
                        } else {
                            return UI.getLocalTime(d.loginLog.loginTime);
                        }
                    }
                }
            }
<!--            , {fixed: 'right', width: 250, title: "操作", align: 'left', toolbar: '#forbidUserBar'}-->
        ]]
        ,done:function(res, curr, count){
        	 if (count == 0 && lock == 1) {
                layer.msg("暂无数据", {"icon": 2});
                renderTable();
            }
            var pageIndex = tableInsUser.config.page.curr;//获取当前页码
            var resCount = res.count;// 获取table总条数
            currentCount = resCount;
            currentPageIndex = pageIndex;
        }
    });
});
</script>

</body>
</html>