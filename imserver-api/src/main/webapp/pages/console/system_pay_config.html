<!DOCTYPE html>
<html xmlns:float="http://www.w3.org/1999/xhtml">
<head> <link rel="shortcut icon" href="favicon.ico" type="image/x-icon" />
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>资金配置</title>
    <link rel="stylesheet" href="/pages/common/layui/css/layui.css" />
    <link rel="stylesheet" href="./css/public.css" />
    <style>
        .scrollbar_zdy::-webkit-scrollbar {/*滚动条整体样式*/
            width: 10px;     /*高宽分别对应横竖滚动条的尺寸*/
            height: 1px;
        }
        .scrollbar_zdy::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
            border-radius: 10px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            background: #EEEEEE;
        }
        .scrollbar_zdy::-webkit-scrollbar-track {/*滚动条里面轨道*/
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 10px;
            background: #FFFEFE;
        }
    </style>
</head>
<body class="scrollbar_zdy" style="margin-left: 15px;margin-right: 15px;">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 24px;">
        <legend>资金配置</legend>
    </fieldset>
        <div class="layui-tab layui-tab-card">
            <ul class="layui-tab-title">
                <li class="layui-this">三方配置</li>
                <li>钱包配置</li>
            </ul>
            <div class="layui-tab-content" style="height: 100%;background-color: #fefefe;">
                <div class="layui-tab-item layui-show" style="margin-bottom: 10px;">
                    <div class="layui-row layui-col-space15">
                        <form id="aliPayForm" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                            <div class="layui-col-md6" style="padding: 3px">
                                <div class="layui-card" style="border: 1px solid #d0cbcb;">
                                    <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>支&nbsp;&nbsp;&nbsp;付&nbsp;&nbsp;&nbsp;宝</strong></span>
                                        <div class="layui-form-item" style=" float: right;">
                                            <input type="hidden" name="id" id="aliId" value="">
                                            <input type="hidden" name="type" id="aliType" value="1">
                                            <input type="hidden" name="name" id="aliName" value="">
                                            <input class="layui-btn" onclick="savePayConfig($('#aliPayForm'))" type="button" style="width:200px;" value="更新支付宝APP支付配置">
                                        </div></div>
                                    <div class="layui-card-body" style="height:400px">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">充值是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isAlipay">
                                                <input type="radio" name="status" id="isAlipayTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="status" id="isAlipayFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">提现是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isWithdrawAlipay">
                                                <input type="radio" name="withdrawStatus" id="isWithdrawAlipayTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="withdrawStatus" id="isWithdrawAlipayFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">应用APP ID</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appId" id="appIdAli" value="" placeholder="请输入支付宝APP ID" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">支付宝账号PID</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="mchId" id="mchIdAli" value="" placeholder="请输入支付宝PID" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">充值回调IP/域名</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="payCallBackUrl" id="payCallBackUrlAli" value="" placeholder="输入充值回调IP/域名【包含http://】" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">提现回调IP/域名</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="transCallBackUrl" id="transCallBackUrlAli" value="" placeholder="输入提现回调IP/域名【包含http://】" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 136px;">应用私钥</label>
                                            <div class="layui-input-block" style="margin-left: 136px;">
                                                <input type="text" name="privateKey" id="privateKeyAli" value="" autocomplete="off" placeholder="请输入应用私钥" class="layui-input" style="width: 400px;">
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 136px;">支付宝公钥</label>
                                            <div class="layui-input-block" style="margin-left: 136px;">
                                                <input type="text" name="publicKey" id="publicKeyAli" value="" autocomplete="off" placeholder="支付宝公钥" class="layui-input" style="width: 400px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <form id="wechatForm" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                            <div class="layui-col-md6" style="padding: 3px">
                                <div class="layui-card" style="border: 1px solid #d0cbcb;">
                                    <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>微&nbsp;&nbsp;&nbsp;信</strong></span></div>
                                    <div class="layui-card-body" style="height:400px;">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">充值是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isWechat">
                                                <input type="radio" name="status" id="isWechatTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="status" id="isWechatFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">提现是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isWithdrawWechat">
                                                <input type="radio" name="withdrawStatus" id="isWithdrawWechatTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="withdrawStatus" id="isWithdrawWechatFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">应用APP ID</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appId" id="appIdWechat" value="" placeholder="请输入微信APP ID" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">应用SECRET</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appKey" id="appKeyWechat" value="" placeholder="请输入微信APP KEY" class="layui-input" >
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label"style="width: 136px;">微信商户ID</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="mchId" id="mchIdWechat" value="" placeholder="请输入微信商户ID" class="layui-input">
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">充值回调IP/域名</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="payCallBackUrl" id="payCallBackUrlWechat" value="" placeholder="输入充值回调IP/域名【包含http://】" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 136px;">应用API KEY</label>
                                            <div class="layui-input-block" style="margin-left: 136px;">
                                                <input type="text" name="apiKey" id="apiKeyWechat" value="" autocomplete="off" placeholder="请输入应用API KEY" class="layui-input" style="width: 625px;">
                                            </div>
                                        </div>
                                        <div class="layui-form-item" style="padding-top: 20px;">
                                            <input type="hidden" name="id" id="wechatId" value="">
                                            <input type="hidden" name="type" id="wechatType" value="2">
                                            <input type="hidden" name="name" id="wechatName" value="">
                                            <input class="layui-btn" onclick="savePayConfig($('#wechatForm'))" type="button" style="width:200px;" value="更新微信APP支付配置">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <!--<form id="otherForm" class="layui-form layui-form-pane">
                            <div class="layui-col-md12"></div>
                        </form>-->
                        <form id="tlFrom" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                            <div class="layui-col-md6" style="padding: 3px">
                                <div class="layui-card" style="border: 1px solid #d0cbcb;">
                                    <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>通&nbsp;&nbsp;&nbsp;联&nbsp;&nbsp;&nbsp;</strong></span></div>
                                    <div class="layui-card-body" style="height:400px">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">充值是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isTlPay">
                                                <input type="radio" name="status" id="isTlPayTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="status" id="isTlPayFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">提现是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isTlWithdraw">
                                                <input type="radio" name="withdrawStatus" id="isTlWithdrawTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="withdrawStatus" id="isTlWithdrawFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">支付APP ID</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appId" id="appIdTl" value="" placeholder="请输入通联支付APP ID" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">支付MD5 KEY</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appKey" id="appKeyTl" value="" placeholder="请输入通联支付MD5 KEY" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">支付商户ID</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="mchId" id="mchIdTl" value="" placeholder="请输入通联支付商户号" class="layui-input">
                                                </div>
                                            </div>
                                            <div class="layui-inline"style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">充值回调IP/域名</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="payCallBackUrl" id="payCallBackUrlTl" value="" placeholder="输入充值回调IP/域名【包含http://】" class="layui-input" >
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">代付用户名</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="withdrawAppId" id="withdrawAppIdTl" value="" placeholder="请输入通联代付用户名" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">代付密码</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="withdrawAppKey" id="withdrawAppKeyTl" value="" placeholder="请输入通联代付密码" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">代付商户ID</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="withdrawMchId" id="withdrawMchIdTl" value="" placeholder="请输入通联代付商户号" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item" style="padding-top: 20px;">
                                            <input type="hidden" name="id" id="tlId" value="">
                                            <input type="hidden" name="type" id="tlType" value="5">
                                            <input type="hidden" name="name" id="tlName" value="">
                                            <input class="layui-btn" onclick="savePayConfig($('#tlFrom'))" type="button" style="width:200px;" value="更新通联支付配置">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <!--<form id="tlWithdrawFrom">
                            <div class="layui-col-md6">
                                <div class="layui-card" style="border: 1px solid #d0cbcb;">
                                    <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>通&nbsp;&nbsp;&nbsp;联&nbsp;&nbsp;&nbsp;代&nbsp;&nbsp;&nbsp;付</strong></span></div>
                                    <div class="layui-card-body" style="height:400px">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isTlWithdraw">
                                                <input type="radio" name="status" id="isTlWithdrawTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="status" id="isTlWithdrawFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline">
                                                <label class="layui-form-label">用户名</label>
                                                <div class="layui-input-inline">
                                                    <input type="text" name="appId" id="appIdTlWithdraw" value="" placeholder="请输入通联代付用户名" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline">
                                                <label class="layui-form-label">密码</label>
                                                <div class="layui-input-inline">
                                                    <input type="text" name="appKey" id="appKeyTlWithdraw" value="" placeholder="请输入通联代付密码" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline">
                                                <label class="layui-form-label">商户ID</label>
                                                <div class="layui-input-inline">
                                                    <input type="text" name="mchId" id="mchIdTlWithdraw" value="" placeholder="请输入通联代付商户号" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item" style="padding-top: 20px;">
                                            <input type="hidden" name="id" id="tlWithdrawId" value="">
                                            <input type="hidden" name="tlWithdrawStatus" id="tlWithdrawStatus" value="">
                                            <input type="hidden" name="type" id="tlWithdrawType" value="6">
                                            <input type="hidden" name="name" id="tlWithdrawName" value="">
                                            <input class="layui-btn" onclick="savePayConfig($('#tlWithdrawFrom'))" type="button" style="width:200px;" value="更新通联代付配置">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>-->
                        <form id="yunFrom" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                            <div class="layui-col-md6" style="padding: 3px">
                                <div class="layui-card" style="border: 1px solid #d0cbcb;">
                                    <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>云&nbsp;&nbsp;&nbsp;支&nbsp;&nbsp;&nbsp;付</strong></span></div>
                                    <div class="layui-card-body" style="height:400px">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">充值是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isYunPay">
                                                <input type="radio" name="status" id="isYunPayTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="status" id="isYunPayFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <!--<div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">提现是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isYunWithdraw">
                                                <input type="radio" name="withdrawStatus" id="isYunWithdrawTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="withdrawStatus" id="isYunWithdrawFalse" value="2" title="否" >
                                            </div>
                                        </div>-->
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">支付APP ID</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appId" id="appIdYun" value="" placeholder="请输入云支付APP ID" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">支付API KEY</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appKey" id="appKeyYun" value="" placeholder="请输入云支付API KEY" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">充值回调IP/域名</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="payCallBackUrl" id="payCallBackUrlYun" value="" placeholder="输入充值回调IP/域名【包含http://】" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">相关接口IP/域名</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="payApiUrl" id="payApiUrlYun" value="" placeholder="输入相关接口IP/域名【包含http://】" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 136px;">应用私钥</label>
                                            <div class="layui-input-block" style="margin-left: 136px;">
                                                <input type="text" name="privateKey" id="privateKeyYun" value="" autocomplete="off" placeholder="请输入应用私钥" class="layui-input" style="width: 625px;">
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 136px;">云支付公钥</label>
                                            <div class="layui-input-block" style="margin-left: 136px;">
                                                <input type="text" name="publicKey" id="publicKeyYun" value="" autocomplete="off" placeholder="云支付公钥" class="layui-input" style="width: 625px;">
                                            </div>
                                        </div>
                                        <div class="layui-form-item" style="padding-top: 20px;">
                                            <input type="hidden" name="id" id="yunId" value="">
                                            <input type="hidden" name="type" id="yunType" value="6">
                                            <input type="hidden" name="name" id="yunName" value="">
                                            <input type="hidden" name="withdrawStatus" value="2">
                                            <input class="layui-btn" onclick="savePayConfig($('#yunFrom'))" type="button" style="width:200px;" value="更新云支付配置">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <!--<form id="jzFrom" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                            <div class="layui-col-md6" style="padding: 3px">
                                <div class="layui-card" style="border: 1px solid #d0cbcb;">
                                    <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>金&nbsp;&nbsp;&nbsp;钻&nbsp;&nbsp;&nbsp;支&nbsp;&nbsp;&nbsp;付</strong></span></div>
                                    <div class="layui-card-body" style="height:400px">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">充值是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isJzPay">
                                                <input type="radio" name="status" id="isJzPayTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="status" id="isJzPayFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">商户标识</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appId" id="appIdJz" value="" placeholder="请输入金钻商户标识" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;"> API 密钥</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="apiKey" id="apiKeyJz" value="" placeholder="请输入金钻商户API秘钥" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">充值回调IP/域名</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="payCallBackUrl" id="payCallBackUrlJz" value="" placeholder="输入充值回调IP/域名【包含http://】" class="layui-input" >
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item" style="padding-top: 20px;">
                                            <input type="hidden" name="id" id="jzId" value="">
                                            <input type="hidden" name="type" id="jzType" value="6">
                                            <input type="hidden" name="name" id="jzName" value="">
                                            <input type="hidden" name="withdrawStatus" value="2">
                                            <input class="layui-btn" onclick="savePayConfig($('#jzFrom'))" type="button" style="width:200px;" value="更新金钻配置">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>-->
                        <form id="hmFrom" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                            <div class="layui-col-md6" style="padding: 3px">
                                <div class="layui-card" style="border: 1px solid #d0cbcb;">
                                    <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>黑&nbsp;&nbsp;&nbsp;马&nbsp;&nbsp;&nbsp;支&nbsp;&nbsp;&nbsp;付</strong></span></div>
                                    <div class="layui-card-body" style="height:400px">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">充值是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isHmPay">
                                                <input type="radio" name="status" id="isHmPayTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="status" id="isHmPayFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">商户标识</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appId" id="appIdHm" value="" placeholder="请输入黑马商户标识" class="layui-input" >
                                                </div>
                                            </div>
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;"> API 密钥</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="apiKey" id="apiKeyHm" value="" placeholder="请输入黑马商户API秘钥" class="layui-input">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">充值回调IP/域名</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="payCallBackUrl" id="payCallBackUrlHm" value="" placeholder="输入充值回调IP/域名【包含http://】" class="layui-input" >
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item" style="padding-top: 20px;">
                                            <input type="hidden" name="id" id="hmId" value="">
                                            <input type="hidden" name="type" id="hmType" value="6">
                                            <input type="hidden" name="name" id="hmName" value="">
                                            <input type="hidden" name="withdrawStatus" value="2">
                                            <input class="layui-btn" onclick="savePayConfig($('#hmFrom'))" type="button" style="width:200px;" value="更新黑马配置">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <form id="weiFrom" class="layui-form layui-form-pane" style="padding: 0px;margin: 10px;">
                            <div class="layui-col-md6" style="padding: 3px">
                                <div class="layui-card" style="border: 1px solid #d0cbcb;">
                                    <div class="layui-card-header" style="border-bottom: 1px solid #d0cbcb;"><span style="font-size: 18px" ><strong>微&nbsp;&nbsp;&nbsp;钱&nbsp;&nbsp;&nbsp;包&nbsp;&nbsp;&nbsp;支&nbsp;&nbsp;&nbsp;付</strong></span></div>
                                    <div class="layui-card-body" style="height:400px">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">充值是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isWeiPay">
                                                <input type="radio" name="status" id="isWeiPayTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="status" id="isWeiPayFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <label class="layui-form-label" style="width: 207px;">提现是否启用</label>
                                            <div class="layui-input-block" style="margin-left: 240px;" id="isWeiWithdraw">
                                                <input type="radio" name="withdrawStatus" id="isWeiWithdrawTrue" value="1" title="是" checked="checked">
                                                <input type="radio" name="withdrawStatus" id="isWeiWithdrawFalse" value="2" title="否" >
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 378px;">
                                                <label class="layui-form-label" style="width: 136px;">商户ID</label>
                                                <div class="layui-input-inline" style="width: 233px;">
                                                    <input type="text" name="appId" id="appIdWei" value="" placeholder="请输入微钱包商户ID" class="layui-input" >
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item">
                                            <div class="layui-inline" style="width: 458px;">
                                                <label class="layui-form-label" style="width: 167px;">微钱包回调IP/域名</label>
                                                <div class="layui-input-inline" style="width: 274px;">
                                                    <input type="text" name="payCallBackUrl" id="payCallBackUrlWei" value="" placeholder="输入微钱包回调IP/域名【包含http://】" class="layui-input" >
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-form-item" style="padding-top: 20px;">
                                            <input type="hidden" name="id" id="weiId" value="">
                                            <input type="hidden" name="type" id="weiType" value="9">
                                            <input type="hidden" name="name" id="weiName" value="">
                                            <input class="layui-btn" onclick="savePayConfig($('#weiFrom'))" type="button" style="width:200px;" value="更新微钱包配置">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="layui-tab-item" style="margin-bottom: 10px;padding: 5px;">
                    <form class="layui-form layui-form-pane">
                        <table class="layui-table mag0">
                            <colgroup>
                                <col width="25%">
                                <col width="45%">
                                <col>
                            </colgroup>
                            <thead>
                            <tr>
                                <th>参数说明</th>
                                <th>参数值</th>
                                <th>变量名</th>
                                <th>参数说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>是否开放钱包</td>
                                <td>
                                    <select class="layui-input-inline displayRedPacket">
                                        <option value="1">开启</option>
                                        <option value="0">关闭</option>
                                    </select>
                                </td>
                                <td pc>displayRedPacket</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>下级充值返利比例</td>
                                <td>
                                    <div style="width: 95%;float: left"><input type="text" onkeyup="value=positiveIFloatConversion(this.value)" class="layui-input rechargeRate" placeholder="请输入充值返利费率"></div>
                                    <span style="float: right;margin-top: 10px">%</span>
                                </td>
                                <td>rechargeRate</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>提现费率</td>
                                <td>
                                    <div style="width: 95%;float: left"><input type="text" onkeyup="value=positiveIFloatConversion(this.value)" class="layui-input transferRate" placeholder="请输入提现费率"></div>
                                    <span style="float: right;margin-top: 10px">%</span>
                                </td>
                                <td>transferRate</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>提现最小金额</td>
                                <td><input type="text" onkeyup="value=positiveIFloatConversion(this.value)" class="layui-input minTransferAmount" placeholder="请输入最小提现金额"></td>
                                <td>minTransferAmount</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>是否提现到后台管理</td>
                                <td>
                                    <select class="layui-input-inline isWithdrawToAdmin" lay-filter="isWithdrawToAdmin" id="isWithdrawToAdmin">
                                        <option value="1">开启</option>
                                        <option value="0">关闭</option>
                                    </select>
                                </td>
                                <td pc>isWithdrawToAdmin</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>发送红包最大金额</td>
                                <td><input type="text" onkeyup="value=positiveIFloatConversion(this.value)" class="layui-input maxSendRedPagesAmount" placeholder="请输入发送红包最大金额"></td>
                                <td>maxSendRedPagesAmount</td>
                                <td>发送红包最大金额默认为【500】，最大上限为【99999】</td>
                            </tr>
                            <tr id="withdrawToAdmin">
                                <td colspan="4">
                                    <div>
                                        <div>设置提现通道<button class="layui-btn addNewWithdraw" id="addNewWithdraw">新增</button></div>
                                        <div class="" style="margin-bottom: 10px;padding: 5px;" id="withdrawToAdmin1">
                                            <form class="layui-form layui-form-pane">
                                                <table class="layui-table mag1">
                                                    <colgroup>
                                                        <col width="25%">
                                                        <col width="45%">
                                                        <col>
                                                    </colgroup>
                                                    <thead>
                                                    <tr>
                                                        <th>提现通道</th>
                                                        <th>是否开启</th>
                                                        <th>操作</th>
                                                        <th>参数说明</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td id="isWithdrawToAlipayName">提现至支付宝</td>
                                                            <td>
                                                                <select class="layui-input-inline" id="isWithdrawToAlipay" lay-filter="withdrawWay">
                                                                    <option value="1">开启</option>
                                                                    <option value="0">关闭</option>
                                                                </select>
                                                            </td>
                                                            <td pc></td>
                                                            <td id="isWithdrawToAlipayId"></td>
                                                        </tr>
                                                        <tr>
                                                            <td id="isWithdrawToBankCardName">提现至银行卡</td>
                                                            <td>
                                                                <select class="layui-input-inline" id="isWithdrawToBankCard" lay-filter="withdrawWay">
                                                                    <option value="1">开启</option>
                                                                    <option value="0">关闭</option>
                                                                </select>
                                                            </td>
                                                            <td pc></td>
                                                            <td id="isWithdrawToBankCardId"></td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </form>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>是否开通微钱包支付</td>
                                <td>
                                    <select class="layui-input-inline isWeiBaoStatus" id="isWeiBaoStatus" lay-filter="isWeiBaoStatus">
                                        <option value="1" >开启</option>
                                        <option value="0" >关闭</option>
                                    </select>
                                </td>
                                <td pc>isWeiBaoStatus</td>
                                <td></td>
                            </tr>
                            <tr id="weiBaoTransferRate">
                                <td>微钱包提现费率</td>
                                <td>
                                    <div style="width: 95%;float: left"><input type="text" onkeyup="value=positiveIFloatConversion(this.value)" class="layui-input weiBaoTransferRate" placeholder="请输入微钱包提现费率"></div>
                                    <span style="float: right;margin-top: 10px">%</span>
                                </td>
                                <td>weiBaoTransferRate</td>
                                <td></td>
                            </tr>
                            <tr id="weiBaoMinTransferAmount">
                                <td>微钱包提现最小金额</td>
                                <td><input type="text" onkeyup="value=positiveIFloatConversion(this.value)" class="layui-input weiBaoMinTransferAmount" placeholder="请输入微钱包最小提现金额"></td>
                                <td>weiBaoMinTransferAmount</td>
                                <td></td>
                            </tr>
                            <tr id="weiBaoMaxTransferAmount">
                                <td>微钱包提现最大金额</td>
                                <td><input type="text" onkeyup="value=positiveIFloatConversion(this.value)" class="layui-input weiBaoMaxTransferAmount" placeholder="请输入微钱包最大提现金额"></td>
                                <td>weiBaoMaxTransferAmount</td>
                                <td></td>
                            </tr>
                            <tr id="weiBaoMaxRedPacketAmount">
                                <td>微钱包红包最大金额</td>
                                <td><input type="text" onkeyup="value=positiveIFloatConversion(this.value)" class="layui-input weiBaoMaxRedPacketAmount" placeholder="请输入微钱包红包最大金额"></td>
                                <td>weiBaoMaxRedPacketAmount</td>
                                <td></td>
                            </tr>
                            </tbody>
                        </table>
                    </form>

                    <div class="magt10 layui-center">
                        <div class="layui-input-block" style="margin-left: 0px">
                            <input type="hidden" id="rateId">
                            <button class="layui-btn save" lay-submit="" lay-filter="rateConfig">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</body>
<script type="text/javascript" src="/pages/common/layui/layui.js"></script>
<script type="text/javascript" src="/pages/common/jquery/jquery-1.11.3.min.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="./js/common.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="text/javascript" src="./js/system_pay_config.js"></script>
</html>