<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>APP充值列表</title>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">
<body>
	<div class="layui-row">
		<div id="back" class="layui-col-md1">&nbsp;</div>
		<input id="pageCount" type="" name="" style="display: none">
		<div id="appRecharge_div" class="layui-col-md10">
			<div class="appRecharge_btn_div" style="margin-top: 2%">
				<input id="userId" type="text" name="" class="layui-input" style="width: 15%;display: inline" placeholder="充值用户Id">
				<form class="layui-form" action="" style="float: left">
					<div class="layui-form-item">
						<select id="complaint_select" class="layui-select" lay-verify="required" value="">
							<option value="0">请选择充值入口</option>
							<option value="1">APP充值</option>
							<option value="3">后台充值</option>
							<option value="14">签到红包</option>
						</select>
					</div>
				</form>
				<button class="layui-btn search_live">搜索</button>
				<div class="layui-form-item timeComponent" style="display: inline">
					<div class="layui-inline">
						<label class="layui-form-label" style="padding: 9px 8px;width: 150px">充值的时间范围：</label>
						<div class="layui-input-inline">
							<input class="layui-input" id="appRechargeMsgDate" placeholder="请选择时间范围" type="text">
						</div>
					</div>
				</div>
			</div>
			<div id="appRecharge" class="layui-card" style="margin-top: 1%">
				<div class="layui-card-header">APP充值列表</div>
				<div class="layui-card-body">
					<table id="appRecharge_table" lay-filter="appRecharge_table">
					</table>
					<div style="margin-left: 50%">
						<span>充值总金额：<cite class="current_total"></cite>  元</span>&nbsp;&nbsp;
						<span>创建充值金额：<cite class="currentCreate_total"></cite>  元</span>&nbsp;&nbsp;
						<span>支付完成总金额：<cite class="currentDone_total"></cite>  元</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
	<script type="text/javascript" src="/pages/common/jquery/jquery.md5.js"></script>
	<script type="text/javascript" src="./js/common.js"></script>
	<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
	<script type="text/javascript" src="./js/console_ui.js"></script>
	<script type="text/javascript" src="./js/appRecharge.js"></script>
	<!--<script type="text/javascript" src="/pages/js/console_init.js"></script>-->

	<!--操作-->
	<script type="text/html" id="userListBar">
		{{#  if(d.type == 1){ }}
		{{#  if(d.status == 0){ }}
		<a class="layui-btn layui-btn-danger layui-btn-xs locking" lay-event="pass">审核通过</a>
		<a class="layui-btn layui-btn-danger layui-btn-xs cancelLocking" lay-event="refuse">审核拒绝</a>
		{{#  }else if(d.status == 1){  }}
		<a class="layui-btn-xs locking" lay-event="locking">审核通过</a>
		{{#  }else if(d.status == 4){  }}
		<a class="layui-btn-xs locking" lay-event="locking">审核拒绝</a>
		{{#  } }}
		{{#  }else{  }}
		<a class="layui-btn-xs cancelLocking">审核通过</a>
		{{#  } }}
	</script>


</body>
</html>