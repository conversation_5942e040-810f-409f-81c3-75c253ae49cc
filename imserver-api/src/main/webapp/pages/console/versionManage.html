<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="format-detection" content="telephone=no">
<link rel="stylesheet" href="/pages/common/layui/css/layui.css" media="all" />
<link rel="stylesheet" href="./css/public.css" media="all" />

<title>版本管理</title>
</head>
<body>
<div class="layui-tab layui-tab-brief" lay-filter="demo">
  <div class="layui-tab-content layui-row">
  	<div id="back" class="layui-col-md1">&nbsp;</div>
  	<!-- 版本管理 -->
    <div id="version" class="layui-tab-item layui-tab-card layui-show layui-col-md10">
    	<div id="versionList">
    		<table id="version_list" lay-filter="version_list">
    		</table>
    		<div class="magt10 layui-center">
				<div class="layui-input-block" style="margin-left: 0px;margin-bottom: 13px;">
					<button onclick="versionManage.addVersion()" class="layui-btn save" lay-submit="" lay-filter="systemConfig">添加版本</button>
			    </div>
			</div>
    	</div>
		<div id="addVersion" style="display: none;">
			<form class="layui-form" id="addVersionFrom">
				<table class="layui-table mag0">
					<colgroup>
						<col width="25%">
						<col width="45%">
						<col>
					</colgroup>
					<thead>
					<tr>
						<th>参数说明</th>
						<th>参数值</th>
						<th>变量名</th>
						<th>参数说明</th>
					</tr>
					</thead>
					<tbody>
					<input type="hidden" name="" id="version_id">
					<tr>
						<td>设备</td>
						<td>
							<form class="layui-form">
								<select class="layui-input-inline device">
									<option value="0">安卓</option>
									<option value="1">IOS</option>
									<option value="2">AppStore</option>
								</select>
							</form>
						</td>
						<td>device</td>
						<td></td>
					</tr>
					<tr>
						<td>APP名称</td>
						<td>
							<input type="text" id="projectName" class="layui-input projectName" lay-verify="required" placeholder="请输入APP名称">
						</td>
						<td>projectName</td>
						<td></td>
					</tr>
					<tr>
						<td>版本名称</td>
						<td>
							<input type="text" id="versionName" class="layui-input versionName" lay-verify="required" placeholder="请输入APP版本名称【如:1.2.3】">
						</td>
						<td>versionName</td>
						<td></td>
					</tr>
					<tr>
						<td>版本号</td>
						<td>
							<input type="number" id="versionNum" class="layui-input versionNum" lay-verify="required" placeholder="请输入APP版本号">
						</td>
						<td>versionNum</td>
						<td></td>
					</tr>
					<tr>
						<td>更新内容</td>
						<td>
							<textarea name="updateContent" id="updateContent" class="layui-textarea updateContent" lay-verify="required" placeholder="请输入更新内容" ></textarea>
						</td>
						<td>updateContent</td>
						<td></td>
					</tr>
					<tr>
						<td>三方更新URL</td>
						<td>
							<input type="text" id="thirdLoadURL" class="layui-input thirdLoadURL" placeholder="请输入三方更新URL">
						</td>
						<td>thirdLoadURL</td>
						<td></td>
					</tr>
<!--					<tr id="oldVersionNameTr">-->
<!--						<td>上一次版本名称</td>-->
<!--						<td>-->
<!--							<input type="text" id="oldVersionName" class="layui-input oldVersionName" placeholder="无" readonly>-->
<!--						</td>-->
<!--						<td></td>-->
<!--						<td></td>-->
<!--					</tr>-->
<!--					<tr  id="oldVersionNumTr">-->
<!--						<td>上一次版本号</td>-->
<!--						<td>-->
<!--							<input type="number" id="oldVersionNum" class="layui-input oldVersionNum" placeholder="无" readonly>-->
<!--						</td>-->
<!--						<td></td>-->
<!--						<td></td>-->
<!--					</tr>-->
					<tr>
						<td>版本状态</td>
						<td>
							<select class="layui-input-inline versionStatus">
								<option value="1">启用</option>
								<option value="0">禁用</option>
							</select>
						</td>
						<td>versionStatus</td>
						<td></td>
					</tr>
					</tbody>
				</table>
				<div class="magt10 layui-center">
					<div class="layui-input-block" style="margin-left: 0px;padding-bottom: 13px;">
						<button lay-submit="" lay-filter="systemConfig" class="layui-btn" type="button" onclick="versionManage.commit_version()">保存</button>
						<button class="layui-btn layui-btn-primary" type="button" onclick="versionManage.back()">返回</button>
					</div>
				</div>
			</form>
		</div>
		<div id="appVersion" style="display: none;">
			<input type="hidden" id="appType">
			<table class="layui-table mag0">
				<colgroup>
					<col width="25%">
					<col width="25%">
					<col width="20%">
					<col >
				</colgroup>
				<tbody>
				<input type="hidden" name="" id="appVersionId">
				<tr>
					<td>APP名称</td>
					<td>
						<input type="text" id="appProjectName" class="layui-input" readonly>
					</td>
					<td>版本名称</td>
					<td>
						<input type="text" id="appVersionName" class="layui-input" readonly>
					</td>
				</tr>
				<tr>
					<td>版本号</td>
					<td>
						<input type="number" id="appVersionNum" class="layui-input" readonly>
					</td>
					<td>三方更新URL</td>
					<td>
						<input type="text" id="appThirdLoadURL" class="layui-input" readonly>
					</td>
				</tr>
				<tr>
					<td>更新内容</td>
					<td>
						<textarea name="updateContent" id="appUpdateContent" class="layui-textarea" readonly></textarea>
					</td>
					<td>版本状态</td>
					<td>
						<input type="text" id="versionStatus" class="layui-input" readonly>
					</td>
				</tr>
				</tbody>
			</table>
			<table class="layui-table mag0">
				<colgroup>
					<col width="25%">
					<col width="45%">
					<col>
				</colgroup>
				<thead>
					<tr>
						<th>参数说明</th>
						<th>参数值</th>
						<th>变量名</th>
						<th>参数说明</th>
					</tr>
				</thead>
				<tbody id="androidInfo" style="display: none;">
					<tr>
						<td>安卓安装包</td>
						<td>
							<button class="layui-btn" onclick="versionManage.selectUpdateApk()">选择APK文件</button>
							<span id="apkPath_update"></span>
							<form id="uploadApkPath_update" name="uploadApkPath_update" action="" method="post" enctype="multipart/form-data" style="display: none;">
								<input id="uploadApk_update" type="file" accept=".apk" name="file" onchange="versionManage.updateUploadApk()">
							</form>
						</td>
						<td>apkLoadUrl</td>
						<td></td>
					</tr>
					<tr>
					<tr>
						<td>APP下载链接</td>
						<td>
							<input type="text" id="apkDownloadUrl" class="layui-input apkDownloadUrl" placeholder="请输入APP下载链接">
						</td>
						<td>apkDownloadUrl</td>
						<td></td>
					</tr>
					</tr>
					<tr>
						<td>强制更新</td>
						<td>
							<form class="layui-form" >
								<select id="androidInfoForceStatus" class="layui-input-inline forceStatus">
									<option value="1">是</option>
									<option value="0">否</option>
								</select>
							</form>
						</td>
						<td>versionStatus</td>
						<td></td>
					</tr>
				</tbody>
				<tbody id="IosInfo" style="display: none;">
					<tr>
						<td>APP下载链接</td>
						<td>
							<input type="text" id="iosDownloadUrl" class="layui-input iosDownloadUrl" placeholder="请输入APP下载链接">
						</td>
						<td>iosDownloadUrl</td>
						<td></td>
					</tr>
					<tr>
						<td>IOS企业安装包</td>
						<td>
							<button class="layui-btn" onclick="versionManage.selectUpdateIosIpa()">选择IPA文件</button>
							<span id="iosIpaPath_update"></span>
							<form id="uploadIosIpaPath_update" name="uploadIosIpaPath_update" action="" method="post" enctype="multipart/form-data" style="display: none;">
								<input id="uploadIosIpa_update" type="file" accept=".ipa" name="file" onchange="versionManage.updateUploadIosIpa()">
							</form>
						</td>
						<td>ipaLoadUrl</td>
						<td></td>
					</tr>
					<tr>
						<td>IOS BundleId</td>
						<td>
							<input type="text" id="bundleId" class="layui-input bundleId" lay-verify="required" placeholder="请输入IOS APP BundleId">
						</td>
						<td>bundleId</td>
						<td></td>
					</tr>
					<tr>
						<td>IOS企业包安装图片</td>
						<td>
							<button class="layui-btn" onclick="versionManage.selectUpdateIosDisplay()">选择图片文件</button>
							<span id="iosDisplayPath_update"></span>
							<form id="uploadIosDisplayPath_update" name="uploadIosDisplayPath_update" action="" method="post" enctype="multipart/form-data" style="display: none;">
								<input id="uploadIosDisplay_update" type="file" accept="image/png" name="file" onchange="versionManage.updateUploadIosDisplay()">
							</form>
						</td>
						<td>displayImg</td>
						<td>下载和安装过程中显示的 57 x 57 像素 PNG 图像</td>
					</tr>
					<tr>
						<td>IOS企业包iTunes图片</td>
						<td>
							<button class="layui-btn" onclick="versionManage.selectUpdateIosFull()">选择图片文件</button>
							<span id="iosFullPath_update"></span>
							<form id="uploadFullPath_update" name="uploadIosFullPath_update" action="" method="post" enctype="multipart/form-data" style="display: none;">
								<input id="uploadIosFull_update" type="file" accept="image/png" name="file" onchange="versionManage.updateUploadIosFull()">
							</form>
						</td>
						<td>fullImg</td>
						<td>在 iTunes 中表示应用程序的 512 x 512 像素 PNG 图像</td>
					</tr>
					<tr>
						<td>强制更新</td>
						<td>
							<form class="layui-form">
								<select id="IosInfoForceStatus" class="layui-input-inline forceStatus">
									<option value="1">是</option>
									<option value="0">否</option>
								</select>
							</form>
						</td>
						<td>versionStatus</td>
						<td></td>
					</tr>
				</tbody>
				<tbody id="appStoreInfo" style="display: none;">

						<tr>
							<td>AppStore更新URL</td>
							<td>
								<form class="layui-form">
									<input type="text" id="appStoreLoadUrl" class="layui-input appStoreLoadUrl" lay-verify="required" placeholder="请输入AppStore下载URL">
								</form>
							</td>
							<td>appStoreLoadUrl</td>
							<td></td>
						</tr>
						<tr>
							<td>强制更新</td>
							<td>
								<form class="layui-form">
									<select id="appStoreInfoForceStatus" class="layui-input-inline forceStatus">
										<option value="1">是</option>
										<option value="0">否</option>
									</select>
								</form>
							</td>
							<td>versionStatus</td>
							<td></td>
						</tr>

				</tbody>
			</table>
			<div class="magt10 layui-center">
				<div class="layui-input-block" style="margin-left: 0px;padding-bottom: 13px;">
					<button lay-submit="" lay-filter="systemConfig" class="layui-btn" type="button" onclick="versionManage.commit_app_version()">保存</button>
					<button class="layui-btn layui-btn-primary" type="button" onclick="versionManage.appBack()">返回</button>
				</div>
			</div>
		</div>
	</div>
  </div>
</div>
<input type="hidden" id="versionId" value="">
<script type="text/html" id="versionList_toolbar">
	{{#  if(d.versionStatus == 1){ }}
		{{#  if(d.device == 0){ }}
			{{#  if(d.androidMsg == null){ }}
				<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="androidVersion">发布安卓版本</a>
			{{#  }else{  }}
				<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="androidVersion">修改安卓版本</a>
			{{#  } }}
		{{#  }else if(d.device == 1){ }}
			{{#  if(d.iosMsg == null){ }}
				<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="iosVersion">发布IOS企业包</a>
			{{#  }else{  }}
				<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="iosVersion">修改IOS企业包</a>
			{{#  } }}
		{{#  }else{ }}
			{{#  if(d.appStoreMsg == null){ }}
				<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="appStoreVersion">发布AppStore</a>
			{{#  }else{  }}
				<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="appStoreVersion">修改AppStore</a>
			{{#  } }}
		{{#  } }}
		<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="updateStatusNo">禁用</a>
	{{#  }else{  }}
		<a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="deleteInfo">删除</a>
		<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="updateInfo">修改</a>
		<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="updateStatusYes">启用</a>
	{{#  } }}
</script>

<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="/pages/console/js/versionManage.js"></script>
<script type="text/javascript">
</script>
</body>
</html>