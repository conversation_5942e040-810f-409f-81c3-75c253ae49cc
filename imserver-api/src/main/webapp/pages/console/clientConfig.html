<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="format-detection" content="telephone=no">
	<link rel="stylesheet" href="/pages/common/layui/css/layui.css" media="all" />
	<link rel="stylesheet" href="./css/public.css" media="all" />
	<link href="./css/scrollbar.css" rel="stylesheet">
	<title>客户端系统配置</title>
</head>
<body class="childrenBody scrollbar_zdy">
<div  class="layui-container" style="width: 90%;margin-top: 30px;">
	<form class="layui-form">
		<table class="layui-table mag0">
			<colgroup>
				<col width="25%">
				<col width="45%">
				<col>
		    </colgroup>
		    <thead>
		    	<tr>
		    		<th>参数说明</th>
		    		<th>参数值</th>
		    		<th>变量名</th>
		    		<th>参数说明</th>
		    	</tr>
		    </thead>
		    <tbody>
		    	<tr>
		    		<td>XMPP 主机 host</td>
		    		<td>
		    			<input type="text" class="layui-input XMPPHost" lay-verify="required" placeholder="请输入XMPP主机域名或者ip">
		    		</td>
		    		<td pc>XMPPHost</td>
		    		<td></td>
		    	</tr>
		    	<tr>
		    		<td>XMPP 虚拟域名</td>
		    		<td>
		    			<input type="text" class="layui-input XMPPDomain" lay-verify="required" placeholder="请输入XMPP主机域名或者ip">
		    		</td>
		    		<td pc>XMPPDomain</td>
		    		<td></td>
		    	</tr>
				<tr>
					<td>PC XMPP 主机 host</td>
					<td>
						<input type="text" class="layui-input PCXMPPHost" lay-verify="required" placeholder="请输入PC XMPP主机域名或者ip">
					</td>
					<td pc>PCXMPPHost</td>
					<td>PC端链接的XMPP主机host</td>
				</tr>
				<tr>
					<td>PC XMPP 虚拟域名</td>
					<td>
						<input type="text" class="layui-input PCXMPPDomain" lay-verify="required" placeholder="请输入PC XMPP主机域名或者ip">
					</td>
					<td pc>PCXMPPDomain</td>
					<td>PC端链接的XMPP虚拟域名</td>
				</tr>
				<tr>
					<td>是否启用消息多节点</td>
					<td>
						<select class="layui-input-inline isNodesStatus">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>
					</td>
					<td pc>isNodesStatus</td>
					<td></td>
				</tr>
		    	<tr>
		    		<td>接口URL</td>
		    		<td><input type="text" class="layui-input apiUrl" placeholder=""></td>
		    		<td pc>apiUrl</td>
		    		<td></td>
		    	</tr>
		    	<tr>
		    		<td>头像下载URL</td>
		    		<td><input type="text" class="layui-input downloadAvatarUrl" placeholder=""></td>
		    		<td pc>downloadAvatarUrl</td>
		    		<td></td>
		    	</tr>
		    	<tr>
		    		<td>资源下载URL</td>
		    		<td><input type="text" class="layui-input downloadUrl" placeholder=""></td>
		    		<td pc>downloadUrl</td>
		    		<td></td>
		    	</tr>
		    	<tr>
		    		<td>资源上传URL</td>
		    		<td><input type="text" class="layui-input uploadUrl" placeholder=""></td>
		    		<td pc>uploadUrl</td>
		    		<td></td>
		    	</tr>
		    	<tr>
		    		<td>视频服务器URL</td>
		    		<td><input type="text" class="layui-input jitsiServer" placeholder=""></td>
		    		<td pc>jitsiServer</td>
		    		<td></td>
		    	</tr>
		    	<tr style="display:none;">
		    		<td>直播服务器URL</td>
		    		<td><input type="text" class="layui-input liveUrl" placeholder=""></td>
		    		<td pc>liveUrl</td>
		    		<td></td>
		    	</tr>
				<tr>
					<td>IOS应用AppleId</td>
					<td><input type="text" class="layui-input appleId" placeholder=""></td>
					<td pc>appleId </td>
					<td></td>
				</tr>
				<tr>
					<td>公司下载页网址</td>
					<td><input type="text" class="layui-input website" placeholder=""></td>
					<td pc>website</td>
					<td>公司下载页网址,用于生成二维码链接</td>
				</tr>
				<tr>
					<td>引导页下载页网址</td>
					<td><input type="text" class="layui-input guideWebsite" placeholder=""></td>
					<td pc>guideWebsite</td>
					<td>引导页下载页网址,用于在浏览器中跳转到引导页下载应用</td>
				</tr>
				<tr>
					<td>头部导航栏背景图</td>
					<td><input type="text" class="layui-input headBackgroundImg" placeholder=""></td>
					<td pc>headBackgroundImg</td>
					<td>发现页的顶部导航图</td>
				</tr>
				<!--<tr>
		    		<td>是否开放IOS零钱</td>
		    		<td>
       				 	<select class="layui-input-inline displayRedPacket">
          					<option value="1">开启</option>
  							<option value="0">关闭</option>
        				</select>
		    		</td>
		    		<td pc>displayRedPacket</td>
		    		<td></td>
		    	</tr>
				<tr>
					<td>是否提现到后台管理</td>
					<td>
						<select class="layui-input-inline isWithdrawToAdmin">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>
					</td>
					<td pc>isWithdrawToAdmin</td>
					<td></td>
				</tr>
				<tr id="minWithdrawToAdminTr">
					<td>单笔最小提现到后台金额</td>
					<td><input type="text" onkeyup="value=positiveIFloatConversion(this.value)" class="layui-input minWithdrawToAdmin" placeholder=""></td>
					<td>minWithdrawToAdmin</td>
					<td></td>
				</tr>-->
				<tr>
					<td>是否开放用户签到红包</td>
					<td>
						<select class="layui-input-inline isUserSignRedPacket">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>
					</td>
					<td pc>isUserSignRedPacket</td>
					<td>开启或关闭该功能必须在服务配置中同时开启或关闭</td>
				</tr>
		    	<tr>
		    		<td>是否启用已读消息回执</td>
		    		<td>
		    			<select class="layui-input-inline isOpenReadReceipt">
		    				<option value="1">开启</option>
		    				<option value="0">关闭</option>
		    			</select>
		    		</td>
		    		<td>isOpenReadReceipt</td>
		    		<td></td>
		    	</tr>
		    	<tr>
		    		<td>启用手机联系人</td>
		    		<td>
		    			<select class="layui-input-inline showContactsUser">
          					<option value="1">开启</option>
  							<option value="0">关闭</option>
        				</select>
		    		</td>
		    		<td pc>showContactsUser</td>
		    		<td></td>
		    	</tr>
				<tr>
					<td>启用个人二维码</td>
					<td>
						<select class="layui-input-inline isOpenTwoBarCode">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>
					</td>
					<td pc>isOpenTwoBarCode</td>
					<td>客户端及个人资料处是否显示二维码</td>
				</tr>
				<tr>
					<td>启用个人手机号</td>
					<td>
						<select class="layui-input-inline isOpenTelnum">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>
					</td>
					<td pc>isOpenTelnum</td>
					<td>客户端及个人资料处是否显示手机号</td>
				</tr>
		    	<tr>
		    		<td>是否开启注册</td>
		    		<td>
		    			<select class="layui-input-inline isOpenRegister">
          					<option value="1">开启</option>
  							<option value="0">关闭</option>
        				</select>
		    		</td>
		    		<td pc>isOpenRegister</td>
		    		<td></td>
		    	</tr>
				<tr>
					<td>是否开启好友搜索功能</td>
					<td>
						<select class="layui-input-inline hideSearchByFriends">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>

					</td>
					<td pc>hideSearchByFriends</td>
					<td></td>
				</tr>

				<tr>
					<td>是否开启普通用户搜索好友</td>
					<td>
						<select class="layui-input-inline isCommonFindFriends">
							<option value="0">开启</option>
							<option value="1">关闭</option>
						</select>
					</td>
					<td pc>isCommonFindFriends</td>
					<td></td>
				</tr>

				<tr>
					<td>是否开启普通用户创建群组</td>
					<td>
						<select class="layui-input-inline isCommonCreateGroup">
							<option value="0">开启</option>
							<option value="1">关闭</option>
						</select>
					</td>
					<td pc>isCommonCreateGroup</td>
					<td></td>
				</tr>
				<tr>
					<td>是否开启阅后即焚</td>
					<td>
						<select class="layui-input-inline isDelAfterReading">
							<option value="0">开启</option>
							<option value="1">关闭</option>
						</select>
					</td>
					<td pc>isDelAfterReading</td>
					<td></td>
				</tr>
				<tr>
					<td>是否开启位置相关服务</td>
					<td>
						<select class="layui-input-inline isOpenPositionService">
							<option value="0">开启</option>
							<option value="1">关闭</option>
						</select>
					</td>
					<td pc>isOpenPositionService</td>
					<td></td>
				</tr>
				<tr>
					<td>是否启用APP发现页配置</td>
					<td>
						<select class="layui-input-inline isDiscoverStatus">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>
					</td>
					<td pc>isDiscoverStatus</td>
					<td></td>
				</tr>
				<tr>
					<td>是否启用APPTabBar配置</td>
					<td>
						<select class="layui-input-inline isTabBarStatus">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>
					</td>
					<td pc>isTabBarStatus</td>
					<td></td>
				</tr>
				<tr style="display:none">
					<td>是否启用联系客服</td>
					<td>
						<select class="layui-input-inline isEnableCusServer" id="isEnableCusServer" lay-filter="isEnableCusServer">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>
					</td>
					<td pc>isEnableCusServer</td>
					<td></td>
				</tr>
				<tr  style="display:none" id="cusServerUrl">
					<td>联系客服链接地址</td>
					<td><input type="text" class="layui-input cusServerUrl" placeholder="请输入联系客服链接地址"></td>
					<td>cusServerUrl</td>
					<td>联系客服链接地址</td>
				</tr>
				<tr>
					<td>APP热门应用界面展示项</td>
					<td>
						<div class="layui-form-item" pane="">
							<!--<label class="layui-form-label">原始复选框</label> checked="" -->
							<div class="layui-input-block">
								<input name="lifeCircle" class="lifeCircle" lay-skin="primary" title="生活圈" type="checkbox" lay-filter="appCheckbox">
								<div style="display: none;" >
									<input name="videoMeeting" class="videoMeeting" lay-skin="primary" title="视频会议" type="checkbox" lay-filter="appCheckbox">
									<input name="liveVideo" class="liveVideo" lay-skin="primary" title="视频直播" type="checkbox" lay-filter="appCheckbox">
									<input name="shortVideo" class="shortVideo" lay-skin="primary" title="短视频" type="checkbox" lay-filter="appCheckbox">
								</div>
								<input name="peopleNearby" class="peopleNearby" lay-skin="primary" title="附近人" type="checkbox" lay-filter="appCheckbox">
								<input name="scan" class="scan" lay-skin="primary" title="扫一扫" type="checkbox" lay-filter="appCheckbox">
							</div>
						</div>
					</td>
					<td>popularAPP</td>
					<td>热门应用动态展示</td>
				</tr>

				<!--<tr>
					<td>公司名称</td>
					<td><input type="text" class="layui-input companyName" placeholder=" "></td>
					<td pc>companyName</td>
					<td></td>
				</tr>

				<tr>
					<td>版权信息</td>
					<td><input type="text" class="layui-input copyright" placeholder=" "></td>
					<td pc>copyright</td>
					<td></td>
				</tr>-->

		    	<tr>
		    		<td>Android最新版本号</td>
		    		<td><input type="text" class="layui-input androidVersion" placeholder=" "></td>
		    		<td pc>androidVersion</td>
		    		<td><br>示例：101，对应版本号1.0.1</br></td>
		    	</tr>
		    	<tr>
		    		<td>Android最新下载URL</td>
		    		<td><input type="text" class="layui-input androidAppUrl" placeholder=" "></td>
		    		<td pc>androidAppUrl</td>
		    		<td></td>
		    	</tr>
		    	<tr>
		    		<td>Android更新说明</td>
		    		<td><input type="text" class="layui-input androidExplain" placeholder=" "></td>
		    		<td pc>androidExplain</td>
		    		<td></td>
		    	</tr>
		    	<tr>
		    		<td>IOS最新版本号</td>
		    		<td><input type="text" class="layui-input iosVersion" placeholder=" "></td>
		    		<td pc>iosVersion</td>
		    		<td><br>示例：101，对应版本号1.0.1</br></td>
		    	</tr>
		    	<tr>
		    		<td>IOS最新下载URL</td>
		    		<td><input type="text" class="layui-input iosAppUrl" placeholder=" "></td>
		    		<td pc>iosAppUrl</td>
		    		<td></td>
		    	</tr>
		    	<tr>
		    		<td>IOS更新说明</td>
		    		<td><input type="text" class="layui-input iosExplain" placeholder=" "></td>
		    		<td pc>iosExplain</td>
		    		<td></td>
		    	</tr>
				<tr>
					<td>PC最新版本号</td>
					<td><input type="text" class="layui-input pcVersion" placeholder=" "></td>
					<td pc>pcVersion</td>
					<td><br>示例：101，对应版本号1.0.1</br></td>
				</tr>
				<tr>
					<td>PC最新下载URL</td>
					<td><input type="text" class="layui-input pcAppUrl" placeholder=" "></td>
					<td pc>pcAppUrl</td>
					<td></td>
				</tr>
				<tr>
					<td>PC更新说明</td>
					<td><input type="text" class="layui-input pcExplain" placeholder=" "></td>
					<td pc>pcExplain</td>
					<td></td>
				</tr>
				<tr>
					<td>MAC最新版本号</td>
					<td><input type="text" class="layui-input macVersion" placeholder=" "></td>
					<td pc>macVersion</td>
					<td><br>示例：101，对应版本号1.0.1</br></td>
				</tr>
				<tr>
					<td>MAC最新下载URL</td>
					<td><input type="text" class="layui-input macAppUrl" placeholder=" "></td>
					<td pc>macAppUrl</td>
					<td></td>
				</tr>
				<tr>
					<td>MAC更新说明</td>
					<td><input type="text" class="layui-input macExplain" placeholder=" "></td>
					<td pc>macExplain</td>
					<td></td>
				</tr>
		    	<tr>
		    		<td>Android禁用版本号</td>
		    		<td><input type="text" class="layui-input androidDisable" placeholder=" "></td>
		    		<td pc>androidDisable</td>
		    		<td>凡低于此版本号的禁用<br>示例：5.1.0</br></td>
		    	</tr>
		    	<tr>
		    		<td>IOS禁用版本号</td>
		    		<td><input type="text" class="layui-input iosDisable" placeholder=" "></td>
		    		<td pc>iosDisable</td>
		    		<td>凡低于此版本号的禁用<br>示例：5.1.0</br></td>
		    	</tr>
		    	<tr>
		    		<td>PC禁用版本号</td>
		    		<td><input type="text" class="layui-input pcDisable" placeholder=" "></td>
		    		<td pc>pcDisable</td>
		    		<td>凡低于此版本号的禁用<br>示例：5.1.0</br></td>
		    	</tr>
		    	<tr>
		    		<td>Mac禁用版本号</td>
		    		<td><input type="text" class="layui-input macDisable" placeholder=" "></td>
		    		<td pc>macDisable</td>
		    		<td>凡低于此版本号的禁用<br>示例：5.1.0</br></td>
		    	</tr>
<!--				<tr>-->
<!--					<td>网页版最新下载URL</td>-->
<!--					<td><input type="text" class="layui-input webDownloadUrl" placeholder=" "></td>-->
<!--					<td pc>webDownloadUrl</td>-->
<!--					<td></td>-->
<!--				</tr>-->
				<tr>
					<td>Web最新下载URL</td>
					<td><input type="text" class="layui-input webNewUrl" placeholder=""></td>
					<td pc>webNewUrl</td>
					<td></td>
				</tr>
				<tr>
					<td>落地页二维码图片地址</td>
					<td>
						<button type="button" class="layui-btn" onclick="projectQRClick()">选择二维码图片</button>
						<span id="project_QR_update"></span>
					</td>
					<td>projectLogo</td>
					<td></td>
				</tr>
				<tr>
					<td>版权信息</td>
					<td><input type="text" class="layui-input copyrightInfo" placeholder=" "></td>
					<td pc>copyrightInfo</td>
					<td></td>
				</tr>

<!--				<tr>-->
<!--					<td>H5下载跳转URL</td>-->
<!--					<td><input type="text" class="layui-input h5DownloadUrl" placeholder=" "></td>-->
<!--					<td pc>h5DownloadUrl</td>-->
<!--					<td></td>-->
<!--				</tr>-->
				<tr>
					<td>最大上传文件大小</td>
					<td><input type="text" class="layui-input uploadMaxSize" placeholder=" "></td>
					<td pc>uploadMaxSize</td>
					<td></td>
				</tr>
				<tr>
					<td>是否启用音视频</td>
					<td>
						<select class="layui-input-inline isAudioStatus">
							<option value="1">开启</option>
							<option value="0">关闭</option>
						</select>
					</td>
					<td pc>isTabBarStatus</td>
					<td></td>
				</tr>
		    </tbody>
		 </table>
		 <div class="magt10 layui-center">
			<div class="layui-input-block" style="margin-left: 0px">
				<button class="layui-btn save" lay-submit="" lay-filter="systemConfig">保存</button>
		    </div>
		</div>
	</form>
	<form id="project_QR_path_update" name="projectQRPath_update" action="" method="post" enctype="multipart/form-data" style="display: none;">
		<input id="project_QR_display_update" type="file" accept="image/png" name="file" onchange="updateProjectQRDisplay()">
	</form>
</div>
<script type="text/javascript" src="/pages/common/layui/layui.js"></script>
<script type="text/javascript" src="/pages/common/jquery/jquery-1.11.3.min.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="./js/common.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.form.js"></script>
<script type="text/javascript" src="./js/clientConfig.js"></script>
</body>
</html>