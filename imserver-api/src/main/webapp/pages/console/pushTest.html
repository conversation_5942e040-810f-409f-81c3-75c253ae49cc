<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>推送测试</title>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet" media="all">
<style>
    .pushDispose span {
        display: block;
        margin-left: 55px;
    }
    /*.pushDispose .XIAOMI,.MEIZU,.HUAWEI,.OPPO,.VIVO span {*/
    /*    margin-left: 76px;*/
    /*}*/
    .pushDispose input {
        display: inline-block;
        width: 80%;
    }
</style>
<body>
<div class="layui-card">
    <div class="layui-card-header">推送设置</div>
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">推送机型：</label>
                <div class="layui-input-block">
                    <input type="checkbox" class="pushMachine" name="XIAOMI" title="小米" checked>
                    <input type="checkbox" class="pushMachine" name="MEIZU" title="魅族">
                    <input type="checkbox" class="pushMachine" name="HUAWEI" title="华为">
                    <input type="checkbox" class="pushMachine" name="OPPO" title="OPPO">
                    <input type="checkbox" class="pushMachine" name="VIVO" title="VIVO">
                    <!--<input type="checkbox" class="pushMachine" name="IPHONE" title="苹果">-->
<!--                    <input type="checkbox" name="SAMSUNG" title="三星">-->
<!--                    <input type="checkbox" name="ONEPLUS" title="一加">-->
<!--                    <input type="checkbox" name="NUBIA" title="努比亚">-->
<!--                    <input type="checkbox" name="NOKIA" title="诺基亚">-->
                    <!--<input type="checkbox" class="pushMachine" name="QITA" title="其它">-->
                </div>
                <div class="pushDispose">
                    <span class="XIAOMI" style="display: block;">
                        <span style="">小米</span>
                        <span style="margin-left: 76px;">appSecret：<input type="text" class="layui-input" value="weHD74Na9Ur+iY00wjhstA=="></span>
                    </span>
                    <span class="MEIZU" style="display: none;">
                        <span>魅族</span>
                        <span style="margin-left: 76px;">appId：<input style="margin-bottom: 5px;margin-left: 29px;" type="text" class="layui-input appId" value="120124"></span>
                        <span style="margin-left: 76px;">appSecret：<input type="text" class="layui-input appSecret" value="5522690221b24dba800ea35be0f41a9a"></span>
                    </span>
                    <span class="HUAWEI" style="display: none;">
                        <span>华为</span>
                        <span style="margin-left: 76px;">appSecret：<input style="margin-bottom: 5px;" type="text" class="layui-input appSecret" value="63e539d62b224e54f98d3954cd7dc83a"></span>
                        <span style="margin-left: 76px;">appId：<input type="text" style="margin-left: 29px;" class="layui-input appId" value="100489901"></span>
                        <span style="margin-left: 76px;">tokenUrl：<input style="margin-bottom: 5px;" type="text" class="layui-input tokenUrl" value=""></span>
                        <span style="margin-left: 76px;">apiUrl：<input type="text" style="margin-left: 29px;" class="layui-input apiUrl" value=""></span>
                        <span style="margin-left: 76px;">iconUrl：<input type="text" style="margin-left: 29px;" class="layui-input iconUrl" value=""></span>
                    </span>
                    <span class="OPPO" style="display: none;">
                        <span>OPPO</span>
                        <span style="margin-left: 76px;">appKey：<input style="margin-bottom: 5px;margin-left: 36px;" type="text" class="layui-input appKey" value="dIHycN8J0NsCokwSGss8sskw4"></span>
                        <span style="margin-left: 76px;">masterSecret：<input type="text" class="layui-input masterSecret" value="Bb30f02fc872ea0bd2e6585644084e15"></span>
                    </span>
                    <span class="VIVO" style="display: none;">
                        <span>VIVO</span>
                        <span style="margin-left: 76px;">appId：<input style="margin-bottom: 5px;margin-left: 30px;" type="text" class="layui-input appId" value="10923"></span>
                        <span style="margin-left: 76px;">appKey：<input style="margin-bottom: 5px;margin-left: 17px;" type="text" class="layui-input appKey" value="4726232c-298c-4ce4-ac5d-0d311412a456"></span>
                        <span style="margin-left: 76px;">appSecret：<input type="text" class="layui-input appSecret" value="1276fa82-9c69-4aa1-b341-a7318b6440b9"></span>
                    </span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">通知标题：</label>
                <div class="layui-input-block">
                    <input type="text" name="title" required  lay-verify="required" placeholder="请输入标题" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">通知内容：</label>
                <div class="layui-input-block">
                    <textarea name="pushContent" required placeholder="请输入发布内容" class="layui-textarea"></textarea>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">接受ID：</label>
                <div class="layui-input-block">
                    <textarea name="toUserIds" required placeholder="请填写接收ID，一行一个，请注意换行" class="layui-textarea"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="pushAndSave">发送</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/console/js/pushTest.js"></script>
</body>
</html>