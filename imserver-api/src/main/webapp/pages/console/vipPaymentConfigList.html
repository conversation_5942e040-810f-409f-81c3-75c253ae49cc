<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
	<title>VIP收费配置管理</title>
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet"  media="all">
<body>

	<div class="layui-row">
		<!-- <div class="layui-fluid"> -->
			<!-- <div class="layui-row layui-col-space15"> -->
				<div class="layui-col-md1">&nbsp;</div>
				<input id="save_roomId" type="" name="" style="display: none">
				<div id="roomList" class="layui-col-md10" style="">
					<div class="room_btn_div" style="margin-top: 2%">
						<button onclick="RoomConfig.addRoom()" class="layui-btn btn_addRoom">新增VIP收费配置</button>
					</div>
					<div id="room_table_div" class="layui-card" style="margin-top: 1%">
						<div class="layui-card-header"><p>VIP收费配置列表</p></div>
						<div class="layui-card-body">
							<table id="room_table" lay-filter="room_table"></table>
						</div>
					</div>

					<!-- 修改群详情 -->
<!--					<div id="updateRoom" class="layui-card">-->
<!--						<div class="layui-card-header">修改群收费配置</div>-->
<!--						&lt;!&ndash;<div class="layui-card-body">&ndash;&gt;-->
<!--						<form class="layui-form" action="">-->
<!--							<table cellspacing="0" cellpadding="0" border="0" class="layui-table">-->
<!--								<tr>-->
<!--									<td>群组配置Id</td>-->
<!--									<td id="update_roomId" disabled="disabled"></td>-->
<!--								</tr>-->
<!--								<tr>-->
<!--									<td>群人数</td>-->
<!--									<td>-->
<!--										<input id="update_number" type="text" class="layui-input">-->
<!--									</td>-->
<!--								</tr>-->
<!--								<tr>-->
<!--									<td>价格</td>-->
<!--									<td><input id="update_price" type="text" class="layui-input"></td>-->
<!--								</tr>-->
<!--							</table>-->
<!--							<button onclick="RoomConfig.commit_update()" class="layui-btn">保存</button>-->
<!--							<button onclick="RoomConfig.button_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>-->
<!--						&lt;!&ndash;</div>&ndash;&gt;-->
<!--						</form>-->

<!--					</div>-->
				</div>

		         <div id="updateRoom" class="layui-card" style="display: none;">
			<div class="layui-card" style="margin-top: 1%">
				<div class="layui-card-header">修改VIP收费配置</div>
				<div class="layui-card-body">
					<table cellspacing="0" cellpadding="0" border="0" class="layui-table">

						<tr>
							<td>配置id</td>
							<td><input id="update_roomId" type="text" name="" class="layui-input layui-disabled"></td>
						</tr>
						<tr>
							<td>VIP等级</td>
							<td><input id="update_level" type="number" name="" class="layui-input" placeholder="请输入等级"></td>
						</tr>
						<tr>
							<td>有效期(单位:月)</td>
							<td><input id="update_number" type="number" name="" class="layui-input" placeholder="请输有效期"></td>
						</tr>
						<tr>
							<td>价格</td>
							<td><input id="update_price" type="number" name="" class="layui-input" placeholder="请输入价格"></td>
						</tr>
					</table>
					<button onclick="RoomConfig.commit_update()" class="layui-btn">修改</button>
					<button onclick="RoomConfig.button_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
				</div>
			</div>
		</div>
				<!-- 新增群组模块 -->
				<div id="addRoom" class="layui-card" style="display: none;">
					<div class="layui-card" style="margin-top: 1%">
						<div class="layui-card-header">新增VIP收费配置</div>
						<div class="layui-card-body">
							<table cellspacing="0" cellpadding="0" border="0" class="layui-table">
								<tr>
									<td>VIP等级</td>
									<td><input id="add_level" type="number" name="" class="layui-input" placeholder="请输入VIP等级"></td>
								</tr>
								<tr>
									<td>有效期(单位:月)</td>
									<td><input id="add_number" type="number" name="" class="layui-input" placeholder="请输入有效期"></td>
								</tr>
								<tr>
									<td>价格</td>
									<td><input id="add_price" type="number" name="" class="layui-input" placeholder="请输入价格"></td>
								</tr>
							</table>
							<button onclick="RoomConfig.commit_addRoom()" class="layui-btn">新增</button>
							<button onclick="RoomConfig.button_back()" class="layui-btn layui-btn-primary layui-btn-sm" style="">&lt;&lt;返回</button>
						</div>
					</div>
				</div>
				
			</div>
	</div>
	<!--操作-->
	<script type="text/html" id="roomListBar">
	    <a class="layui-btn layui-btn-primary layui-btn-xs modifyConf" lay-event="modifyConf">修改配置</a>
       	<a class="layui-btn layui-btn-xs layui-btn-danger del" lay-event="del">删除</a>
	</script>


	<script type="text/html" id="roomMessageListBar">
		<a class="layui-btn layui-btn-danger layui-btn-xs deleteMessage" lay-event="deleteMessage">删除</a>
	</script>
	<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
	<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
	<script type="text/javascript" src="./js/common.js"></script>
	<script type="text/javascript" src="/pages/common/echarts/echarts.min.js"></script>
	<script type="text/javascript" src="/pages/common/echarts/shine.js"></script>
	<script type="text/javascript" src="./js/console_ui.js"></script>
	<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
	<script type="text/javascript" src="./js/core.js"></script>
	<script type="text/javascript" src="./js/tripledes.js"></script>
	<script type="text/javascript" src="./js/vipPaymentConfigList.js"></script>
	<script type="text/javascript" src="/pages/common/echarts/echarts.min.js"></script>
	<script type="text/javascript" src="/pages/common/echarts/shine.js"></script>
</body>
</html>