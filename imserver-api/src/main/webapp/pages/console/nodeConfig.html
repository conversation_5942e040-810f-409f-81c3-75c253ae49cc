<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="format-detection" content="telephone=no">
<link rel="stylesheet" href="/pages/common/layui/css/layui.css" media="all" />
<link rel="stylesheet" href="./css/public.css" media="all" />

<title>节点配置</title>
</head>
<body>
<div class="layui-tab layui-tab-brief" lay-filter="demo">
  <div class="layui-tab-content layui-row">
  	<div id="back" class="layui-col-md1">&nbsp;</div>
  	<!-- 地区配置 -->
    <div id="node" class="layui-tab-item layui-tab-card layui-show layui-col-md10">
    	<div id="nodeList">
    		<table id="node_list" lay-filter="node_list">
    		</table>
    		<div class="magt10 layui-center">
				<div class="layui-input-block" style="margin-left: 0px">
					<button onclick="Node.addNode()" class="layui-btn save" lay-submit="" lay-filter="systemConfig">添加</button>
			    </div>
			</div>
    	</div>
		<div id="add_node" style="display: none;">
			<form class="layui-form">
				<table class="layui-table mag0">
					<colgroup>
						<col width="25%">
						<col width="45%">
						<col>
					</colgroup>
					<thead>
					<tr>
						<th>参数说明</th>
						<th>参数值</th>
						<th>变量名</th>
						<th>参数说明</th>
					</tr>
					</thead>
					<tbody>
					<input type="hidden" name="" id="node_id">
					<tr>
						<td>节点名称</td>
						<td>
							<input type="text" id="name" class="layui-input name" lay-verify="required" placeholder="请输入节点名称">
						</td>
						<td>nodeName</td>
						<td></td>
					</tr>
					<tr>
						<td>节点域名</td>
						<td>
							<input type="text" id="realmName" class="layui-input realmName" lay-verify="required" placeholder="请输入节点域名">
						</td>
						<td>realmName</td>
						<td></td>
					</tr>
					<tr>
						<td>节点IP</td>
						<td>
							<input type="text" id="nodeIp" class="layui-input nodeIp" lay-verify="required" placeholder="请输入节点IP">
						</td>
						<td>nodeIp</td>
						<td></td>
					</tr>
					<tr>
						<td>节点端口</td>
						<td>
							<input type="text" id="nodePort" class="layui-input nodePort" lay-verify="required" placeholder="请输入节点端口">
						</td>
						<td>nodePort</td>
						<td></td>
					</tr>
					<tr>
						<td>是否开启Socks代理</td>
						<td id="Socks5Tr">
							<select id="isSocks" lay-filter="isSocks" class="layui-input-inline isSocks">
								<option value="1">开启</option>
								<option value="0">关闭</option>
							</select>
						</td>
						<td>isSocks5</td>
						<td>是否开启Socks代理</td>
					</tr>
					<tr id="hostSocksTr">
						<td>Socks代理Host</td>
						<td><input type="text" id="hostSocks" class="layui-input hostSocks" placeholder="请输入Socks代理Host"></td>
						<td>hostSocks</td>
						<td></td>
					</tr>
					<tr  id="postSocksTr">
						<td>Socks代理Post</td>
						<td><input type="text" id="postSocks" class="layui-input postSocks" placeholder="请输入Socks代理Post"></td>
						<td>postSocks</td>
						<td></td>
					</tr>
					<tr id="userSocksTr">
						<td>Socks代理用户名</td>
						<td><input type="text" id="userSocks" class="layui-input userSocks" placeholder="请输入Socks代理用户名"></td>
						<td>userSocks</td>
						<td></td>
					</tr>
					<tr  id="passSocksTr">
						<td>Socks代理密码</td>
						<td><input type="text" id="passSocks" class="layui-input passSocks" placeholder="请输入Socks代理密码"></td>
						<td>passSocks</td>
						<td></td>
					</tr>
					<tr>
						<td>节点状态</td>
						<td>
							<select class="layui-input-inline status">
								<option value="1">启用</option>
								<option value="0">禁用</option>
							</select>
						</td>
						<td>status</td>
						<td></td>
					</tr>
					</tbody>
				</table>
				<div class="magt10 layui-center">
					<div class="layui-input-block" style="margin-left: 0px">
						<button lay-submit="" lay-filter="systemConfig" class="layui-btn" type="button" onclick="Node.commit_node()">保存</button>
						<button class="layui-btn layui-btn-primary" type="button" onclick="Node.back()">返回</button>
					</div>
				</div>
			</form>
		</div>
	</div>
  </div>
</div>
<script type="text/html" id="nodeList_toolbar">
	<a class="layui-btn layui-btn-danger layui-btn-xs delete" lay-event="deleteInfo">删除</a>
	<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="updateInfo">修改</a>
	{{#  if(d.status == 1){ }}
		<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="updateStatusNo">禁用</a>
	{{#  }else{  }}
		<a class="layui-btn layui-btn-primary layui-btn-xs update" lay-event="updateStatusYes">启用</a>
	{{#  } }}
</script>

<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="/pages/console/js/nodeConfig.js"></script>
<script type="text/javascript">
</script>
</body>
</html>