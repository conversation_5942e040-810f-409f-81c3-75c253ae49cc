* {
    margin: 0;
    padding: 0;
}

body,
html {
    height: 100%;
    width: 100%
}

.content {
    position: absolute;
    /* border: 1px solid rgba(255,255,255,0.54); */
    width: 960px;
    height: 520px;
    margin-left: 10px;
    box-shadow: 0px 0px 5px 5px rgba(0, 0, 0, 0.15);
}

.form-con {
    /*border: 1px solid #000;*/
    position: absolute;
    width: 440px;
    top: 0px;
    left: 520px;
    background-color: #fff;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.icon {
    /*left: 160px;*/
    /*top: 184px;*/
    position: absolute;
    width: 100px;
    height: 100px;
    left: 200px;
    top: 150px;
}

.fmg {
    left: 95px;
    top: 275px;
    position: absolute;
}

span {
    color: #C8CAD0;
    font-size: 14px;
}

.item {
    display: flex;
    flex-direction: column;
}

input {
    background-color: #efefef;
    border: none;
    padding-left: 10px;
    outline-color: #EFEFEF;
    border-radius: 4px;
}

input::-webkit-input-placeholder {
    color: #969BA5;
}

input:-moz-placeholder {
    color: #969BA5;
}

input::-moz-placeholder {
    color: #969BA5;
}

input:-ms-input-placeholder {
    color: #969BA5;
}


span {
    color: #C8CAD0;
    font-size: 14px;
}

#adminLogin {
    width: 305px;
    height: 40px;
    background: #3468F5;
    color: #fff;
    font-size: 14px;
    margin-top: 30px;
}

#box {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}
#getImgCode{
    height: 40px;
    width: 100px;
    display:none;
    padding-left: 10px;
}
.item input{
    width: 305px;
    height: 40px;
    margin-top: 6px;
}