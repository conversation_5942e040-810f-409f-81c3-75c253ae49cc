<!DOCTYPE  html>
<html>
<head>
	<meta charset="utf-8">
	<title>用户登录设备</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
</head>
<link href="/pages/common/layui/css/layui.css" rel="stylesheet">

<body class="childrenBody">
	<div class="layui-row">
		<div class="layui-col-md1">&nbsp;</div>
		<div id="deviceList" class="layui-col-md10">
			<div class="layui-card-header"><p>用户登录设备</p></div>
<!--			<div id="device_table" class="layui-card" style="margin-top: 1%">-->
<!--				<div class="layui-card-body">-->
<!--					<table id="device_list" lay-filter="device_list" style="table-layout:fixed;word-break:break-all;" >-->

<!--					</table>-->
<!--				</div>-->
<!--			</div>-->
			<div class="layui-tab">
				<ul class="layui-tab-title">
					<li class="layui-this">登录历史</li>
					<li>推送token</li>
				</ul>
				<div class="layui-tab-content">
					<div class="layui-tab-item  layui-show">
						<div id="device_table_history" class="layui-card" style="margin-top: 1%">
							<div class="layui-card-body">
								<table id="device_list_history" lay-filter="device_list_history" style="table-layout:fixed;word-break:break-all;" >

								</table>
							</div>
						</div>
					</div>
					<div class="layui-tab-item">
						<div id="device_table" class="layui-card" style="margin-top: 1%">
							<div class="layui-card-body">
								<table id="device_list" lay-filter="device_list" style="table-layout:fixed;word-break:break-all;" >

								</table>
							</div>
						</div>
					</div>

				</div>
			</div>
		</div>
	</div>

<script type="text/javascript" src="/pages/common/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/pages/common/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/assets/js/jquery.md5.js"></script>
<script type="text/javascript" src="/pages/console/js/common.js"></script>
<script type="text/javascript" src="/pages/console/js/roomList.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="./js/console_ui.js"></script>
<script type="text/javascript">
    var userIds = new Array();
    console.log(' currentUserId '+localStorage.getItem("currentUserId"));
    var tableIns = layui.table.render({
        elem: '#device_list_history'
        ,url:request("/console/getDeviceHistoryByUserId") + "&userId="+localStorage.getItem("currentUserId")
        ,id: 'device_list_history'
        ,page: true
        ,curr: 0
        ,limit:10
        ,limits:[10,15,20]
        ,groups: 7
        ,cols: [[ //表头
        	{field: 'device', title: '登录设备类型',sort:'true', width:120}
            ,{field: 'deviceInfo', title: '登录设备编号',sort:'true', width:200}
            ,{field: 'loginIp', title: '登录IP',sort:'true', width:150}
            ,{field: 'loginNum', title: '登录次数',sort:'true', width:100}
            ,{field: 'updateTime', title: '登录时间',sort:'true', width:170,templet: function(d){
                    return UI.getLocalTime(d.updateTime);
                }}
        ]]
        ,done:function(res, curr, count){

        }

    });
    var tableIns = layui.table.render({
        elem: '#device_list'
        ,url:request("/console/getAllDeviceByUserId") + "&userId="+localStorage.getItem("currentUserId")
        ,id: 'device_list'
        ,page: true
        ,curr: 0
        ,limit:10
        ,limits:[10,15,20]
        ,groups: 7
        ,cols: [[ //表头
        	{field: 'deviceKey', title: '登录设备类型',sort:'true', width:120}
            ,{field: 'pushServer', title: '登录设备',sort:'true', width:100,templet(d){
                    if(null != d.pushServer){
                        var model = d.pushServer;
                        if("apns" == model){
                            return "IOS";
                        }else{
                            return model;
                        }
                    }
                    return "其他设备"
                }}
            ,{field: 'pushToken', title: '登录设备Token',sort:'true', width:600}
            ,{field: 'time', title: '登录时间',sort:'true', width:170,templet: function(d){
                    return UI.getLocalTime(d.time);
                }}
        ]]
        ,done:function(res, curr, count){

        }
    });
</script>

</body>
</html>