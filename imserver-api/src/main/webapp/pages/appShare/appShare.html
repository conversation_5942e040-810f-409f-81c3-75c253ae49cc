<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script type="text/javascript" src="/pages/common/jquery/jquery-3.3.1.js"></script>
    <script type="text/javascript" src="/pages/common/jquery/jquery.md5.js"></script>
    <script type="text/javascript" src="/pages/common/qrcode/qrcode.min.js"></script>
    <script>
        var fun = (
            function (doc, win) {
                var docEl = doc.documentElement,
                    resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
                    recalc = function () {
                        var clientWidth = docEl.clientWidth;
                        if (!clientWidth) return;

                        //这里是假设在640px宽度设计稿的情况下，1rem = 10px；
                        //可以根据实际需要修改
                        docEl.style.fontSize = 10 * (clientWidth / 750) + 'px';

                    };
                if (!doc.addEventListener) return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener('DOMContentLoaded', recalc, false);
            }
        )(document, window)
    </script>
    <link rel="stylesheet" href="./css/appShare.css" media="all" />
</head>

<body class="box">
<div>
    <div class="logo-wraper">
        <img class="logo" src="" alt="">
        <span class="logo-right">
                <span class="logo-title"></span>
            </span>
    </div>
    <div class="top-info-wraper hide">
            <span class="top-info-text">
                点击右上角按钮，然后在弹出的菜单中，点击在浏览器打开
            </span>
        <img src="./assets/line-arrow.png" alt="">
    </div>
</div>
<div class="body-content">
    <span class="body-group-photo"></span>
    <div class="body-group-title"></div>
    <div>
        <input hidden id="qrCodeValue" type="text" value="http://www.runoob.com" style="border: none;" />
        <div id="qrcode" style="display: block;margin-bottom: 48px;"></div>
    </div>
    <div class="qrcode-subinfo">
        打开<span class="appName"></span>扫码入群
    </div>
    <a href="" class="go-but">立即打开</a>
    <div class="but-subtitle">
        如果已下载<span class="appName"></span>，可立即打开app
    </div>
</div>


<script type="text/javascript">
    var isIphone = navigator.userAgent.match(/(iPhone|iPod|iPad);?/i);
    var isAndroid = navigator.userAgent.match(/android/i);
    //iOS 9 以下
    var iphoneSchema = "youliaobai://";
    //'xxxxxx://open?type=article&cid=39783&cover=http://file.xxxxxx.com/eImg/uimages/20170901/1504260781459851.jpg';
    //iOS 9及以上版本
    var universalLink = 'https://app.xxxxxx.com/share/project?cid=39783&cover=http://file.xxxxxx.com/eImg/uimages/20170901/1504260781459851.jpg';
    var androidSchema = "domain://www.domain.com";
    //appName + '://open?type=article&cid=39783&cover=http://file.domain.com/eImg/uimages/20170901/1504260781459851.jpg';
    var downUrl = 'https://www.pgyer.com';//'http://a.app.qq.com/o/simple.jsp?pkgname=com.android.app.' + appName;//下载连接

    var qrcode = new QRCode(document.getElementById("qrcode"), {
        width : 592,
        height : 592,

    });
    // document.getElementById("qrCodeValue").value = "test";
    // elText = document.getElementById("qrCodeValue");
    // qrcode.makeCode(elText.value);

    var isWeixin = function () { //判断是否是微信
        var ua = navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == "micromessenger") {
            return true;
        } else {
            return false;
        }
    };
    $(document).ready(function () {
        if(isWeixin()){
            $('.top-info-wraper').removeClass('hide');
            $('.go-but').addClass('hide');
            $('.but-subtitle').addClass('hide');
        }
        var url = decodeURIComponent(location.search);// url urlDncode 解码
        var theRequest = new Object();
        if (url.indexOf("?") != -1) {
            var str = url.substr(1);
            strs = str.split("&");
            for(var i = 0; i < strs.length; i ++) {
                theRequest[strs[i].split("=")[0]]=unescape(strs[i].split("=")[1]);
            }
        }
        $.post("/getRoomDetailsQcCode", { roomId: theRequest["roomId"], token: theRequest["token"] }, function(data) {
            console.log(data);
            if (data.resultCode == 1) {
                androidSchema = data.data.androidSchema+"?roomId="+data.data.roomId+"&userId="+data.data.userId+"&nickName="+data.data.nickName;
                iphoneSchema = data.data.iosSchema+"?roomId="+data.data.roomId+"&userId="+data.data.userId+"&nickName="+data.data.nickName;
                document.getElementById("qrCodeValue").value = data.data.website+"?action=group&tigId="+data.data.roomId;
                var elText = document.getElementById("qrCodeValue");
                qrcode.makeCode(elText.value);
                $('.body-group-title').html(data.data.roomName);
                $('.body-group-photo').html(data.data.roomName.substring(0,1));
            }
        }, "json");
        $.post("/projectConfig", function(data) {
            console.log(data);
            if (data.resultCode == 1) {
                var link = document.createElement('link');
                link.rel = 'icon';
                link.href = data.data.projectIco;
                document.getElementsByTagName('head')[0].appendChild(link);
                $(".logo").attr("src",data.data.projectLogo);
                $(".logo-title").text(data.data.projectName==null?"":data.data.projectName);
                $(".appName").text(data.data.projectName==null?"":data.data.projectName);
                if(data.data.guideWebsite!=null){
                    downUrl = data.data.guideWebsite;
                }
            }
        }, "json");
    });
    $('a').click(function () {
        //微信
        if (false&&isIphone && navigator.userAgent.match(/os\s+(\d+)/i)[1] - 0 >= 9) {
            //使用通用链接跳转（连接 路径 为 下载页路径， 后跟参数， 安装， 则跳转app，  未安装则跳转对应 通用连接 进行下载）
            //重定向 到下载页面
            // alert("1");
            window.location = downUrl;
            return false;
        } else {
            if (isWeixin()) {
                //在微信内置浏览器中，提醒用户用系统浏览器打开(Android  iOS)
                // alert('使用手机浏览器打开');
                // alert("wechat");
            } else {
                if (isIphone) {
                    // alert("iphone");
                    $('a').attr('href', iphoneSchema);
                    setTimeout(function () {
                        // console.log('清除');
                        $('body').empty();
                        var ifr = document.createElement('iframe');
                        ifr.style.height = "100%";
                        ifr.src = downUrl;
                        document.body.appendChild(ifr);
                    }, 3000);
                } else if (isAndroid) {
                    // alert(androidSchema);
                    window.location.href = androidSchema;
                    setTimeout(function () {
                        window.location.href = downUrl; //android下载地址
                    }, 1000);
                    return false;
                } else {
                    //非iOS、Android 手机， 隐藏打开文章按钮
                }
            }
        }
    });
</script>
</body>

</html>