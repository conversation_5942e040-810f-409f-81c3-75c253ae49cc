html {
    height: 100%;
}

body {
    display: flex;
    flex-direction: column;
    flex: 1;
    background-color: #F6F7FB;
}

* {
    margin: 0;
    padding: 0;
}
.logo-wraper {
    display: flex;
    justify-content: center;
    padding: 7rem 0;
    position: relative;
    background-color: #fff;
}
img.logo {
    width: 11rem;
    height: 11rem;
}
.logo-right {
    padding-left: 2rem;
    display: flex;
    justify-content: center;
    flex-direction: column;
}
.logo-title {
    font-size: 4.6rem;
    color: #333333;

}
.top-info-wraper {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 19rem;
    background-color: #343434;
    font-size: 2.8rem;
    color: #7F7E7E;
    display: flex;
    align-items: start;
    justify-content: flex-end;
    padding-right: .8rem;
    padding-left: 1rem;
}

.hide {
    display: none !important;
}
.top-info-text {
    display: flex;
    flex: 1;
    margin-top: 6rem;
    justify-content: flex-end;
}
.body-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 4rem;
    padding-bottom: 4rem;
}
.body-group-photo {
    width: 9rem;
    height: 9rem;
    line-height: 9rem;
    text-align: center;
    font-size: 3.4rem;
    border-radius: 5rem;
    margin-bottom: 1.6rem;
    color: #F6F7FB;
    background-color: #2693ff;
}
.body-group-title {

    font-size: 3rem;
    color: #3A404C;
    margin-bottom: 2rem;

}
.qrcode-subinfo {

    font-size: 2.8rem;
    color: #999;
    margin-bottom: 8.3rem;
}

.go-but {
    height: 8.8rem;
    width: 44rem;
    text-align: center;
    display: block;
    background-color: #0093FF;
    font-size: 3.2rem;
    text-decoration: none;
    color: #fff;
    line-height: 8.8rem;
    border-radius: .9rem;
    margin-bottom: 2.2rem;
}
.but-subtitle {
    color: #999;
    font-size: 2.8rem;
}