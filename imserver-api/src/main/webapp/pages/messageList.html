<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Insert title here</title>
</head>
<link href="/pages/layui/css/layui.css" rel="stylesheet">
<script type="text/javascript" src="/pages/js/jquery.min.js"></script>
<script type="text/javascript" src="/pages/layui/layui.all.js"></script>
<script type="text/javascript" src="/pages/js/_coolchat-commons.js"></script>
<script type="text/javascript" src="/pages/js/console_ui.js"></script>
<!--<script type="text/javascript" src="/pages/js/console_init.js"></script>-->
<body>
	<div class="layui-container">
		<div class="layui-fluid">
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md12">
					<div style="margin-top: 2%">
						<input type="text" name="" class="layui-input" style="width: 15%;display: inline" placeholder="发送者">
						<input type="text" name="" class="layui-input" style="width: 15%;display: inline" placeholder="接收者">
						<button class="layui-btn">搜索消息</button>
						<button class="layui-btn">删除消息</button>
					</div>
					<div class="layui-card">
						<div class="layui-card-header">单聊聊天记录</div>
						<div class="layui-card-body">
							<table cellspacing="0" cellpadding="0" border="0" class="layui-table">
								<thead>
									<tr>
										<td>发送者Id</td>
										<td>发送者</td>
										<td>接受者Id</td>
										<td>接受者</td>
										<td>时间</td>
										<td>内容</td>
									</tr>
								</thead>
								<tbody id="message_table">
									
								</tbody>
							</table>
							<div class="layui-box layui-laypage layui-laypage-default">
								<a href="javascript:void(0);" onclick="UI.findMsgList(1)" class="layui-laypage-prev layui-disabled" data-page="0">上一页</a>
								<!-- <span class="layui-laypage-curr">
									<em class="layui-laypage-em" style="background-color:#1E9FFF;"></em>
									<em>1</em>
								</span>
								<a href="javascript:void(0);" onclick="UI.findMsgList(this,2)" data-page="2">2</a>
								<a href="javascript:void(0);" data-page="3">3</a>
								<a href="" data-page="4">4</a>
								<a href="" data-page="5">5</a>
								<span class="layui-laypage-spr">
								...
								</span>
								<a href="" class="layui-laypage-last" data-page="7">7</a> -->
								<a href="javascript:void(0);" onclick="UI.findMsgList(2)" class="layui-laypage-next" data-page="2">下一页</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

</body>
</html>