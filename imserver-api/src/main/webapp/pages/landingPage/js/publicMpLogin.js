// 点击二维码登陆
//        let Login = document.getElementsByClassName('img-ringhttt')
////        console.log(Login)
//        Login[0].onclick = function () {
//            let containAuto = document.getElementsByClassName('contain-auto')
//            let containErweima = document.getElementsByClassName('contain-erwwema')
//            containAuto[0].style = "display:none"
//            containErweima[0].style = "display:block"
//        }
//        // 点击手机手机登陆
//        let containErwwemaRight = document.getElementsByClassName('contain-erwwema-img-right')
//        containErwwemaRight[0].onclick = function () {
//            let containAuto = document.getElementsByClassName('contain-auto')
//            let containErweima = document.getElementsByClassName('contain-erwwema')
//            containAuto[0].style = "display:block"
//            containErweima[0].style = "display:none"
//        }
        $("#select li").click(function () {
            $(this).siblings('li').removeClass('active');
            $(this).addClass('active');
        });
        // 跳转 首页
        $('#shouye').click(function () {
//            console.log('aa')
            window.location.href = "./index.html"
        })
        // 跳转开放平台
        $("#kfpt").click(function () {
            window.location.href = "./publicOpenLogin.html"
        })

        window.onload = function () {
            $.get("/config", function (result) {
                var link = document.createElement('link');
                link.rel = 'icon';
                link.href = result.data.projectIco;
                document.getElementsByTagName('head')[0].appendChild(link);
                $('.top-Logo').attr('src', result.data.projectLogo)
                $('.footerImg').attr('src', result.data.projectLogo)
                $('.projectName').html(result.data.projectName)
                $('.footer-name').html(result.data.projectName)
                $(".copyright p").html(result.data.copyrightInfo);
//                 if(result.data.projectName=="TIG"){
//                     $('.copyright').css("display", "block");
//                 }else{
//                     $('.copyright').css("display", "none");
//                 }
            })
            var intevalTimer = null;

            function clearIntervalFun() {
                intevalTimer && clearInterval(intevalTimer);
                intevalTimer = null;
            }
            var userId;
            function pollingInterval() {
                if (!intevalTimer) {
                    $.get("/mp/getQcCodeValue",
                        function (res) {
//                            console.log('二维码结果', res.data)

                            function makeCode() {
                                var elText = document.getElementById("text");
                                if (!elText.value) {
                                    alert("Input a text");
                                    elText.focus();
                                    return;
                                }
                                qrcode.makeCode(res.data);
                                clearIntervalFun()
                                intevalTimer = setInterval(function getOpenResponse() {
                                    // console.log('定时器开启')
                                    $.get("/mp/getResponse/"+res.data,
                                        function (res) {
//                                            console.log('请求登录结果', res);
                                            if (res && res.success) {
                                                clearIntervalFun();
                                                $.get("/mp/loginPubAcc?access_token="+res.access_token,
                                                        function (response){
                                                               if(response&&response.resultCode == 1){
//                                                                    location.href = '/mp/register';
                                                                   if(response.resultMsg == "1"){
                                                                        $('.contain-auto').css('display','none')
                                                                        $('.contain-erwwema').css('display','none')
                                                                        $('.contain-apply').css('display','block')
                                                                        userId = response.data;
                                                                   }else{
                                                                     location.href = '/mp/home';
                                                                   }
                                                                }else{
//                                                                    alert(response.resultMsg);
                                                                    $.MsgBox.Alert("温馨提示", response.resultMsg);
                                                                }
                                                        }
                                                    )
//                                                location.href = '/mp/loginPubAcc?access_token='+res.access_token
                                            }
                                        }
                                    )
                                }, 1000);
                            }
                            makeCode();
                        }
                    )
                    setTimeout(function () {
//                        console.log('二维码过期')
                        $('.propre').css({
                            "display": 'block'
                        })
                        clearIntervalFun();

                    }, 60000);
                } else {
                    clearIntervalFun()
                }

            }
            $(function () {
                $(".img-ringht").click(function () {
                    pollingInterval()
                });
            });
            // 点击登陆按钮进行登陆
            $('#login').click(
                function () {
//                    console.log("我点击了登陆按钮")
                    if ($('#account').val() == '') {
                        $.MsgBox.Alert("温馨提示", '账号不能位空！');
                        return false;
                    }
                    if ($('.input2').val() == '') {
                        $.MsgBox.Alert("温馨提示", '密码不能为空！');
                        return false;
                    }
                    if ($('.input3').val() == '') {
                        $.MsgBox.Alert("温馨提示", '验证码不能为空！');
                        return false;
                    }
                    uploading("正在加载中......")
                    $.post("/mp/mpLogin", {
                            'username': $('#account').val(),
                            'password': $('.input2').val(),
                            'imgCode': $('.input3').val()
                        },
                        function (res) {
                            if (res.resultCode == 1) {
                                $("#account").val("");
                                $(".input2").val("");
                                location.href = "/mp/home";
                                uploadClose();
                            }
                            if (res.resultCode == 0) {
                                //alert(res.resultMsg)
                                $.MsgBox.Alert("出错啦", res.resultMsg);
                                uploadClose();
                            }
                        })
                })
            // 当账号失去焦点时更新验证码
            $('#account').blur(
                function () {
                    $('#autoImg').attr('src',
                        "/getImgCode?telephone="+$('#account').val()+"&timestamp="+new Date().getTime()
                    )
                }
            )
            // 点击验证码
            $('#autoImg').click(
                function () {
                    $('#autoImg').attr('src',
                        "/getImgCode?telephone="+$('#account').val()+"&timestamp="+new Date().getTime()
                    )
                }
            )
            //   轮询扫码结果
            // 当账号失去焦点时更新验证码
            // 轮询已扫二维码
            // setInterval(function getOpenResponse() {
            //     console.log("已扫码")
            //     $.get(`${baseurl}mp/loginPublicAcc`,
            //         function (res) {
            //             // console.log()
            //         }
            //     )

            // }, 2000);


            var qrcode = new QRCode(document.getElementById("qrcode"), {
                width: 194,
                height: 194
            });
            // 点击更新 二维码 自己更新
            // $('.qrcode').click(function (event) {
            //     console.log('我点击啦二维码本身')
            //     clearIntervalFun()
            //     pollingInterval()
            // })
            // 扫码公众号申请
            $('#appliySubmit').click(function () {
                if ($('#accountR').val() == '') {
                     $.MsgBox.Alert("温馨提示", '账号不能位空！');
                    return false;
                }
                if ($('.telephone').val() == '') {
                    $.MsgBox.Alert("温馨提示", '联系电话不能为空！');
                    return false;
                }
                if ($('.applyReason').val() == '') {
                    $.MsgBox.Alert("温馨提示", '申请理由不能为空');
                    return false;
                }

                if(userId==""){
                     $.MsgBox.Alert("出错啦", '未获取用户参数');
                     return false;
                }
                //  注册
                $.post("/mp/accessPnExamineRegister", {
                        'userName': $('#accountR').val(),
                        'telephone': $('.telephone').val(),
                        'reason': $('.applyReason').val(),
                        'userId': userId
                    },
                    function (result) {
                        if (result.resultCode == 0) {
                            $('.contain-apply').css('display','none')
                            $('.contain-auto').css('display','none')
                            $('.contain-top').css('display','block')
//                            alert(result.resultMsg)
                            $.MsgBox.Alert("温馨提示", result.resultMsg);
                        }
                        if (result.resultCode == 1) {
                            $('.contain-apply').css('display','none')
                            $('.contain-auto').css('display','none')
                            $('.contain-top').css('display','block')
//                            alert(result.resultMsg)
                             $.MsgBox.Alert("温馨提示", result.resultMsg);
                        }
                    })

            })
            // 点击刷新刷新二维码
            $('.refresh').click(function () {
                $('.propre').css({
                    'display': 'none'
                })
                pollingInterval()
            })
             // 点击登陆界面图标进入扫码界面
             $('.btnlogin').click(function(){
                $('.contain-auto').css('display','none')
                $('.contain-apply').css('display','none')
                $('.contain-erwwema').css('display','block')
             })
             // 在二维码页面点击图标进入 申请页面
             $('.contain-erwwema-img-right').click(function(){
                $('.contain-apply').css('display','none')
                $('.contain-erwwema').css('display','none')
                $('.contain-auto').css('display','block')
             })
             // 在申请页面 点击图标 跳转登陆页面
             $('.img-apply').click(function(){
                 $('.contain-apply').css('display','none')
                 $('.contain-auto').css('display','block')
             })
             function uploading(content){
                 layui.use(['layer','jquery'],function(){
                     var layer = layui.layer, $ = layui.jquery;
                     layer.load(1, {
                         content: content,
                         shade: [0.4, '#393D49'],
                         // time: 10 * 1000,
                         success: function(uploadLayer) {
                             uploadLayer.css('padding-left', '30px');
                             uploadLayer.find('.layui-layer-content').css({
                                 'padding-top': '40px',
                                 'width': "161px",
                                 'color':'white',
                                 'background-position-x': '16px'
                             });
                         }
                     })
                 })
             }
             function uploadClose(){
                 layui.use(['layer','jquery'],function(){
                     layer.closeAll();
                 })
             }
        }