 // 点击二维码登陆
        let Login = document.getElementsByClassName('img-ringhttt')
//        console.log(Login)
        Login[0].onclick = function () {
            let containAuto = document.getElementsByClassName('contain-auto')
            let containErweima = document.getElementsByClassName('contain-erwwema')
            containAuto[0].style = "display:none"
            containErweima[0].style = "display:block"
        }
        // 点击手机手机登陆
        let containErwwemaRight = document.getElementsByClassName('contain-erwwema-img-right')
        containErwwemaRight[0].onclick = function () {
            let containAuto = document.getElementsByClassName('contain-auto')
            let containErweima = document.getElementsByClassName('contain-erwwema')
            containAuto[0].style = "display:block"
            containErweima[0].style = "display:none"
        }

        // 跳转 首页
        $('#shouye').click(function () {
//            console.log('aa')
            window.location.href = "./index.html"
        })
        // 跳转公众平台
        $("#gzlogin").click(function () {
//            console.log("公众")
            window.location.href = "./login1.html"
        })

        window.onload = function () {
            $.get("/config", function (result) {
                var link = document.createElement('link');
                link.rel = 'icon';
                link.href = result.data.projectIco;
                document.getElementsByTagName('head')[0].appendChild(link);
                $('.top-Logo').attr('src', result.data.projectLogo)
                $('.footerImg').attr('src', result.data.projectLogo)
                $('.projectName').html(result.data.projectName)
                $('.footer-name').html(result.data.projectName)
                $(".copyright p").html(result.data.copyrightInfo);
//                if(result.data.projectName=="TIG"){
//                    $('.copyright').css("display", "block");
//                }else{
//                    $('.copyright').css("display", "none");
//                }
            })
            // 轮询扫码结果
            // setInterval( function getOpenResponse (){
            //     $.get("http://newapi.domain.im:8092/open/getOpenResponse/43399c0004pub&open&acc94f29baac"),
            //     function(res){
            //         console.log(res)
            //     }
            // },2000);

            // 当账号失去焦点时更新验证码
            // 轮询已扫二维码
            // setInterval( function getOpenResponse (){
            //     console.log("已扫码")
            //     $.get(`${baseurl}/open/loginPublicOpenAcc`),
            //     function(res){
            //         console.log('已扫码',res)
            //     }
            // },2000);
            // 轮询函数
            var intevalTimer = null;

            function clearIntervalFun() {
                intevalTimer && clearInterval(intevalTimer);
                intevalTimer = null;
            }

            function pollingInterval() {
                if (!intevalTimer) {
                    $.get("/open/getOpenQcCodeValue",
                        function (res) {
//                            console.log('二维码结果', res.data)

                            function makeCode() {
                                var elText = document.getElementById("text");
                                if (!elText.value) {
                                    alert("Input a text");
                                    elText.focus();
                                    return;
                                }
                                qrco.makeCode(res.data);
                                clearIntervalFun()
                                intevalTimer = setInterval(function getOpenResponse() {
                                    // console.log('定时器开启')
                                    $.get("/open/getOpenResponse/"+res.data,
                                        function (res) {
//                                            console.log('请求登录结果', res);
                                            if (res && res.success) {
                                                clearIntervalFun();
                                                 $.post("/open/login", {
                                                        'account': res.account,
                                                        'password': res.password,
                                                         'imgCode': "1"
                                                        },
                                                        function (res) {
                                                          if (res.resultCode == 1) {
                                                               localStorage.setItem("telephone",res.data.telephone);
                                                               localStorage.setItem("status",res.data.status);
                                                               localStorage.setItem("account",res.data.account);
                                                               localStorage.setItem("access_token",res.data.access_token);
                                                               localStorage.setItem("userId",res.data.userId);
                                                               localStorage.setItem("apiKey",res.data.apiKey);
                                                               localStorage.setItem("nickName",res.data.nickName);
                                                               setTimeout(function() {
                                                                     location.replace("/pages/open/index.html");
                                                               }, 1000);
                                                //              console.log('登陆成功')
                                                           }
                                                           if (res.resultCode == 0) {
//                                                                 alert(res.resultMsg)
                                                                 $.MsgBox.Alert("出错啦", result.resultMsg);
                                                            }
                                                    }
                                               )
                                            }
                                        }
                                    )
                                }, 1000);
                            }
                            makeCode();
                        }
                    )
                    setTimeout(function () {
//                        console.log('二维码过期')
                        $('.propre').css({
                            "display": 'block'
                        })
                        clearIntervalFun();

                    }, 60000);
                } else {
                    clearIntervalFun()
                }
            }
            //点击跳到二维码页面轮询
            $(function () {
                $(".img-ringht").click(function () {
                    pollingInterval()
                });
            });

            $('#account').blur(
                function () {
                    $('#autoImg').attr('src',
                        "/getImgCode?telephone="+$('#account').val()+"&timestamp="+new Date().getTime()
                    )
                }
            )
            // 点击二维码验证码
            $('#autoImg').click(
                function () {
                    $('#autoImg').attr('src',
                        "/getImgCode?telephone="+$('#account').val()+"&timestamp="+new Date().getTime()
                    )
                }
            )
            //点击登陆
            $('#login').click(
                function () {
                    if ($('#account').val() == '') {
                        $.MsgBox.Alert("温馨提示", '账号不能位空！');
                        return false;
                    }
                    if ($('.input2').val() == '') {
                        $.MsgBox.Alert("温馨提示", '密码不能为空！');
                        return false;
                    }
                    if ($('.input3').val() == '') {
                        $.MsgBox.Alert("温馨提示", '验证码不能为空！');
                        return false;
                    }
                    $.post("/open/login", {
                            'account': $('#account').val(),
                            'password': $.md5($('.input2').val()),
                            'imgCode': $('.input3').val()
                        },
                        function (res) {
                            if (res.resultCode == 1) {
                                 localStorage.setItem("telephone",res.data.telephone);
                            	 localStorage.setItem("status",res.data.status);
                            	 localStorage.setItem("account",res.data.account);
                            	 localStorage.setItem("access_token",res.data.access_token);
                            	 localStorage.setItem("userId",res.data.userId);
                            	 localStorage.setItem("apiKey",res.data.apiKey);
                            	 localStorage.setItem("nickName",res.data.nickName);
                            	 setTimeout(function() {
                            	     location.replace("/pages/open/index.html");
                            	 }, 1000);
//                                console.log('登陆成功')
                            }
                            if (res.resultCode == 0) {
//                                alert(res.resultMsg)
                                $.MsgBox.Alert("出错啦", res.resultMsg);
                            }
                        }
                    )
                })
                 // 点击更新 二维码 自己更新
            $('#qrco').click(function (event) {

                pollingInterval()
            })
                   // 点击刷新刷新二维码
            $('.refresh').click(function () {
                $('.propre').css({
                    'display': 'none'
                })
                pollingInterval()
            })
        }
        var qrco = new QRCode(document.getElementById("qrco"), {
            width: 194,
            height: 194
        });