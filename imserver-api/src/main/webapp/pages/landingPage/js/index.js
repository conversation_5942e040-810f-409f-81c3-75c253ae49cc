//let pc = document.getElementById('pcdowload')
//    pc.onclick = function () {
//        let wxb = document.getElementById('wxb')
//        wxb.style.display = "none"
//        let select = document.getElementById('select')
//        select.style.display = "block"
//    }

// 实现顶部悬浮
//      $(document).ready(function(){
//           //首先获取导航栏距离浏览器顶部的高度
//           var top = $('.top').offset().top;
//           //开始监控滚动栏scroll
//           $(document).scroll(function(){
//             //获取当前滚动栏scroll的高度并赋值
//             var scrTop = $(window).scrollTop();
//             //开始判断如果导航栏距离顶部的高度等于当前滚动栏的高度则开启悬浮
//             if(scrTop >= top){
//             $('.top').css({'position':'fixed','top':'0','width':'100%'});
//             }else{//否则清空悬浮
//             $('.top').css({'position':'','top':''});
//             }
//           })
//      })
window.onload = function () {
    var isWeixin = function () { //判断是否是微信
        var ua = navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == "micromessenger") {
            return true;
        } else {
            return false;
        }
    };
    if (isWeixin()) {
        //$('.top-info-wraper').css('display','block');
        // $('.go-but').addClass('hide');
        // $('.but-subtitle').addClass('hide');
        //    $('.contain-main-left').css('marginTop','40px')
        $('.footer').css('display', 'none')
    }
    if (!isWeixin()) {
        $('.top-info-wraper').css('display', 'none');
        //             $('.go-but').addClass('hide');
        //             $('.but-subtitle').addClass('hide');
    }

    var androidAppUrl;
    var iosAppUrl;
    $.get("/config", function (result) {
        var link = document.createElement('link');
        link.rel = 'icon';
        link.href = result.data.projectIco;
        document.getElementsByTagName('head')[0].appendChild(link);
        document.title = result.data.projectName;
        //            document.getElementsByTagName('head')[0].html(result.data.projectName);
        $('.top-Logo').attr('src', result.data.projectLogo)
        $('.footerImg').attr('src', result.data.projectLogo)
        $('.projectName').html(result.data.projectName)
        $('.footer-name').html(result.data.projectName)
        // 下面时点击下载

        androidAppUrl = result.data.androidAppUrl;
        iosAppUrl = result.data.iosAppUrl;
        $('.AndroidDownLoad').click(function () {
            //                console.log(result.data.androidAppUrl)
            window.open(result.data.androidAppUrl);
        })
        $('.appDownLoad').click(function () {
            //                console.log(result.data)
            //                console.log(result.data.iosAppUrl)
            window.open(result.data.iosAppUrl);
        })

        $('.macoSDownLoad').click(function () {
            //                console.log(result.data.macAppUrl)
            window.open(result.data.macAppUrl);
        })
        // pc 下载url
        $('.windowsDownLoad').click(function () {

            //                console.log('pc下载url', result.data.pcAppUrl)
            window.open(result.data.pcAppUrl);
        })
        $('.wyDownLoad').click(function () {

            //  console.log('pc下载url', result.data.pcAppUrl)
            window.open(result.data.webNewUrl);
        })
        $("#qrcode").attr('src', result.data.webDownloadUrl);
        //            if(result.data.projectName=="TIG"){
        //               $('.copyright').css("display", "none");
        //            }else{
        //                $('.copyright').css("display", "none");
        //            }
        $(".copyright").html(result.data.copyrightInfo);
        //            var qrCodeValue = result.data.webDownloadUrl;
        //            if(result.data.webDownloadUrl==undefined){
        //                qrCodeValue = "./pages/landingPage/index.html";
        //            }
        //            qrcode.makeCode(qrCodeValue);
    });
    $(".dowmLoad").click(function () {
        //            alert('1')
        var u = navigator.userAgent;
        var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
        var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
        if (isiOS) {
            window.location.href = iosAppUrl;//'https://apps.apple.com/cn/app/%E5%93%87%E5%91%BC/id1410002160'; //'安卓下载地址'
        } else if (isAndroid) {
            window.location.href = androidAppUrl;//'https://domain-top.obs.cn-south-1.myhuaweicloud.com/u/0/0/201909/569cdf2201cf451f8f84b34c68189635.apk';//安卓下载地址'
        }
    })
    $(".AndroidDownLoad").hover(function () {
        $(".AndroidDownLoad img").attr("src", "./img/androidW.png");
        $(".AndroidDownLoad img").css("display", "black");
        $(".AndroidDownLoad img").css("height", "24px");
        $(".AndroidDownLoad img").css("width", "24px");
        $(".AndroidDownLoad").css("background-color", "black");
        $(".AndroidDownLoad span").css("color", "white");
    }, function () {
        $(".AndroidDownLoad img").attr("src", "./img/android.png");
        $(".AndroidDownLoad span").css("color", "black");
        $(".AndroidDownLoad").css("background-color", "white");
    });

    $(".pcDownLoad").hover(function () {
        $(".pcDownLoad img").attr("src", "./img/windowsW.png");
        $(".pcDownLoad img").css("display", "black");
        $(".pcDownLoad img").css("height", "24px");
        $(".pcDownLoad img").css("width", "24px");
        $(".pcDownLoad").css("background-color", "black");
        $(".pcDownLoad span").css("color", "white");
    }, function () {
        $(".pcDownLoad img").attr("src", "./img/windows.png");
        $(".pcDownLoad span").css("color", "black");
        $(".pcDownLoad").css("background-color", "white");
    });
    $(".appDownLoad").hover(function () {
        $(".appDownLoad img").attr("src", "./img/macosW.png");
        $(".appDownLoad img").css("display", "black");
        $(".appDownLoad img").css("height", "24px");
        $(".appDownLoad img").css("width", "24px");
        $(".appDownLoad").css("background-color", "black");
        $(".appDownLoad span").css("color", "white");
    }, function () {
        $(".appDownLoad img").attr("src", "./img/macosB.png");
        $(".appDownLoad span").css("color", "black");
        $(".appDownLoad").css("background-color", "white");
    });
    $(".wyDownLoad").hover(function () {
        $(".wyDownLoad img").attr("src", "./img/ie.png");
        $(".wyDownLoad img").css("display", "black");
        $(".wyDownLoad img").css("height", "24px");
        $(".wyDownLoad img").css("width", "24px");
        $(".wyDownLoad").css("background-color", "black");
        $(".wyDownLoad span").css("color", "white");
    }, function () {
        $(".wyDownLoad img").attr("src", "./img/ieB.png");
        $(".wyDownLoad span").css("color", "black");
        $(".wyDownLoad").css("background-color", "white");
    });
    $(".macoSDownLoad").hover(function () {
        $(".macoSDownLoad img").attr("src", "./img/macosW.png");
        $(".macoSDownLoad img").css("display", "white");
        $(".macoSDownLoad img").css("height", "24px");
        $(".macoSDownLoad img").css("width", "24px");
        $(".macoSDownLoad").css("background-color", "black");
        $(".macoSDownLoad span").css("color", "white");
    }, function () {
        $(".macoSDownLoad img").attr("src", "./img/macosB.png");
        $(".macoSDownLoad span").css("color", "black");
        $(".macoSDownLoad").css("background-color", "white");
    });

    $(".windowsDownLoad").hover(function () {
        $(".windowsDownLoad img").attr("src", "./img/windowsW.png");
        $(".windowsDownLoad img").css("display", "white");
        $(".windowsDownLoad img").css("height", "24px");
        $(".windowsDownLoad img").css("width", "24px");
        $(".windowsDownLoad").css("background-color", "black");
        $(".windowsDownLoad span").css("color", "white");
    }, function () {
        $(".windowsDownLoad img").attr("src", "./img/windows.png");
        $(".windowsDownLoad span").css("color", "black");
        $(".windowsDownLoad").css("background-color", "white");
    });
    //  实现顶部选项卡
    $("#select1 li").click(function () {
        $(this).siblings('li').removeClass('active');
        $(this).addClass('active');
    });
    // 点击开放平台实现页面跳转
    $('#kfpt').click(function () {
        window.location.href = "./publicOpenLogin.html"
    })
    // 点击跳转公众平台
    $('#gzpt').click(function () {
        window.location.href = "./login1.html"
    })
    //    var qrcode = new QRCode(document.getElementById("qrcode"), {
    //        width: 178,
    //        height: 178
    //     });
    $('.pcDownLoad').mouseover(function () {
        $('#pcdowload').css("display", "none")
        $('#pcdowloadTwo').css("display", "block")
        $('#select').css("display", 'block')
    })
    $('.select-mian').mouseleave(function () {
        $('#pcdowload').css("display", "none")
        $('#pcdowloadTwo').css("display", "block")
        $('#select').css("display", 'none')
    })
}



