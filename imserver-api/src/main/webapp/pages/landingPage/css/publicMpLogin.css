* {
    margin: 0;
    padding: 0
}

li {
    list-style: none;
    float: left;
}

html,body{
            background: url(../img/gongzhong.png)no-repeat;
            background-size: cover;
            height: 100%;
            height:100%
        }



.top {
    width: 1200px;
    margin: 0 auto;
    height: 96px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    position: fixed;
    top: 0;
}

.top-left {
    width: 121px;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.top-left .projectName {
    font-size: 25px;
    color: #fff;
}

.top img {

    height: 36px;

}

.top ul li {
    font-size: 18px;
    color: #fff;
    height: 31px;
    margin: 0 30px;
    position: relative;
}

.top ul li:last-child {
    margin: 0 0 0 30px;

}

.top ul li:before {
    content: "|";
    height: 17px;
    position: absolute;

    right: 98px;
}

.top ul li:first-child:before {
    display: none;
}

.top ul .active {
    border-bottom: 1px solid #fff;
}

html,
body,
.drap {
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
}

.contain {
    width: 330px;
    height: 302px;
    background: #ffffff;
    position: fixed;
    z-index: 99;
    overflow: hidden;
    border-radius: 10px
}

.contain-top {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #333333;
    position: relative;
    margin-top: 15px;
}

.img-ringht {
    height: 44px;
    width: 44px;
    position: absolute;
    top: 0px;
    right: 20px;
}

/* 鼠标经过换背景图 */
.img-ringhttt {
    background: url(../img/morendn.png)no-repeat;
    cursor: pointer;
}

.img-ringhttt:hover {
    background: url(../img/xuanzhongewm.png)no-repeat;
}

.img-ringht img {
    display: block;
    width: 44px;
    height: 44px;
}

.from-table {
    padding: 18px 55px 0px 55px;
}

.from-table .table input {
    width: 100%;
    height: 32px;
    border: 1px solid #ECEDEF;
    padding-left: 10px
}

.from-table .table .input2 {
    margin-top: 16px;
}

.from-table .table .auto {
    border: 1px solid red;
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding-left: 10px;
    border: 1px solid #ECEDEF;
    height: 32px;
    align-items: center;
    margin-top: 16px;
}

.from-table .table .auto-right {
    font-size: 30px
}

.from-table .table p {
    color: #CBCBCB
}

.login {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
}

.login .login-tip {
    width: 100%;
    height: 32px;
    background: rgba(28, 120, 235, 1);
    box-shadow: 0px 7px 15px 0px rgba(28, 120, 235, 0.42);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: rgba(255, 255, 255, 1);
    cursor: pointer;
}

/* 以上是账号登陆样式 下面是二维码登陆样式 */
.contain-erwwema-top {
    height: 60px;
}

.erwema-warp {
    height: 344px;
    width: 100%;
    padding-left: 69px;
}

.erwema-warp .weweima {
    height: 192px;
    width: 192px;
    border: 1px solid black
}

.contain-erwwema-img-right {
    background: url(../img/morendn.png)no-repeat;
    cursor: pointer;
}

.contain-erwwema-img-right:hover {
    background: url(../img/xuzhogn.png)
}
/* 版权样式*/
.copyright{
    text-align: center;
    font-size: 12px;
    color: #fff;
    position: fixed;
    bottom: 20px;
}
.showMessage {
    padding: 5px 10px;
    border-radius: 5px;
    position: fixed;
    top: 45%;
    left: 45%;
    color: #ffffff;
}

.showMessageSuccess {
    background-color: #00B7EE;
}

.showMessageError {
    background-color: #ff0000;
}

#meg {
    display: none
}
#qrcode {
    position: relative;
}

.propre {
    height: 194px;
    width: 194px;
    background: #000;
    position: absolute;
    top: 10;
    display: none;
    opacity: 0.79;
    text-align: center;
}

textarea {
    width: 100%;
    height: 32px;
    border: 1px solid #ECEDEF;
    padding-left: 10px;
    max-height: 40px;
    overflow: auto;
    z-index: 2;
}

.propre p {
    color: white;
    margin-top:60px;
}

.propre .refresh {
    height: 30px;
    width: 80px;
    background: #E4393C;
    border-radius: 1 px;
    -webkit-border-radius: 1 px;
    -moz-border-radius: 1 px;
    -ms-border-radius: 1 px;
    -o-border-radius: 1 px;
    cursor: pointer;

   margin-top:20px;
   margin-left:60px;
   line-height: 30px;
}