* {
    margin: 0;
    padding: 0;
}

html {
    background: #1C78EB;
    height: 100%;


}

body {
    /* background: url(../img/background.png)no-repeat; */
    background-size: cover;
    height: 100%;
    display: flex !important;
    flex-direction: column;
    justify-content:space-between
}


section{flex:1 !important; display: flex;flex-direction: column; justify-content: space-around;}

.contain-pic{flex: 1 !important;display: flex; flex-direction: column;}
.contain-top {
    margin-left: 75px;
    margin-top: 40px;
}

.contain-top img {
    width: 217px;
    height: 79px;
}
.contain-text {
    height: 60px;
    font-size: 15px;
    color: rgba(255, 255, 255, 1);
    line-height: 30px;
    margin-left: 75px;
    margin-top: 15px;
}

.contain-pic {
    height: 341px;
    justify-content:space-between ;
    position: relative;
    
}

.contain-pic img {
    display: block;
    width: 330px;
    height: 380px;
     position: absolute;
    right: 0px;
    bottom: 0px;
}
footer {
    height: 75px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;

}

.footer-left {
    display: flex;
    width: 110px;
    justify-content: space-between;
    align-items: center;
}

.footer-left img {
    height: 47px;
    width: 47px;
}

.footer-text h1 {
    font-size: 25px;
    color: #333;
}

.footer-text {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
   padding: 0 10px;
}

.footer-text p {
    width: 150px;
}

button {
    width: 88px;
    height: 35px;
    background: rgba(28, 120, 235, 1);
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500px;
    color: rgba(255, 255, 255, 1);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border: none;
}