* {
    margin: 0;
    padding: 0;
}

li {
    list-style: none;
    float: left;
}
  #pcdowload{position: relative;}
  #pcdowload img{position: absolute;left: 25px;top: 14px;}
  #pcdowload span{position: absolute;right: 25px;}
  #pcdowloadTwo {position: relative;}
  #pcdowloadTwo img{position: absolute;left: 25px;top: 14px;}
  #pcdowloadTwo span{position: absolute;right: 25px;}
@media only screen and (min-width: 900px) {
    * {
        margin: 0;
        padding: 0;
    }

    html {
        height: 100%;
        background: #1C78EB;

    }

    body {
        background: url(../img/background.png)no-repeat;
        background-size: cover;
        height: 100%;
        display: flex;
        flex-direction: column;

    }

    .top {
        width:100%;
        margin: 0 auto;
        min-height: 96px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;




    }
       .top-wap {
            width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #1c78eb12;
           
        }

    .top-left{width:200px; display: flex;align-items: center;}
    .top-left .projectName{font-size: 25px;color: #fff;}
    .top img {
        margin-right: 20px;;
      
        height: 36px;

    }

    .top ul li {
        font-size: 18px;
        color: #fff;
        height: 31px;
        margin: 0 30px;
        position: relative;
    }

    .top ul li:last-child {
        margin: 0 0 0 30px;

    }

    .top ul li:before {
        content: "|";
        height: 17px;
        position: absolute;

        right: 98px;
    }

    .top ul li:first-child:before {
        display: none;
    }

    .top ul .active {
        border-bottom: 1px solid #fff;
    }

    .contain-main {
        width: 1200px;
        flex: 1;
        margin: 0 auto;
        position: relative;
       padding-top:96px;
       min-height:765px;
    }

    .contain-top {
        width: 600px;
        height: 110px;
    }

    .contain-contain {

        font-size: 31px;
        color: white;
        margin-top: 52px;
        line-height: 59px;
    }

    .contain-bottom {
        margin-top: 60px;
        display: flex;
    }

    .downLoadItem {
        width: 154px;
        height: 52px;
        background: rgba(255, 255, 255, 1);
        border-radius: 10px;
        border: 2px solid #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        text-align: center;
        line-height: 52px;
    }
    .downLoadItem:hover{border: 2px solid rgba(26, 26, 26, 1);}

    .downLoadItem img {
        margin-right: 10px;
        ;
    }

    .contain-bottom-left {
        height: 180px;
        width: 180px;
        background: rgba(216, 216, 216, 1);
        border-radius: 10px;
        border: 2px solid rgba(26, 26, 26, 1);

    }

    .contain-bottom-right ul {
        height: 100%;
        display: flex;
        justify-content: space-around;
        width: 380px;
        flex-wrap: wrap;
        align-items: center;
    }
    .contain-main-left-right {
        height: 810px;
        width: 716px;
        position: absolute;
        right: -120px;
        bottom: 0px;
    }

    /* 显示隐藏部分 */
    .select {
        width: 172px;
        height: 90px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 11px 0px rgba(56, 104, 250, 0.49);
        border-radius: 14px;
        color: #999;

    }

    .downLoadItem-list {
        width: 50%;
        height: 100%;
        float: left;
        display: flex;
        align-items: center;
        flex-direction: column;
    }

    .downLoadItem-list img {
        margin-top: 21px;
    }

    .downLoadItem-list span {
        height: 30px;
        line-height: 30px;
    }

    .contain-main-left-right img {
        height:810px;
        width: 716px;
        display: block;
    }

    footer {
        display: none;
    }
    /* 版权信息 */
    .copyright {
        text-align: center;
        padding-top: 18%;
        font-size: 12px;
        color: #fff;
        font-weight: 400;
         margin: 0 auto;
         width: 100%;
    }

}
 #pcdowload {
            position: relative;
        }

        #pcdowload img {
            position: absolute;
            left: 25px;
            top: 14px;
        }

        #pcdowload span {
            position: absolute;
            right: 25px;
        }

        #pcdowloadTwo {
            position: relative;
        }

        #pcdowloadTwo img {
            position: absolute;
            left: 25px;
            top: 14px;
        }

        #pcdowloadTwo span {
            position: absolute;
            right: 25px;
        }

        #Downlist {
            position: relative;
        }

        #select {
            position: absolute;
            position: absolute;
            top: -60px;
            left: 208px;
            height: 68px;
            width: 156px;
            display:none;
        }
        .select-mian{
        position:relative
        }
        #select img{
            margin-top: 10px;
        }

/* 小屏幕样式 */
@media only screen and (max-width: 900px) {
    html {
        background: #1C78EB;
        height: 100%;
      
    }
    /* 版权隐藏 */
    .copyright{
        display: none;

    }

    body {
        /* background: url(../img/background.png)no-repeat; */
        background-size: cover;
        height: 100%;
      
        display: flex;
        flex-direction: column;
    }

    .top {
        display: none;
      
    }
   .contain-main{flex: 1;}
    .contain-bottom-right {
        display: none;
    }
    .contain-bottom-left .qrcode{display:none}

    .contain-top {
        height: 50px;
        width: 217px;
        margin-left: 70px;
        margin-top: 45px;

    }
    .contain-main{
        display: flex;
        flex-direction: column;
        justify-content: space-around;


    }
    .contain-contain{
        height: 60px;
    font-size: 15px;
    color: rgba(255, 255, 255, 1);
    line-height: 30px;
    margin-left: 75px;
    margin-top: 15px;
    }
    .contain-main-left-right{
        flex: 1;
        position: relative;
        margin-top:5%;
    }

    .contain-main-left-right img{
        position: absolute;
        right: 0px;
        width: 330px;
    height: 380px;
    bottom: 0px;

    }

   

   footer{height: 75px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;}
    .footer-left {
        display: flex;
        width: 110px;
        justify-content: space-between;
        align-items: center;
    }
    
    .footer-left img {
        height: 47px;
        width: 47px;
    }
    
    .footer-text h1 {
        font-size: 25px;
        color: #333;
    }
    
    .footer-text {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
       padding: 0 10px;
    }
    
    .footer-text p {
        width: 150px;
    }
    
    button {
        width: 88px;
        height: 35px;
        background: rgba(28, 120, 235, 1);
        border-radius: 5px;
        font-size: 14px;
        font-weight: 500px;
        color: rgba(255, 255, 255, 1);
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        -ms-border-radius: 5px;
        -o-border-radius: 5px;
        border: none;
    }


  
}