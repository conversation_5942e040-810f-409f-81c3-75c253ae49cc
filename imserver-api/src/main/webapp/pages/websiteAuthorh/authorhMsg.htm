<!DOCTYPE html>
<html><head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<meta charset="utf-8">
	<title>授权提示信息</title>
	<meta id="viewport" name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, target-densitydpi=medium-dpi">
	<meta name="format-detection" content="telephone=no">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<style type="text/css">
	.hide{ display: none; }
	html,body{background:none;}
	</style>
</head>
<body style="background:none;">
	<!--<script type="text/javascript">
		var T=[];T.push(new Date()-0);
		var g_status = 1, g_t_js1, g_t_js2;
		window.addEventListener && window.addEventListener('load', function() {
			setTimeout(function() {
				var duration = (g_t_js2||new Date())-g_t_js1;
				var img = document.createElement('img');
				img.onerror = img.onload = function() {
					img = img.onload = img.onerror = null;
				};
				g_status = String(g_status);
				img.src = [
					'https://huatuospeed.weiyun.com/cgi-bin/r.cgi?flag1=320716&flag2=', (g_status.length>3&&!/0/.test(g_status)?1:2),
						'&flag3=', g_status,
						'&1=1&2=', (duration||0),
						'&sds=', Math.random()].join('');
			}, 3000);
		});

		document.write('<link id="link" href="//imgcache.qq.com/open_proj/qqconnect/h5login/css/main320.css" rel="stylesheet" type="text/css" media="all" />');

		if (/[?#&]style=(xmtv|36)(?=[&#]|$)/i.test(location.href)) {
			document.write('<link id="link" href="//imgcache.qq.com/open_proj/qq-load/css/mi-ui.css" rel="stylesheet" type="text/css" media="all" />');
			document.body.className = 'mi-ui';
		}
	</script>-->
	<!--<link id="link" href="QQ%E5%B0%8F%E9%A1%B5%E9%9D%A2_files/main320.css" rel="stylesheet" type="text/css" media="all">-->
	<link rel="stylesheet" type="text/css" href="css/txcss/main320.css">
	<div id="web_login">
		<div class="page_content" style="padding:0;margin:0;">
			<div class="login_form_panel">
				<div class="authorized_form_list hidecontrol" style="padding-top:0;">
					<!--<p id="auth_tips">途牛旅游网将获得以下权限:</p>-->
					<p id="auth_tips"></p>
					<ul id="api_list" class="api_list">

						<li id="item_1010" value="1010" state="" class="selected disabled" checked="checked" _dis="1" tabindex="7">
							<span class="ico_authorize"></span>
							<!--<span>访问你的详细资料</span>-->
							<span>获得您的昵称、头像、性别、生日</span>
						(必选)</li>
					

						<!--<li id="item_1011" value="1011" state="" class="selected disabled" checked="checked" _dis="1" tabindex="8">
							<span class="ico_authorize"></span>
							<span>访问你的基础资料</span>
						(必选)</li> -->
					
					</ul>
					<div class="control" tabindex="9">
						<span class="show_all"></span>
						<span id="controlText">查看全部</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<footer id="page_footer" class="page_footer">
		<span id="copyright" data-cpys="2010">Copyright © 2010 - 2019</span> Tencent. All Rights Reserved.
	</footer>
	<div id="reflow" style="height: 1px;"></div>
	<!--<script type="text/javascript">
		T.push(new Date()-0);
		g_t_js1 = new Date();
		g_status += 10;
		document.domain = /\.pengyou\.com$/.test(location.hostname) ? 'pengyou.com' : 'qq.com';
		T.push(new Date()-0);
		g_status += 1000;
		document.write('<script type="text/javascript" src="//imgcache.qq.com/c/=/ptlogin/ac/v9/js/x-msg.js,/open/connect/widget/mobile/login/js/authlist.js,/ptlogin/ac/v9/js/copyright.js?v=20140418001"><\/script>');
	</script><script type="text/javascript" src="QQ%E5%B0%8F%E9%A1%B5%E9%9D%A2_files/copyright.js"></script>
	<script type="text/javascript">
		if (!window.login) {
			g_status += 1000;
			document.write('<script type="text/javascript" src="//imgcache.qq.com/c/=/ptlogin/ac/v9/js/x-msg.js,/open/connect/widget/mobile/login/js/authlist.js,/ptlogin/ac/v9/js/copyright.js?v=20140418001"><\/script>');
		}
	</script>
	<script type="text/javascript">
		if (!window.login) {
			g_status += 1000;
			document.write('<script type="text/javascript" src="//qzs.qq.com/c/=/ptlogin/ac/v9/js/x-msg.js,/open/connect/widget/mobile/login/js/authlist.js,/ptlogin/ac/v9/js/copyright.js?v=20140418001"><\/script>');
		}
	</script>
	<script type="text/javascript" id="script-init" data-nick="">
		g_status += 100;
		g_t_js2 = new Date();
		if (!window.login) {
			g_status = g_status - (parseInt(g_status/1000)%10)*1000;
		}

		var __nick = document.getElementById('script-init').getAttribute('data-nick')||'';
		var __skey = 0;
		var qua = "";
		var _idt = "1551494581";
		T.push(new Date()-0);
		window.login && login.init();
	</script>-->
	<!--<link rel="stylesheet" type="text/css" href="QQ%E5%B0%8F%E9%A1%B5%E9%9D%A2_files/new_style.css">-->
	<link rel="stylesheet" type="text/css" href="css/txcss/new_style.css">
	<!--<link rel="stylesheet" type="text/css" href="/css/txcss/main320.css">-->


</body>
<script src="vendor/jquery/jquery-3.2.1.min.js"></script>
	<script type="text/javascript">
		console.log("webName : "+localStorage.getItem("webName"));
		$("#auth_tips").empty().text(localStorage.getItem("webName")+"将获得以下权限:");
	</script>
</html>