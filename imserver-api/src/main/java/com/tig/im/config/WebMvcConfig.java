package com.tig.im.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类，用于配置静态资源映射
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 添加webapp目录的静态资源映射
        registry.addResourceHandler("/pages/**")
                .addResourceLocations("classpath:/webapp/pages/");
        
        // 添加其他静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
                
        registry.addResourceHandler("/css/**")
                .addResourceLocations("classpath:/webapp/css/");
                
        registry.addResourceHandler("/js/**")
                .addResourceLocations("classpath:/webapp/js/");
                
        registry.addResourceHandler("/images/**")
                .addResourceLocations("classpath:/webapp/images/");
    }
}
