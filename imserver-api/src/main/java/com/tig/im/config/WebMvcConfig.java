package com.tig.im.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类，用于配置静态资源映射
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 获取项目根目录的绝对路径
        String projectRoot = System.getProperty("user.dir");

        // 添加webapp目录的静态资源映射
        registry.addResourceHandler("/pages/**")
                .addResourceLocations("file:" + projectRoot + "/imserver-api/src/main/webapp/pages/");

        // 添加其他静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");

        registry.addResourceHandler("/css/**")
                .addResourceLocations("file:" + projectRoot + "/imserver-api/src/main/webapp/css/");

        registry.addResourceHandler("/js/**")
                .addResourceLocations("file:" + projectRoot + "/imserver-api/src/main/webapp/js/");

        registry.addResourceHandler("/images/**")
                .addResourceLocations("file:" + projectRoot + "/imserver-api/src/main/webapp/images/");
    }
}
