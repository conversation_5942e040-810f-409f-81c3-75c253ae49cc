package com.tig.im.controller;

import cn.tig.commons.constants.MsgType;
import cn.tig.commons.support.mongo.MongoOperator;
import cn.tig.commons.support.spring.SpringBeansUtils;
import cn.tig.commons.utils.DES;
import cn.tig.commons.utils.DateUtil;
import cn.tig.commons.utils.ReqUtil;
import cn.tig.commons.utils.StringUtil;
import cn.tig.commons.vo.JSONMessage;
import cn.tig.im.service.impl.RoomManagerImplForIM;
import cn.tig.im.service.impl.UserManagerImpl;
import cn.tig.im.utils.ConstantUtil;
import cn.tig.im.utils.KSessionUtil;
import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.Fans;
import cn.tig.im.vo.Room;
import cn.tig.im.vo.Room.Member;
import cn.tig.im.vo.User;
import cn.tig.service.KXMPPServiceImpl;
import cn.tig.service.KXMPPServiceImpl.MessageBean;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.*;
import com.tig.im.utils.TimerTask;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.redisson.api.RScheduledExecutorService;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.tig.im.utils.TigBeanUtils.getDatastore;

/**
 * @author:
 * @package: com.tig.im.controller.TigaseController
 * @fileName: TigaseController.java
 * @dateTime: 2019/7/4 14:56
 * @description: Tigase支持接口
 **/
@RestController
@RequestMapping("/tigase")
public class TigaseController extends AbstractController {

    @Resource(name = "dsForTigase")
    private Datastore dsForTigase;
    //	@Resource(name = "dsForRW")
//	protected Datastore dsForRW;
    @Resource(name = "dsForRoom")
    protected Datastore dsForRoom;

    @Autowired
    private RedissonClient redissonClient;

    public static final String RevokeTASK = "RevokeTASK";

    private static final Logger log = LoggerFactory.getLogger(TigaseController.class);

//    private ScheduledExecutorService revokeTask = Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors());

    private RScheduledExecutorService rScheduledExecutorService;

    private static RoomManagerImplForIM getRoomManager() {
        RoomManagerImplForIM roomManager = TigBeanUtils.getRoomManagerImplForIM();
        return roomManager;
    }

    private static UserManagerImpl getUserManager() {
        UserManagerImpl userManager = TigBeanUtils.getUserManager();
        return userManager;
    }



    @PostConstruct
    public void init() {
        rScheduledExecutorService = redissonClient.getExecutorService(RevokeTASK);
    }

    /**
     * @author:
     * @moduleName: getMsgs
     * @dateTime: 2019/7/4 14:57
     * @param: [receiver, startTime, endTime, pageIndex, pageSize, maxType]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 单聊聊天记录
     **/
    @RequestMapping("/tig_msgs")
    public JSONMessage getMsgs(@RequestParam int receiver, @RequestParam(defaultValue = "0") long startTime,
                               @RequestParam(defaultValue = "0") long endTime, @RequestParam(defaultValue = "0") int pageIndex,
                               @RequestParam(defaultValue = "20") int pageSize, @RequestParam(defaultValue = "200") int maxType) {
        int sender = ReqUtil.getUserId();
        DBCollection dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
        BasicDBObject q = new BasicDBObject();
        q.put("sender", sender);
        q.put("receiver", receiver);
        if (startTime > 0) {
            startTime = (startTime / 1000) - 1;
        }
        if (endTime > 0) {
            endTime = (endTime / 1000) + 1;
        }
        if (maxType > 0) {
            q.put("contentType", new BasicDBObject(MongoOperator.LT, maxType));
        }
        if (0 != startTime && 0 != endTime) {
            q.put("timeSend", new BasicDBObject("$gte", startTime).append("$lte", endTime));
        } else if (0 != startTime || 0 != endTime) {
            if (0 != startTime) {
                q.put("timeSend", new BasicDBObject("$gte", startTime));
            } else {
                q.put("timeSend", new BasicDBObject("$lte", endTime));
            }
        }
        List<DBObject> list = Lists.newArrayList();
        DBCursor cursor = dbCollection.find(q).sort(new BasicDBObject("timeSend", -1)).skip(pageIndex * pageSize).limit(pageSize);
        while (cursor.hasNext()) {
            list.add(cursor.next());
        }
        return JSONMessage.success("", list);
    }


    /**
     * @author:
     * @moduleName: getMucMsgs
     * @dateTime: 2019/7/4 14:57
     * @param: [roomId, startTime, endTime, pageIndex, pageSize, maxType]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 群组聊天记录
     **/
    @RequestMapping("/tig_muc_msgs")
    public JSONMessage getMucMsgs(@RequestParam String roomId, @RequestParam(defaultValue = "0") long startTime,
                                  @RequestParam(defaultValue = "0") long endTime, @RequestParam(defaultValue = "0") int pageIndex,
                                  @RequestParam(defaultValue = "20") int pageSize, @RequestParam(defaultValue = "200") int maxType) {
        DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + roomId);
        BasicDBObject q = new BasicDBObject();
        q.put("room_jid_id", roomId);
        if (startTime > 0) {
            startTime = (startTime / 1000) - 1;
        }
        if (endTime > 0) {
            endTime = (endTime / 1000) + 1;
        }
		/*if(maxType>0)
			q.put("contentType",new BasicDBObject(MongoOperator.LT, maxType));*/
        ObjectId roomObjId = TigBeanUtils.getRoomManager().getRoomId(roomId);
        long cleanedTime = 0;
        if (null != roomObjId) {
            Member member = TigBeanUtils.getRoomManager().getMember(roomObjId, ReqUtil.getUserId());
            if (null != member) {
                startTime = member.getCreateTime();
                cleanedTime = member.getCleanedTime();
            }else{
                return null;
            }

        }
        if (0 != startTime && 0 != endTime) {
            q.put("timeSend", new BasicDBObject("$gte", startTime).append("$lte", endTime));
        } else if (0 != startTime || 0 != endTime) {
            if (0 != startTime) {
                q.put("timeSend", new BasicDBObject("$gte", startTime));
            } else {
                q.put("timeSend", new BasicDBObject("$lte", endTime));
            }
        }
        if (0 != cleanedTime) {
            q.put("ts", new BasicDBObject("$gte", cleanedTime));
        }
        List<DBObject> list = Lists.newArrayList();
	/*	DBObject projection=new BasicDBList();
		projection.put("body", 1);*/
        DBCursor cursor = dbCollection.find(q).sort(new BasicDBObject("timeSend", -1)).skip(pageIndex * pageSize).limit(pageSize);
        while (cursor.hasNext()) {
            list.add(cursor.next());
        }
        /*Collections.reverse(list);//倒序*/
        return JSONMessage.success("", list);
    }

    @RequestMapping("/deleteOneLastChat")
    public JSONMessage deleteOneLastChat(@RequestParam(defaultValue = "0") long startTime, @RequestParam(defaultValue = "0") long endTime
            , @RequestParam(defaultValue = "") String jid) {
        Pattern NUMBER_PATTERN = Pattern.compile("-?[0-9]+(\\.[0-9]+)?");
        String userId = ReqUtil.getUserId().toString();
        DBCollection dbCollection = dsForTigase.getDB().getCollection("tig_lastChats");
        BasicDBObject query = new BasicDBObject();
//		query.append("_id",new ObjectId(id));
//		DBCursor lastMsgObj= dbCollection.find(query);
//		String strJid ="";
//		while (lastMsgObj.hasNext()) {
//			BasicDBObject dbObj = (BasicDBObject) lastMsgObj.next();
//			strJid = (String) dbObj.get("jid");
//		}
        if (!"".equals(jid)) {
            Matcher isNum = NUMBER_PATTERN.matcher(jid);
            if (!isNum.matches()) {
                ObjectId roomObjId = TigBeanUtils.getRoomManager().getRoomId(jid);
                if (null != roomObjId) {
                    Query<Room.Member> qmember = TigBeanUtils.getRoomManager().getRoomDatastore().createQuery(Room.Member.class).field("userId")
                            .equal(Integer.parseInt(userId)).field("roomId").equal(roomObjId);
                    UpdateOperations<Member> ops1 = getDatastore().createUpdateOperations(Room.Member.class);
                    if (null != qmember) {
                        qmember.get().setDeleteLastChatTime(DateUtil.currentTimeSeconds());
                        ops1.set("deleteLastChatTime", DateUtil.currentTimeSeconds());
                        TigBeanUtils.getRoomManager().getRoomDatastore().update(qmember, ops1);
                    }
                    emptyMsg(0, 3, roomObjId.toString());
                }
            } else {
//				if("10000".equals(toUser)){
//					BasicDBList values = new BasicDBList();
//					values.add(new BasicDBObject ("from",toUser));
//					values.add(new BasicDBObject("to",toUser));
//					query.append(MongoOperator.OR,values);
//				}else{
//					query.append("to",toUser);
//				}
                query.append("userId", userId).append("isRoom", 0);
                query.append("jid", jid);
                dbCollection.remove(query);
                emptyMsg(Integer.parseInt(jid), 0, "");
            }
        }
        return JSONMessage.success();
    }

    /**
     * @param endTime   结束时间  毫秒数
     * @param startTime 开始时间     毫秒数
     * @return 参数
     * @Description: TODO(一段时间内最新的聊天历史记录)
     */
    @RequestMapping("/getLastChatList")
    public JSONMessage getLastChatList(@RequestParam(defaultValue = "0") long startTime, @RequestParam(defaultValue = "0") long endTime
            , @RequestParam(defaultValue = "0") int pageSize) {
        String userId = ReqUtil.getUserId().toString();
        DBCollection dbCollection = dsForTigase.getDB().getCollection("tig_lastChats");
        BasicDBObject query = new BasicDBObject();
        if (0 != startTime && 0 != endTime) {
            query.put("timeSend", new BasicDBObject("$gte", startTime).append("$lte", endTime));
        }
        if (0 != startTime || 0 != endTime) {
            if (0 != startTime) {
                query.put("timeSend", new BasicDBObject("$gte", startTime));
            } else {
                query.put("timeSend", new BasicDBObject("$lte", endTime));
            }
        }
        List<String> roomJidList = getRoomManager().queryUserRoomsJidList(Integer.parseInt(userId));
//		List<String> roomJidList = TigBeanUtils.getRedisService().queryUserRoomJidList(Integer.valueOf(userId));
        if (null != roomJidList && 0 < roomJidList.size()) {
            BasicDBList values = new BasicDBList();
            values.add(new BasicDBObject("userId", userId).append("isRoom", 0));
            for (int i = 0; i < roomJidList.size(); i++) {
                ObjectId roomObjId = TigBeanUtils.getRoomManager().getRoomId(roomJidList.get(i));
                Query<Room.Member> qmember = TigBeanUtils.getRoomManager().getRoomDatastore().createQuery(Room.Member.class).field("userId")
                        .equal(Integer.parseInt(userId)).field("roomId").equal(roomObjId);
                long deleteLastChatTime = qmember.get().getDeleteLastChatTime();
                values.add(new BasicDBObject("jid", roomJidList.get(i)).append("timeSend", new BasicDBObject("$gte", deleteLastChatTime)));
            }
            query.append(MongoOperator.OR, values);
        } else {
            query.append("userId", userId).append("isRoom", 0);
        }
        //logger.info("query ==> "+query.toJson());
        DBCursor cursor = null;
        if (0 == pageSize)
            cursor = dbCollection.find(query);
        else {
            cursor = dbCollection.find(query)
                    .sort(new BasicDBObject("timeSend", -1)).skip(0).limit(pageSize);
        }
        BasicDBList resultList = new BasicDBList();
//		DBObject dbObj=null;
        while (cursor.hasNext()) {
            DBObject dbObj = cursor.next();
			 /*if((int)dbObj.get("isRoom")!=1){
				// User user=userManager.getUser((int)cursor.next().get("userId"));
				 User toUser=getUserManager().getUser(Integer.valueOf((String)dbObj.get("jid")));
				 if(null==toUser) {
					continue; 
				 }
					
				 dbObj.put("toUserName",toUser.getNickname());
			 }else{
				 User roomIdToUser=getUserManager().getUser(Integer.valueOf((String)dbObj.get("userId")));
				 dbObj.put("toUserName",roomIdToUser.getNickname());
			 }*/
            resultList.add(dbObj);
        }
        return JSONMessage.success(resultList);
    }


//	@RequestMapping(value = "/push")
//	public JSONMessage push(@RequestParam String text, @RequestParam String body) {
//		logger.info("push");
//		List<Integer> userIdList = JSON.parseArray(text, Integer.class);
//		try {
//			//String c = new String(body.getBytes("iso8859-1"),"utf-8");
//			KXMPPServiceImpl.getInstance().send(userIdList,body);
//			return JSONMessage.success();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return JSONMessage.failure("推送失败");
//		// {userId:%1$s,toUserIdList:%2$s,body:'%3$s'}
//	}

    @RequestMapping(value = "/OnlineState")
    public JSONMessage OnlineState(@RequestParam long userId, @RequestParam int OnlineState) {
        List<Fans> data = TigBeanUtils.getFriendsManager().getFansList((int) userId);
        List<Integer> userList = new ArrayList<>();
        MessageBean messageBean = new MessageBean();
        messageBean.setFromUserId(Long.toString(userId));
        messageBean.setMessageId(StringUtil.randomUUID());
        for (Fans fans : data) {
            userList.add(fans.getToUserId());
        }
        if (OnlineState == 0) {
            messageBean.setType(KXMPPServiceImpl.OFFLINE);
        } else {
            messageBean.setType(KXMPPServiceImpl.ONLINE);
        }
        try {
            KXMPPServiceImpl.getInstance().send(messageBean, userList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return JSONMessage.failure("推送失败");
    }

    //加密
    @RequestMapping(value = "/encrypt")
    public JSONMessage encrypt(@RequestParam String text, @RequestParam String key) {
        Map<String, String> map = Maps.newConcurrentMap();
        try {
            //text=DesUtil.encrypt(text, key);
            text = DES.encryptDES(text, key);
            map.put("text", text);
            return JSONMessage.success(null, map);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            map.put("text", text);
            return JSONMessage.success(null, map);
        }
    }


    /**
     * @param text 消息内容
     * @param key  AppConfig.apiKey+msg.timeSend+msg.messageId;
     * @return
     * @Description: 消息解密
     **/
    @RequestMapping(value = "/decrypt")
    public JSONMessage decrypt(@RequestParam String text, @RequestParam String key) {
        Map<String, String> map = Maps.newConcurrentMap();
//		String content=null;
        try {
            //content=DesUtil.decrypt(text, key);
            String content = DES.encryptDES(text, key);
            map.put("text", content);
            return JSONMessage.success(null, map);
        } catch (StringIndexOutOfBoundsException e) {
            //没有加密的 消息
            map.put("text", text);
            return JSONMessage.success(null, map);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            map.put("text", text);
            return JSONMessage.success(null, map);
        }
    }

    //	获取消息接口(阅后即焚)
    //type 1 单聊  2 群聊
    @RequestMapping("/getMessage")
    public JSONMessage getMessage(@RequestParam(defaultValue = "1") int type, @RequestParam String messageId, @RequestParam(defaultValue = "0") ObjectId roomJid) throws Exception {
        DBCollection dbCollection = null;
        if (type == 1) {
            dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
        } else {
            dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + roomJid);
        }

        BasicDBObject query = new BasicDBObject();
        query.put("messageId", messageId);
        Object data = dbCollection.findOne(query);

        return JSONMessage.success(null, data);

    }

    //	删除消息接口
    @RequestMapping("/deleteMsg")
    //type 1 单聊  2 群聊
    //delete 1  删除属于自己的消息记录 2：撤回 删除 整条消息记录
    public JSONMessage deleteMsg(@RequestParam(defaultValue = "1") int type, @RequestParam(defaultValue = "1") int delete, @RequestParam String messageId, @RequestParam(defaultValue = "") String roomJid) throws Exception {
        int sender = ReqUtil.getUserId();
//		DBCursor cursor = null;
        // 群聊、单聊消息
        DBCollection dbCollection;
        // 最后一条聊天消息
        DBCollection lastdbCollection;
        try {
            if (type == 1) {
                dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
            } else {
                dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + roomJid);
            }
            lastdbCollection = dsForTigase.getDB().getCollection("tig_lastChats");
            BasicDBObject query = new BasicDBObject();
            BasicDBObject lastquery = new BasicDBObject();
            if (!StringUtil.isEmpty(messageId)) {
                query.put("messageId", new BasicDBObject(MongoOperator.IN, messageId.split(",")));
            }
            if (1 == delete) {
                query.put("sender", sender);
            }
            /**
             * 清除聊天记录接口里，不删除文件
             */
            BasicDBObject base = (BasicDBObject) dbCollection.findOne(query);
            if (base == null){
                return JSONMessage.failure("消息删除或撤回失败");
            }
            if (type == 2 && !StringUtil.isEmpty(messageId)) {
                if (redissonClient.getMap(RevokeTASK).get(messageId) == null) {
                    Entry entry = new Entry();
                    entry.setPacketId(messageId);
                    redissonClient.getMap(RevokeTASK).put(messageId, entry);
                    RevokeTimeoutTask revokeTimeoutTask = new RevokeTimeoutTask(messageId, type, roomJid);
                    rScheduledExecutorService.scheduleAtFixedRate(revokeTimeoutTask, 10, 10, TimeUnit.SECONDS);
                }
            }

            // 维护最后一条消息记录表
            BasicDBList queryOr = new BasicDBList();
            if (1 == type) {
                if (delete == 1) {
                    lastquery.put("userId", sender);
                } else if (delete == 2) {
                    query.append("contentType", new BasicDBObject(MongoOperator.IN, MsgType.FileTypeArr));
                    List<String> fileList = dbCollection.distinct("content", query);
                    for (String fileUrl : fileList) {
                        // 调用删除方法将文件从服务器删除
                        ConstantUtil.deleteFile(fileUrl);
                    }
                    query.remove("contentType");
                    queryOr.add(new BasicDBObject("jid", String.valueOf(base.get("sender"))).append("userId", base.get("receiver").toString()));
                    queryOr.add(new BasicDBObject("userId", String.valueOf(base.get("sender"))).append("jid", base.get("receiver").toString()));
                    lastquery.append(MongoOperator.OR, queryOr);
                }
            } else {
                lastquery.put("jid", base.get("room_jid_id"));

            }
            // 新增一条
            BasicDBList baslist = new BasicDBList();
            if (type != 2) {
                baslist.add(new BasicDBObject("receiver", sender));
                baslist.add(new BasicDBObject("sender", sender));
                query.append(MongoOperator.OR, baslist);
            } else {
                query.put("room_jid_id", base.get("room_jid_id"));
            }
            //将消息记录中的数据删除
            dbCollection.remove(query);
            query.remove("messageId");
            query.remove("sender");
            DBObject lastMsgObj = dbCollection.find(query).sort(new BasicDBObject("timeSend", -1)).limit(1).one();
            logger.info("last message query>>>>>>>>{}", JSONObject.toJSONString(query));
            BasicDBObject values = new BasicDBObject();
            values.put("messageId", lastMsgObj.get("messageId"));
            values.put("timeSend", new Double(lastMsgObj.get("timeSend").toString()).longValue());
            values.put("content", lastMsgObj.get("content"));
            logger.info("last message lastquery>>>>>>>>{}", JSONObject.toJSONString(lastquery));
            WriteResult writeResult = lastdbCollection.update(lastquery, new BasicDBObject(MongoOperator.SET, values), false, true);
            logger.info("last message update affect>>>>>>>>{}", writeResult.getN());
        } catch (Exception e) {
            e.printStackTrace();
        }
// 		finally {
//			if(cursor != null)
//				cursor.close();
//		}
        return JSONMessage.success();

    }

    /**
     * 单聊清空消息
     *
     * @param toUserId
     * @return
     */
    // type 0 是清空单个   1 是 清空所有
    @RequestMapping("/emptyMyMsg")
    public JSONMessage emptyMsg(@RequestParam(defaultValue = "0") int toUserId, @RequestParam(defaultValue = "0") int type, @RequestParam(defaultValue = "") String roomId) {
        int sender = ReqUtil.getUserId();
        // 群聊、单聊消息
        if (type == 3) {
            Query<Room.Member> qmember = TigBeanUtils.getRoomManager().getRoomDatastore().createQuery(Room.Member.class).field("userId").equal(sender).field("roomId").equal(new ObjectId(roomId));
            UpdateOperations<Member> ops1 = getDatastore().createUpdateOperations(Room.Member.class);
            if (qmember.get() != null) {
                qmember.get().setCleanedTime(DateUtil.currentTimeMilliSeconds());
                ops1.set("cleanedTime", DateUtil.currentTimeMilliSeconds());
                TigBeanUtils.getRoomManager().getRoomDatastore().update(qmember, ops1);
            }
        } else {
            DBCollection dbCollection;
            // 最后一条聊天消息
            DBCollection lastdbCollection;
            BasicDBList queryOr = new BasicDBList();
            try {
                dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
                lastdbCollection = dsForTigase.getDB().getCollection("tig_lastChats");
                BasicDBObject queryAll = new BasicDBObject();
                BasicDBObject lastqueryAll = new BasicDBObject();
                if (type == 1) {
                    queryAll.append("sender", sender);
				/*queryAll.append("contentType", new BasicDBObject(MongoOperator.IN, MsgType.FileTypeArr));
				List<String> fileList=dbCollection.distinct("content", queryAll);
				for(String fileUrl:fileList){
					// 调用删除方法将文件从服务器删除
					ConstantUtil.deleteFile(fileUrl);
				}
				queryAll.remove("contentType");*/
                    BasicDBObject baseAll = (BasicDBObject) dbCollection.findOne(queryAll);
                    //queryOr.add(new BasicDBObject("jid", String.valueOf(baseAll.get("sender"))).append("userId", baseAll.get("receiver").toString()));
                    if (null != baseAll) {
                        queryOr.add(new BasicDBObject("userId", String.valueOf(baseAll.get("sender"))).append("jid", baseAll.get("receiver").toString()));
                        lastqueryAll.append(MongoOperator.OR, queryOr);
                        lastdbCollection.remove(lastqueryAll);
                    }
                    dbCollection.remove(queryAll);
                }
                BasicDBObject query = new BasicDBObject();
                BasicDBObject lastquery = new BasicDBObject();
                query.append("sender", sender);
                if (0 != toUserId) {
                    query.append("receiver", toUserId);
                }
                /*query.append("contentType", new BasicDBObject(MongoOperator.IN, MsgType.FileTypeArr));
                List<String> fileList=dbCollection.distinct("content", query);
                for(String fileUrl:fileList){
                    // 调用删除方法将文件从服务器删除
                    ConstantUtil.deleteFile(fileUrl);
                }
                query.remove("contentType");*/
                // 维护最后一条消息表
                BasicDBObject base = (BasicDBObject) dbCollection.findOne(query);
                if (base != null) {
//				queryOr.add(new BasicDBObject("jid", String.valueOf(base.get("sender"))).append("userId", base.get("receiver").toString()));
                    queryOr.add(new BasicDBObject("userId", String.valueOf(base.get("sender"))).append("jid", base.get("receiver").toString()));
                }
                // 删除消息记录
                dbCollection.remove(query);
                if (queryOr.size() > 0){
                    lastquery.append(MongoOperator.OR, queryOr);
                    lastdbCollection.remove(lastquery);
                }
                if (type == 1) {
                    Query<Room.Member> qmember = TigBeanUtils.getRoomManager().getRoomDatastore().createQuery(Room.Member.class).field("userId").equal(sender);
                    UpdateOperations<Member> ops1 = getDatastore().createUpdateOperations(Room.Member.class);
                    if (qmember.asList() != null) {
                        for (int i = 0; i < qmember.asList().size(); i++) {
                            qmember.asList().get(i).setCleanedTime(DateUtil.currentTimeMilliSeconds());
                            ops1.set("cleanedTime", DateUtil.currentTimeMilliSeconds());
                            ops1.set("deleteLastChatTime", DateUtil.currentTimeSeconds());
                            TigBeanUtils.getRoomManager().getRoomDatastore().update(qmember, ops1);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return JSONMessage.success();
    }

//	//修改消息的已读状态
//	public void modifyIsRead(String messageId) {
//		BasicDBObject dbObj = new BasicDBObject(9);
//		dbObj.put("messageId", messageId);
//	
//		BasicDBObject msgObj = (BasicDBObject) db.getCollection(MSGS_COLLECTION).findOne(dbObj);
//		Map<String,Object> msgBody = JSON.parseObject(msgObj.getString("body").replace("&quot;", "\""), Map.class);
//		msgBody.put("isRead",true);
//		String body = JSON.toJSON(msgBody).toString();
//		db.getCollection(MSGS_COLLECTION).update(dbObj, new BasicDBObject("body", body));
//	}
//	


    //修改消息的已读状态
    @RequestMapping("/changeRead")
    public JSONMessage changeRead(@RequestParam String messageId) throws Exception {
        try {
            DBCollection dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
            BasicDBObject query = new BasicDBObject();
            query.put("messageId", messageId);
            BasicDBObject dbObj = (BasicDBObject) dbCollection.findOne(query);
            String body;
            if (null == dbObj) {
                return JSONMessage.success();
            } else {
                body = dbObj.getString("body");
                if (null == body) {
                    return JSONMessage.success();
                }
            }
            //解析消息体
            Map<String, Object> msgBody = JSON.parseObject(body.replace("&quot;", "\""), Map.class);
            msgBody.put("isRead", 1);
            body = JSON.toJSON(msgBody).toString();
            dbCollection.update(query, new BasicDBObject(MongoOperator.SET, new BasicDBObject("body", body)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return JSONMessage.success();
    }


    //修改消息的内容 1 单聊  2 群聊
    @RequestMapping("/changeContent")
    public JSONMessage changeContent(@RequestParam(defaultValue = "1") int type,
                                     @RequestParam String messageId,
                                     @RequestParam String content,
                                     @RequestParam(defaultValue = "") String roomJid) throws Exception {
        try {
            DBCollection dbCollection = null;
            if (type == 1) {
                dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
            } else {
                dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + roomJid);
            }

            BasicDBObject query = new BasicDBObject();
            query.put("messageId", messageId);
            BasicDBObject dbObj = (BasicDBObject) dbCollection.findOne(query);
            String body;
            if (null == dbObj) {
                return JSONMessage.failure("消息不存在");
            } else {
                body = dbObj.getString("body");
                if (null == body) {
                    return JSONMessage.failure("修改失败");
                }
            }
            //解析消息体
            Map<String, Object> msgBody = JSON.parseObject(body.replace("&quot;", "\""), Map.class);
            log.info("msgBody ={}",msgBody);
            msgBody.put("content", content);
            body = JSON.toJSON(msgBody).toString();
            dbCollection.update(query, new BasicDBObject(MongoOperator.SET, new BasicDBObject("body", body)));
            log.info("changeContent success");
        } catch (Exception e) {
            log.error("changeContent error {}",e.getMessage(),e);
            return JSONMessage.failure("修改失败");
        }
        JSONObject result = new JSONObject();
        result.put("content", content);
        return JSONMessage.success("修改成功",result);
    }


    /**
     * 双向撤回消息 主群主才能操作
     *  @param roomId 群组jid
     *  @param access_token 当前时间戳
     *  @param time 当前时间戳
     *  @param  secret
     *  @return type=1 返回三段消息删除成功标识
     *
     */
    @RequestMapping("/delectRoomMsg")
    public JSONMessage updateIsCreateRoomByAPI(@RequestParam(defaultValue="")  String roomId, @RequestParam(defaultValue="")  Long time, @RequestParam(defaultValue="")  String access_token, @RequestParam(defaultValue="")  String secret) {
        String accessToken = KSessionUtil.getAccess_token(ReqUtil.getUserId());
        log.info("用户accessToken========"+accessToken+"====="+access_token);
        if(!accessToken.equals(access_token)){
            log.info("========"+accessToken+"====="+access_token);
            return JSONMessage.failure("对不起！不是同一用户！");
        }
//      KSessionUtil.getUserIdBytoken(access_token);
        User user = TigBeanUtils.getUserManager().getUser(ReqUtil.getUserId());
        log.info("用户user========"+user.getUserId()+"====="+user.getUsername());
        ObjectId roomObjId = TigBeanUtils.getRoomManager().getRoomId(roomId);
        if (null != roomObjId) {
            Member member = TigBeanUtils.getRoomManager().getMember(roomObjId, ReqUtil.getUserId());
            if (null != member) {
                log.info("用户对应的角色========"+member.getId()+"角色"+member.getRole());
               // 成员角色：1=创建者(群主)、2=管理员、3=普通成员、4=隐身人、5=监控人
                if(1 ==member.getRole()){
                    if(time>0){
                        time = time * 1000;
                    }
                    // 通过房间id 查询群组消息
                    DBCollection mucmsgdbCollection = dsForRoom.getDB().getCollection("mucmsg_" + roomId);
                    BasicDBObject mucmsgAll = new BasicDBObject();
                    mucmsgAll.append("room_jid_id",roomId);
                    mucmsgAll.append("ts", new BasicDBObject(MongoOperator.LTE, time));
                    // 删除服务器  文件地址
                    List<String> fileList = mucmsgdbCollection.distinct("content", mucmsgAll);
                    for (String url : fileList) {
                        ConstantUtil.deleteFile(url);
                    }
                    mucmsgdbCollection.remove(mucmsgAll);
                    //由于在imRoom数据库中没有找到muc_history表，所以注释掉下面的代码
                    //删除消息队列中的存储
//                    DBCollection mucHistoryRoomCollection = dsForRoom.getDB().getCollection("muc_history");
//                    if(mucHistoryRoomCollection != null){
//                        BasicDBObject mucHistoryAll = new BasicDBObject();
//                        mucHistoryAll.append("room_jid",new BasicDBObject(MongoOperator.REGEX,roomId));
//                        mucHistoryAll.append("timestamp", new BasicDBObject(MongoOperator.LTE, new Date(time)));
//                        mucHistoryRoomCollection.remove(mucHistoryAll);
//                    }
                    DBCollection mucHistoryTigaseCollection = dsForTigase.getDB().getCollection("muc_history");
                    if(mucHistoryTigaseCollection != null){
                        BasicDBObject mucHistoryAll = new BasicDBObject();
                        mucHistoryAll.append("timestamp", new BasicDBObject(MongoOperator.LTE, new Date(time)));
                        mucHistoryAll.append("room_jid",new BasicDBObject(MongoOperator.REGEX,roomId));
                        mucHistoryTigaseCollection.remove(mucHistoryAll);
                    }
//                  //todo  查询会话列表数据 清空房间的会话消息  没有那个房间的id
                    DBCollection lastChatsCollection = dsForTigase.getDB().getCollection("tig_lastChats");
                    BasicDBObject lastChatsAll = new BasicDBObject();
                    lastChatsAll.append("jid",roomId);
                    lastChatsAll.append("timeSend", new BasicDBObject(MongoOperator.LTE, time/1000));
                    lastChatsCollection.remove(lastChatsAll);
                    MessageBean messageBean = new MessageBean();
                    messageBean.setRoomJid(roomId);
                    messageBean.setType(MsgType.TYPE_MORE_WITHDRAW);
                    messageBean.setMessageId(StringUtil.randomUUID());
                    messageBean.setFromUserId(user.getUserId()+"");
                    messageBean.setFromUserName(user.getNickname());
                    messageBean.setContent("双向撤回消息");
                    // 发送群聊
                    try {
                        KXMPPServiceImpl.getInstance().sendMsgToGroupByJid(roomId, messageBean);
//                      mucmsgdbCollection.remove(mucmsgAll);
                      return JSONMessage.success("双向撤回消息成功");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return JSONMessage.failure("对不起！你不是群主！没有权限");

    }

    private static class Entry {
        private String packetId;
        private long stamp = System.currentTimeMillis();
        private int reCount = 0;

        public void refreshStamp() {
            this.stamp = System.currentTimeMillis();
        }

        public void addReCount() {
            this.reCount++;
            this.refreshStamp();
        }


        public String getPacketId() {
            return packetId;
        }

        public void setPacketId(String packetId) {
            this.packetId = packetId;
        }

        public long getStamp() {
            return stamp;
        }

        public int getReCount() {
            return reCount;
        }

        public void setReCount(int reCount) {
            this.reCount = reCount;
        }
    }

    private static class RevokeTimeoutTask extends TimerTask {

        private String messageId;
        private int type;
        private String roomJID;

        public RevokeTimeoutTask(String messageId, int type, String roomJID) {
            this.messageId = messageId;
            this.type = type;
            this.roomJID = roomJID;
        }

        @Override
        public void run() {
            RedissonClient redissonClient = SpringBeansUtils.getBeane(RedissonClient.class);
            Object ret = redissonClient.getMap(RevokeTASK).get(this.messageId);
            if (ret != null) {
                Entry r = (Entry) ret;
                if (r.getReCount() > 10) {
                    redissonClient.getMap(RevokeTASK).remove(this.messageId);
                    this.cancel();
                } else {
                    BasicDBObject query = new BasicDBObject();
                    query.append("messageId", this.messageId);
                    Datastore dsForTigase = SpringBeansUtils.getBean("dsForTigase");
                    Datastore dsForRoom = SpringBeansUtils.getBean("dsForRoom");
                    DBCollection dbCollection = null;
                    if (type == 1) {
                        dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
                    } else {
                        dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + roomJID);
                    }
                    WriteResult wr = dbCollection.remove(query);
                    if (wr.getN() > 0) {
                        redissonClient.getMap(RevokeTASK).remove(this.messageId);
                        this.cancel();
                    } else {
                        r.addReCount();
                        redissonClient.getMap(RevokeTASK).put(this.messageId, r);
                    }
                }
            }
        }
    }

}





















