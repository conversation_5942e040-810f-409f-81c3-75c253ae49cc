package com.tig.im.controller;

import cn.tig.commons.autoconfigure.KApplicationProperties.ApiAccessConfig;
import cn.tig.commons.autoconfigure.KApplicationProperties.InviteConfig;
import cn.tig.commons.constants.KConstants;
import cn.tig.commons.constants.KConstants.Result;
import cn.tig.commons.constants.MsgType;
import cn.tig.commons.ex.ServiceException;
import cn.tig.commons.support.mongo.MongoOperator;
import cn.tig.commons.utils.*;
import cn.tig.commons.vo.JSONMessage;
import cn.tig.commons.vo.PhoneModel;
import cn.tig.im.invite.UserInviteManager;
import cn.tig.im.invite.UserInvitePassCard;
import cn.tig.im.model.*;
import cn.tig.im.opensdk.entity.TigOpenAccount;
import cn.tig.im.opensdk.entity.TigOpenApp;
import cn.tig.im.opensdk.entity.TigOpenCheckLog;
import cn.tig.im.service.impl.LiveRoomManagerImpl;
import cn.tig.im.service.impl.RoomManagerImplForIM;
import cn.tig.im.service.impl.UserManagerImpl;
import cn.tig.im.utils.ConstantUtil;
import cn.tig.im.utils.KSessionUtil;
import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.*;
import cn.tig.im.vo.LiveRoom.LiveRoomMember;
import cn.tig.im.vo.Room.Member;
import cn.tig.im.vo.User.UserLoginLog;
import cn.tig.service.KXMPPServiceImpl;
import cn.tig.service.KXMPPServiceImpl.MessageBean;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.*;
import com.tig.im.utils.GeoIP;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Criteria;
import org.mongodb.morphia.query.FindOptions;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * @Package: com.tig.im.controller
 * @Author:
 * @Date: 2019/7/3 14:44
 * @Description 后台管理请求控制类
 */
@RestController
@RequestMapping("/console")
public class AdminController extends AbstractController {

    public static final String LOGIN_USER_KEY = "LOGIN_USER";

    @Resource(name = "dsForRW")
    private Datastore dsForRW;

    @Resource(name = "dsForTigase")
    private Datastore dsForTigase;

    @Resource(name = "dsForRoom")
    private Datastore dsForRoom;
    private UpdateOperations<ForbidDetails> query;

    private static RoomManagerImplForIM getRoomManagerImplForIM() {
        RoomManagerImplForIM roomManagerImplForIM = TigBeanUtils.getRoomManagerImplForIM();
        return roomManagerImplForIM;
    }

    ;

    private static LiveRoomManagerImpl getLiveRoomManager() {
        LiveRoomManagerImpl liveRoomManagerImpl = TigBeanUtils.getLiveRoomManager();
        return liveRoomManagerImpl;
    }

    private static UserManagerImpl getUserManager() {
        UserManagerImpl userManagerImpl = TigBeanUtils.getUserManager();
        return userManagerImpl;
    }

    private static UserInviteManager getUserInviteManager() {
        UserInviteManager userInviteManager = TigBeanUtils.getUserInviteManager();
        return userInviteManager;
    }

    @RequestMapping(value = "/adminConfig")
    public JSONMessage getAdminConfig() {
        Map<String, Object> adminConfig = new HashMap<>();
        adminConfig.put("apiKey", appConfig.getApiKey());
        adminConfig.put("registerInviteCode", TigBeanUtils.getAdminManager().getConfig().getRegisterInviteCode());
        ApiAccessConfig apiAccessConfig = TigBeanUtils.getLocalSpringBeanManager().getApplicationConfig().getApiAccessConfig();
        adminConfig.put("apiAccessStatus", apiAccessConfig.isAccessIsSet());
        adminConfig.put("nodeConfigStatus", TigBeanUtils.getAdminManager().getClientConfig().getIsNodesStatus());
        adminConfig.put("appDiscoverStatus", TigBeanUtils.getAdminManager().getClientConfig().getIsDiscoverStatus());
        adminConfig.put("appTabBarStatus", TigBeanUtils.getAdminManager().getClientConfig().getIsTabBarStatus());
        adminConfig.put("tlPayIsOpen", TigBeanUtils.getLocalSpringBeanManager().getApplicationConfig().getSysPayConfig().isTlPayIsOpen());

        return JSONMessage.success(adminConfig);
    }

    /**
     * 获取服务器配置
     *
     * @return
     */
    @RequestMapping(value = "/config")
    public JSONMessage getConfig() {
        Config config = TigBeanUtils.getAdminManager().getConfig();
        config.setDistance(ConstantUtil.getAppDefDistance());
        return JSONMessage.success(null, config);
    }

    /**
     * 获取客户端配置
     *
     * @return
     */
    @RequestMapping(value = "/clientConfig")
    public JSONMessage getClientConfig() {
        ClientConfig clientConfig = TigBeanUtils.getAdminManager().getClientConfig();
        return JSONMessage.success(null, clientConfig);
    }

    @RequestMapping(value = "/chat_logs", method = {RequestMethod.GET})
    public ModelAndView chat_logs(@RequestParam(defaultValue = "0") long startTime,
                                  @RequestParam(defaultValue = "0") long endTime, @RequestParam(defaultValue = "0") int pageIndex,
                                  @RequestParam(defaultValue = "10") int pageSize, HttpServletRequest request) {
        // User user = getUser();

        DBCollection dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
        BasicDBObject q = new BasicDBObject();
        // q.put("sender", user.getUserId());
        if (0 != startTime) {
            q.put("ts", new BasicDBObject("$gte", startTime));
        }
        if (0 != endTime) {
            q.put("ts", new BasicDBObject("$lte", endTime));
        }

        long total = dbCollection.count(q);
        java.util.List<DBObject> pageData = Lists.newArrayList();

        DBCursor cursor = dbCollection.find(q).skip(pageIndex * pageSize).limit(pageSize);
        while (cursor.hasNext()) {
            BasicDBObject dbObj = (BasicDBObject) cursor.next();
            if (1 == dbObj.getInt("direction")) {
                int sender = dbObj.getInt("receiver");
                dbObj.put("receiver_nickname", getUserManager().getUser(sender).getNickname());
            }
            pageData.add(dbObj);
        }
        request.setAttribute("page", new PageVO(pageData, total, pageIndex, pageSize));
        return new ModelAndView("chat_logs");
    }

    @RequestMapping(value = "/chat_logs_all")
    public JSONMessage chat_logs_all(@RequestParam(defaultValue = "0") long startTime,
                                     @RequestParam(defaultValue = "0") long endTime, @RequestParam(defaultValue = "0") int sender,
                                     @RequestParam(defaultValue = "0") int receiver, @RequestParam(defaultValue = "0") int page,
                                     @RequestParam(defaultValue = "10") int limit, @RequestParam(defaultValue = "") String keyWord, HttpServletRequest request) throws Exception {
        DBCollection dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
        BasicDBObject q = new BasicDBObject();
        if (0 == receiver) {
            q.put("receiver", new BasicDBObject("$ne", 10005));
            q.put("direction", 0);
        } else {
            q.put("direction", 0);
            q.put("receiver", BasicDBObjectBuilder.start("$eq", receiver).add("$ne", 10005).get());
        }
        if (0 == sender) {
            q.put("sender", new BasicDBObject("$ne", 10005));
            q.put("direction", 0);
        } else {
            q.put("direction", 0);
            q.put("sender", BasicDBObjectBuilder.start("$eq", sender).add("$ne", 10005).get());
        }
        if (!StringUtil.isEmpty(keyWord)) {
            q.put("content", new BasicDBObject(MongoOperator.REGEX, keyWord));
        }

        if (0 != startTime) {
            q.put("ts", new BasicDBObject("$gte", startTime));
        }
        if (0 != endTime) {
            q.put("ts", new BasicDBObject("$lte", endTime));
        }

        long total = dbCollection.count(q);
        java.util.List<DBObject> pageData = Lists.newArrayList();

        DBCursor cursor = dbCollection.find(q).sort(new BasicDBObject("_id", -1)).skip((page - 1) * limit).limit(limit);
        PageResult<DBObject> result = new PageResult<>();
        while (cursor.hasNext()) {
            BasicDBObject dbObj = (BasicDBObject) cursor.next();
            @SuppressWarnings("deprecation")
            String unescapeHtml3 = StringEscapeUtils.unescapeHtml3((String) dbObj.get("body"));
            JSONObject body = JSONObject.parseObject(unescapeHtml3);
            if (null != body.get("isEncrypt") && "1".equals(body.get("isEncrypt").toString())) {
                dbObj.put("isEncrypt", 1);
            } else {
                dbObj.put("isEncrypt", 0);
            }
            try {
                dbObj.put("sender_nickname", getUserManager().getNickName(dbObj.getInt("sender")));
            } catch (Exception e) {
                dbObj.put("sender_nickname", "未知");
            }
            try {
                dbObj.put("receiver_nickname", getUserManager().getNickName(dbObj.getInt("receiver")));
            } catch (Exception e) {
                dbObj.put("receiver_nickname", "未知");
            }
            try {
                dbObj.put("content", JSON.parseObject(dbObj.getString("body").replace("&quot;", "\""), Map.class).get("content"));
            } catch (Exception e) {
                dbObj.put("content", "--");
            }

            pageData.add(dbObj);

        }
        result.setData(pageData);
        result.setCount(total);
        return JSONMessage.success(result);
    }

    @RequestMapping(value = "/chat_logs_all/del", method = {RequestMethod.POST})
    public JSONMessage chat_logs_all_del(@RequestParam(defaultValue = "0") long startTime,
                                         @RequestParam(defaultValue = "0") long endTime, @RequestParam(defaultValue = "0") int sender,
                                         @RequestParam(defaultValue = "0") int receiver, @RequestParam(defaultValue = "0") int pageIndex,
                                         @RequestParam(defaultValue = "25") int pageSize, HttpServletRequest request, HttpServletResponse response) throws Exception {
        DBCollection dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
        BasicDBObject q = new BasicDBObject();
        if (0 == sender) {
            q.put("sender", new BasicDBObject("$ne", 10005));
        } else {
            q.put("sender", BasicDBObjectBuilder.start("$eq", sender).add("$ne", 10005).get());
        }
        if (0 == receiver) {
            q.put("receiver", new BasicDBObject("$ne", 10005));
        } else {
            q.put("receiver", BasicDBObjectBuilder.start("$eq", receiver).add("$ne", 10005).get());
        }
        if (0 != startTime) {
            q.put("ts", new BasicDBObject("$gte", startTime));
        }
        if (0 != endTime) {
            q.put("ts", new BasicDBObject("$lte", endTime));
        }
        dbCollection.remove(q);
        return JSONMessage.success();

    }

    @RequestMapping(value = "/deleteChatMsgs")
    public JSONMessage deleteChatMsgs(@RequestParam(defaultValue = "") String msgId, @RequestParam(defaultValue = "0") int type) {
        DBCollection dbCollection = dsForTigase.getDB().getCollection("tig_msgs");
        BasicDBObject q = new BasicDBObject();
        try {
            if (0 == type) {
                if (StringUtil.isEmpty(msgId)) {
                    return JSONMessage.failure("参数有误");
                } else {
                    String[] msgIds = StringUtil.getStringList(msgId);
                    for (String strMsgId : msgIds) {
                        q.put("_id", new ObjectId(strMsgId));
                        DBObject rt = dbCollection.findOne(new ObjectId(strMsgId));
                        if (rt != null) {
                            Integer receiver = ((Long) rt.get("receiver")).intValue();
                            Integer sender = ((Long) rt.get("sender")).intValue();
                            String messageId = (String) rt.get("messageId");
//
                            ThreadUtil.executeInThread((a) -> {
                                MessageBean msg = new MessageBean();
                                msg.setContent(rt);
                                msg.setMsgType(0);
                                msg.setType(MsgType.TYPE_REMOVE);
                                msg.setMessageId(UUID.randomUUID().toString().replace("-", ""));
                                msg.setTimeSend(DateUtil.currentTimeSeconds());
                                KXMPPServiceImpl.getInstance().send(msg, Lists.newArrayList(sender, receiver));
                            });
                        }
                        dbCollection.remove(q);
                    }
                }
            } else if (1 == type) {
                // 删除一个月前的聊天记录
                long onedayNextDay = DateUtil.getOnedayNextDay(DateUtil.currentTimeSeconds(), 30, 1);
                logger.info("上个月的时间：" + onedayNextDay);
                q.put("timeSend", new BasicDBObject("$lte", onedayNextDay));
                dbCollection.remove(q);
            } else if (2 == type) {
                final int num = 100000;
                int count = dbCollection.find().count();
                if (count <= num) {
                    throw new ServiceException("数量小于等于" + num);
                }
                // 删除十万条前的聊天记录
                DBCursor cursor = dbCollection.find().sort(new BasicDBObject("timeSend", -1)).skip(num).limit(count);
                List<DBObject> list = cursor.toArray();
                for (DBObject dbObject : list) {
                    dbCollection.remove(dbObject);
                }
                logger.info("超过" + num + "的条数有：" + list.size());
            }
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getErrMessage());
        }
        return JSONMessage.success();
    }

    @RequestMapping(value = "/deleteRoom")
    public JSONMessage deleteRoom(@RequestParam(defaultValue = "") String roomId, @RequestParam(defaultValue = "0") Integer userId) {
        try {
            DBCollection dbCollection = dsForTigase.getCollection(Room.class);
            getRoomManagerImplForIM().delete(new ObjectId(roomId), userId);
            dbCollection.remove(new BasicDBObject("_id", new ObjectId(roomId)));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    // 邀请用户加入群成员
    @RequestMapping(value = "/inviteJoinRoom")
    public JSONMessage inviteJoinRoom(@RequestParam(defaultValue = "") String roomId, @RequestParam(defaultValue = "") String userIds, @RequestParam(defaultValue = "") Integer inviteUserId) {
        try {
            if (StringUtil.isEmpty(userIds)) {
                return JSONMessage.failure("请选择邀请的人");
            }
            Room room = TigBeanUtils.getRoomManager().getRoom(new ObjectId(roomId));
            if (null == room) {
                return JSONMessage.failure("群组不存在 或已解散!");
            } else if (-1 == room.getS()) {
                return JSONMessage.failure("该群组已被后台锁定!");
            } else {
                List<Integer> userIdList = StringUtil.getIntList(userIds, ",");
                if (room.getMaxUserSize() < room.getUserSize() + userIdList.size()) {
                    return JSONMessage.failure("群人数已满 不能加入!");
                }
                User user = new User();
                user.setUserId(inviteUserId);
                user.setNickname("后台管理员");
                getRoomManagerImplForIM().consoleJoinRoom(user, new ObjectId(roomId), userIdList);
                return JSONMessage.success();
            }
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 删除房间成员
     *
     * @param roomId
     * @param userId
     * @param pageIndex
     * @return
     */
    @RequestMapping(value = "/deleteMember")
    public JSONMessage deleteMember(@RequestParam String roomId, @RequestParam String userId,
                                    @RequestParam(defaultValue = "0") int pageIndex, @RequestParam String adminUserId) {
        try {
            if (StringUtil.isEmpty(userId) || StringUtil.isEmpty(adminUserId)) {
                return JSONMessage.failure("参数有误");
            } else {
                User user = getUserManager().getUser(Integer.parseInt(adminUserId));
                if (null != user) {
                    String[] userIds = StringUtil.getStringList(userId);
                    for (String strUserids : userIds) {
                        Integer strUserId = Integer.valueOf(strUserids);
                        getRoomManagerImplForIM().deleteMember(user, new ObjectId(roomId), strUserId);
                    }
                } else {
                    return JSONMessage.failure("用户不存在");
                }
            }
            return JSONMessage.success();
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    @RequestMapping(value = "/deleteUser")
    public JSONMessage deleteUser(@RequestParam(defaultValue = "") String userId) {
        try {
            if (!StringUtil.isEmpty(userId)) {
                String[] strUserIds = StringUtil.getStringList(userId, ",");
                getUserManager().deleteUser(strUserIds);
            }
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    public User getUser() {
        Object obj = RequestContextHolder.getRequestAttributes().getAttribute(LOGIN_USER_KEY,
                RequestAttributes.SCOPE_SESSION);
        return null == obj ? null : (User) obj;
    }

    @RequestMapping(value = "/groupchat_logs", method = {RequestMethod.GET})
    public ModelAndView groupchat_logs(@RequestParam(defaultValue = "") String room_jid_id,
                                       @RequestParam(defaultValue = "0") long startTime, @RequestParam(defaultValue = "0") long endTime,
                                       @RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "10") int pageSize,
                                       HttpServletRequest request) {
        ModelAndView mav = new ModelAndView("groupchat_logs");
        Object historyList = getRoomManagerImplForIM().selectHistoryList(getUser().getUserId(), 0, pageIndex, pageSize);
        if (!StringUtil.isEmpty(room_jid_id)) {
            DBCollection dbCollection = dsForTigase.getDB().getCollection("tig_muc_msgs");

            BasicDBObject q = new BasicDBObject();
            // q.put("room_jid_id", room_jid_id);
            if (0 != startTime) {
                q.put("ts", new BasicDBObject("$gte", startTime));
            }
            if (0 != endTime) {
                q.put("ts", new BasicDBObject("$lte", endTime));
            }
            long total = dbCollection.count(q);
            java.util.List<DBObject> pageData = Lists.newArrayList();

            DBCursor cursor = dbCollection.find(q).sort(new BasicDBObject("ts", -1)).skip(pageIndex * pageSize)
                    .limit(pageSize);
            while (cursor.hasNext()) {
                pageData.add(cursor.next());
            }
            mav.addObject("page", new PageVO(pageData, total, pageIndex, pageSize));
        }
        mav.addObject("historyList", historyList);
        return mav;
    }

    /**
     * 群聊记录
     *
     * @param startTime
     * @param endTime
     * @param room_jid_id
     * @param page
     * @param limit
     * @param request
     * @return
     */
    @RequestMapping(value = "/groupchat_logs_all")
    public JSONMessage groupchat_logs_all(@RequestParam(defaultValue = "0") long startTime,
                                          @RequestParam(defaultValue = "0") long endTime, @RequestParam(defaultValue = "") String room_jid_id,
                                          @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit, @RequestParam(defaultValue = "") String keyWord,
                                          HttpServletRequest request) {
        DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + room_jid_id);

        BasicDBObject q = new BasicDBObject();
        if (0 != startTime) {
            q.put("ts", new BasicDBObject("$gte", startTime));
        }
        if (0 != endTime) {
            q.put("ts", new BasicDBObject("$lte", endTime));
        }
        if (!StringUtil.isEmpty(keyWord)) {
            if (StringUtil.isNumeric(keyWord)){
                BasicDBList orgList = new BasicDBList();
                orgList.add(new BasicDBObject("content", new BasicDBObject(MongoOperator.REGEX, keyWord)));
                orgList.add(new BasicDBObject("sender", new BasicDBObject("$eq", Integer.valueOf(keyWord))));
                q.append(MongoOperator.OR, orgList);
            }else {
                q.put("content", new BasicDBObject(MongoOperator.REGEX, keyWord));
            }
        }

        long total = dbCollection.count(q);
        java.util.List<DBObject> pageData = Lists.newArrayList();
        PageResult<DBObject> result = new PageResult<>();
        DBCursor cursor = dbCollection.find(q).sort(new BasicDBObject("ts", -1)).skip((page - 1) * limit).limit(limit);
        while (cursor.hasNext()) {
            BasicDBObject dbObj = (BasicDBObject) cursor.next();
            @SuppressWarnings("deprecation")
            String unescapeHtml3 = StringEscapeUtils.unescapeHtml3((String) dbObj.get("body"));
            JSONObject body = JSONObject.parseObject(unescapeHtml3);
            if (null != body.get("isEncrypt") && "1".equals(body.get("isEncrypt").toString())) {
                dbObj.put("isEncrypt", 1);
            } else {
                dbObj.put("isEncrypt", 0);
            }
            try {
                Map<?, ?> params = JSON.parseObject(dbObj.getString("body").replace("&quot;", "\""), Map.class);
                dbObj.put("content", params.get("content"));
                dbObj.put("fromUserName", params.get("fromUserName"));
            } catch (Exception e) {
                dbObj.put("content", "--");
            }
            pageData.add(dbObj);

        }
//        int count = 4999;
//        Comparator beanPropertyCompare = new BeanComparator("content");
//        Comparator reverseComparator = new ComparatorChain(beanPropertyCompare, true);
//        Collections.sort(pageData, reverseComparator);
//        for (DBObject dbObject:pageData){
//            if (!Integer.valueOf(String.valueOf(dbObject.get("content"))).equals(count)){
//                logger.info(count);
//            }
//            count--;
//        }
        result.setData(pageData);
        result.setCount(total);
        return JSONMessage.success(result);
    }

    @RequestMapping(value = "/groupchat_logs_all/del")
    public JSONMessage groupchat_logs_all_del(@RequestParam(defaultValue = "0") long startTime,
                                              @RequestParam(defaultValue = "0") long endTime, @RequestParam(defaultValue = "") String msgId,
                                              @RequestParam(defaultValue = "") String room_jid_id, @RequestParam(defaultValue = "0") int pageIndex,
                                              @RequestParam(defaultValue = "25") int pageSize, HttpServletRequest request, HttpServletResponse response) throws Exception {
        DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + room_jid_id);

        BasicDBObject q = new BasicDBObject();
        if (StringUtil.isEmpty(msgId)) {
            return JSONMessage.failure("参数有误");
        } else {

            String[] msgIds = StringUtil.getStringList(msgId);
            for (String strMsgId : msgIds) {
                q.put("_id", new ObjectId(strMsgId));
                DBObject rt = dbCollection.findOne(new ObjectId(strMsgId));
                if (rt != null) {
                    String roomJid = "" + rt.get("room_jid_id");
////                            String sender = ""+ rt.get("sender");
                    String messageId = (String) rt.get("messageId");
                    ThreadUtil.executeInThread((a) -> {
                        MessageBean msg = new MessageBean();
                        msg.setContent(rt);
                        msg.setMessageId(UUID.randomUUID().toString().replace("-", ""));
                        msg.setMsgType(1);
                        msg.setType(MsgType.TYPE_REMOVE);
                        msg.setTo(roomJid);
                        msg.setToUserId(roomJid);
                        msg.setRoomJid(roomJid);
                        msg.setTimeSend(DateUtil.currentTimeSeconds());
                        KXMPPServiceImpl.getInstance().send(msg);
                    });
                }
                dbCollection.remove(q);
            }
        }
        if (0 != startTime) {
            q.put("ts", new BasicDBObject("$gte", startTime));
        }
        if (0 != endTime) {
            q.put("ts", new BasicDBObject("$lte", endTime));
        }

        dbCollection.remove(q);
        return JSONMessage.success();
    }

    @RequestMapping(value = "/groupchatMsgDel")
    public JSONMessage groupchatMsgDel(@RequestParam(defaultValue = "") String roomJid,
                                       @RequestParam(defaultValue = "0") int type) {
        DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + roomJid);
        BasicDBObject q = new BasicDBObject();
        try {
            if (0 == type) {
                // 删除一个月前的聊天记录
                long onedayNextDay = DateUtil.getOnedayNextDay(DateUtil.currentTimeSeconds(), 30, 1);
                logger.info("上个月的时间：" + onedayNextDay);
                q.put("timeSend", new BasicDBObject("$lte", onedayNextDay));
                dbCollection.remove(q);
            } else if (1 == type) {
                final int num = 100000;
                int count = dbCollection.find().count();
                if (count <= num) {
                    throw new ServiceException("数量小于等于" + num);
                }
                // 删除十万条前的聊天记录
                DBCursor cursor = dbCollection.find().sort(new BasicDBObject("timeSend", -1)).skip(num).limit(count);
                List<DBObject> list = cursor.toArray();
                for (DBObject dbObject : list) {
                    dbCollection.remove(dbObject);
                }
                logger.info("超过" + num + "的条数有：" + list.size());
            }
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getErrMessage());
        }
        return JSONMessage.success();
    }

    @RequestMapping(value = {"", "/"})
    public ModelAndView index(HttpServletRequest request, HttpServletResponse response) {
        return new ModelAndView("userStatus");
    }

    @RequestMapping(value = "/login", method = {RequestMethod.GET})
    public void openLogin(HttpServletRequest request, HttpServletResponse response) {
        try {
            String path = request.getContextPath() + "/pages/console/login1.html";
            response.sendRedirect(path);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * @param request
     * @param response
     * @return
     * @throws Exception
     * @Description: 后台管理登录：允许超级管理员，管理员，游客，客服，财务登录
     **/
    @RequestMapping(value = "/login", method = {RequestMethod.POST})
    public JSONMessage login(HttpServletRequest request, HttpServletResponse response) throws Exception {
        final Integer code = 86;
        String account = request.getParameter("account");
        String password = request.getParameter("password");
        String areaCode = request.getParameter("areaCode");
        String imgCode = request.getParameter("imgCode");
        String googleVal = request.getParameter("googleVal");
        String isGoogleSec = request.getParameter("isGoogleSec");
        Map<String, Object> params = new HashMap<String, Object>();
        if (StringUtil.isEmpty(imgCode)) {
            return JSONMessage.failureByErrCode(KConstants.ResultCode.NullImgCode, "zh", params);
        } else {
            if (!TigBeanUtils.getSMSService().checkImgCode(account, imgCode)) {
                String key = String.format(KConstants.Key.IMGCODE, account);
                String cached = TigBeanUtils.getRedisCRUD().get(key);
                logger.info("ImgCodeError  getImgCode " + cached + "  imgCode " + imgCode);
                return JSONMessage.failureByErrCode(KConstants.ResultCode.ImgCodeError, "zh", params);
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        User user = getUserManager().getUser((StringUtil.isEmpty(areaCode) ? (code + account) : (areaCode + account)));
        if (null == user) {
            user = TigBeanUtils.getUserRepository().getUserByAccount(account);
        }
        if (null == user) {
            return JSONMessage.failure("账号不存在");
        }
        if ("true".equals(isGoogleSec)) {
            boolean result = false;
            if ("".equals(googleVal)) {
                return JSONMessage.failure("Google动态验证码不能为空");
            } else {
                String topMsg = TigBeanUtils.getUserManager().topUserLock(user);
                if (topMsg != null){
                    return JSONMessage.failure(topMsg);
                }
                long time = System.currentTimeMillis();
                GoogleAuthenticatorUtil g = new GoogleAuthenticatorUtil();
                String keySec = String.format(KSessionUtil.GET_GOOGLE_SEC_BY_USERID, String.valueOf(user.getUserId()));
                String googleSec = KSessionUtil.getRedisCRUD().get(keySec);
                logger.info("googleSec " + googleSec);
                result = g.check_code(googleSec, googleVal, time);
                if (!result) {
                    String msgStr = TigBeanUtils.getUserManager().isUserLock(user,false,"Google动态验证码不正确");
                    if (msgStr != null){
                        return JSONMessage.failure(msgStr);
                    }
                    return JSONMessage.failure("Google动态验证码不正确");
                }
            }
        }
        Role userRole = TigBeanUtils.getRoleManager().getUserRole(user.getUserId(), null, 5);
        /*
         * List<Role> userRoles =
         * TigBeanUtils.getRoleManager().getUserRoles(user.getUserId(), null, 0);
         * if(userRoles.size()>0 && null != userRoles){
         * if(userRoles.contains(o)) }
         */
        if (null == userRole) {
            return JSONMessage.failure("权限不足");
        }
        if (null != userRole && -1 == userRole.getStatus()) {
            return JSONMessage.failure("您的账号已被禁用");
        }
        /*
         * if (userRole.getRole() !=1 && userRole.getRole() !=5 &&
         * userRole.getRole() !=6) return JSONMessage.failure("权限不足");
         */
        String topMsg = TigBeanUtils.getUserManager().topUserLock(user);
        if (topMsg != null){
            return JSONMessage.failure(topMsg);
        }
        if (user != null && password.equals(user.getPassword())) {
            RequestContextHolder.getRequestAttributes().setAttribute(LOGIN_USER_KEY, user, RequestAttributes.SCOPE_SESSION);
            Map<String, Object> tokenMap = KSessionUtil.loginSaveAccessToken(user.getUserId(), user.getUserId(), null);

            map.put("access_Token", tokenMap.get("access_token"));
            map.put("adminId", user.getUserId());
            map.put("account", user.getUserId() + "");
            map.put("apiKey", appConfig.getApiKey());
            map.put("role", userRole.getRole() + "");
            map.put("nickname", user.getNickname());
            map.put("googleValue", user.getGoogleDynamicValue() == null ? "" : user.getGoogleDynamicValue());
            map.put("registerInviteCode", TigBeanUtils.getAdminManager().getConfig().getRegisterInviteCode());

            ApiAccessConfig apiAccessConfig = TigBeanUtils.getLocalSpringBeanManager().getApplicationConfig().getApiAccessConfig();
            map.put("apiAccessStatus", apiAccessConfig.isAccessIsSet());
            map.put("nodeConfigStatus", TigBeanUtils.getAdminManager().getClientConfig().getIsNodesStatus());
            map.put("appDiscoverStatus", TigBeanUtils.getAdminManager().getClientConfig().getIsDiscoverStatus());
            map.put("appTabBarStatus", TigBeanUtils.getAdminManager().getClientConfig().getIsTabBarStatus());
            // 维护最后登录时间
            // updateLastLoginTime(admin.getId(),admin.getPassword(),admin.getRole(),admin.getState(),DateUtil.currentTimeSeconds());
//            updateLastLoginTime(user.getUserId(),userRole.getRole());
            TigBeanUtils.getRoleManager().modifyRole(userRole);
            TigBeanUtils.getUserManager().isUserLock(user,true,null);
            return JSONMessage.success(map);
        }
        String msgStr = TigBeanUtils.getUserManager().isUserLock(user,false,"帐号或密码不正确");
        if (msgStr != null){
            return JSONMessage.failure(msgStr);
        }
        return JSONMessage.failure("帐号或密码错误！");
    }

//    private void updateLastLoginTime(Integer userId,byte userType) {
//        Role role = new Role(userId,userType);
//        TigBeanUtils.getRoleManager().modifyRole(role);
//    }

    @RequestMapping(value = "/logout", method = {RequestMethod.GET})
    public void logout(HttpServletRequest request, HttpServletResponse response) throws Exception {
        request.getSession().removeAttribute(LOGIN_USER_KEY);
        response.sendRedirect("/console/login");
    }

    @RequestMapping(value = "/pushToAll")
    public void pushToAll(HttpServletResponse response, @RequestParam int fromUserId, @RequestParam String body) {

        MessageBean mb = JSON.parseObject(body, MessageBean.class);
        mb.setFromUserId(fromUserId + "");
        mb.setTimeSend(DateUtil.currentTimeSeconds());
        mb.setMsgType(0);
        mb.setMessageId(StringUtil.randomUUID());
        ThreadUtil.executeInThread(obj -> {
            DBCursor cursor = dsForRW.getDB().getCollection("user").find(null, new BasicDBObject("_id", 1)).sort(new BasicDBObject("_id", -1));
            while (cursor.hasNext()) {
                BasicDBObject dbObj = (BasicDBObject) cursor.next();
                int userId = dbObj.getInt("_id");
                try {
                    mb.setToUserId(String.valueOf(userId));
                    KXMPPServiceImpl.getInstance().send(mb);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        try {
            response.setContentType("text/html; charset=UTF-8");
            PrintWriter writer = response.getWriter();
            writer.write(
                    "<script type='text/javascript'>alert('\u6279\u91CF\u53D1\u9001\u6D88\u606F\u5DF2\u5B8C\u6210\uFF01');window.location.href='/pages/qf.jsp';</script>");
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "/roomList")
    public JSONMessage roomList(@RequestParam(defaultValue = "") String keyWorld,
                                @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "15") int limit, @RequestParam(defaultValue = "0") int leastNumbers) {

        PageResult<Room> result = new PageResult<Room>();

        Query<Room> query = dsForRoom.createQuery(Room.class);

        if (!StringUtil.isEmpty(keyWorld)) {
            query.criteria("name").containsIgnoreCase(keyWorld);
        }
        if (leastNumbers > 0) {
            query.field("userSize").greaterThan(leastNumbers);
        }
        List list = query.order("-userSize,-createTime").asList(getRoomManagerImplForIM().pageFindOption(page, limit, 1));
        result.setData(list);
        TigBeanUtils.getRoomManagerImplForIM().getMsgAllAndActMem(dsForRoom, list, result);
        result.setCount(query.count());
        return JSONMessage.success(result);
    }

    @RequestMapping(value = "/getRoomMember")
    public JSONMessage getRoom(@RequestParam(defaultValue = "") String roomId) {
        Room room = TigBeanUtils.getRoomManagerImplForIM().consoleGetRoom(new ObjectId(roomId));
        return JSONMessage.success(null, room);
    }

    /**
     * 获取计划发送信息
     *
     * @param roomId
     * @return
     */
    @RequestMapping(value = "/getRoomPlanInfo")
    public JSONMessage getRoomPlanInfo(@RequestParam(defaultValue = "") String roomId) {
        Room room = TigBeanUtils.getRoomManagerImplForIM().getRoom(new ObjectId(roomId));
        if (room.getMember() == null) {
            List<Member> adminMember = TigBeanUtils.getRoomManagerImplForIM().getAdminMemberList(new ObjectId(roomId));
            room.setPlanMember(adminMember == null ? new Member() : adminMember.get(0));
        }
        Map<String, Object> resultMap = new HashMap<>();
        ClientConfig clientConfig = TigBeanUtils.getAdminManager().getClientConfig();
        if (clientConfig.getApiUrl() != null) {
            if (clientConfig.getApiUrl().endsWith("/")) {
                resultMap.put("url", clientConfig.getApiUrl() + "special/sendGroupPlanMsg");
            } else {
                resultMap.put("url", clientConfig.getApiUrl() + "/special/sendGroupPlanMsg");
            }
        }
        resultMap.put("planMember", room.getPlanMember());
        String keyStr = room.getJid() + "|" + room.getPlanMember().getUserId();
        Room.RoomApiKey roomApiKey = TigBeanUtils.getRoomManagerImplForIM().getApiKeyByRoomId(roomId, keyStr);
        resultMap.put("key", roomApiKey.getPlanMD5Str());
        return JSONMessage.success(null, resultMap);
    }

    /**
     * 直播间聊天记录
     *
     * @param page
     * @param limit
     * @param room_jid_id
     * @return
     */
    @RequestMapping(value = "/roomMsgDetail")
    public JSONMessage roomDetail(@RequestParam(defaultValue = "0") int page,
                                  @RequestParam(defaultValue = "50") int limit, @RequestParam(defaultValue = "") String room_jid_id) {

        DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + room_jid_id);
        BasicDBObject q = new BasicDBObject();
        q.put("contentType", 1);
        if (!StringUtil.isEmpty(room_jid_id)) {
            q.put("room_jid_id", room_jid_id);
        }
        logger.info("消息 总条数" + dbCollection.find(q).count());
        long total = dbCollection.count(q);
        List<DBObject> pageData = Lists.newArrayList();
        DBCursor cursor = dbCollection.find(q).sort(new BasicDBObject("_id", 1)).skip((page - 1) * limit).limit(limit);
        while (cursor.hasNext()) {
            BasicDBObject dbObj = (BasicDBObject) cursor.next();
            try {
                Map<?, ?> params = JSON.parseObject(dbObj.getString("body").replace("&quot;", "\""), Map.class);
                dbObj.put("content", params.get("content"));
                dbObj.put("fromUserName", params.get("fromUserName"));
            } catch (Exception e) {
                dbObj.put("content", "--");
            }
            pageData.add(dbObj);
        }
        PageResult<DBObject> result = new PageResult<DBObject>();
        result.setData(pageData);
        result.setCount(total);
        return JSONMessage.success(result);
    }

    /**
     * 直播间收到的礼物列表
     *
     * @param userId
     * @param startDate
     * @param endDate
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/getGiftList")
    public JSONMessage get(@RequestParam Integer userId, @RequestParam(defaultValue = "") String startDate,
                           @RequestParam(defaultValue = "") String endDate, @RequestParam(defaultValue = "0") int page,
                           @RequestParam(defaultValue = "10") int limit) {
        try {
            PageResult<Givegift> result = TigBeanUtils.getLiveRoomManager().getGiftList(userId, startDate, endDate, page, limit);
            return JSONMessage.success(result);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    @RequestMapping(value = "/userList")
    public JSONMessage userList(@RequestParam(defaultValue = "0") int page,
                                @RequestParam(defaultValue = "10") int limit, @RequestParam(defaultValue = "") String onlinestate,
                                @RequestParam(defaultValue = "") String keyWorld) {
        Query<User> query = getUserManager().createQuery().field("_id").notEqual(999);
        Pattern pattern1 = Pattern.compile("^-?[0-9]+");
        if (!StringUtil.isEmpty(keyWorld)) {
            if (pattern1.matcher(keyWorld).matches() && Long.parseLong(keyWorld)< Integer.MAX_VALUE) {
                query.or(query.criteria("nickname").containsIgnoreCase(keyWorld),
                        query.criteria("telephone").containsIgnoreCase(keyWorld),
                        query.criteria("userId").equal(Integer.parseInt(keyWorld)),
                        query.criteria("userId").containsIgnoreCase(keyWorld),
                        query.criteria("phone").containsIgnoreCase(keyWorld),
                        query.criteria("account").containsIgnoreCase(keyWorld),
                        query.criteria("serInviteCode").containsIgnoreCase(keyWorld));
            } else {
                query.or(query.criteria("nickname").containsIgnoreCase(keyWorld),
                        query.criteria("telephone").containsIgnoreCase(keyWorld),
                        query.criteria("userId").containsIgnoreCase(keyWorld),
                        query.criteria("phone").containsIgnoreCase(keyWorld),
                        query.criteria("account").containsIgnoreCase(keyWorld),
                        query.criteria("serInviteCode").containsIgnoreCase(keyWorld));
            }
        }
        if (!StringUtil.isEmpty(onlinestate)) {
            query.filter("onlinestate", Integer.valueOf(onlinestate));
        }
        // 排序、分页
        List<User> pageData = query.order("-createTime").asList(getUserManager().pageFindOption(page, limit, 1));
        pageData.forEach(userInfo -> {
            Query<UserLoginLog> loginLog = TigBeanUtils.getDatastore().createQuery(UserLoginLog.class).field("userId").equal(userInfo.getUserId());
            if (null != loginLog.get()) {
                userInfo.setLoginLog(loginLog.get().getLoginLog());
            }
        });
        PageResult<User> result = new PageResult<>();
        for (User user : pageData) {
            user.setPassword("");
            user.setPayPassword("");
            if (user.getPhoneToLocation() == null && user.getPhone() != null && !"".equals(user.getPhone()) && user.getAreaCode() != null && !"".equals(user.getAreaCode())) {
                Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
                if (!pattern.matcher(user.getPhone()).matches()) {
                    user.setPhoneToLocation("未知区域");
                    user.setCarrier("未知");
                } else {
                    PhoneModel phoneModel = PhoneUtils.getPhoneModel(user.getPhone(), Integer.parseInt(user.getAreaCode()));
                    //处理归属地信息
                    if (phoneModel != null) {
                        if (phoneModel.getProvinceName() != null) {
                            String phoneToLocation = phoneModel.getProvinceName() + (phoneModel.getCityName() == null ? "" : "-" + phoneModel.getCityName());
                            user.setPhoneToLocation(phoneToLocation);
                        } else {
                            String phoneToLocation = phoneModel.getCityName() == null ? "" : phoneModel.getCityName();
                            user.setPhoneToLocation(phoneToLocation);
                        }
                        user.setCarrier(phoneModel.getCarrier());
                    }
                }
            } else if (user.getPhone() == null) {
                user.setAreaCode("");
            }
//			if (user.getLoginIp() != null){
//				JSONObject json = GeoIP.getGeoIpLocation(user.getLoginIp());
//				if (json != null){
//					user.setLoginIpAdr(json.getString("country")+(json.getString("city") == null ? "":json.getString("city")));
//				}else {
//					user.setLoginIpAdr("本机/局域网");
//				}
//			}
            //设置用户签到红包次数及累计金额
            Map<String,String> signCountMap = TigBeanUtils.getExtendManager().getUserSignCount(user.getUserId());
            user.setSignCount(signCountMap.get("signCount"));
            user.setSignMoneyCount(signCountMap.get("signMoneyCount"));
            DeviceHistory deviceHistory = TigBeanUtils.getDatastore().createQuery(DeviceHistory.class).field("userId").equal(user.getUserId()).order("-updateTime").get();
            if(null!=deviceHistory){
                TigBeanUtils.getAdminManager().isForbidDevice(deviceHistory,user,"");
                TigBeanUtils.getAdminManager().isForbidIp(deviceHistory,user,"");
            }
        }
        result.setData(pageData);
        result.setCount(query.count());
        return JSONMessage.success(result);
    }
    @RequestMapping(value = "/doForbid")
    public JSONMessage doForbid(int userId, int status) {
        Query<DeviceHistory> deviceHistory = TigBeanUtils.getDatastore().createQuery(DeviceHistory.class).field("userId").equal(userId).order("-updateTime");
        if(null!=deviceHistory.get()){
            ForbidDetails forbidDetails = new ForbidDetails();
            forbidDetails.setDevice(deviceHistory.get().getDevice());
            forbidDetails.setStatus(status);
            forbidDetails.setTime(DateUtil.currentTimeSeconds());
            MessageBean messageBean = new MessageBean();
            try {
                messageBean.setFromUserId("10005");
                messageBean.setFromUserName( "后台管理员");
                messageBean.setToUserId(String.valueOf(userId));
                messageBean.setToUserName(TigBeanUtils.getUserManager().getNickName(userId));
                messageBean.setObjectId(userId);
                messageBean.setMessageId(StringUtil.randomUUID());
                messageBean.setMsgType(0);

//                XMPPTCPConnection conn = TigBeanUtils.getXmppService().getConnection();
//                messageBean = new MessageBean();
//                messageBean.setType(KXMPPServiceImpl.prohibitDeviceId);
//                messageBean.setFromUserId("10005");
//                messageBean.setFromUserName("后台系统管理员");
//                messageBean.setMessageId(StringUtil.randomUUID());
//                messageBean.setContent("抱歉！该设备已被禁用");
//                messageBean.setToUserId(String.valueOf(userId));
                if(status==0){
                    if(null==deviceHistory.get().getDeviceInfo()||"".equals(deviceHistory.get().getDeviceInfo())){
                        return JSONMessage.failure("抱歉，没有找到该用户最近登录设备，目前不能禁用！");
                    }
                    if(StringUtils.isEmpty(TigBeanUtils.getRedisCRUD().get("forbid:device"))){
                        TigBeanUtils.getRedisCRUD().set("forbid:device", deviceHistory.get().getDeviceInfo());
                    }else{
                        TigBeanUtils.getRedisCRUD().set("forbid:device",TigBeanUtils.getRedisCRUD().get("forbid:device")
                                + "," + deviceHistory.get().getDeviceInfo());
                    }
                    messageBean.setType(KXMPPServiceImpl.forbidUsersDeviceOrIP);
                    messageBean.setContent("您登录的设备已被锁定");
                    forbidDetails.setForbidDevice(deviceHistory.get().getDeviceInfo());
                }else if(status==1){
                    if(null==deviceHistory.get().getLoginIp()||"".equals(deviceHistory.get().getLoginIp())){
                        return JSONMessage.failure("抱歉，没有找到该用户最近登录IP，目前不能禁用！");
                    }
                    if(StringUtils.isEmpty(TigBeanUtils.getRedisCRUD().get("forbid:ip"))){
                        TigBeanUtils.getRedisCRUD().set("forbid:ip",deviceHistory.get().getLoginIp());
                    }else{
                        TigBeanUtils.getRedisCRUD().set("forbid:ip",TigBeanUtils.getRedisCRUD().get("forbid:ip")
                                + "," + deviceHistory.get().getLoginIp());
                    }
                    messageBean.setType(KXMPPServiceImpl.forbidUsersDeviceOrIP);
                    messageBean.setContent("您登录的IP已被锁定");
                    forbidDetails.setForbidIp(deviceHistory.get().getLoginIp());
                }
//                Message message = new Message();
//                message.setFrom(conn.getUser());
//                message.setTo(JidCreate.from(String.valueOf(userId) + "@" + conn.getXMPPServiceDomain()));
//                message.setBody(messageBean.toString());
//                message.setType(Message.Type.chat);
//                String packetId = StringUtil.randomUUID();
//                message.setStanzaId(packetId);
//                conn.sendStanza(message);
                User user = TigBeanUtils.getDatastore().createQuery(User.class).field("userId").equal(userId).get();
                if(user.getOnlinestate()==1){
                    KXMPPServiceImpl.getInstance().send(messageBean);
                }
                //维护redis中的数据
                KSessionUtil.removeAccessToken(userId);
                KSessionUtil.deleteUserByUserId(userId);
            }catch (Exception e){
                e.printStackTrace();
            }
            ObjectId id = (ObjectId) TigBeanUtils.getDatastore().save(forbidDetails).getId();
            forbidDetails.setId(id);
            return JSONMessage.success();
        }else{
            if(status == 0){
                return JSONMessage.failure("抱歉，没有找到该用户最近登录设备，目前不能禁用！");
            }else if(status==1){
                return JSONMessage.failure("抱歉，没有找到该用户最近登录IP，目前不能禁用！");
            }
        }
        return null;
    }
    @RequestMapping(value = "/initUserSerInvite")
    public JSONMessage initUserSerInvite() {
        int inviteCodeMode = TigBeanUtils.getAdminManager().getConfig().getRegisterInviteCode();
        if (inviteCodeMode == 2) {
            Query<User> query = getUserManager().createQuery().field("serInviteCode").equal(null);
            Long count = query.count();
            if (count > 1000) {
                Long pageNo = (count + 1000 - 1) / 1000;
                for (int page = 0; page < pageNo; page++) {
                    List<User> pageData = query.asList(getUserManager().pageFindOption(page, 1000, 0));
                    pageData.forEach(userInfo -> getUserManager().initUserSerInviteCode(userInfo.getUserId()));
                }
            } else {
                List<User> userList = query.asList();
                userList.forEach(userInfo -> getUserManager().initUserSerInviteCode(userInfo.getUserId()));
            }
        }
        // 排序、分页
        return JSONMessage.success("处理成功");
    }

    /**
     * 最新注册用户
     *
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @SuppressWarnings("deprecation")
    @RequestMapping(value = "/newRegisterUser")
    public JSONMessage newRegisterUser(@RequestParam(defaultValue = "0") int pageIndex,
                                       @RequestParam(defaultValue = "10") int pageSize) {
        Query<User> query = getUserManager().createQuery();
        long total = query.count();
        List<User> pageData = query.order("-createTime").offset(pageIndex * pageSize).limit(pageSize).asList();
        PageVO page = new PageVO(pageData, total, pageIndex, pageSize);
        for (User user : pageData) {
            user.setPassword("");
            user.setPayPassword("");
        }
        return JSONMessage.success(null, page);
    }

    /**
     * 重置密码
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "/restPwd")
    public JSONMessage restPwd(@RequestParam(defaultValue = "0") Integer userId) {
        if (0 < userId) {
            getUserManager().resetPassword(userId, Md5Util.md5Hex("123456"));
        }
        return JSONMessage.success();
    }

    /**
     * 可以自定义重置密码
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "/updatePassword")
    public JSONMessage updatePassword(@RequestParam(defaultValue = "0") Integer userId, String password) {
        try {
            getUserManager().resetPassword(userId, password);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 好友列表
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "/friendsList")
    public JSONMessage friendsList(@RequestParam(defaultValue = "0") Integer userId,
                                   @RequestParam(defaultValue = "0") Integer toUserId, @RequestParam(defaultValue = "0") int status,
                                   @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit) {
        try {
            PageResult<Friends> friendsList = TigBeanUtils.getFriendsManager().consoleQueryFollow(userId, toUserId,
                    status, page, limit);
            return JSONMessage.success(friendsList);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 删除好友
     *
     * @param userId
     * @param toUserIds
     * @return
     */
    @RequestMapping("/deleteFriends")
    public JSONMessage deleteFriends(@RequestParam(defaultValue = "0") Integer userId,
                                     @RequestParam(defaultValue = "") String toUserIds) {
        try {
            if (StringUtil.isEmpty(toUserIds)) {
                JSONMessage.failure("参数为空");
            } else {
                String[] toUserId = StringUtil.getStringList(toUserIds, ",");

                TigBeanUtils.getFriendsManager().getFriends(userId, toUserId);
                /*
                 * Friends friends =
                 * TigBeanUtils.getFriendsManager().getFriends(userId, toUserId);
                 * if (null == friends) return JSONMessage.failure("对方不是你的好友!");
                 */
                TigBeanUtils.getFriendsManager().consoleDeleteFriends(userId, toUserId);
            }
            return JSONMessage.success();

        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    @RequestMapping(value = "/getUpdateUser")
    public JSONMessage updateUser(@RequestParam(defaultValue = "0") Integer userId) {
        User user;
        if (0 == userId) {
            user = new User();
        } else {
            user = getUserManager().getUser(userId);
            List<Integer> userRoles = TigBeanUtils.getRoleManager().getUserRoles(userId);
            logger.info("用户角色：" + JSONObject.toJSONString(userRoles));
            if (null != userRoles) {
                for (Integer role : userRoles) {
                    if (role.equals(2)) {
                        user.setUserType(2);
                    } else {
                        user.setUserType(0);
                    }
                }
            }
        }
        return JSONMessage.success(user);
    }

    @RequestMapping(value = "/updateUser")
    public JSONMessage saveUserMsg(HttpServletRequest request, HttpServletResponse response, @RequestParam(defaultValue = "0") Integer userId, @ModelAttribute UserExample example) throws Exception {
        if (example.getRegisterType() == 0 && !StringUtil.isEmpty(example.getTelephone())) {
            example.setPhone(example.getTelephone());
            example.setTelephone(example.getAreaCode() + example.getTelephone());
        } else {
            example.setAccount(example.getTelephone());
            example.setTelephone("");
        }
        // 后台注册用户(后台注册传的密码没有加密，这里进行加密)
        if (!StringUtil.isEmpty(example.getPassword())) {
            example.setPassword(DigestUtils.md5Hex(example.getPassword()));
        }
        // 保存到数据库
        if (0 == userId) {
            getUserManager().registerIMUser(example, KConstants.API_TYPE_ADMIN);
        } else {
            getUserManager().updateUser(userId, example);
            // 修改好友关系表中的toUserType
//			TigBeanUtils.getRoleManager().updateFriend(userId, example.getUserType());
        }

        return JSONMessage.success();
    }

    /**
     * 审核实名认证 2审核通过3拒绝
     * @param request
     * @param response
     * @param userId
     * @param status
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/checkRealNameAuth")
    public JSONMessage checkRealNameAuth(HttpServletRequest request, HttpServletResponse response, @RequestParam(defaultValue = "0") Integer userId, @RequestParam(defaultValue = "0")Integer status) throws Exception {

        getUserManager().checkRealNameAuth(userId, status);
        return JSONMessage.success();
    }

    /**
     * @param page
     * @param limit
     * @return
     * @Description:（红包记录）
     **/
    @RequestMapping("/redPacketList")
    public JSONMessage getRedPacketList(@RequestParam(defaultValue = "") String userName, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit) {
        try {
            PageResult<RedPacket> result = TigBeanUtils.getRedPacketManager().getRedPacketList(userName, page, limit);
            return JSONMessage.success(result);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getErrMessage());
        }
    }

    @RequestMapping("/receiveWater")
    public JSONMessage receiveWater(@RequestParam(defaultValue = "") String redId, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit) {
        try {
            PageResult<RedReceive> result = TigBeanUtils.getRedPacketManager().receiveWater(redId, page, limit);
            return JSONMessage.success(result);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getErrMessage());
        }
    }

    @RequestMapping(value = "/editRoom")
    public ModelAndView addRoom(HttpServletRequest request, HttpServletResponse response,@RequestParam(defaultValue = "") String id) throws Exception {
        ModelAndView mav = new ModelAndView("editRoom");
        if (StringUtil.isEmpty(id)) {
            mav.addObject("o", new Room());
            mav.addObject("action", "addRoom");
        } else {
            mav.addObject("o", getRoomManagerImplForIM().getRoom(parse(id)));
            mav.addObject("action", "updateRoom");
        }

        return mav;
    }

    @RequestMapping(value = "/addRoom")
    public JSONMessage addRoom(HttpServletRequest request, HttpServletResponse response, @ModelAttribute Room room,@RequestParam(defaultValue = "") String ids) throws Exception {
        List<Integer> idList = StringUtil.isEmpty(ids) ? null : JSON.parseArray(ids, Integer.class);
        if (null == room.getId()) {
            User user = getUserManager().getUser(room.getUserId());
            String jid = TigBeanUtils.getXmppService().createMucRoom(user.getPassword(), user.getUserId().toString(),
                    room.getName(), null, room.getSubject(), room.getDesc());
            room.setJid(jid);
            getRoomManagerImplForIM().add(user, room, idList);
        }

        return JSONMessage.success();
    }

    @RequestMapping(value = "/updateRoom")
    public JSONMessage updateRoom(HttpServletRequest request, HttpServletResponse response, @ModelAttribute RoomVO roomVo) throws Exception {
        try {
            User user = getUserManager().get(roomVo.getUserId());
            if (null == user) {
                return JSONMessage.failure("操作失败");
            }
            getRoomManagerImplForIM().update(user, roomVo, 1, 1);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }

        return JSONMessage.success();
    }

    @RequestMapping(value = "/roomUserManager")
    public JSONMessage roomUserManager(@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit, @RequestParam(defaultValue = "") String id, @RequestParam(defaultValue = "") String keyWorld) throws Exception {
        try {
            PageResult<Member> result = null;
            if (!StringUtil.isEmpty(id)) {
                result = getRoomManagerImplForIM().getMemberListByPage(new ObjectId(id), page, limit, keyWorld);
            }
            return JSONMessage.success(result);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    @RequestMapping(value = "/roomMemberList")
    public JSONMessage roomMemberList(@RequestParam String id) {
        Object data = getRoomManagerImplForIM().getMemberList(new ObjectId(id), "");
        return JSONMessage.success(null, data);
    }

    @RequestMapping(value = "/sendMessage", method = {RequestMethod.POST})
    public ModelAndView sendMessage(@RequestParam String body, Integer from, Integer to, Integer count) {
        ModelAndView mav = new ModelAndView("qf");
        try {
            logger.info("body=======>  " + body);
            // String msg = new String(body.getBytes("iso8859-1"),"utf-8");
            if (null == from) {
                List<Friends> uList = TigBeanUtils.getFriendsManager().queryFriendsList(to, 0, 0, count);
                new Thread(() -> {
                    User user = null;
                    MessageBean messageBean = null;
                    for (Friends friends : uList) {
                        try {
                            user = getUserManager().getUser(friends.getToUserId());
                            messageBean = new MessageBean();
                            messageBean.setType(1);
                            messageBean.setContent(body);
                            messageBean.setFromUserId(user.getUserId() + "");
                            messageBean.setFromUserName(user.getNickname());
                            messageBean.setMessageId(UUID.randomUUID().toString());
                            messageBean.setToUserId(to.toString());
                            messageBean.setMsgType(0);
                            messageBean.setMessageId(StringUtil.randomUUID());
                            KXMPPServiceImpl.getInstance().send(messageBean);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        ;
                    }
                }).start();
            } else {
                new Thread(() -> {
                    User user = getUserManager().get(from);
                    MessageBean messageBean = new MessageBean();
                    messageBean.setContent(body);
                    messageBean.setFromUserId(user.getUserId().toString());
                    messageBean.setFromUserName(user.getNickname());
                    messageBean.setToUserId(to.toString());
                    messageBean.setMsgType(0);
                    messageBean.setMessageId(StringUtil.randomUUID());
                    KXMPPServiceImpl.getInstance().send(messageBean);
                }).start();
            }
            return mav;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mav;
    }

    @RequestMapping(value = "/addAllUser")
    public JSONMessage updateTigaseDomain() throws Exception {
        Cursor attach = dsForRW.getDB().getCollection("user").find();
        String userId;
        String password;
        while (attach.hasNext()) {
            DBObject fileobj = attach.next();
            DBObject ref = new BasicDBObject();
            ref.put("user_id", fileobj.get("_id") + "@" + TigBeanUtils.getXMPPConfig().getServerName());
            DBObject obj = dsForTigase.getDB().getCollection("tig_users").findOne(ref);
            userId = fileobj.get("_id").toString().replace(".", "0");
            password = fileobj.get("password").toString();
            if (null != obj) {
                logger.info(fileobj.get("_id").toString() + "  已注册");
            } else {
                String user_id = userId + "@" + TigBeanUtils.getXMPPConfig().getServerName();
                BasicDBObject jo = new BasicDBObject();
                jo.put("_id", generateId(user_id));
                jo.put("user_id", user_id);
                jo.put("domain", TigBeanUtils.getXMPPConfig().getServerName());
                jo.put("password", password);
                jo.put("type", "tig");
                dsForTigase.getDB().getCollection("tig_users").save(jo);
                logger.info(user_id + "  注册到Tigase：" + TigBeanUtils.getXMPPConfig().getServerName());
            }
        }
        return JSONMessage.success();
    }

    private byte[] generateId(String username) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        return md.digest(username.getBytes(Charset.defaultCharset()));
    }

    @RequestMapping(value = "/initSysUser")
    public JSONMessage initSysUser(@RequestParam(defaultValue = "0") int userId) throws Exception {
        if (0 < userId) {
            getUserManager().addUser(userId, String.valueOf(userId));
            KXMPPServiceImpl.getInstance().registerSystemNo(String.valueOf(userId), Md5Util.md5Hex(String.valueOf(userId)));
        } else {
            // TigBeanUtils.getAdminManager().initSystemNo();
        }
        return JSONMessage.success();
    }

    /**
     * 直播间列表
     *
     * @param name
     * @param nickName
     * @param userId
     * @param page
     * @param limit
     * @param status
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/liveRoomList")
    public JSONMessage liveRoomList(@RequestParam(defaultValue = "") String name,
                                    @RequestParam(defaultValue = "") String nickName, @RequestParam(defaultValue = "0") Integer userId,
                                    @RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "10") Integer limit,
                                    @RequestParam(defaultValue = "-1") Integer status) throws Exception {
        PageResult<LiveRoom> result;
        try {
            result = getLiveRoomManager().findConsoleLiveRoomList(name, nickName, userId, page, limit, status, 1);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
        if(result == null){
            return JSONMessage.success(new PageResult<LiveRoom>());
        }
        return JSONMessage.success(result);
    }

    @RequestMapping(value = "/addLiveRoom", method = {RequestMethod.GET})
    public ModelAndView addLiveRoom() {
        ModelAndView mav = new ModelAndView("addliveRoom");
        mav.addObject("o", new LiveRoom());
        return mav;
    }

    /**
     * 保存新增直播间
     *
     * @param request
     * @param response
     * @param liveRoom
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/saveNewLiveRoom", method = {RequestMethod.POST})
    public JSONMessage saveNewLiveRoom(HttpServletRequest request, HttpServletResponse response, LiveRoom liveRoom)
            throws IOException {
        User user = getUserManager().getUser(liveRoom.getUserId());
        String jid = TigBeanUtils.getXmppService().createMucRoom(user.getPassword(), user.getUserId().toString(),
                liveRoom.getName(), null, liveRoom.getNotice(), liveRoom.getNotice());
        liveRoom.setJid(jid);
        getLiveRoomManager().createLiveRoom(liveRoom);
        return JSONMessage.success();
    }

    /**
     * 删除直播间
     *
     * @param liveRoomId
     * @return
     */
    @RequestMapping(value = "/deleteLiveRoom", method = {RequestMethod.POST})
    public JSONMessage deleteLiveRoom(@RequestParam String liveRoomId) {
        try {
            DBCollection dbCollection = dsForTigase.getCollection(LiveRoom.class);
            getLiveRoomManager().deleteLiveRoom(new ObjectId(liveRoomId));
            /* liveRoomManager.deleteLiveRoom(new ObjectId(liveRoomId)); */
            dbCollection.remove(new BasicDBObject("_id", new ObjectId(liveRoomId)));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 锁定、解锁直播间
     *
     * @param liveRoomId
     * @param currentState
     * @return
     */
    @RequestMapping(value = "/operationLiveRoom")
    public JSONMessage operationLiveRoom(@RequestParam String liveRoomId,
                                         @RequestParam(defaultValue = "0") int currentState) {
        try {
            getLiveRoomManager().operationLiveRoom(new ObjectId(liveRoomId), currentState);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 查询直播间人员
     *
     * @param pageIndex
     * @param name
     * @param nickName
     * @param userId
     * @param pageSize
     * @param roomId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/liveRoomUserManager")
    public JSONMessage liveRoomManager(@RequestParam(defaultValue = "0") int pageIndex,
                                       @RequestParam(defaultValue = "") String name, @RequestParam(defaultValue = "") String nickName,
                                       @RequestParam(defaultValue = "0") Integer userId, @RequestParam(defaultValue = "10") int pageSize,
                                       @RequestParam(defaultValue = "") String roomId) throws Exception {
        List<LiveRoomMember> pageData;
        pageData = getLiveRoomManager().findLiveRoomMemberList(new ObjectId(roomId));
        PageResult<LiveRoomMember> result = new PageResult<LiveRoomMember>();
        result.setData(pageData);
        result.setCount(pageData.size());
        return JSONMessage.success(result);
    }

    /**
     * 删除直播间成员
     *
     * @param userId
     * @param liveRoomId
     * @param response
     * @param pageIndex
     * @return
     */
    @RequestMapping(value = "/deleteRoomUser")
    public JSONMessage deleteLiveRoomUserManager(@RequestParam Integer userId,
                                                 @RequestParam(defaultValue = "") String liveRoomId, HttpServletResponse response,
                                                 @RequestParam(defaultValue = "0") int pageIndex) {
        try {
            getLiveRoomManager().kick(userId, new ObjectId(liveRoomId));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 禁言
     *
     * @param userId
     * @param state
     * @param roomId
     * @return
     */
    @RequestMapping(value = "/shutup")
    public JSONMessage shutup(@RequestParam Integer userId, @RequestParam int state, @RequestParam ObjectId roomId) {
        try {
            LiveRoomMember shutup = getLiveRoomManager().shutup(state, userId, roomId);
            logger.info(JSONObject.toJSONString(shutup));
            return JSONMessage.success(shutup);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 禁播
     */
    @RequestMapping(value = "/banplay")
    public void ban() {
        try {

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    /**
     * 礼物列表
     *
     * @param name
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/giftList")
    public JSONMessage giftList(@RequestParam(defaultValue = "") String name,
                                @RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "10") int pageSize) {
        try {
            Map<String, Object> pageData = getLiveRoomManager().consolefindAllgift(name, pageIndex, pageSize);
            if (null != pageData) {
                long total = (long) pageData.get("total");
                List<Gift> giftList = (List<Gift>) pageData.get("data");
                return JSONMessage.success(null, new PageVO(giftList, total, pageIndex, pageSize, total));
            }
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
        return null;
    }

    /**
     * 添加礼物
     *
     * @return
     */
    @RequestMapping(value = "/add/gift", method = {RequestMethod.GET})
    public ModelAndView getAddGiftPage() {
        ModelAndView mav = new ModelAndView("addGift");
        mav.addObject("o", new LiveRoom());
        return mav;
    }

    /**
     * 添加礼物
     *
     * @param request
     * @param response
     * @param name
     * @param photo
     * @param price
     * @param type
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/add/gift", method = {RequestMethod.POST})
    public JSONMessage addGift(HttpServletRequest request, HttpServletResponse response, @RequestParam String name,
                               @RequestParam String photo, @RequestParam double price, @RequestParam int type) throws IOException {
        // ModelAndView mav = new ModelAndView("editRoom");
        try {
            getLiveRoomManager().addGift(name, photo, price, type);
            return JSONMessage.success();
        } catch (Exception e) {
            // e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 删除礼物
     *
     * @param giftId
     * @return
     */
    @RequestMapping(value = "/delete/gift")
    public JSONMessage deleteGift(@RequestParam String giftId) {
        getLiveRoomManager().deleteGift(new ObjectId(giftId));
        return JSONMessage.success();

    }

    /**
     * 查询提示信息
     *
     * @param keyword
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/messageList")
    public JSONMessage messageList(String keyword, @RequestParam(defaultValue = "0") int pageIndex,
                                   @RequestParam(defaultValue = "10") int pageSize) {
        try {
            long totalNum = 0;
            Map<Long, List<ErrorMessage>> errorMessage = TigBeanUtils.getErrorMessageManage().findErrorMessage(keyword,
                    pageIndex, pageSize);
            if (null != errorMessage.keySet()) {
                for (Long total : errorMessage.keySet()) {
                    if (total == 0) {
                        return JSONMessage.success("暂无提示信息");
                    }
                    totalNum = total;
                }
            }
            return JSONMessage.success(null,
                    new PageVO(errorMessage.get(totalNum), totalNum, pageIndex, pageSize, totalNum));
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 新增提示消息
     *
     * @param errorMessage
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/saveErrorMessage")
    public JSONMessage saveErrorMessage(ErrorMessage errorMessage) throws IOException {
        try {
            if (null == errorMessage) {
                return JSONMessage.failure("参数有误");
            }
            return TigBeanUtils.getErrorMessageManage().saveErrorMessage(errorMessage);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 修改提示消息
     *
     * @param errorMessage
     * @param id
     * @return
     */
    @RequestMapping(value = "/messageUpdate", method = {RequestMethod.POST})
    public JSONMessage messageUpdate(ErrorMessage errorMessage, String id) {
        if (null == errorMessage) {
            return JSONMessage.failure("参数有误： errorMessage " + errorMessage);
        }
        ErrorMessage data = TigBeanUtils.getErrorMessageManage().updataErrorMessage(id, errorMessage);
        if (null == data) {
            return JSONMessage.failure("修改提示消息失败");
        } else {
            return JSONMessage.success("修改提示消息成功", data);
        }
    }

    /**
     * 删除提示消息
     *
     * @param code
     * @return
     */
    @RequestMapping(value = "/deleteErrorMessage")
    public JSONMessage deleteErrorMessage(@RequestParam(defaultValue = "") String code) {
        if (StringUtil.isEmpty(code)) {
            return JSONMessage.failure("参数有误,code: " + code);
        }
        boolean falg = TigBeanUtils.getErrorMessageManage().deleteErrorMessage(code);
        if (!falg) {
            return JSONMessage.failure("删除提示消息失败");
        } else {
            return JSONMessage.success();
        }
    }

    /**
     * 关键词列表
     *
     * @param word
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/keywordfilter")
    public JSONMessage keywordfilter(@RequestParam(defaultValue = "") String word, @RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "10") int pageSize) {
        Query<KeyWord> query = dsForRW.createQuery(KeyWord.class);
        if (!StringUtil.isEmpty(word)) {
            query.filter("word", word);
        }
        List<KeyWord> list = query.order("-createTime").asList(new FindOptions().skip(pageIndex * pageSize).limit(pageSize));
        long total = query.count();
        return JSONMessage.success(null, new PageVO(list, total, pageIndex, pageSize, total));
    }

    @RequestMapping("/sendMsg")
    public JSONMessage sendMsg(@RequestParam(defaultValue = "") String jidArr, @RequestParam(defaultValue = "1") int userId, @RequestParam(defaultValue = "1") int type, @RequestParam(defaultValue = "") String content) throws InterruptedException {
        String[] split = jidArr.split(",");
//        for (int i = 0;i<5000;i++){
//            TigBeanUtils.getRoomManagerImplForIM().sendMsgToRooms(split, userId, type, content+i);
////            Thread.currentThread().sleep(50);
//        }
        TigBeanUtils.getRoomManagerImplForIM().sendMsgToRooms(split, userId, type, content);
        return JSONMessage.success();
    }

    /**
     * 添加敏感词
     *
     * @param id
     * @param context 白名单内容
     * @throws IOException
     */
    @RequestMapping(value = "/addUrlWhite", method = {RequestMethod.POST})
    public JSONMessage addUrlWhite(@RequestParam(defaultValue = "") String id, @RequestParam String context) throws IOException {
        if (StringUtil.isEmpty(id)) {
            UrlWhite urlWhite = new UrlWhite();
            urlWhite.setContext(context);
            urlWhite.setCreateTime(DateUtil.currentTimeSeconds());
            dsForRW.save(urlWhite);
            TigBeanUtils.getAdminManager().updateTigaseConfig();
            return JSONMessage.success("添加白名单成功");
        } else {
            Query<UrlWhite> query = dsForRW.createQuery(UrlWhite.class);
            UpdateOperations<UrlWhite> ops = dsForRW.createUpdateOperations(UrlWhite.class);
            ops.set("context", context);
            ops.set("createTime", DateUtil.currentTimeSeconds());
            dsForRW.update(query, ops);
            TigBeanUtils.getAdminManager().updateTigaseConfig();
            return JSONMessage.success("修改白名单成功");
        }
    }

    /**
     * 获取URL白名单
     *
     * @param context
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/getUrlWhiteList")
    public JSONMessage getUrlWhiteList(@RequestParam(defaultValue = "") String context, @RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "10") int pageSize) {
        Query<UrlWhite> query = dsForRW.createQuery(UrlWhite.class);
        if (!StringUtil.isEmpty(context)) {
            query.criteria("context").containsIgnoreCase(context);
        }
        List<UrlWhite> list = query.order("-createTime").asList(new FindOptions().skip(pageIndex * pageSize).limit(pageSize));
        long total = query.count();
        return JSONMessage.success(null, new PageVO(list, total, pageIndex, pageSize, total));
    }

    /**
     * 删除URL白名单
     *
     * @param response
     * @param id
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/deleteUrlWhite", method = {RequestMethod.POST})
    public JSONMessage deleteUrlWhite(HttpServletResponse response, @RequestParam String id) {
        Query<UrlWhite> query = dsForRW.createQuery(UrlWhite.class);
        query.field("_id").equal(new ObjectId(id));
        dsForRW.delete(query.get());
        TigBeanUtils.getAdminManager().updateTigaseConfig();
        return JSONMessage.success();
    }

    /**
     * 添加敏感词
     *
     * @param response
     * @param id
     * @param word
     * @throws IOException
     */
    @RequestMapping(value = "/addkeyword", method = {RequestMethod.POST})
    @ResponseBody
    public void addkeyword(HttpServletResponse response, @RequestParam(defaultValue = "") String id, @RequestParam String word) throws IOException {
        if (StringUtil.isEmpty(id)) {
            KeyWord keyword = new KeyWord();
            keyword.setWord(word);
            keyword.setCreateTime(DateUtil.currentTimeSeconds());
            dsForRW.save(keyword);
            TigBeanUtils.getAdminManager().updateTigaseConfig();
        } else {
            Query<KeyWord> query = dsForRW.createQuery(KeyWord.class);
            UpdateOperations<KeyWord> ops = dsForRW.createUpdateOperations(KeyWord.class);
            ops.set("word", word);
            ops.set("createTime", DateUtil.currentTimeSeconds());
            dsForRW.update(query, ops);
            TigBeanUtils.getAdminManager().updateTigaseConfig();
        }
        response.sendRedirect("/console/keywordfilter");
    }

    /**
     * 删除敏感词
     *
     * @param response
     * @param id
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/deletekeyword", method = {RequestMethod.POST})
    public JSONMessage deletekeyword(HttpServletResponse response, @RequestParam String id) throws IOException {

        Query<KeyWord> query = dsForRW.createQuery(KeyWord.class);
        query.field("_id").equal(new ObjectId(id));
        dsForRW.delete(query.get());
        TigBeanUtils.getAdminManager().updateTigaseConfig();
        return JSONMessage.success();
    }

    /**
     * 删除聊天记录
     *
     * @param request
     * @param response
     * @param startTime
     * @param endTime
     * @param room_jid_id
     * @throws Exception
     */
    @RequestMapping(value = "/deleteMsgGroup", method = {RequestMethod.POST})
    public void deleteMsgGroup(HttpServletRequest request, HttpServletResponse response, @RequestParam(defaultValue = "0") long startTime, @RequestParam(defaultValue = "0") long endTime,
                               @RequestParam(defaultValue = "") String room_jid_id) throws Exception {
        if (room_jid_id != null) {
            DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + room_jid_id);
            BasicDBObject query = new BasicDBObject();
            if (0 != startTime) {
                query.put("ts", new BasicDBObject("$gte", startTime));
            }
            if (0 != endTime) {
                query.put("ts", new BasicDBObject("$gte", endTime));
            }
            DBCursor cursor = dbCollection.find(query);
            if (cursor.size() > 0) {
                BasicDBObject dbObj = (BasicDBObject) cursor.next();
                // 解析消息体
                Map<String, Object> body = JSON.parseObject(dbObj.getString("body").replace("&quot;", "\""), Map.class);
                int contentType = (int) body.get("type");
                if (contentType == 2 || contentType == 3 || contentType == 5 || contentType == 6 || contentType == 7 || contentType == 9) {
                    String paths = (String) body.get("content");
                    Query<Emoji> que = dsForRW.createQuery(Emoji.class);
                    List<Emoji> list = que.asList();
                    for (int i = 0; i < list.size(); i++) {
                        if (list.get(i).getUrl() == paths) {
                            return;
                        } else {
                            try {
                                // 调用删除方法将文件从服务器删除
                                ConstantUtil.deleteFile(paths);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
                dbCollection.remove(query); // 将消息记录中的数据删除
            }
        } else {
            List<String> jidList = dsForRoom.getCollection(Room.class).distinct("jid", new BasicDBObject());
            for (int j = 0; j < jidList.size(); j++) {
                DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + jidList.get(j));
                BasicDBObject query = new BasicDBObject();
                if (0 != startTime) {
                    query.put("ts", new BasicDBObject("$gte", startTime));
                }
                if (0 != endTime) {
                    query.put("ts", new BasicDBObject("$gte", endTime));
                }
                DBCursor cursor = dbCollection.find(query);
                if (cursor.size() > 0) {
                    BasicDBObject dbObj = (BasicDBObject) cursor.next();
                    // 解析消息体
                    Map<String, Object> body = JSON.parseObject(dbObj.getString("body").replace("&quot;", "\""), Map.class);
                    int contentType = (int) body.get("type");
                    if (contentType == 2 || contentType == 3 || contentType == 5 || contentType == 6 || contentType == 7 || contentType == 9) {
                        String paths = (String) body.get("content");
                        Query<Emoji> que = dsForRW.createQuery(Emoji.class);
                        List<Emoji> list = que.asList();
                        for (int i = 0; i < list.size(); i++) {
                            if (list.get(i).getUrl() == paths) {
                                return;
                            } else {
                                try {
                                    // 调用删除方法将文件从服务器删除
                                    ConstantUtil.deleteFile(paths);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    dbCollection.remove(query); // 将消息记录中的数据删除
                }
            }
        }
        referer(response, "/console/groupchat_logs_all?room_jid_id=" + room_jid_id, 0);
    }

    /**
     * 后台自动创建用户
     *
     * @param userNum 需要生成的数量
     * @param roomId
     */
    @RequestMapping(value = "/autoCreateUser")
    public JSONMessage autoCreateUser(@RequestParam(defaultValue = "0") int userNum, @RequestParam(defaultValue = "") String roomId) {
        try {
            if (userNum > 0){
                getUserManager().autoCreateUserOrRoom(userNum, roomId);
            }else{
                return JSONMessage.failure("至少输入1个");
            }
            return JSONMessage.success();
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 导出自动创建的用户数据到 excel
     */
    @RequestMapping(value = "/exportData")
    public JSONMessage exportData(HttpServletRequest request, HttpServletResponse response, @RequestParam(defaultValue = "3") short userType) {
        int maxNum = 30000; // 最多导出3万条数据
        List<User> userList = getUserManager().findUserList(0, maxNum, "", userType);
        String name;
        String fileName;
        if (userType == 3){
            name = "系统自动创建的账号";
            fileName = "usersAutoCreateUser.xlsx";
        }else {
            name = "平台注册用户";
            fileName = "users.xlsx";
        }
        List<String> titles = Lists.newArrayList();
        titles.add("用户ID");
        titles.add("用户名");
        titles.add("用户昵称");
        titles.add("手机号");
        titles.add("登录密码");
        titles.add("用户性别");
        titles.add("创建时间");
        List<Map<String, Object>> values = Lists.newArrayList();
        for (User user : userList) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("用户ID", user.getUserId());
            map.put("用户名", user.getAccount());
            map.put("用户昵称", user.getNickname());
            map.put("手机号", user.getTelephone());
            map.put("登录密码", user.getUserType() == 3 ? "" + (user.getUserId() - 1000) / 2 : user.getPassword());
            map.put("用户性别", user.getSex() == 1 ? "女" : "男");
//            map.put("创建时间", Calendar.getInstance());
            map.put("创建时间", DateUtil.strToDateTime(user.getCreateTime()));
            values.add(map);
        }
        Workbook workBook = ExcelUtil.generateWorkbook(name, "xlsx", titles, values);
        try {
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(Charset.defaultCharset()), "utf-8"));
            ServletOutputStream out = response.getOutputStream();
            workBook.write(out);
            // 弹出下载对话框
            out.close();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return JSONMessage.success();
    }

    @RequestMapping(value = "/exportExcelByFriends")
    public JSONMessage exportExcelByFriends(HttpServletRequest request, HttpServletResponse response, @RequestParam(defaultValue = "0") Integer userId) {
        try {
            Workbook workBook = TigBeanUtils.getFriendsManager().exprotExcelFriends(userId, request, response);
            ServletOutputStream out = response.getOutputStream();
            workBook.write(out);
            // 弹出下载对话框
            out.close();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return JSONMessage.success();
    }

    /**
     * 导出群成员
     *
     * @param request
     * @param response
     * @param roomId
     * @return
     * @Description:导出群成员
     **/
    @RequestMapping(value = "/exportExcelByGroupMember")
    public JSONMessage exportExcelByGroupMember(HttpServletRequest request, HttpServletResponse response,
                                                @RequestParam(defaultValue = "") String roomId) {
        try {
            Workbook workBook = TigBeanUtils.getRoomManagerImplForIM().exprotExcelGroupMembers(roomId, request, response);
            ServletOutputStream out = response.getOutputStream();
            workBook.write(out);
            // 弹出下载对话框
            out.close();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return JSONMessage.success();
    }

    /**
     * 统计用户注册信息
     */
    @RequestMapping(value = "/getUserRegisterCount")
    public JSONMessage getUserRegisterCount(@RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "100") int pageSize, @RequestParam(defaultValue = "2") short timeUnit,
                                            @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate) {

        try {
            Object data = getUserManager().getUserRegisterCount(startDate.trim(), endDate.trim(), timeUnit);
            return JSONMessage.success(null, data);
        } catch (MongoCommandException e) {
            return JSONMessage.success(0);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 用户，群组，单聊消息，好友关系数量 统计
     */
    @RequestMapping(value = "/countNum")
    public JSONMessage countNum(HttpServletRequest request, HttpServletResponse response) {
        try {
            long userNum = getUserManager().count();
            BigDecimal remainMoneyNum = getUserManager().sumUserBalance();
            long roomNum = getRoomManagerImplForIM().countRoomNum();
            long msgNum = TigBeanUtils.getTigaseManager().getMsgCountNum();
            long friendsNum = TigBeanUtils.getFriendsManager().count();
            BigDecimal withdrawMoneyNum = TigBeanUtils.getConsumeRecordManager().sumUserWithdrawMoney();
            BigDecimal rechargeMoneyNum = TigBeanUtils.getConsumeRecordManager().sumUserRechargeMoney();
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("userNum", userNum);
            dataMap.put("roomNum", roomNum);
            dataMap.put("msgNum", msgNum);
            dataMap.put("friendsNum", friendsNum);
            dataMap.put("rechargeMoneyNum", rechargeMoneyNum);
            dataMap.put("withdrawMoneyNum", withdrawMoneyNum);
            dataMap.put("remainMoneyNum", remainMoneyNum);
            return JSONMessage.success(null, dataMap);
        } catch (MongoCommandException e) {
            return JSONMessage.success(0);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 统计单聊消息数量
     */
    @RequestMapping(value = "/chatMsgCount")
    public JSONMessage chatMsgCount(@RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "100") int pageSize, @RequestParam(defaultValue = "2") short timeUnit,
                                    @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate) {
        try {
            Object data = TigBeanUtils.getTigaseManager().getChatMsgCount(startDate.trim(), endDate.trim(), timeUnit);
            return JSONMessage.success(null, data);
        } catch (MongoCommandException e) {
            return JSONMessage.success(0);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 统计群聊聊消息数量
     */
    @RequestMapping(value = "/groupMsgCount")
    public JSONMessage groupMsgCount(@RequestParam String roomId, @RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "100") int pageSize, @RequestParam(defaultValue = "2") short timeUnit,
                                     @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate) {
        try {
            Object data = TigBeanUtils.getTigaseManager().getGroupMsgCount(roomId, startDate.trim(), endDate.trim(), timeUnit);
            return JSONMessage.success(null, data);
        } catch (MongoCommandException e) {
            return JSONMessage.success(0);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 统计添加好友数量
     */
    @RequestMapping(value = "/addFriendsCount")
    public JSONMessage addFriendsCount(@RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "100") int pageSize, @RequestParam(defaultValue = "2") short timeUnit,
                                       @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate) {
        try {
            Object data = TigBeanUtils.getFriendsManager().getAddFriendsCount(startDate.trim(), endDate.trim(), timeUnit);
            return JSONMessage.success(null, data);
        } catch (MongoCommandException e) {
            return JSONMessage.success(0);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 统计创建群组数量
     */
    @RequestMapping(value = "/addRoomsCount")
    public JSONMessage addRoomsCount(@RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "100") int pageSize, @RequestParam(defaultValue = "2") short timeUnit,
                                     @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate) {
        try {
            Object data = TigBeanUtils.getRoomManager().addRoomsCount(startDate.trim(), endDate.trim(), timeUnit);
            return JSONMessage.success(null, data);
        } catch (MongoCommandException e) {
            return JSONMessage.success(0);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 用户在线数量统计
     *
     * @param pageIndex 页码起始也
     * @param pageSize  页码总数
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param timeUnit  时间
     * @throws Exception
     */
    @RequestMapping(value = "/getUserStatusCount")
    public JSONMessage getUserStatusCount(@RequestParam(defaultValue = "0") int pageIndex,
                                          @RequestParam(defaultValue = "100") int pageSize, @RequestParam(defaultValue = "2") short timeUnit,
                                          @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate) throws Exception {
        try {
            Object data = TigBeanUtils.getUserManager().userOnlineStatusCount(startDate.trim(), endDate.trim(), timeUnit);
            return JSONMessage.success(null, data);
        } catch (MongoCommandException e) {
            return JSONMessage.success(0);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param type     (type = 0查询被举报的用户,type=1查询被举报的群主,type=2查询被举报的网页)
     * @param pageSize
     * @return
     * @Description:（被举报的用户和群组列表）
     **/
    @SuppressWarnings("static-access")
    @RequestMapping(value = "/beReport")
    public JSONMessage beReport(@RequestParam(defaultValue = "0") int type,
                                @RequestParam(defaultValue = "0") int sender, @RequestParam(defaultValue = "") String receiver,
                                @RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "25") int pageSize) {
        Map<String, Object> dataMap;
//        JSONMessage jsonMessage = new JSONMessage();
        try {
            dataMap = TigBeanUtils.getUserManager().getReport(type, sender, receiver, pageIndex, pageSize);
            logger.info("举报详情：" + JSONObject.toJSONString(dataMap.get("data")));
            if (!dataMap.isEmpty()) {
                List<Report> reportList = (List<Report>) dataMap.get("data");
                long total = (long) dataMap.get("count");
                return JSONMessage.success(null, new PageVO(reportList, total, pageIndex, pageSize, total));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
        return new JSONMessage();

    }

    @RequestMapping("/isLockWebUrl")
    public JSONMessage isLockWebUrl(@RequestParam(defaultValue = "") String webUrlId, @RequestParam(defaultValue = "-1") int webStatus) {
        if (StringUtil.isEmpty(webUrlId)) {
            return JSONMessage.failure("webUrl is null");
        }
        Query<Report> query = TigBeanUtils.getDatastore().createQuery(Report.class).field("_id").equal(new ObjectId(webUrlId));
        if (null == query.get()) {
            return JSONMessage.failure("暂无该链接的举报数据");
        }
        UpdateOperations<Report> ops = TigBeanUtils.getDatastore().createUpdateOperations(Report.class);
        ops.set("webStatus", webStatus);
        TigBeanUtils.getDatastore().update(query, ops);
        return JSONMessage.success();
    }

    /**
     * 删除举报
     *
     * @param response
     * @param id
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/deleteReport")
    public JSONMessage deleteReport(HttpServletResponse response, @RequestParam String id) throws IOException {
        BasicDBObject query = new BasicDBObject("_id", parse(id));
        getUserManager().getDatastore().getDB().getCollection("Report").remove(query);
        return JSONMessage.success();
    }

    /**
     * api 调用日志
     *
     * @param keyWorld
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/ApiLogList")
    public JSONMessage apiLogList(@RequestParam(defaultValue = "") String keyWorld,
                                  @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "15") int limit) {

        try {
            PageResult<SysApiLog> data = TigBeanUtils.getAdminManager().apiLogList(keyWorld, page, limit);
            return JSONMessage.success(data);

        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 删除 api 日志
     *
     * @param apiLogId
     * @param type
     * @return
     */
    @RequestMapping(value = "/delApiLog")
    public JSONMessage delApiLog(@RequestParam(defaultValue = "") String apiLogId, @RequestParam(defaultValue = "0") int type) {
        try {
            TigBeanUtils.getAdminManager().deleteApiLog(apiLogId, type);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param limit
     * @param page
     * @return
     * @Description:（后台朋友圈列表）
     **/
    @RequestMapping(value = "/getFriendsMsgList")
    public JSONMessage getFriendsMsgList(@RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "10") Integer limit, @RequestParam(defaultValue = "") String nickname, @RequestParam(defaultValue = "0") Integer userId) {
        try {
            PageResult<Msg> data = TigBeanUtils.getMsgRepository().getMsgList(page, limit, nickname, userId);
            return JSONMessage.success(data);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param messageId 朋友圈ID
     * @return
     * @Description:（删除朋友圈）
     **/
    @RequestMapping(value = "/deleteFriendsMsg")
    public JSONMessage deleteMsg(@RequestParam String messageId) {
        JSONMessage jMessage;
        if (StringUtil.isEmpty(messageId)) {
            jMessage = Result.ParamsAuthFail;
        } else {
            try {
                String[] messageIds = StringUtil.getStringList(messageId);
                boolean ok = TigBeanUtils.getMsgRepository().delete(messageIds);
                jMessage = ok ? JSONMessage.success() : JSONMessage.failure(null);
            } catch (Exception e) {
                logger.error("删除商务圈消息失败", e);
                jMessage = JSONMessage.error(e);
            }
        }
        return jMessage;
    }

    /**
     * @param msgId 朋友圈ID
     * @param state 状态
     * @return
     * @Description:（锁定朋友圈）
     **/
    @RequestMapping(value = "/lockingMsg")
    public JSONMessage lockingMsg(@RequestParam String msgId, @RequestParam int state) {
        try {
            TigBeanUtils.getMsgRepository().lockingMsg(new ObjectId(msgId), state);
            return JSONMessage.success();
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param msgId 朋友去ID
     * @param page  页码
     * @param limit 每页总数
     * @return
     * @Description:（朋友圈评论）
     **/
    @RequestMapping(value = "/commonListMsg")
    public JSONMessage commonListMsg(@RequestParam String msgId, @RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "10") Integer limit) {
        try {
            PageResult<Comment> result = TigBeanUtils.getMsgRepository().commonListMsg(new ObjectId(msgId), page, limit);
            return JSONMessage.success(result);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param msgId 朋友去ID
     * @param page  页码
     * @param limit 每页总数
     * @return
     * @Description:（朋友圈点赞）
     **/
    @RequestMapping(value = "/praiseListMsg")
    public JSONMessage praiseListMsg(@RequestParam String msgId, @RequestParam(defaultValue = "0") Integer page,
                                     @RequestParam(defaultValue = "10") Integer limit) {
        try {
            PageResult<Praise> result = TigBeanUtils.getMsgRepository().praiseListMsg(new ObjectId(msgId), page, limit);
            return JSONMessage.success(result);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param messageId 朋友圈ID
     * @param commentId 评论ID
     * @return
     * @Description:（删除评论）
     **/
    @RequestMapping(value = "/comment/delete")
    public JSONMessage deleteComment(@RequestParam String messageId, @RequestParam String commentId) {
        JSONMessage jMessage;
        if (StringUtil.isEmpty(messageId) || StringUtil.isEmpty(commentId)) {
            jMessage = Result.ParamsAuthFail;
        } else {
            try {
                boolean ok = TigBeanUtils.getMsgCommentRepository().delete(new ObjectId(messageId), commentId);
                jMessage = ok ? JSONMessage.success() : JSONMessage.failure(null);
            } catch (Exception e) {
                logger.error("删除评论失败", e);
                jMessage = JSONMessage.error(e);
            }
        }
        return jMessage;
    }

    /**
     * @param userId
     * @param status
     * @return
     * @Description:（用户账号锁定解锁）
     **/
    @RequestMapping("/changeStatus")
    public JSONMessage changeStatus(@RequestParam int userId, @RequestParam int status) {
        getUserManager().changeStatus(userId, status);
        return JSONMessage.success();
    }

    /**
     * @param userId
     * @return
     * @Description:（用户账号登录解锁）
     **/
    @RequestMapping("/unlockUserLogin")
    public JSONMessage unlockUserLogin(@RequestParam int userId) {
        getUserManager().unlockUserLogin(userId);
        return JSONMessage.success();
    }

    /**
     * @param status
     * @Description:（审核拒绝）
     **/
    @RequestMapping("/passAndRefuse")
    public JSONMessage unlockUserLogin(@RequestParam String id,@RequestParam int status) {
        // 创建充值记录getUserStatusCount
        ConsumeRecord record = TigBeanUtils.getConsumeRecordManager().get(new ObjectId(id));
        if(status == 1){
            getUserManager().rechargeUserMoeny(record.getUserId(), record.getMoney(), KConstants.MOENY_ADD);
        }
        record.setStatus(status);
        TigBeanUtils.getConsumeRecordManager().update(new ObjectId(id),record);
        return JSONMessage.success();
    }

    /**
     * @param userId    用户ID
     * @param type      类型
     * @param page      页码
     * @param limit     每页条数
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return
     * @Description:（系统充值记录）
     **/
    @RequestMapping("/systemRecharge")
    public JSONMessage systemRecharge(@RequestParam(defaultValue = "0") int userId, @RequestParam(defaultValue = "0") int type, @RequestParam(defaultValue = "0") int page,
                                      @RequestParam(defaultValue = "10") int limit, @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate) {
        try {
            PageResult<ConsumeRecord> result = TigBeanUtils.getConsumeRecordManager().recharge(userId, type, page, limit, startDate, endDate);
            return JSONMessage.success(result);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getErrMessage());
        }
    }

    /**
     * 后台充值
     *
     * @param money  金额
     * @param userId 用户ID
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/Recharge")
    public JSONMessage Recharge(Double money, int userId) throws Exception {
        // 核验用户是否存在
       User user = getUserManager().getUser(userId);
        if (null == user) {
            return JSONMessage.failure("充值失败, 用户不存在!");
        }
        String tradeNo = StringUtil.getOutTradeNo();
        Map<String, Object> data = Maps.newHashMap();
        // 创建充值记录getUserStatusCount
        ConsumeRecord record = new ConsumeRecord();
        record.setUserId(userId);
        record.setTradeNo(tradeNo);
        record.setMoney(money);
        record.setStatus(KConstants.OrderStatus.END);
        record.setType(KConstants.ConsumeType.SYSTEM_RECHARGE);
        record.setPayType(KConstants.PayType.SYSTEMPAY); // type = 3 ：管理后台充值
        record.setDesc("后台余额充值");
        record.setTime(DateUtil.currentTimeSeconds());
        TigBeanUtils.getConsumeRecordManager().save(record);
        try {
            Double balance = getUserManager().rechargeUserMoeny(userId, money, KConstants.MOENY_ADD);

            User spreadUser = TigBeanUtils.getUserManager().getInviteCode(user.getRegInviteCode());
            if(spreadUser != null) {
                //返利上级
                ConsumeRecord spreadRecord = new ConsumeRecord();
                spreadRecord.setUserId(spreadUser.getUserId());
                spreadRecord.setTradeNo(tradeNo);
                TransferConfig transferConfig = TigBeanUtils.getRedisService().queryTransferConfig();
                if (transferConfig.getRechargeRate() != null && transferConfig.getRechargeRate() != 0) {
                    BigDecimal result = BigDecimal.valueOf(money).multiply(BigDecimal.valueOf(transferConfig.getRechargeRate()));
                    record.setMoney(result.doubleValue());
                    record.setStatus(KConstants.OrderStatus.END);
                    record.setType(KConstants.ConsumeType.RECHARGE_REBATE);
                    record.setPayType(KConstants.PayType.ALIPAY);
                    record.setDesc("下级充值返利");
                    record.setTime(DateUtil.currentTimeSeconds());
                    TigBeanUtils.getConsumeRecordManager().save(record);

                    TigBeanUtils.getUserManager().rechargeUserMoeny(spreadUser.getUserId(), result.doubleValue(), KConstants.MOENY_ADD);
                }
            }
            data.put("balance", balance);
            return JSONMessage.success(null, data);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 后台扣款
     *
     * @param money  金额
     * @param userId 用户ID
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/transfer")
    public JSONMessage transfer(Double money, int userId) throws Exception {
        // 核验用户是否存在
        User user = getUserManager().getUser(userId);
        if (null == user) {
            return JSONMessage.failure("扣款失败, 用户不存在!");
        }
        DecimalFormat df = new DecimalFormat("#.00");
        Double totalFee = Double.valueOf(df.format(money));
        if (totalFee > user.getBalance()) {
            return JSONMessage.failure("账号余额不足,当前余额:" + user.getBalance());
        }
        String tradeNo = StringUtil.getOutTradeNo();
        // 创建充值记录getUserStatusCount
        ConsumeRecord record = new ConsumeRecord();
        record.setUserId(userId);
        record.setTradeNo(tradeNo);
        record.setMoney(money);
        record.setStatus(KConstants.OrderStatus.END);
        record.setType(KConstants.ConsumeType.ADMIN_WITHHOLD);
        record.setPayType(KConstants.PayType.BALANCEAY); // type = 3 ：管理后台充值
        record.setDesc("后台余额扣款");
        record.setTime(DateUtil.currentTimeSeconds());
        TigBeanUtils.getConsumeRecordManager().save(record);
        try {
            getUserManager().rechargeUserMoeny(userId, money, KConstants.MOENY_REDUCE);
            return JSONMessage.success("扣款成功");
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 后台审核提现
     *
     * @param raiseId 申请提现ID
     * @param status  需要修改的状态（0：初始化，1：同意 2待审核 3：拒绝  4交易完成-已打款）
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/updataRaiseStatus")
    public JSONMessage updataRaiseStatus(String raiseId, int status,String remark){
        boolean lockStatus = TigBeanUtils.getRedisService().acquireAuthWithdraw(raiseId);
        if (!lockStatus){
            return JSONMessage.success("请稍后操作");
        }
        try {
            ConsumeRecord record = TigBeanUtils.getConsumeRecordManager().get(new ObjectId(raiseId));
            if (record == null) {
                return JSONMessage.success("扣款成功");
            }
            if (record.getStatus() == KConstants.OrderStatus.REVIEWED_CREATE){
                record.setStatus(status);
                record.setDesc("审核通过,等待打款");
                if (status == 3) {
                    record.setDesc(remark);
                    getUserManager().rechargeUserMoeny(record.getUserId(), record.getMoney(), KConstants.MOENY_ADD);
//                    TigBeanUtils.getConsumeRecordManager().updateAttribute(new ObjectId(raiseId), "status", status);
                    TigBeanUtils.getConsumeRecordManager().update(new ObjectId(raiseId), record);
                    return JSONMessage.success("驳回成功");
                }
                TigBeanUtils.getConsumeRecordManager().update(new ObjectId(raiseId), record);
                return JSONMessage.success("扣款成功");
            }else {
                return JSONMessage.success("请勿重复操作订单");
            }
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }finally {
            TigBeanUtils.getRedisService().releaseAuthWithdraw(raiseId);
        }

    }

    /**
     * 用户账单
     *
     * @param userId 用户ID
     * @param page   页码
     * @param limit  每页条数
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/userBill")
    public JSONMessage userBill(@RequestParam int userId, int page, int limit) throws Exception {
        try {
            // 核验用户是否存在
            if (null == getUserManager().getUser(userId)) {
                return JSONMessage.failure("用户不存在!");
            }
            PageResult<ConsumeRecord> result = TigBeanUtils.getConsumeRecordManager().consumeRecordList(userId, page, limit, (byte) 1);
            return JSONMessage.success(result);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 添加服务器
     *
     * @param server
     * @return
     */
    @RequestMapping(value = "/addServerList")
    public JSONMessage addServerList(@ModelAttribute ServerListConfig server) {
        try {
            TigBeanUtils.getAdminManager().addServerList(server);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 获取服务器列表
     *
     * @param id
     * @param pageIndex
     * @param limit
     * @return
     */
    @RequestMapping(value = "/serverList")
    public JSONMessage serverList(@RequestParam(defaultValue = "") String id, @RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "10") int limit) {
        PageResult<ServerListConfig> result = TigBeanUtils.getAdminManager().getServerList((!StringUtil.isEmpty(id) ? new ObjectId(id) : null), pageIndex, limit);
        return JSONMessage.success(null, result);
    }

    @RequestMapping(value = "/findServerByArea")
    public JSONMessage findServerByArea(@RequestParam(defaultValue = "") String area) {
        PageResult<ServerListConfig> result = TigBeanUtils.getAdminManager().findServerByArea(area);
        return JSONMessage.success(null, result);
    }

    /**
     * 修改服务器
     *
     * @param server
     * @return
     */
    @RequestMapping(value = "/updateServer")
    public JSONMessage updateServer(@ModelAttribute ServerListConfig server) {
        try {
            TigBeanUtils.getAdminManager().updateServer(server);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 删除服务器
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/deleteServer")
    public JSONMessage deleteServer(@RequestParam String id) {
        try {
            TigBeanUtils.getAdminManager().deleteServer(new ObjectId(id));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 地区配置列表
     *
     * @param area
     * @param pageIndex
     * @param limit
     * @return
     */
    @RequestMapping(value = "/areaConfigList")
    public JSONMessage areaConfigList(@RequestParam(defaultValue = "") String area, @RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "10") int limit) {
        PageResult<AreaConfig> result = TigBeanUtils.getAdminManager().areaConfigList(area, pageIndex, limit);
        return JSONMessage.success(result);
    }

    /**
     * 添加地区配置
     *
     * @param area
     * @return
     */
    @RequestMapping(value = "/addAreaConfig")
    public JSONMessage addAreaConfig(@ModelAttribute AreaConfig area) {
        try {
            TigBeanUtils.getAdminManager().addAreaConfig(area);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 修改地区配置
     *
     * @param area
     * @return
     */
    @RequestMapping(value = "/updateAreaConfig")
    public JSONMessage updateAreaConfig(@ModelAttribute AreaConfig area) {
        try {
            TigBeanUtils.getAdminManager().updateAreaConfig(area);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 删除地区配置
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/deleteAreaConfig")
    public JSONMessage deleteAreaConfig(@RequestParam String id) {
        try {
            TigBeanUtils.getAdminManager().deleteAreaConfig(new ObjectId(id));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 添加入口配置
     *
     * @param urlConfig
     * @return
     */
    @RequestMapping(value = "/addUrlConfig")
    public JSONMessage addUrlConfig(@ModelAttribute UrlConfig urlConfig) {
        try {
            TigBeanUtils.getAdminManager().addUrlConfig(urlConfig);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 查询入口配置
     *
     * @param id
     * @param type
     * @return
     */
    @RequestMapping(value = "/findUrlConfig")
    public JSONMessage findUrlConfig(@RequestParam(defaultValue = "") String id, @RequestParam(defaultValue = "") String type) {
        PageResult<UrlConfig> result = TigBeanUtils.getAdminManager().findUrlConfig((!StringUtil.isEmpty(id) ? new ObjectId(id) : null), type);
        return JSONMessage.success(null, result);

    }

    /**
     * 删除入口
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/deleteUrlConfig")
    public JSONMessage deleteUrlConfig(@RequestParam(defaultValue = "") String id) {
        try {
            TigBeanUtils.getAdminManager().deleteUrlConfig(!StringUtil.isEmpty(id) ? new ObjectId(id) : null);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 中心服务器
     *
     * @param centerConfig
     * @return
     */
    @RequestMapping(value = "/addcenterConfig")
    public JSONMessage addCenterConfig(@ModelAttribute CenterConfig centerConfig) {
        try {
            TigBeanUtils.getAdminManager().addCenterConfig(centerConfig);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 查询中心服务器
     *
     * @param type
     * @param id
     * @return
     */
    @RequestMapping(value = "/findCenterConfig")
    public JSONMessage findCentConfig(@RequestParam(defaultValue = "") String type, @RequestParam(defaultValue = "") String id) {
        PageResult<CenterConfig> result = TigBeanUtils.getAdminManager().findCenterConfig(type, (!StringUtil.isEmpty(id) ? new ObjectId(id) : null));
        return JSONMessage.success(null, result);
    }

    /**
     * 删除中心服务器
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/deleteCenter")
    public JSONMessage deleteCenter(@RequestParam String id) {
        try {
            TigBeanUtils.getAdminManager().deleteCenter(new ObjectId(id));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.success();
        }

    }

    /**
     * 保存总配置
     *
     * @param totalConfig
     * @return
     */
    @RequestMapping(value = "/addTotalConfig")
    public JSONMessage addTotalConfig(@ModelAttribute TotalConfig totalConfig) {
        try {
            TigBeanUtils.getAdminManager().addTotalConfig(totalConfig);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    @RequestMapping(value = "/addAdmin")
    public JSONMessage addAdmin(@RequestParam(defaultValue = "86") Integer areaCode, @RequestParam String telePhone, @RequestParam byte role, @RequestParam(defaultValue = "0") Integer type) {
        try {
            // 核验账号是否重复
            // User user = getUserManager().getUser(areaCode+account);
            /*
             * Admin admin =
             * TigBeanUtils.getAdminManager().findAdminByAccount(account);
             * if(admin!=null) { return JSONMessage.failure("该账号已存在"); }
             * TigBeanUtils.getAdminManager().addAdmin(account, password, role);
             */
            TigBeanUtils.getRoleManager().addAdmin(areaCode + telePhone, telePhone, role, type);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    @RequestMapping(value = "/adminList")
    public JSONMessage adminList(@RequestParam String adminId, @RequestParam(defaultValue = "") String keyWorld,
                                 @RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "10") int limit,
                                 @RequestParam(defaultValue = "0") Integer type, @RequestParam(defaultValue = "0") Integer userId) {
        try {
            Role userRole = TigBeanUtils.getRoleManager().getUserRole(Integer.parseInt(adminId), null, 5);
            if (userRole != null && (userRole.getRole() == 5 || userRole.getRole() == 6 || userRole.getRole() == 1 || userRole.getRole() == 4 || userRole.getRole() == 7)) {
                PageResult<Role> result = TigBeanUtils.getRoleManager().adminList(keyWorld, page, limit, type, userId);
                return JSONMessage.success(result);
            } else {
                return JSONMessage.failure("权限不足");
            }
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param adminId  管理员ID
     * @param password 密码
     * @param role     角色
     * @return
     * @Description:（后台角色禁用解禁）
     **/
    @RequestMapping(value = "/modifyAdmin")
    public JSONMessage modifyAdmin(@RequestParam Integer adminId, @RequestParam(defaultValue = "") String password, @ModelAttribute Role role) {
        try {
            if (!StringUtil.isEmpty(password)) {
                User user = TigBeanUtils.getUserRepository().getUser(adminId);
                if (!password.equals(user.getPassword())) {
                    return JSONMessage.failure("密码有误");
                }
            }
            Role oAdmin = TigBeanUtils.getRoleManager().getUserRole(adminId, null, 5);
            if (oAdmin != null && (oAdmin.getRole() == 6 || oAdmin.getRole() == 5)) { // role// =// 1// 超级管理员
                Object result = TigBeanUtils.getRoleManager().modifyRole(role);
                return JSONMessage.success(result);
            } else {
                return JSONMessage.failure("权限不足");
            }
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param adminId
     * @return
     * @Description:（删除管理角色）
     **/
    @RequestMapping(value = "/delAdmin")
    public JSONMessage deleteAdmin(@RequestParam String adminId, @RequestParam Integer type) {
        try {
            TigBeanUtils.getRoleManager().delAdminById(adminId, type);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * @param userId
     * @param toUserId
     * @return
     * @Description:（好友的聊天记录）
     **/
    @RequestMapping("/friendsChatRecord")
    public JSONMessage friendsChatRecord(@RequestParam(defaultValue = "0") Integer userId,@RequestParam(defaultValue = "0") Integer toUserId, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit) {
        try {
            PageResult<DBObject> result = TigBeanUtils.getFriendsManager().chardRecord(userId, toUserId, page, limit);
            return JSONMessage.success(result);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param messageId
     * @return
     * @Description:（删除好友间的聊天记录）
     **/
    @RequestMapping("/delFriendsChatRecord")
    public JSONMessage delFriendsChatRecord(@RequestParam(defaultValue = "") String messageId) {
        try {
            if (StringUtil.isEmpty(messageId))
                return JSONMessage.failure("参数有误");
            String[] strMessageIds = StringUtil.getStringList(messageId);
            TigBeanUtils.getFriendsManager().delFriendsChatRecord(strMessageIds);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param toUserId
     * @param type     0 ： 加入黑名单， 1：移除黑名单
     * @return
     * @Description:（黑名单操作）
     **/
    @SuppressWarnings("static-access")
    @RequestMapping("/blacklist/operation")
    public JSONMessage blacklistOperation(@RequestParam Integer userId, @RequestParam Integer toUserId,
                                          @RequestParam(defaultValue = "0") Integer type) {
        JSONMessage jsonMessage = null;
        try {
            if (0 == type) {
                if (TigBeanUtils.getFriendsManager().isBlack(userId, toUserId))
                    return jsonMessage.failure("不能重复拉黑好友");
                Friends data = TigBeanUtils.getFriendsManager().consoleAddBlacklist(userId, toUserId);
                return jsonMessage.success("加入黑名单成功", data);
            } else if (1 == type) {
                if (!TigBeanUtils.getFriendsManager().isBlack(userId, toUserId))
                    return jsonMessage.failure("好友：" + toUserId + "不在我的黑名单中");
                Friends data = TigBeanUtils.getFriendsManager().consoleDeleteBlacklist(userId, toUserId);
                return jsonMessage.success("取消拉黑成功", data);
            }
        } catch (Exception e) {
            return jsonMessage.failure(e.getMessage());
        }
        return null;

    }

    /**
     * 开放平台app列表
     *
     * @param status   状态
     * @param type     类型
     * @param page     页码
     * @param limit    每页条数
     * @param keyWorld 关键字
     * @return
     */
    @RequestMapping(value = "/openAppList")
    public JSONMessage openAppList(@RequestParam(defaultValue = "-2") int status, @RequestParam(defaultValue = "0") int type,
                                   @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit,
                                   @RequestParam(defaultValue = "") String keyWorld) {
        try {
            PageResult<TigOpenApp> list = TigBeanUtils.getAdminManager().openAppList(status, type, page, limit, keyWorld);
            return JSONMessage.success(list);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 开放平台app详情
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/openAppDetail")
    public JSONMessage openAppDetail(@RequestParam(defaultValue = "") String id) {
        try {
            Object data = TigBeanUtils.getOpenAppManage().appInfo(new ObjectId(id));
            return JSONMessage.success(data);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 开放平台通过审核、禁用应用
     *
     * @param id
     * @param userId
     * @param status
     * @return
     */
    @RequestMapping(value = "/approvedAPP")
    public JSONMessage approved(@RequestParam(defaultValue = "") String id,
                                @RequestParam(defaultValue = "") String userId, @RequestParam(defaultValue = "0") int status,
                                @RequestParam(defaultValue = "") String reason) {
        try {
            TigBeanUtils.getOpenAppManage().approvedAPP(id, status, userId, reason);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 开放平台通过审核app权限
     *
     * @param tigOpenApp
     * @return
     */
    @RequestMapping(value = "/checkPermission")
    public JSONMessage checkPermission(@ModelAttribute TigOpenApp tigOpenApp) {
        try {
            TigBeanUtils.getOpenAppManage().openAccess(tigOpenApp);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

//	/**
//	 * 开放平台通过审核web权限
//	 * 
//	 * @param tigOpenWeb
//	 * @return
//	 */
//	@RequestMapping(value = "/checkWebPermission")
//	public JSONMessage checkWebPermission(@ModelAttribute TigOpenWeb tigOpenWeb) {
//		try {
//			TigBeanUtils.getOpenWebAppManage().openAccess(tigOpenWeb);
//			return JSONMessage.success();
//		} catch (Exception e) {
//			e.printStackTrace();
//			return JSONMessage.failure(e.getMessage());
//		}
//	}

    /**
     * 开放平台删除应用
     *
     * @param id
     * @param accountId
     * @return
     */
    @RequestMapping(value = "/deleteOpenApp")
    public JSONMessage deleteOpenApp(@RequestParam(defaultValue = "") String id,
                                     @RequestParam(defaultValue = "") String accountId) {
        try {
            TigBeanUtils.getOpenAppManage().deleteAppById(new ObjectId(id), accountId);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 开放平台审核日志列表
     *
     * @param page  页码
     * @param limit 每页条数
     * @return
     */
    @RequestMapping(value = "/checkLogList")
    public JSONMessage checkLogList(@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit) {
        try {
            PageResult<TigOpenCheckLog> data = TigBeanUtils.getOpenCheckLogManage().getOpenCheckLogList(page, limit);
            return JSONMessage.success(data);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 开放平台删除日志
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/delOpenCheckLog")
    public JSONMessage delOpenCheckLog(@RequestParam(defaultValue = "") String id) {
        try {
            TigBeanUtils.getOpenCheckLogManage().delOpenCheckLog(new ObjectId(id));
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 开放平台开发者列表
     *
     * @param page     页码
     * @param limit    每页条数
     * @param status   状态
     * @param keyWorld 关键字
     * @return
     */
    @RequestMapping(value = "/developerList")
    public JSONMessage developerList(@RequestParam(defaultValue = "0") int page,
                                     @RequestParam(defaultValue = "10") int limit, @RequestParam(defaultValue = "-2") int status,
                                     @RequestParam(defaultValue = "") String keyWorld) {
        try {
            PageResult<TigOpenAccount> data = TigBeanUtils.getOpenAccountManage().developerList(page, limit, status,
                    keyWorld);
            return JSONMessage.success(data);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 开放平台开发者详情
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "/developerDetail")
    public JSONMessage developerDetail(@RequestParam(defaultValue = "") Integer userId) {
        try {
            Object data = TigBeanUtils.getOpenAccountManage().getOpenAccount(userId);
            return JSONMessage.success(data);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 开放平台删除开发者
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/deleteDeveloper")
    public JSONMessage deleteDeveloper(@RequestParam(defaultValue = "") String id) {
        try {
            TigBeanUtils.getOpenAccountManage().deleteDeveloper(new ObjectId(id));
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 开放平台审核开发者、禁用
     *
     * @param id
     * @param userId
     * @param status
     * @return
     */
    @RequestMapping(value = "/checkDeveloper")
    public JSONMessage checkDeveloper(@RequestParam(defaultValue = "") String id,
                                      @RequestParam(defaultValue = "") String userId, @RequestParam(defaultValue = "0") int status) {
        try {
            TigBeanUtils.getOpenAccountManage().checkDeveloper(new ObjectId(id), status);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    // 校验第三方网站是否有对应权限
    @RequestMapping(value = "/authInterface")
    public JSONMessage authInterface(@RequestParam(defaultValue = "") String appId, @RequestParam(defaultValue = "1") int type) {
        try {
            TigBeanUtils.getOpenAppManage().authInterfaceWeb(appId, type);
            return JSONMessage.success();

        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    //生成邀请码
    @RequestMapping(value = "/create/inviteCode")
    public JSONMessage createInviteCode(@RequestParam(defaultValue = "20") int nums, @RequestParam int userId, @RequestParam short type) throws IOException {
        try {
            TigBeanUtils.getAdminManager().createInviteCode(nums, userId);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }

    }

    // 邀请码列表
    @RequestMapping(value = "/inviteCodeList")
    public JSONMessage inviteCodeList(@RequestParam(defaultValue = "0") int userId,
                                      @RequestParam(defaultValue = "") String keyworld, @RequestParam(defaultValue = "-1") short state,
                                      @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit) {
        try {
            PageResult<InviteCode> data = TigBeanUtils.getAdminManager().inviteCodeList(userId, keyworld, state, page, limit);
            InviteConfig inviteConfig = TigBeanUtils.getLocalSpringBeanManager().getApplicationConfig().getInviteConfig();
            data.getData().forEach(inviteCode -> inviteCode.setPassCardButton(inviteConfig.isPassCardIsOpen()));
            return JSONMessage.success(data);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    // 删除邀请码
    @RequestMapping(value = "/delInviteCode")
    public JSONMessage delInviteCode(@RequestParam(defaultValue = "") int userId,
                                     @RequestParam(defaultValue = "") String inviteCodeId) {

        try {
            boolean data = TigBeanUtils.getAdminManager().delInviteCode(userId, inviteCodeId);
            return JSONMessage.success(data);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param param checkNum 参与发送的人数
     *              roomJid 房间jid
     *              sendMsgNum 发送总条数
     *              timeInterval 每条消息间隔时间
     * @return
     * @Description:（压力测试）
     **/
    @RequestMapping("/pressureTest")
    public JSONMessage pressureTest(@ModelAttribute PressureParam param, HttpServletRequest request) {
        try {
            if (param.getTimeInterval() < 10) {
                param.setTimeInterval(10);
            }
            logger.info("pressureTest ====> " + request.getSession().getCreationTime() + " " + request.getSession().getId());
            List<String> jids = StringUtil.getListBySplit(param.getRoomJid(), ",");
            param.setJids(jids);
            param.setSendAllCount(param.getSendMsgNum() * jids.size());
            PressureParam.PressureResult result = TigBeanUtils.getPressureTestManager().mucTest(param);
            if (null == result) {
                return JSONMessage.failure("已有压测 任务 运行中  请稍后 请求 。。。。。。");
            }
            return JSONMessage.success(result);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 短视频音乐列表
     *
     * @param page
     * @param limit
     * @param keyword
     * @return
     */
    @RequestMapping(value = "/musicList")
    public JSONMessage queryMusicList(@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") Integer limit, @RequestParam(defaultValue = "") String keyword) {
        PageResult<MusicInfo> result = TigBeanUtils.getLocalSpringBeanManager().getAdminManager().queryMusicInfo(page, limit, keyword);
        return JSONMessage.success(result);
    }

    /**
     * 添加短视频音乐
     *
     * @param musicInfo
     * @return
     */
    @RequestMapping(value = "/addMusic")
    public JSONMessage addMusic(@ModelAttribute MusicInfo musicInfo) {
        try {
            TigBeanUtils.getLocalSpringBeanManager().getMusicManager().addMusicInfo(musicInfo);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 删除短视频音乐
     *
     * @param musicInfoId
     * @return
     */
    @RequestMapping(value = "/deleteMusic")
    public JSONMessage deleteMusic(@RequestParam(defaultValue = "") String musicInfoId) {
        try {
            TigBeanUtils.getLocalSpringBeanManager().getMusicManager().deleteMusicInfo(new ObjectId(musicInfoId));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * 修改短视频音乐
     *
     * @param musicInfo
     * @return
     */
    @RequestMapping(value = "/updateMusic")
    public JSONMessage updateMusic(@ModelAttribute MusicInfo musicInfo) {
        try {
            TigBeanUtils.getLocalSpringBeanManager().getMusicManager().updateMusicInfo(musicInfo);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 转账记录
     *
     * @param userId
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/transferList")
    public JSONMessage transferList(@RequestParam(defaultValue = "") String userId,
                                    @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "15") int limit,
                                    @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate) {
        PageResult<Transfer> result = TigBeanUtils.getAdminManager().queryTransfer(page, limit, userId, startDate, endDate);
        return JSONMessage.success(result);
    }

    /**
     * 付款记录
     *
     * @param userId
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/paymentCodeList")
    public JSONMessage paymentCodeList(@RequestParam(defaultValue = "0") int userId,
                                       @RequestParam(defaultValue = "0") int type, @RequestParam(defaultValue = "0") int page,
                                       @RequestParam(defaultValue = "15") int limit, @RequestParam(defaultValue = "") String startDate,
                                       @RequestParam(defaultValue = "") String endDate) {
        PageResult<ConsumeRecord> result = TigBeanUtils.getConsumeRecordManager().payment(userId, type, page, limit, startDate, endDate);
        return JSONMessage.success(result);
    }

    /**
     * 获取第三方绑定列表
     *
     * @param userId
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/getSdkLoginInfoList")
    public JSONMessage getSdkLoginInfoList(@RequestParam(defaultValue = "") String userId,
                                           @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "15") int limit) {
        try {
            PageResult<SdkLoginInfo> result = TigBeanUtils.getAdminManager().getSdkLoginInfoList(page, limit, userId);
            return JSONMessage.success(result);
        } catch (NumberFormatException e) {
            logger.info("error : {}" + e.getMessage());
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 解除第三方绑定
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/deleteSdkLoginInfo")
    public JSONMessage deleteSdkLoginInfo(@RequestParam(defaultValue = "") String id) {
        try {
            TigBeanUtils.getAdminManager().deleteSdkLoginInfo(new ObjectId(id));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @param appId
     * @param callbackUrl
     * @param response
     * @Description:授权登录
     **/
    @RequestMapping(value = "/oauth/authorize")
    public void authorizeUrl(String appId, String callbackUrl, HttpServletResponse response) {
        try {
            Map<String, String> webInfo = TigBeanUtils.getOpenAppManage().authorizeUrl(appId, callbackUrl);
            String webAppName = webInfo.get("webAppName");
            response.sendRedirect("/pages/websiteAuthorh/index.html" + "?" + "appId=" + appId + "&" + "callbackUrl=" + callbackUrl + "&webAppName=" + URLEncoder.encode(webAppName, "UTF-8") + "&webAppsmallImg=" + webInfo.get("webAppsmallImg"));
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @RequestMapping(value = "/oauth/authorize/v1")
    public JSONMessage authorizeUrlV1(String appId, String callbackUrl, HttpServletResponse response) {
        try {
            Map<String, String> webInfo = TigBeanUtils.getOpenAppManage().authorizeUrl(appId, callbackUrl);
            webInfo.put("callbackUrl", callbackUrl);
            return JSONMessage.success(webInfo);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return JSONMessage.failure("授权失败");
        }
    }


    /**
     * 发送系统通知
     *
     * @param type
     * @param body
     * @return
     */
    @RequestMapping(value = "/sendSysNotice")
    public JSONMessage sendSysNotice(@RequestParam(defaultValue = "") Integer type, @RequestParam(defaultValue = "") String body) {
        try {
            TigBeanUtils.getAdminManager().sendSysNotice(type, body);
        } catch (Exception e) {

        }
        return null;
    }

    /**
     * 全部用户
     *
     * @param page
     * @param limit
     * @param userId
     * @return
     */
    @RequestMapping(value = "/otherUserList")
    public JSONMessage otherUserList(@RequestParam(defaultValue = "0") int page,
                                     @RequestParam(defaultValue = "10") int limit,
                                     @RequestParam(defaultValue = "") Long userId, @RequestParam(defaultValue = "") String keyWorld) {

        Query<User> query = getUserManager().createQuery();
        query.field("_id").greaterThanOrEq(10000000);
        query.field("_id").notEqual(userId);
        if (!StringUtil.isEmpty(keyWorld)) {
            query.field("nickname").containsIgnoreCase(keyWorld);
        }

        // 排序、分页
        List<User> pageData = query.order("-createTime").asList(getUserManager().pageFindOption(page, limit, 1));
        pageData.forEach(userInfo -> {
            Query<UserLoginLog> loginLog = TigBeanUtils.getDatastore().createQuery(UserLoginLog.class).field("userId").equal(userInfo.getUserId());
            if (null != loginLog.get()) {
                userInfo.setLoginLog(loginLog.get().getLoginLog());
            }
        });
        PageResult<User> result = new PageResult<>();
        for (User user : pageData) {
            if (user.getPhoneToLocation() == null && user.getPhone() != null && user.getAreaCode() != null) {
                Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
                if (!pattern.matcher(user.getPhone()).matches()) {
                    user.setPhoneToLocation("未知区域");
                    user.setCarrier("未知");
                } else {
                    PhoneModel phoneModel = PhoneUtils.getPhoneModel(user.getPhone(), Integer.parseInt(user.getAreaCode()));
                    //处理归属地信息
                    if (phoneModel != null) {
                        if (phoneModel.getProvinceName() != null) {
                            String phoneToLocation = phoneModel.getProvinceName() + (phoneModel.getCityName() == null ? "" : "-" + phoneModel.getCityName());
                            user.setPhoneToLocation(phoneToLocation);
                        } else {
                            String phoneToLocation = phoneModel.getCityName() == null ? "" : phoneModel.getCityName();
                            user.setPhoneToLocation(phoneToLocation);
                        }
                        user.setCarrier(phoneModel.getCarrier());
                    }
                }
            }
        }
        result.setData(pageData);
        result.setCount(query.count());
        return JSONMessage.success(result);
    }

    /**
     * 批量添加好友
     *
     * @param currentUserId
     * @param toUserIds
     * @return
     */
    @RequestMapping(value = "/batchAddFriends")
    public JSONMessage batchAddFriends(@RequestParam(defaultValue = "") Integer currentUserId, @RequestParam(defaultValue = "") String toUserIds) {
        return TigBeanUtils.getFriendsManager().batchFollowUser(currentUserId, toUserIds);
    }

    /**
     * ----------------------------邀请码管理----------Start------------------------------
     **/

    @RequestMapping(value = "/userInvitePassCardList")
    public JSONMessage userInvitePassCardList(@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit, @RequestParam(defaultValue = "") String userId) {
        PageResult<UserInvitePassCard> result = getUserInviteManager().getAdminUserPassCardByUserId(userId, limit, page);
        return JSONMessage.success(result);
    }
    /**----------------------------邀请码管理----------End------------------------------**/

    /**
     * ----------------------------接口安全管理----------Start------------------------------
     **/
    @RequestMapping(value = "/setIpWhiteList")
    public JSONMessage setIpWhiteList(@RequestParam(defaultValue = "") String ipWhiteStr) {
        TigBeanUtils.getAdminManager().setIpWhiteList(ipWhiteStr);
        return JSONMessage.success(ipWhiteStr);
    }

    @RequestMapping(value = "/setIpWhiteListManage")
    public JSONMessage setIpWhiteListManage(@RequestParam(defaultValue = "") String ipWhiteStr) {
        TigBeanUtils.getAdminManager().setIpWhiteListManage(ipWhiteStr);
        return JSONMessage.success(ipWhiteStr);
    }

    @RequestMapping(value = "/getIpWhiteList")
    public JSONMessage getIpWhiteList() {
        Config config = TigBeanUtils.getAdminManager().getConfig();
        return JSONMessage.success(config.getIpWhiteList() == null ? "" : config.getIpWhiteList());
    }

    @RequestMapping(value = "/getIpWhiteListManage")
    public JSONMessage getIpWhiteListManage() {
        Config config = TigBeanUtils.getAdminManager().getConfig();
        return JSONMessage.success(config.getIpWhiteListManage() == null ? "" : config.getIpWhiteListManage());
    }

    /**
     * ----------------------------接口安全管理----------End------------------------------
     **/

    @RequestMapping(value = "/media/list")
    public JSONMessage getMediaList(@RequestParam(defaultValue = "0") int type, @RequestParam(defaultValue = "0") int page,
                                    @RequestParam(defaultValue = "15") int limit) {
        List<Media> data = TigBeanUtils.getMediaRepository().queryByType(type, page, limit);
        return JSONMessage.success(data);
    }

    @RequestMapping(value = "/media/add")
    public JSONMessage addMedia(@ModelAttribute Media media) {
        Media media1 = TigBeanUtils.getMediaRepository().addMedia(media);
        return JSONMessage.success(media1);
    }

    @RequestMapping(value = "/media/update")
    public JSONMessage updateMedia(@ModelAttribute Media media) {
        TigBeanUtils.getMediaRepository().updateMedia(media);
        return JSONMessage.success();
    }

    @RequestMapping(value = "/income/list")
    public JSONMessage getMediaList( @RequestParam(defaultValue = "0") int page,
                                    @RequestParam(defaultValue = "15") int limit) {
        List<Income> data = TigBeanUtils.getIncomeRepository().queryByType( page, limit);
        return JSONMessage.success(data);
    }

    @RequestMapping(value = "/income/add")
    public JSONMessage addMedia(@ModelAttribute Income media) {
        Income media1 = TigBeanUtils.getIncomeRepository().addMedia(media);
        return JSONMessage.success(media1);
    }

    @RequestMapping(value = "/income/update")
    public JSONMessage updateMedia(@ModelAttribute Income media) {
        TigBeanUtils.getIncomeRepository().updateMedia(media);
        return JSONMessage.success();
    }

    @RequestMapping(value = "/income/delete")
    public JSONMessage delete(@RequestParam String id) {
        TigBeanUtils.getIncomeRepository().deleteById(new ObjectId(id));
        return JSONMessage.success();
    }

    @RequestMapping("/updateIsCreateRoom")
    public JSONMessage updateIsCreateRoom(Integer userId, int type) {
        TigBeanUtils.getAdminManager().updateIsCreateRoom(userId, type);
        HashMap map = new HashMap();
        map.put("code", 1);
        map.put("msg", "设置成功");
        KSessionUtil.deleteUserByUserId(userId);
        return JSONMessage.success(map);
    }

    @RequestMapping("/updateIsAddFirend")
    public JSONMessage updateIsAddFirend(Integer userId, int type) {
        TigBeanUtils.getAdminManager().updateIsAddFirend(userId, type);
        HashMap map = new HashMap();
        map.put("code", 1);
        map.put("msg", "设置成功");
        KSessionUtil.deleteUserByUserId(userId);
        return JSONMessage.success(map);
    }

    /**
     * @author:
     * @moduleName: nodeInfoList
     * @dateTime: 2019/7/3 19:00
     * @param: [area, pageIndex, limit]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 获取节点列表信息
     **/
    @RequestMapping(value = "/nodeInfoList")
    public JSONMessage nodeInfoList(@RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "10") int limit) {
        PageResult<NodesInfo> result = TigBeanUtils.getAdminManager().nodeInfoList(pageIndex, limit);
        return JSONMessage.success(result);
    }

    /**
     * @author:
     * @moduleName: addNodeInfo
     * @dateTime: 2019/7/3 19:04
     * @param: [nodesInfo]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 添加节点信息
     **/
    @RequestMapping(value = "/addNodeInfo")
    public JSONMessage addNodeInfo(@ModelAttribute NodesInfo nodesInfo) {
        try {
            TigBeanUtils.getAdminManager().addNodesInfo(nodesInfo);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }

    }

    /**
     * @author:
     * @moduleName: updateNodeInfo
     * @dateTime: 2019/7/3 19:08
     * @param: [nodesInfo]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 修改节点信息
     **/
    @RequestMapping(value = "/updateNodeInfo")
    public JSONMessage updateNodeInfo(@ModelAttribute NodesInfo nodesInfo) {
        try {
            TigBeanUtils.getAdminManager().updateNodeInfo(nodesInfo);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @author:
     * @moduleName: deleteNodeInfo
     * @dateTime: 2019/7/3 19:14
     * @param: [id]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 删除节点信息
     **/
    @RequestMapping(value = "/deleteNodeInfo")
    public JSONMessage deleteNodeInfo(@RequestParam String id) {
        try {
            TigBeanUtils.getAdminManager().deleteNodeInfo(new ObjectId(id));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @author:
     * @moduleName: versionInfoList
     * @dateTime: 2019/7/3 19:00
     * @param: []
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 获取更新的版本信息
     **/
    @RequestMapping(value = "/versionInfoList")
    public JSONMessage versionInfoList(@RequestParam(defaultValue = "0") int pageIndex, @RequestParam(defaultValue = "10") int limit) {
        PageResult<VersionInfo> result = TigBeanUtils.getAdminManager().versionInfoList(pageIndex, limit);
        return JSONMessage.success(result);
    }

    /**
     * @author:
     * @moduleName: newVersionInfo
     * @dateTime: 2019/7/20 16:03
     * @param: []
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 获取最新APP版本信息
     **/
    @RequestMapping(value = "/newVersionInfo")
    public JSONMessage newVersionInfo() {
        VersionInfo result = TigBeanUtils.getAdminManager().newVersionInfo();
        return JSONMessage.success(result);
    }

    /**
     * @author:
     * @moduleName: addVersionInfo
     * @dateTime: 2019/7/20 16:03
     * @param: [versionInfo（id：版本ID，projectName：项目名称，versionName：版本名称，versionNum：版本号，updateContent：更新内容，thirdLoadURL：第三方更新链接，versionStatus：是否禁用 1：启动 0：禁用）]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 增加修改版本信息
     **/
    @RequestMapping(value = "/addVersionInfo")
    public JSONMessage addVersionInfo(@ModelAttribute VersionInfo versionInfo) {
        try {
            TigBeanUtils.getAdminManager().addVersionInfo(versionInfo);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @author:
     * @moduleName: updateVersionInfo
     * @dateTime: 2019/7/20 16:03
     * @param: [versionInfo（id：版本ID，projectName：项目名称，versionName：版本名称，versionNum：版本号，updateContent：更新内容，thirdLoadURL：第三方更新链接，versionStatus：是否禁用 1：启动 0：禁用）]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 修改版本信息
     **/
    @RequestMapping(value = "/updateVersionInfo")
    public JSONMessage updateVersionInfo(@ModelAttribute VersionInfo versionInfo) {
        try {
            TigBeanUtils.getAdminManager().updateVersionInfo(versionInfo);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @author:
     * @moduleName: deleteVersionInfo
     * @dateTime: 2019/7/20 16:23
     * @param: [id:版本ID]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 删除版本信息
     **/
    @RequestMapping(value = "/deleteVersionInfo")
    public JSONMessage deleteVersionInfo(@RequestParam String id) {
        try {
            TigBeanUtils.getAdminManager().deleteVersionInfo(new ObjectId(id));
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @author:
     * @moduleName: addAndroidVersionInfo
     * @dateTime: 2019/7/20 16:24
     * @param: [apkLoadUrl:apk下载链接, forceStatus：是否强制更新（1：是，0：否）, versionId：版本ID]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 发布安卓版本
     **/
    @RequestMapping(value = "/addAndroidVersionInfo")
    @ResponseBody
    public JSONMessage addAndroidVersionInfo(String apkLoadUrl, String downloadUrl, Integer forceStatus, String versionId) {
        try {
            TigBeanUtils.getAdminManager().addAndroidVersionInfo(versionId, apkLoadUrl, forceStatus, downloadUrl);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @author:
     * @moduleName: addIosVersionInfo
     * @dateTime: 2019/7/20 16:25
     * @param: [ipaLoadUrl：IPA下载链接, displayImg：安装logoURL, fullImg：ituns logo Url, bundleId：IOS bundleId, forceStatus：forceStatus：是否强制更新（1：是，0：否）, versionId：版本ID]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 发布IOS企业包版本
     **/
    @RequestMapping(value = "/addIosVersionInfo")
    @ResponseBody
    public JSONMessage addIosVersionInfo(String ipaLoadUrl, String displayImg, String fullImg, String bundleId, Integer forceStatus, String versionId, String downloadUrl) {
        try {
            TigBeanUtils.getAdminManager().addIosVersionInfo(versionId, ipaLoadUrl, displayImg, fullImg, bundleId, forceStatus, downloadUrl);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @author:
     * @moduleName: addAppStoreVersionInfo
     * @dateTime: 2019/7/20 16:28
     * @param: [appStoreLoadUrl：appStore下载URL, forceStatus：forceStatus：是否强制更新（1：是，0：否）, versionId：版本ID]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 发布APPStore版本
     **/
    @RequestMapping(value = "/addAppStoreVersionInfo")
    @ResponseBody
    public JSONMessage addAppStoreVersionInfo(String appStoreLoadUrl, Integer forceStatus, String versionId) {
        try {
            TigBeanUtils.getAdminManager().addAppStoreVersionInfo(versionId, appStoreLoadUrl, forceStatus);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 保存三方支付配置
     *
     * @param payConfig
     * @return
     */
    @RequestMapping("/savePayConfig")
    @ResponseBody
    public JSONMessage savePayConfig(PayConfig payConfig) {
        if (payConfig == null) {
            return JSONMessage.failure("设置失败");
        }
        boolean status = TigBeanUtils.getAdminManager().savePayConfig(payConfig);
        if (status) {
            return JSONMessage.success("设置成功");
        }
        return JSONMessage.failure("设置失败");
    }

    @RequestMapping("/getPayConfig")
    @ResponseBody
    public JSONMessage getPayConfig(Integer type) {
        PayConfig payConfig = TigBeanUtils.getAdminManager().getPayConfig(type);
        return JSONMessage.success(payConfig);
    }

    /**
     * 保存提现配置
     *
     * @param transferConfig
     * @return
     */
    @RequestMapping("/setTransferConfig")
    @ResponseBody
    public JSONMessage setTransferConfig(@ModelAttribute TransferConfig transferConfig,String newWithdrawDetails) {
        if (transferConfig == null) {
            return JSONMessage.failure("设置失败");
        }
        if(transferConfig.getMaxSendRedPagesAmount()<=0 || transferConfig.getMaxSendRedPagesAmount() >100000){
            return JSONMessage.failure("设置失败,发送红包最大金额请输入【0~100000】之间的数字");
        }
        boolean status = TigBeanUtils.getAdminManager().setTransferConfig(transferConfig,newWithdrawDetails);
        if (status) {
            return JSONMessage.success("设置成功");
        }
        return JSONMessage.failure("设置失败");
    }

    @RequestMapping("/getTransferConfig")
    @ResponseBody
    public JSONMessage getTransferConfig() {
        TransferConfig transferConfig = TigBeanUtils.getAdminManager().getTransferConfig();
        return JSONMessage.success(transferConfig);
    }

    @RequestMapping("/addWithdrawWay")
    @ResponseBody
    public JSONMessage addWithdrawWay(String withdrawKeyDetails,String withdrawWayName) {
        int sort = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).order("-withdrawWaySort").get().getWithdrawWaySort();
        WithdrawWayList withdrawWayList = new WithdrawWayList();
        withdrawWayList.setWithdrawWayName(withdrawWayName);
        withdrawWayList.setWithdrawWaySort(sort+1);
        withdrawWayList.setWithdrawWayStatus(1);
        withdrawWayList.setWithdrawWayTime(DateUtil.currentTimeSeconds());
        JSONArray json = JSONObject.parseArray(withdrawKeyDetails);
        List<WithdrawWayList.WithdrawKey> withdrawKeyListAll = new ArrayList();
        for(int i = 1; i < json.size(); i++){
            JSONArray jsonOne = JSONObject.parseArray(json.get(i).toString());
            WithdrawWayList.WithdrawKey withdrawKey = new WithdrawWayList.WithdrawKey();
            withdrawKey.setWithdrawName(jsonOne.get(0).toString());
            withdrawKey.setWithdrawStatus(jsonOne.get(1).toString());
            withdrawKey.setWithdrawKeyTime(DateUtil.currentTimeSeconds());
            ObjectId id = (ObjectId) TigBeanUtils.getDatastore().save(withdrawKey).getId();
            withdrawKey.setWithdrawId(id);
            withdrawKeyListAll.add(withdrawKey);
        }
        withdrawWayList.setWithdrawKeyDetails(withdrawKeyListAll);
        withdrawWayList.setWithdrawWayKeyId(StringUtil.randomUUID());
        TigBeanUtils.getDatastore().save(withdrawWayList);
        List<WithdrawWayList> withdrawWayListAll = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).asList();
        Query<TransferConfig> transferConfigQuery = TigBeanUtils.getDatastore().createQuery(TransferConfig.class).limit(1);
        UpdateOperations<TransferConfig> ops = TigBeanUtils.getDatastore().createUpdateOperations(TransferConfig.class);
        ops.set("withdrawWayList", withdrawWayListAll);
        TigBeanUtils.getDatastore().update(transferConfigQuery, ops);
        TigBeanUtils.getRedisService().deleteTransferConfig();
        TigBeanUtils.getRedisService().saveTransferConfig(transferConfigQuery.get());
        return JSONMessage.success(withdrawWayList);
    }
    @RequestMapping("/updateWithdrawWay")
    @ResponseBody
    public JSONMessage updateWithdrawWay(String withdrawKeyDetails,String withdrawWayKeyId,String platformName) {
        Query<WithdrawWayList> query = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).field("withdrawWayKeyId").equal(withdrawWayKeyId);
        UpdateOperations<WithdrawWayList> ops1 = TigBeanUtils.getDatastore().createUpdateOperations(WithdrawWayList.class);
        ops1.set("withdrawWayName",platformName);
        JSONArray json = JSONObject.parseArray(withdrawKeyDetails);
        List<WithdrawWayList.WithdrawKey> withdrawKeyListAll = new ArrayList();
        for(int i = 1; i < json.size(); i++){
            JSONArray jsonOne = JSONObject.parseArray(json.get(i).toString());
            WithdrawWayList.WithdrawKey withdrawKey = new WithdrawWayList.WithdrawKey();
            withdrawKey.setWithdrawName(jsonOne.get(0).toString());
            withdrawKey.setWithdrawStatus(jsonOne.get(1).toString());
            withdrawKey.setWithdrawKeyTime(DateUtil.currentTimeSeconds());
            ObjectId id = (ObjectId) TigBeanUtils.getDatastore().save(withdrawKey).getId();
            withdrawKey.setWithdrawId(id);
            withdrawKeyListAll.add(withdrawKey);
        }
        ops1.set("withdrawKeyDetails",withdrawKeyListAll);
        TigBeanUtils.getDatastore().update(query, ops1);
        List<WithdrawWayList> withdrawWayListAll = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).asList();
        Query<TransferConfig> transferConfigQuery = TigBeanUtils.getDatastore().createQuery(TransferConfig.class).limit(1);
        UpdateOperations<TransferConfig> ops = TigBeanUtils.getDatastore().createUpdateOperations(TransferConfig.class);
        ops.set("withdrawWayList", withdrawWayListAll);
        TigBeanUtils.getDatastore().update(transferConfigQuery, ops);
        TigBeanUtils.getRedisService().deleteTransferConfig();
        TigBeanUtils.getRedisService().saveTransferConfig(transferConfigQuery.get());
        return JSONMessage.success();
    }
    @RequestMapping("/updateWithdrawWayStatus")
    @ResponseBody
    public JSONMessage updateWithdrawWayStatus(String withdrawWayKeyId,int status) {
        Query<WithdrawWayList> query = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).field("withdrawWayKeyId").equal(withdrawWayKeyId);
        UpdateOperations<WithdrawWayList> ops1 = TigBeanUtils.getDatastore().createUpdateOperations(WithdrawWayList.class);
        ops1.set("withdrawWayStatus",status);
        TigBeanUtils.getDatastore().update(query, ops1);
        List<WithdrawWayList> withdrawWayListAll = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).asList();
        Query<TransferConfig> transferConfigQuery = TigBeanUtils.getDatastore().createQuery(TransferConfig.class).limit(1);
        UpdateOperations<TransferConfig> ops = TigBeanUtils.getDatastore().createUpdateOperations(TransferConfig.class);
        ops.set("withdrawWayList", withdrawWayListAll);
        TigBeanUtils.getDatastore().update(transferConfigQuery, ops);
        TigBeanUtils.getRedisService().deleteTransferConfig();
        TigBeanUtils.getRedisService().saveTransferConfig(transferConfigQuery.get());
        return JSONMessage.success();
    }
    @RequestMapping("/deleteWithdrawWay")
    @ResponseBody
    public JSONMessage deleteWithdrawWay(String withdrawWayKeyId) {
        Query<WithdrawWayList> query = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).field("withdrawWayKeyId").equal(withdrawWayKeyId);
        int sort = query.get().getWithdrawWaySort();
        TigBeanUtils.getDatastore().findAndDelete(query);
        Query<TransferConfig> transferConfigQuery = TigBeanUtils.getDatastore().createQuery(TransferConfig.class).limit(1);
        UpdateOperations<TransferConfig> ops = TigBeanUtils.getDatastore().createUpdateOperations(TransferConfig.class);
        List<WithdrawWayList> list = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).asList();
        ops.set("withdrawWayList", list);
        TigBeanUtils.getDatastore().update(transferConfigQuery, ops);
        TigBeanUtils.getRedisService().deleteTransferConfig();
        TigBeanUtils.getRedisService().saveTransferConfig(transferConfigQuery.get());
        //开启一个多线程，当后台删除一个提现方式时，用户对应的绑定的帐号也删除
            ThreadUtil.executeInThread(obj -> {
                Query<WithdrawMethod> queryMethod = TigBeanUtils.getDatastore().createQuery(WithdrawMethod.class).field("otherMethod.type").equal(sort);
                UpdateOperations<WithdrawMethod> ops1 = TigBeanUtils.getDatastore().createUpdateOperations(WithdrawMethod.class);
                List<WithdrawMethod.OtherMethod> otherListOld= new ArrayList<>();
                for(int i = 0; i < queryMethod.asList().size(); i ++){
                    otherListOld = queryMethod.asList().get(i).getOtherMethod();
                    List<WithdrawMethod.OtherMethod> otherListNew = new ArrayList<>();
                    int k = 0;
                    for(int j = 0; j < otherListOld.size(); j++){
                        if(Integer.valueOf(otherListOld.get(j).getType().toString())!=sort){
                            otherListNew.add(k,otherListOld.get(j));
                            k++;
                        }
                    }
                    ops1.set("otherMethod", otherListNew);
                    TigBeanUtils.getDatastore().update(queryMethod, ops1);
                }
            });
        return JSONMessage.success(query.asList());
    }

    @RequestMapping("/deleteWithdrawKey")
    @ResponseBody
    public JSONMessage deleteWithdrawKey(String withdrawId) {
        Query<WithdrawWayList.WithdrawKey> query = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.WithdrawKey.class).field("withdrawId").equal(new ObjectId(withdrawId));
        TigBeanUtils.getDatastore().findAndDelete(query);
        return JSONMessage.success(query.asList());
    }
    @RequestMapping("/getWithdrawWay")
    @ResponseBody
    public JSONMessage getWithdrawWay(String withdrawWayKeyId) {
        Query<WithdrawWayList> query = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).field("withdrawWayKeyId").equal(withdrawWayKeyId);
        return JSONMessage.success(query.get());
    }
    /**
     * 保存三方支付配置
     *
     * @param loginConfig
     * @return
     */
    @RequestMapping("/saveLoginConfig")
    @ResponseBody
    public JSONMessage saveLoginConfig(LoginConfig loginConfig) {
        if (loginConfig == null) {
            return JSONMessage.failure("设置失败");
        }
        boolean status = TigBeanUtils.getAdminManager().saveLoginConfig(loginConfig);
        if (status) {
            return JSONMessage.success("设置成功");
        }
        return JSONMessage.failure("设置失败");
    }

    @RequestMapping("/getLoginConfig")
    @ResponseBody
    public JSONMessage getLoginConfig(Integer type) {
        LoginConfig loginConfig = TigBeanUtils.getAdminManager().getLoginConfig(type);
        return JSONMessage.success(loginConfig);
    }

    /**
     * 文章预览
     */
    @RequestMapping(value = "/articlePreview", method = {RequestMethod.GET})
    public void articlePreview(HttpServletRequest request, HttpServletResponse response) {
        try {
            String path = request.getContextPath() + "/pages/console/articlePreview.html";
            //取得文章ID
            String articleId = request.getParameter("aid");
            //先获取文章阅读数量
            DBCollection dbCollection = dsForRW.getDB().getCollection("article");
            BasicDBObject q = new BasicDBObject();
            q.put("_id", new ObjectId(articleId));
            DBCursor cursor = dbCollection.find(q);
            int pageView = 0;
            while (cursor.hasNext()) {
                BasicDBObject dbObj = (BasicDBObject) cursor.next();
                pageView = (int) dbObj.get("pageView");
            }

            BasicDBObject values = new BasicDBObject();
            values.append("pageView", (pageView + 1));
            dbCollection.update(q, new BasicDBObject("$set", values));

            request.setAttribute("pageView", pageView);

            //response.sendRedirect(path);
            response.setHeader("cache-control", "no-store");
            request.getRequestDispatcher(path).forward(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 推送测试并保存（推送测试目前需求定为以公众号用10000的形式发送）
     */
    @RequestMapping(value = "/pushTestAndSave")
    @ResponseBody
    public JSONMessage pushTestAndSave(@RequestParam String appBrands,
                                       @RequestParam String toUserIds,
                                       @RequestParam(defaultValue = "") String title,
                                       @RequestParam(defaultValue = "") String pushContent,
                                       @RequestParam(defaultValue = "") String firmDispose) {
//        User user = getUser();

        //处理要发送的toUserIds
        List<Integer> toUserIdList = Lists.newArrayList();
        String[] idList = toUserIds.split(",");
        //取得品牌list集合
        List<String> appBrandList = new ArrayList<>();
        for (String s : appBrands.split(",")) {
            appBrandList.add(s);
        }
        //这里的idList要根据appBrands和userId的上线状态的条件筛选
        for (String s : idList) {
            toUserIdList.add(Integer.parseInt(s));
        }
        //查找到这个用户ID集合的在线状态为1的结果
//        List<Integer> findUserIdList = TigBeanUtils.getUserRepository().findUserIdByOnlinestate(toUserIdList);
//        List<User.UserLoginLog> findQuery = TigBeanUtils.getUserManager().getUserIdListByAppBrand(findUserIdList, appBrandList);

//        List<Integer> finalUserIdList = new ArrayList<>();
//        findQuery.forEach(ul -> finalUserIdList.add(ul.getUserId()));

        MessageBean mb = new MessageBean();

        JSONObject jsonObj = new JSONObject();

        jsonObj.put("通知标题", title);
        //对 firmDispose 进行处理
        JSONObject jsonFirmDispose = JSONObject.parseObject(firmDispose);
        for (int i = 0; i < appBrandList.size(); i++) {
            if ("Xiaomi".equals(appBrandList.get(i))) {
                jsonObj.put("厂商配置参数", jsonFirmDispose.get("XIAOMI"));
                JSONObject jsonFirmDispose1 = jsonFirmDispose.getJSONObject("XIAOMI");
                TigBeanUtils.getRedisCRUD().setObject("xiaoMiPushConfig", jsonFirmDispose1.get("appSecret"), 60);
                logger.info("Redis in Xiomi push " + TigBeanUtils.getRedisCRUD().get("xiaoMiPushConfig"));
            } else if ("MEIZU".equals(appBrandList.get(i))) {
                jsonObj.put("厂商配置参数", jsonFirmDispose.get("MEIZU"));
                JSONObject jsonFirmDispose1 = jsonFirmDispose.getJSONObject("MEIZU");
                TigBeanUtils.getRedisCRUD().setObject("meiZuPushConfigAppId", jsonFirmDispose1.get("appId"), 60);
                TigBeanUtils.getRedisCRUD().setObject("meiZuPushConfigAppSecret", jsonFirmDispose1.get("appSecret"), 60);
            } else if ("HUAWEI".equals(appBrandList.get(i))) {
                jsonObj.put("厂商配置参数", jsonFirmDispose.get("HUAWEI"));
                JSONObject jsonFirmDispose1 = jsonFirmDispose.getJSONObject("HUAWEI");
                TigBeanUtils.getRedisCRUD().setObject("huaWeiPushConfigAppId", jsonFirmDispose1.get("appId"), 60);
                TigBeanUtils.getRedisCRUD().setObject("huaWeiPushConfigAppSecret", jsonFirmDispose1.get("appSecret"), 60);
                TigBeanUtils.getRedisCRUD().setObject("huaWeiPushConfigTokenUrl", jsonFirmDispose1.get("tokenUrl"), 60);
                TigBeanUtils.getRedisCRUD().setObject("huaWeiPushConfigApiUrl", jsonFirmDispose1.get("apiUrl"), 60);
                TigBeanUtils.getRedisCRUD().setObject("huaWeiPushConfigIconUrl", jsonFirmDispose1.get("iconUrl"), 60);
            } else if ("oppo".equals(appBrandList.get(i))) {
                jsonObj.put("厂商配置参数", jsonFirmDispose.get("OPPO"));
                JSONObject jsonFirmDispose1 = jsonFirmDispose.getJSONObject("OPPO");
                TigBeanUtils.getRedisCRUD().setObject("oppoPushConfigAppKey", jsonFirmDispose1.get("appKey"), 60);
                TigBeanUtils.getRedisCRUD().setObject("oppoPushConfigMasterSecret", jsonFirmDispose1.get("masterSecret"), 60);
            } else if ("VIVO".equals(appBrandList.get(i))) {
                jsonObj.put("厂商配置参数", jsonFirmDispose.get("VIVO"));
                JSONObject jsonFirmDispose1 = jsonFirmDispose.getJSONObject("VIVO");
                TigBeanUtils.getRedisCRUD().setObject("vivoPushConfigAppId", jsonFirmDispose1.get("appId"), 60);
                TigBeanUtils.getRedisCRUD().setObject("vivoPushConfigAppKey", jsonFirmDispose1.get("appKey"), 60);
                TigBeanUtils.getRedisCRUD().setObject("vivoPushConfigAppSecret", jsonFirmDispose1.get("appSecret"), 60);
            } else {
                jsonObj.put("厂商配置参数", "暂时不支持当前类型");
            }
        }

        jsonObj.put("通知内容", pushContent);
        mb.setContent(jsonObj.toJSONString());
        mb.setFromUserId(10000 + "");
        mb.setFromUserName("客服公众号");
        mb.setTimeSend(DateUtil.currentTimeSeconds());
        mb.setMsgType(0);
        mb.setType(1);
        mb.setMessageId(StringUtil.randomUUID());
//        try {
//            //这个thread群发
//            ThreadUtil.executeInThread(new Callback() {
//                @Override
//                public void execute(Object obj) {
//                    KXMPPServiceImpl.getInstance().send(mb,finalUserIdList);
//                }
//            });
//            //这个thread保存发送信息
//            //..
//        } catch (Exception e) {
//            logger.info(user.getUserId() + "：推送失败");
//            return JSONMessage.failure(e.getMessage());
//        }
        KXMPPServiceImpl.getInstance().send(mb, toUserIdList);
        return JSONMessage.success();
    }

    /**
     * 密保问题
     *
     * @return
     */
    @RequestMapping("/question/list")
    public JSONMessage getSecretQuestionList() {
        List<SecretQuestion> questions = TigBeanUtils.getLocalSpringBeanManager().getSecretQuestionRepository().queryAll();
        return JSONMessage.success(questions);
    }

    @RequestMapping("/question/page")
    public JSONMessage getSecretQuestionPage(@RequestParam(defaultValue = "") String question, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit) {
        PageResult<SecretQuestion> result = TigBeanUtils.getLocalSpringBeanManager().getSecretQuestionRepository().find(question, (page - 1) * limit, limit);
//        List<SecretQuestion> questions = SKBeanUtils.getLocalSpringBeanManager().getSecretQuestionRepository().find(question, (page - 1) * limit, limit);
        return JSONMessage.success(result);
    }

    /**
     * 密保问题增加
     *
     * @param question
     * @return
     */
    @RequestMapping("/question/add")
    public JSONMessage addSecretQuestion(@RequestParam(value = "") String question) {
        try {
            SecretQuestion q = TigBeanUtils.getLocalSpringBeanManager().getSecretQuestionRepository().add(question);
            return JSONMessage.success(q);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 密保问题修改
     *
     * @param question
     * @return
     */
    @RequestMapping("/question/update")
    public JSONMessage updateSecretQuestion(@ModelAttribute SecretQuestion question) {
        try {
            TigBeanUtils.getLocalSpringBeanManager().getSecretQuestionRepository().update(question);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
        return JSONMessage.success();
    }

    /**
     * 密保问题删除
     *
     * @param question
     * @return
     */
    @RequestMapping("/question/delete")
    public JSONMessage deleteSecretQuestion(@ModelAttribute SecretQuestion question) {
        try {
            List<SecretQuestion> questionList = TigBeanUtils.getLocalSpringBeanManager().getSecretQuestionRepository().queryAll();
            if (questionList.size() > 3) {
                TigBeanUtils.getLocalSpringBeanManager().getSecretQuestionRepository().delete(question);
            } else {
                for (int i = 0; i < questionList.size(); i++) {
                    TigBeanUtils.getLocalSpringBeanManager().getSecretQuestionRepository().delete(questionList.get(i));
                }
            }
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
        return JSONMessage.success();
    }

    @RequestMapping("/pushConfig/get")
    public JSONMessage adminPushConfigGet() {
        PushConfig config = TigBeanUtils.getAdminManager().getPushConfig();
        return JSONMessage.success(null, config);
    }

    @RequestMapping("/pushConfig/set")
    public JSONMessage adminPushConfigSet(@ModelAttribute PushConfig pushConfig) {
        Query<PushConfig> query = TigBeanUtils.getDatastore().createQuery(PushConfig.class).field("id").equal(10000);
        TigBeanUtils.getDatastore().delete(query);
        TigBeanUtils.getDatastore().save(pushConfig);
        return JSONMessage.success();
    }

    @RequestMapping("/smsConfig/get")
    public JSONMessage adminSmsConfigGet() {
        SmsConfig config = TigBeanUtils.getAdminManager().getSmsConfig();
        return JSONMessage.success(null, config);
    }

    @RequestMapping("/smsConfig/set")
    public JSONMessage adminSmsConfigSet(@ModelAttribute SmsConfig smsConfig) {
        Query<SmsConfig> query = TigBeanUtils.getDatastore().createQuery(SmsConfig.class).field("id").equal(10000);
        TigBeanUtils.getDatastore().delete(query);
        TigBeanUtils.getDatastore().save(smsConfig);
        return JSONMessage.success();
    }

    @RequestMapping("/updateRedPacketVip")
    public JSONMessage updateRedPacketVip(@RequestParam(defaultValue = "0") Integer userId, @ModelAttribute UserExample example) {
        getUserManager().updateUser(userId, example);
        return JSONMessage.success();
    }

    /**
     * 得到google secret
     *
     * @param userId 用户id
     * @return
     */
    @RequestMapping("/getGoogleQRValue")
    public JSONMessage getGoogleQrValue(@RequestParam(defaultValue = "0") Integer userId, @RequestParam(defaultValue = "") String projectName, @RequestParam(defaultValue = "") String apiUrl) {
        try {
            String domainName = "";
            if (!"".equals(apiUrl)) {
                String[] endPoint = apiUrl.split("/");
                domainName = endPoint[2];
            }
            User user = TigBeanUtils.getUserRepository().getUser(userId);
            String secret = GoogleAuthenticatorUtil.generateSecretKey();
            String qrCode = GoogleAuthenticatorUtil.getQRBarcode(domainName + "--" + URLEncoder.encode(projectName, "UTF-8") + "--" + URLEncoder.encode(user.getNickname(), "UTF-8"), secret);
            logger.info("secret " + secret + " qrCode " + qrCode);
            String key = String.format(KSessionUtil.GET_GOOGLE_VALUE_BY_USERID, String.valueOf(userId));
            KSessionUtil.getRedisCRUD().setWithExpireTime(key, secret, 1800);
            return JSONMessage.success(null, qrCode);
        } catch (IOException e) {
            e.printStackTrace();
            return JSONMessage.failure("获取key失败");
        }
    }

    /**
     * 第一次登陆是可以绑定Google验证码
     *
     * @param userId
     * @param googleValue
     * @return
     */
    @RequestMapping("/bindGoogleQRValueByUserId")
    public JSONMessage bindGoogleQRValueByUserId(@RequestParam(defaultValue = "0") Integer userId, @RequestParam(defaultValue = "") String googleValue) {
        try {
            String key = String.format(KSessionUtil.GET_GOOGLE_VALUE_BY_USERID, String.valueOf(userId));
            if(null==KSessionUtil.getRedisCRUD().get(key)||"".equals(KSessionUtil.getRedisCRUD().get(key))){
                return JSONMessage.failure("绑定google验证器失败或者已过期，请重新获取google验证码");
            }
            String value = KSessionUtil.getRedisCRUD().get(key);
            String keySec = String.format(KSessionUtil.GET_GOOGLE_SEC_BY_USERID, String.valueOf(userId));
            getUserManager().insertGoogleDynamicValue(userId, value);
            KSessionUtil.getRedisCRUD().set(keySec, value);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 解绑验证码
     *
     * @param userId
     * @return
     */
    @RequestMapping("/unbindGoogleQRValueByUserId")
    public JSONMessage unbindGoogleQRValueByUserId(@RequestParam(defaultValue = "0") Integer userId) {
        try {
            getUserManager().deleteGoogleDynamicValue(userId);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 判断该用户是否绑定google验证码
     *
     * @param account
     * @return
     */
    @RequestMapping("/getGoogleQRValueByAccount")
    public JSONMessage getGoogleQRValueIntoUser(@RequestParam(defaultValue = "0") String account) {
        try {
            boolean value = getUserManager().getGoogleDynamicValueByAccount(account);
            return JSONMessage.success(null, value);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    @RequestMapping("/getGoogleQRValueByUserId")
    public JSONMessage getGoogleQRValue(@RequestParam(defaultValue = "0") int userId) {
        try {
            boolean value = getUserManager().getGoogleDynamicValueByUserId(userId);
            return JSONMessage.success(null, value);
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    @RequestMapping("/getAllDeviceByUserId")
    public JSONMessage getAllDeviceByUserId(@RequestParam(defaultValue = "0") Integer userId, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit) {
        Query<PushInfo> query = TigBeanUtils.getDatastore().createQuery(PushInfo.class);
        query.filter("userId", userId);
        List<PushInfo> pushInfoList = query.offset((page-1) * limit).limit(limit).order("-time").asList();
        PageResult<PushInfo> result = new PageResult<>();
        result.setData(pushInfoList);
        result.setCount(query.count());
        return JSONMessage.success(result);
    }
    @RequestMapping("/getDeviceHistoryByUserId")
    public JSONMessage getDeviceHistoryByUserId(@RequestParam(defaultValue = "0") Integer userId, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit) {
        Query<DeviceHistory> query = TigBeanUtils.getDatastore().createQuery(DeviceHistory.class);
        query.filter("userId", userId);
//        Calendar calendar = Calendar.getInstance();
//        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 7);
//        List<DeviceHistory> historyList = query.offset(pageIndex * pageSize).limit(pageSize).filter("updateTime",new BasicBSONObject(MongoOperator.GT, calendar.getTimeInMillis()/1000)).order("-updateTime").asList();
        List<DeviceHistory> historyList = query.offset((page-1) * limit).limit(limit).order("-updateTime").asList();
        PageResult<DeviceHistory> result = new PageResult<>();
        result.setData(historyList);
        result.setCount(query.count());
        return JSONMessage.success(result);
    }
    @RequestMapping(value = "/addOpenMaterial")
    public JSONMessage addOpenMaterial(@ModelAttribute OpenMaterial openMaterial) throws Exception {
        openMaterial.setUpdateTime(DateUtil.currentTimeSeconds());
        ObjectId id = (ObjectId) TigBeanUtils.getDatastore().save(openMaterial).getId();
        openMaterial.setId(id);
        return JSONMessage.success(openMaterial);
    }

    @RequestMapping(value = "/updateOpenMaterial")
    public JSONMessage updateOpenMaterial(@ModelAttribute OpenMaterial openMaterial) throws Exception {
        ObjectId id = openMaterial.getId();
        if (id == null) {
            return null;
        }
        Query<OpenMaterial> query = TigBeanUtils.getDatastore().createQuery(OpenMaterial.class).field("_id").equal(id);
        UpdateOperations<OpenMaterial> ops = TigBeanUtils.getDatastore().createUpdateOperations(OpenMaterial.class);
        if (null != openMaterial.getDoc()) {
            ops.set("doc", openMaterial.getDoc());
        }
        if (null != openMaterial.getDocExplain()) {
            ops.set("docExplain", openMaterial.getDocExplain());
        }
        if (null != openMaterial.getDocName()) {
            ops.set("docName", openMaterial.getDocName());
        }
        if (null != openMaterial.getStyleType()) {
            ops.set("styleType", openMaterial.getStyleType());
        }
        if (null != openMaterial.getUpdateContent()) {
            ops.set("updateContent", openMaterial.getUpdateContent());
        }
        if (!"".equals(String.valueOf(openMaterial.getNumber()))) {
            ops.set("number", openMaterial.getNumber());
        }
        ops.set("updateTime", DateUtil.currentTimeSeconds());
        OpenMaterial bl = TigBeanUtils.getDatastore().findAndModify(query, ops);
        return JSONMessage.success(bl);
    }

    @RequestMapping(value = "/deleteOpenMaterial")
    public JSONMessage deleteOpenMaterial(@RequestParam String id) throws Exception {
        ObjectId idOpen = new ObjectId(id);
        Query<OpenMaterial> q = TigBeanUtils.getDatastore().createQuery(OpenMaterial.class).field("_id").equal(idOpen);
        TigBeanUtils.getDatastore().findAndDelete(q);
        return JSONMessage.success("刪除成功");
    }

    @RequestMapping(value = "/getOpenMaterial")
    public JSONMessage getOpenMaterial() throws Exception {
        Query<OpenMaterial> q = TigBeanUtils.getDatastore().createQuery(OpenMaterial.class).order("number");
        return JSONMessage.success(q.asList());
    }

    /**
     * @param searchParam    搜索条件
     * @param page      页码
     * @param limit     每页条数
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return
     * @Description:（系统充值记录）
     **/
    @RequestMapping("/signInfoCount")
    public JSONMessage signInfoCount(@RequestParam(defaultValue = "") String searchParam, @RequestParam(defaultValue = "0") int page,
                                      @RequestParam(defaultValue = "10") int limit, @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate) {
        try {
            PageResult<UserSign> result = TigBeanUtils.getExtendManager().signInfoCount(searchParam,page, limit, startDate, endDate);
            if (result.getData() != null){
                result.getData().forEach(userSign -> {
                    if(StringUtil.isNullOrEmpty(userSign.getUserPhone())){
                        userSign.setUserPhone("");
                    }
                    if (StringUtil.isNullOrEmpty(userSign.getSignIP())){
                        userSign.setIpAddress("未知地域");
                    }else {
                        JSONObject json = GeoIP.getGeoIpLocation(userSign.getSignIP());
                        if (json != null){
                            userSign.setIpAddress(json.getString("country")+(json.getString("city") == null ? "":json.getString("city")));
                        }else {
                            userSign.setIpAddress("本机/局域网");
                        }
                    }
                });
            }
            return JSONMessage.success(result);
        } catch (ServiceException e) {
            return JSONMessage.failure(e.getErrMessage());
        } catch (ParseException e) {
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * 导出签到红包数据到 excel
     */
    @RequestMapping(value = "/exportSignData")
    public JSONMessage exportSignData( HttpServletResponse response, @RequestParam(defaultValue = "") String searchParam, String startDate, String endDate) {
        try {
            String fileName = "userSignInfoDataTable.xlsx";
            int maxNum = 30000; // 最多导出3万条数据
            List<UserSign> userSignList = TigBeanUtils.getExtendManager().exportSignData(0, maxNum, searchParam, startDate, endDate);
            String name = "签到信息数据";
            List<String> titles = Lists.newArrayList();
            titles.add("用户ID");
            titles.add("用户昵称");
            titles.add("用户账号");
            titles.add("用户手机号");
            titles.add("签到金额(元)");
            titles.add("签到设备");
            titles.add("签到IP");
            titles.add("IP地址");
            titles.add("签到时间");
            List<Map<String, Object>> values = Lists.newArrayList();
            userSignList.forEach(userSign -> {
                Map<String, Object> map = Maps.newHashMap();
                map.put("用户ID", userSign.getUserId());
                map.put("用户昵称", userSign.getUserName());
                map.put("用户账号", userSign.getUserAccount());
                map.put("用户手机号", userSign.getUserPhone());
                if (StringUtil.isNullOrEmpty(userSign.getSignAward())){
                    map.put("签到金额", "0");
                } else {
                    String[] money = userSign.getSignAward().split(",");
                    map.put("签到金额(元)", money[0].replace("money:", ""));
                }
                map.put("签到设备", userSign.getDevice());
                map.put("签到IP", userSign.getSignIP());
                if (StringUtil.isNullOrEmpty(userSign.getSignIP())) {
                    map.put("IP地址", "未知地域");
                }else {
                    JSONObject json = GeoIP.getGeoIpLocation(userSign.getSignIP());
                    if (json != null){
                        map.put("IP地址", json.getString("country")+(json.getString("city") == null ? "":json.getString("city")));
                    }else {
                        map.put("IP地址", "本机/局域网");
                    }
                }

                map.put("签到时间", DateUtil.getDateStr(userSign.getCreateDate(),"yyyy-MM-dd hh:mm:ss"));
                values.add(map);
            });
            Workbook workBook = ExcelUtil.generateWorkbook(name, "xlsx", titles, values);
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(Charset.defaultCharset()), "utf-8"));
            ServletOutputStream out = response.getOutputStream();
            workBook.write(out);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return JSONMessage.success();
    }
    @RequestMapping(value = "/getAllForbidUsers")
    public JSONMessage getAllForbidUsers( @RequestParam(defaultValue = "0") int page,
                                          @RequestParam(defaultValue = "10") int limit,String keyWord){
        Query<User> query = getUserManager().createQuery().field("status").equal(-1).order("-createTime");
        if (!StringUtil.isEmpty(keyWord)&&StringUtil.isNumeric(keyWord)&& keyWord.length() < 11){
            query.or(query.criteria("userId").equal(Integer.parseInt(keyWord)), query.criteria("nickname").contains(keyWord)
                    , query.criteria("phone").contains(keyWord), query.criteria("account").contains(keyWord));
        }else if(!StringUtil.isEmpty(keyWord)){
            query.or(query.criteria("nickname").contains(keyWord)
                    , query.criteria("phone").contains(keyWord), query.criteria("account").contains(keyWord));
        }
        long total = query.count();
        List<User> pageData = query.offset((page-1) * limit).limit(limit).asList();
        PageResult<User> result = new PageResult<>();
        result.setData(pageData);
        result.setCount(total);
        return JSONMessage.success(result);
    }
    @RequestMapping(value = "/getAllForbidDevice")
    public JSONMessage getAllForbidDevice( @RequestParam(defaultValue = "0") int page,
                                           @RequestParam(defaultValue = "10") int limit,String keyWord){

        Query<DeviceHistory> query1 = TigBeanUtils.getDatastore().createQuery(DeviceHistory.class);
        if (!StringUtil.isEmpty(keyWord)&&StringUtil.isNumeric(keyWord)&& keyWord.length() < 11){
            query1.or(query1.criteria("userId").equal(Integer.parseInt(keyWord)), query1.criteria("account").contains(keyWord));
        }else if(!StringUtil.isEmpty(keyWord)) {
//            query1.field(query1.criteria("account").contains(keyWord));
            query1.field("account").contains(keyWord);
        }
        Query<ForbidDetails> query = TigBeanUtils.getDatastore().createQuery(ForbidDetails.class).field("status").equal(0).order("-time");
        if(StringUtils.isNotEmpty(keyWord)){
            if(null!=query1&&query1.asList().size()>0){
                Criteria[] array = new Criteria[query1.asList().size()];//定义一个Criteria类型的数组，为下面or查询做准备
                for(int j = 0; j < query1.asList().size(); j++){
                    String forbidDevice = query1.asList().get(j).getDeviceInfo();
                    array[j] = query.criteria("forbidDevice").equal(forbidDevice);
                }
                query.or(array);//放进query中
            }else{
                if(!StringUtil.isEmpty(keyWord)) {
                    query.or(query.criteria("forbidDevice").contains(keyWord));
                }
            }
        }
        long total = query.count();
        List<ForbidDetails> pageData = query.offset((page-1) * limit).limit(limit).asList();
        PageResult<ForbidDetails> result = new PageResult<>();
        result.setData(pageData);
        result.setCount(total);
        return JSONMessage.success(result);
    }
    @RequestMapping(value = "/getAllForbidIp")
    public JSONMessage getAllForbidIp( @RequestParam(defaultValue = "0") int page,
                                       @RequestParam(defaultValue = "10") int limit,String keyWord){

        Query<DeviceHistory> query1 = TigBeanUtils.getDatastore().createQuery(DeviceHistory.class);
        if (!StringUtil.isEmpty(keyWord)&&StringUtil.isNumeric(keyWord)&& keyWord.length() < 11){
            query1.or(query1.criteria("userId").equal(Integer.parseInt(keyWord)), query1.criteria("account").contains(keyWord));
        }else if(!StringUtil.isEmpty(keyWord)) {
            query1.or(query1.criteria("account").contains(keyWord));
        }
        Query<ForbidDetails> query = TigBeanUtils.getDatastore().createQuery(ForbidDetails.class).field("status").equal(1).order("-time");
       if(StringUtils.isNotEmpty(keyWord)){
           if(null!=query1&&query1.asList().size()>0){
               Criteria[] array = new Criteria[query1.asList().size()];//定义一个Criteria类型的数组，为下面or查询做准备
               for(int j = 0; j < query1.asList().size(); j++){
                   String loginIP = query1.asList().get(j).getLoginIp();
                   array[j] = query.criteria("forbidIp").equal(loginIP);
               }
               query.or(array);//放进query中
           }else{
               if(!StringUtil.isEmpty(keyWord)) {
                   query.or(query.criteria("forbidIp").contains(keyWord));
               }
           }
       }
//        Query<ForbidDetails> query = TigBeanUtils.getDatastore().createQuery(ForbidDetails.class).field("status").equal(1).order("-time");
        long total = query.count();
        List<ForbidDetails> pageData = query.offset((page-1) * limit).limit(limit).asList();
        PageResult<ForbidDetails> result = new PageResult<>();
        result.setData(pageData);
        result.setCount(total);
        return JSONMessage.success(result);
    }
    @RequestMapping(value = "/unforbidUsers")
    public JSONMessage unforbidUsers( String userIds){
        if(!StringUtils.isEmpty(userIds)){
            UpdateOperations<User> operations = TigBeanUtils.getDatastore().createUpdateOperations(User.class);
            String[] values = userIds.split(",");
            if(values.length>0){
                for(int i = 0; i < values.length; i++){
                    operations.set("status", 1);
                    operations.set("forbidUsersTime",0);
                    TigBeanUtils.getUserManager().updateAttributeByOps(Integer.parseInt(values[i]), operations);
                    //维护redis中的数据
                    KSessionUtil.removeAccessToken(Integer.parseInt(values[i]));
                    KSessionUtil.deleteUserByUserId(Integer.parseInt(values[i]));
                }
            }
        }
        return JSONMessage.success();
     }
    @RequestMapping(value = "/unforbidDevices")
    public JSONMessage unforbidDevices( String devices){
        if(!StringUtils.isEmpty(devices)){
            String[] values = devices.split(",");
            if(values.length>0){
                for(int i = 0; i < values.length; i++){
                    Query<ForbidDetails> query = TigBeanUtils.getDatastore().createQuery(ForbidDetails.class);
                    query.field("forbidDevice").equal(values[i]);
                    query.field("status").equal(0);
                    TigBeanUtils.getDatastore().delete(query);
                    String forbidDevice = TigBeanUtils.getRedisCRUD().get("forbid:device");
                    if(forbidDevice.split(",").length>1){
                        if(forbidDevice.contains(values[i]+",")){
                            forbidDevice = forbidDevice.replaceAll(values[i]+",","");
                            TigBeanUtils.getRedisCRUD().set("forbid:device",forbidDevice);
                        }else if(forbidDevice.contains(","+values[i])){
                            forbidDevice = forbidDevice.replaceAll(","+values[i],"");
                            TigBeanUtils.getRedisCRUD().set("forbid:device",forbidDevice);
                        }
                    }else if(forbidDevice.split(",")[0].equals(values[i])){
                        TigBeanUtils.getRedisCRUD().delete("forbid:device");
                    }
                }
            }
        }
        return JSONMessage.success();
    }
    @RequestMapping(value = "/unforbidIps")
    public JSONMessage unforbidIps( String ips){
        if(!StringUtils.isEmpty(ips)){
            String[] values = ips.split(",");
            if(values.length>0){
                for(int i = 0; i < values.length; i++){
                    Query<ForbidDetails> query = TigBeanUtils.getDatastore().createQuery(ForbidDetails.class);
                    query.field("forbidIp").equal(values[i]);
                    query.field("status").equal(1);
                    TigBeanUtils.getDatastore().delete(query);
                    String forbidIp = TigBeanUtils.getRedisCRUD().get("forbid:ip");
                    if(forbidIp.split(",").length>1){
                        if(forbidIp.contains(values[i]+",")){
                            forbidIp = forbidIp.replaceAll(values[i]+",","");
                            TigBeanUtils.getRedisCRUD().set("forbid:ip",forbidIp);
                        }else if(forbidIp.contains(","+values[i])){
                            forbidIp = forbidIp.replaceAll(","+values[i],"");
                            TigBeanUtils.getRedisCRUD().set("forbid:ip",forbidIp);
                        }
                    }else if(forbidIp.split(",")[0].equals(values[i])){
                        TigBeanUtils.getRedisCRUD().delete("forbid:ip");
                    }
                }
            }
        }
        return JSONMessage.success();
    }
    @RequestMapping(value = "/getAllForbidUsersPopup")
    public JSONMessage getAllForbidUsersPopup( String status, String deviceMsg,@RequestParam(defaultValue = "0") int page,
    @RequestParam(defaultValue = "10") int limit){
        Query<DeviceHistory> query1 = null;
        if("0".equals(status)){
            query1 = TigBeanUtils.getDatastore().createQuery(DeviceHistory.class).field("deviceInfo").equal(deviceMsg);
        }else if("1".equals(status)){
            query1 = TigBeanUtils.getDatastore().createQuery(DeviceHistory.class).field("loginIp").equal(deviceMsg);
        }
        Query<User> query = TigBeanUtils.getDatastore().createQuery(User.class).field("status").equal(1).order("-showLastLoginTime");
        if(null!=query1&&query1.asList().size()>0){
            Criteria[] array = new Criteria[query1.asList().size()];//定义一个Criteria类型的数组，为下面or查询做准备
            for(int j = 0; j < query1.asList().size(); j++){
                int userId = query1.asList().get(j).getUserId();
                array[j] = query.criteria("userId").equal(userId);
            }
            query.or(array);//放进query中
        }
        long total = query.count();
        List<User> pageData = query.offset((page-1) * limit).limit(limit).asList();
        pageData.forEach(userInfo -> {
            Query<UserLoginLog> loginLog = TigBeanUtils.getDatastore().createQuery(UserLoginLog.class).field("userId").equal(userInfo.getUserId());
            if (null != loginLog.get()) {
                userInfo.setLoginLog(loginLog.get().getLoginLog());
            }
        });
        PageResult<User> result = new PageResult<>();
        result.setData(pageData);
        result.setCount(total);
        return JSONMessage.success(result);
    }
    @RequestMapping(value = "/doUnforbid")
    public JSONMessage doUnforbid( int userId, int status){
        Query<DeviceHistory> query1 = TigBeanUtils.getDatastore().createQuery(DeviceHistory.class).field("userId").equal(userId).order("-updateTime");
            if(null!=query1&&null!=query1.get()){
//                for(int i = 0; i < values.length; i++){
                    Query<ForbidDetails> query = TigBeanUtils.getDatastore().createQuery(ForbidDetails.class);
                    if(status==0){
                        query.field("forbidDevice").equal(query1.get().getDeviceInfo());
                        query.field("status").equal(0);
                        TigBeanUtils.getDatastore().delete(query);
                        String forbidDevice = TigBeanUtils.getRedisCRUD().get("forbid:device");
                        if(forbidDevice.split(",").length>1){
                            if(forbidDevice.contains(query1.get().getDeviceInfo()+",")){
                                forbidDevice = forbidDevice.replaceAll(query1.get().getDeviceInfo()+",","");
                                TigBeanUtils.getRedisCRUD().set("forbid:device",forbidDevice);
                            }else if(forbidDevice.contains(","+query1.get().getDeviceInfo())){
                                forbidDevice = forbidDevice.replaceAll(","+query1.get().getDeviceInfo(),"");
                                TigBeanUtils.getRedisCRUD().set("forbid:device",forbidDevice);
                            }
                        }else if(forbidDevice.split(",")[0].equals(query1.get().getDeviceInfo())){
                            TigBeanUtils.getRedisCRUD().delete("forbid:device");
                        }
                    }else if(status==1){
                        query.field("forbidIp").equal(query1.get().getLoginIp());
                        query.field("status").equal(1);
                        TigBeanUtils.getDatastore().delete(query);
                        String forbidIp = TigBeanUtils.getRedisCRUD().get("forbid:ip");
                        if(forbidIp.split(",").length>1){
                            if(forbidIp.contains(query1.get().getLoginIp()+",")){
                                forbidIp = forbidIp.replaceAll(query1.get().getLoginIp()+",","");
                                TigBeanUtils.getRedisCRUD().set("forbid:ip",forbidIp);
                            }else if(forbidIp.contains(","+query1.get().getLoginIp())){
                                forbidIp = forbidIp.replaceAll(","+query1.get().getLoginIp(),"");
                                TigBeanUtils.getRedisCRUD().set("forbid:ip",forbidIp);
                            }
                        }else if(forbidIp.split(",")[0].equals(query1.get().getLoginIp())){
                            TigBeanUtils.getRedisCRUD().delete("forbid:ip");
                        }
                    }

//                }
            }
        return JSONMessage.success();
    }
    @RequestMapping(value = "/getWithdrawalMsg")
    public JSONMessage getWithdrawalMsg(){
        if(null!=TigBeanUtils.getRedisService().getWithdrawal()&&"1".equals(TigBeanUtils.getRedisService().getWithdrawal())){
            TigBeanUtils.getRedisService().delWithdrawal();
            return JSONMessage.success();
        }else{
            return JSONMessage.failure("没有提现");
        }
    }

    /**
     * vip收费配置列表
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/vipConfigList")
    public JSONMessage vipConfigList(
            @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "15") int limit) {

        PageResult<VipPaymentConfig> data = TigBeanUtils.getVipPaymentConfigManagerImpl().selectList1(page, limit);
        logger.info("roomPaymentConfigList resut :{}",data.getData());
        return JSONMessage.success(data);
    }

    @RequestMapping(value = "/getRoomPaymentConfig")
    public JSONMessage getRoomPaymentConfig(@RequestParam(defaultValue = "") String id) {
        VipPaymentConfig room = TigBeanUtils.getVipPaymentConfigManagerImpl().get(new ObjectId(id));
        return JSONMessage.success(null, room);
    }

    /**
     * 删除VIP收费配置
     * @param id
     * @return
     */
    @RequestMapping(value = "/deleteVipConfig")
    public JSONMessage deleteRoomPaymentConfig(@RequestParam(defaultValue = "") String id) {
        WriteResult room = TigBeanUtils.getVipPaymentConfigManagerImpl().deleteById(new ObjectId(id));
        return JSONMessage.success(null, room);
    }

    /**
     * @author:
     * @dateTime: 2019/7/3 19:08
     * @param: [nodesInfo]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 修改VIP收费配置
     **/
    @RequestMapping(value = "/updateVipConfig")
    public JSONMessage updateVipConfig(@ModelAttribute VipPaymentConfig vipPaymentConfig) {
        try {
            TigBeanUtils.getVipPaymentConfigManagerImpl().update(vipPaymentConfig);
            return JSONMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONMessage.failure(e.getMessage());
        }
    }

    /**
     * @author:
     * @dateTime: 2019/7/3 19:08
     * @param: [nodesInfo]
     * @return: cn.xyz.commons.vo.JSONMessage
     * @description: 添加VIP收费配置
     **/
    @RequestMapping(value = "/addVipConfig")
    public JSONMessage addVipConfig(@ModelAttribute VipPaymentConfig vipPaymentConfig) {
        try {
            TigBeanUtils.getVipPaymentConfigManagerImpl().add(vipPaymentConfig);
            return JSONMessage.success();
        } catch (Exception e) {
            return JSONMessage.failure(e.getMessage());
        }
    }
}