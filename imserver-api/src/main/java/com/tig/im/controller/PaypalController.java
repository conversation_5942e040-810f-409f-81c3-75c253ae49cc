package com.tig.im.controller;

import cn.tig.commons.constants.KConstants;
import cn.tig.commons.utils.CollectionUtil;
import cn.tig.commons.utils.DateUtil;
import cn.tig.commons.utils.ReqUtil;
import cn.tig.commons.utils.StringUtil;
import cn.tig.commons.vo.JSONMessage;
import cn.tig.im.service.impl.ConsumeRecordManagerImpl;
import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.ConsumeRecord;
import cn.tig.service.shop.BuyOrderService;
import cn.tig.service.shop.GoodsService;
import cn.tig.service.shop.UserAddressService;
import cn.tig.vo.PayPalVo;
import cn.tig.vo.shop.BuyOrder;
import cn.tig.vo.shop.Goods;
import cn.tig.vo.shop.UserAddress;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paypal.orders.*;
import com.tig.im.utils.PayPalUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/paypal")
public class PaypalController extends AbstractController {

    @Autowired
    PayPalUtils payPalUtils;
    @Autowired
    private ConsumeRecordManagerImpl consumeRecordManager;
    @Autowired
    UserAddressService userAddressService;
    @Autowired
    GoodsService goodsService;
    @Autowired
    BuyOrderService buyOrderService;
    //成功url
    public static final String returnUrl = "http://**************:8092/paypal/payPalNotify";
    //失败url
    public static final String cancelUrl = "http://**************:8092/paypal/cancel";
    //充值成功url
    public static final String rechargeReturnUrl = "http://**************:8092/paypal/rechargeSuccessNotify";
    /**
     * 贝宝充值
     * @param payPalVo 请求对象
     * @return 返回结果
     */
    @RequestMapping("/createRechargeOrder")
    public JSONMessage createRechargeOrder(PayPalVo payPalVo) {

        log.info("createRechargeOrder param ={}", JSONObject.toJSONString(payPalVo));

        Integer userId = ReqUtil.getUserId();

        String orderNo = StringUtil.getOutTradeNo();
        Map<String, Object> result = makeRechargeOrderParam(orderNo,payPalVo.getPrice());


        if(CollectionUtil.isEmpty(result)){
            log.info("createOrder, makeRechargeOrderParam ={}",JSON.toJSONString(result));
            return JSONMessage.failure("paypal创建订单失败");
        }
        ConsumeRecord entity = new ConsumeRecord();
        entity.setUserId(userId);
        entity.setTime(DateUtil.currentTimeSeconds());
        entity.setType(KConstants.ConsumeType.PAYPAL_RECHARGE);
        entity.setDesc("paypal充值");
        entity.setOperationAmount(Double.valueOf(payPalVo.getPrice()));
        entity.setStatus(KConstants.OrderStatus.CREATE);
        entity.setTradeNo(orderNo);
        entity.setPayType(6);
        entity.setMoney(new Double(payPalVo.getPrice()));
        entity.setExtendId((String) result.get("orderId"));
        consumeRecordManager.saveConsumeRecord(entity);
        log.info("createOrder result ={}", JSONObject.toJSONString(result));

        return JSONMessage.success("下单成功",result);
    }

    private Map<String,Object> makeRechargeOrderParam(String orderNo, String price){

        OrderRequest orderRequest = new OrderRequest();
        orderRequest.checkoutPaymentIntent("CAPTURE");
//        orderRequest.checkoutPaymentIntent("AUTHORIZE");

        PurchaseUnitRequest purchaseUnitRequest = new PurchaseUnitRequest();
        AmountWithBreakdown amountWithBreakdown = new AmountWithBreakdown();
        amountWithBreakdown.currencyCode("USD");
        amountWithBreakdown.value(price);

        AddressPortable addressPortable = new AddressPortable();
        addressPortable.addressLine1("2211 N First Street");
        addressPortable.addressLine2("17.3.160");
        addressPortable.adminArea1("CA");
        addressPortable.adminArea2("San Jose");
        addressPortable.countryCode("US");
        addressPortable.postalCode("36003");

        ShippingDetail shippingDetail = new ShippingDetail();
        shippingDetail.addressPortable(addressPortable);

        purchaseUnitRequest.shippingDetail(shippingDetail);

        purchaseUnitRequest.customId(orderNo);
        purchaseUnitRequest.referenceId(orderNo);
        purchaseUnitRequest.amountWithBreakdown(amountWithBreakdown);
        orderRequest.purchaseUnits(Arrays.asList(purchaseUnitRequest));
        /**
         * 用户认证完成后，paypal会通过returnUrl通知get方式我们
         * 在我们的returnUrl后拼接参数，token及orderId
         * 示例：https://www.example.com/?token=8GR24834939276359&PayerID=V97XV4A6DQ52N
         */
        ApplicationContext applicationContext = new ApplicationContext().brandName("EXAMPLE INC").landingPage("BILLING")
                .cancelUrl(cancelUrl).returnUrl(rechargeReturnUrl).userAction("CONTINUE")
                .shippingPreference("SET_PROVIDED_ADDRESS");
        orderRequest.applicationContext(applicationContext);

        return payPalUtils.createOrder(orderRequest);
    }

    /**
     * 贝宝支付
     * @return 返回结果
     */
    @RequestMapping("/rechargeSuccessNotify")
    public JSONMessage rechargeSuccessNotify(@RequestParam("token") String token, @RequestParam("PayerID") String PayerID) {
        log.info("rechargeSuccessNotify param ,token={},PayerID={}",token, PayerID);

        ConsumeRecord entity = consumeRecordManager.getConsumeRecordByExtendId(token);
        log.info("rechargeSuccessNotify param ,entity={}",JSON.toJSONString(entity));
        //进行二次验证
        Boolean pay = payPalUtils.verifyOrderInfo(token);

        //如果成功则执行后面的逻辑
        if (Boolean.TRUE.equals(pay)) {
            if (entity != null){
                entity.setStatus(1);
                consumeRecordManager.saveConsumeRecord(entity);

                //充值钱加到用户余额里面
                TigBeanUtils.getUserManager().rechargeUserMoeny(entity.getUserId(), entity.getOperationAmount(), KConstants.MOENY_ADD);
            }
            log.info("rechargeSuccessNotify param fail,token={},PayerID={},return = {}",token, PayerID, JSON.toJSONString(entity));
            return JSONMessage.success("paypal充值成功");
        }
        entity.setStatus(3);
        consumeRecordManager.saveConsumeRecord(entity);
        //失败返回提示
        log.info("rechargeSuccessNotify param fail,token={},PayerID={},return = {}",token, PayerID, JSON.toJSONString(entity));
        return JSONMessage.failure("paypal充值失败");
    }

    /**
     * 贝宝支付
     * @param payPalVo 请求对象
     * @return 返回结果
     */
    @RequestMapping("/createOrder")
    public JSONMessage createOrder(PayPalVo payPalVo) {

        log.info("createOrder param ={}", JSONObject.toJSONString(payPalVo));

        Integer userId = ReqUtil.getUserId();

        String orderNo = StringUtil.getOutTradeNo();
        Map<String, Object> result = makeOrderParam(orderNo,payPalVo.getPrice());


        if(CollectionUtil.isEmpty(result)){
            log.info("createOrder, makeRechargeOrderParam ={}",JSON.toJSONString(result));
            return JSONMessage.failure("paypal创建充值订单失败");
        }
        ConsumeRecord entity = new ConsumeRecord();
        entity.setUserId(userId);
        entity.setTime(DateUtil.currentTimeSeconds());
        entity.setType(KConstants.ConsumeType.PAYPAL_PAY);
        entity.setDesc("paypal支付");
        entity.setOperationAmount(Double.valueOf(payPalVo.getPrice()));
        entity.setStatus(KConstants.OrderStatus.CREATE);
        entity.setTradeNo(orderNo);
        entity.setPayType(6);
        entity.setMoney(new Double(payPalVo.getPrice()));
        entity.setExtendId((String) result.get("orderId"));
        entity.setRemark(payPalVo.getGoodsId()+":"+payPalVo.getAddressId()+":"+payPalVo.getNum());
        consumeRecordManager.saveConsumeRecord(entity);
        log.info("createOrder result ={}", JSONObject.toJSONString(result));

        return JSONMessage.success("下单成功",result);
    }


    private Map<String,Object> makeOrderParam(String orderNo, String price){

        OrderRequest orderRequest = new OrderRequest();
        orderRequest.checkoutPaymentIntent("CAPTURE");
//        orderRequest.checkoutPaymentIntent("AUTHORIZE");

       PurchaseUnitRequest purchaseUnitRequest = new PurchaseUnitRequest();
       AmountWithBreakdown amountWithBreakdown = new AmountWithBreakdown();
       amountWithBreakdown.currencyCode("USD");
       amountWithBreakdown.value(price);

       AddressPortable addressPortable = new AddressPortable();
       addressPortable.addressLine1("2211 N First Street");
       addressPortable.addressLine2("17.3.160");
       addressPortable.adminArea1("CA");
       addressPortable.adminArea2("San Jose");
       addressPortable.countryCode("US");
       addressPortable.postalCode("36003");

       ShippingDetail shippingDetail = new ShippingDetail();
       shippingDetail.addressPortable(addressPortable);

       purchaseUnitRequest.shippingDetail(shippingDetail);

       purchaseUnitRequest.customId(orderNo);
       purchaseUnitRequest.referenceId(orderNo);
       purchaseUnitRequest.amountWithBreakdown(amountWithBreakdown);
       orderRequest.purchaseUnits(Arrays.asList(purchaseUnitRequest));
        /**
         * 用户认证完成后，paypal会通过returnUrl通知get方式我们
         * 在我们的returnUrl后拼接参数，token及orderId
         * 示例：https://www.example.com/?token=8GR24834939276359&PayerID=V97XV4A6DQ52N
         */
        ApplicationContext applicationContext = new ApplicationContext().brandName("EXAMPLE INC").landingPage("BILLING")
                .cancelUrl(cancelUrl).returnUrl(returnUrl).userAction("CONTINUE")
                .shippingPreference("SET_PROVIDED_ADDRESS");
        orderRequest.applicationContext(applicationContext);

        return payPalUtils.createOrder(orderRequest);
    }

    /**
     * 贝宝支付
     * @return 返回结果
     */
    @RequestMapping("/payPalNotify")
    public JSONMessage payPalNotify(@RequestParam("token") String token, @RequestParam("PayerID") String PayerID) {
        log.info("payPalNotify param ,token={},PayerID={}",token, PayerID);

        ConsumeRecord entity = consumeRecordManager.getConsumeRecordByExtendId(token);
        //进行二次验证
        Boolean pay = payPalUtils.verifyOrderInfo(token);

        //如果成功则执行后面的逻辑
        if (Boolean.TRUE.equals(pay)) {
            if (entity != null){
                entity.setStatus(1);
                consumeRecordManager.saveConsumeRecord(entity);

                String remark = entity.getRemark();
                if(StringUtils.isNotBlank(remark)){
                    String[] array = remark.split(":");
                    String goodIds = array[0];
                    String addressId = array[1];
                    String num = array[2];

                    //保存发货状态记录
                    Goods goods = goodsService.get(new ObjectId(goodIds));
                    UserAddress userAddress = userAddressService.get(new ObjectId(addressId));
                    BuyOrder buyOrder = new BuyOrder();
                    buyOrder.setUserId(entity.getUserId());
                    buyOrder.setUserName(TigBeanUtils.getUserManager().getNickName(entity.getUserId()));
                    buyOrder.setUserAddressId(userAddress.getId());
                    buyOrder.setUserAddress(userAddress.getName() + " " +
                            userAddress.getPhone() + " " +
                            userAddress.getArea() + " " +
                            userAddress.getFullAddress());
                    buyOrder.setUserPhone(userAddress.getPhone());
                    buyOrder.setUserAddressObject(userAddress);
                    buyOrder.setGoodsId(goods.getId());
                    buyOrder.setGoods(goods);
                    buyOrder.setGoodsName(goods.getGoodsName());
                    buyOrder.setGoodsNum(goods.getGoodsNum());
                    buyOrder.setGoodsMoney(goods.getGoodsMoney());
                    buyOrder.setBuyNum(Integer.valueOf(num));
                    buyOrder.setGoodsImg(goods.getMainImg());

                    buyOrder.setState(0);
                    buyOrder.setCreateTime(DateUtil.currentTimeMilliSeconds());
                    buyOrder.setUpdateTime(DateUtil.currentTimeMilliSeconds());
                    buyOrder.setGoodsMoneySum(BigDecimal.valueOf(entity.getOperationAmount()));
                    buyOrder.setPayState(1);
                    buyOrder.setPayType(6);
                    buyOrder.setOrderNo(token);
                    buyOrderService.save(buyOrder);
                    log.info("token={},保存发货状态成功",token);
                }
            }

            log.info("payPalNotify param fail,token={},PayerID={},return = {}",token, PayerID, JSON.toJSONString(entity));
            return JSONMessage.success("paypal扣款成功");
        }
        entity.setStatus(3);
        consumeRecordManager.saveConsumeRecord(entity);
        //失败返回提示
        log.info("payPalNotify param fail,token={},PayerID={},return = {}",token, PayerID, JSON.toJSONString(entity));
        return JSONMessage.failure("paypal扣款失败");
    }

    /**
     * 贝宝支付
     * @return 返回结果
     */
    @RequestMapping("/cancel")
    public JSONMessage cancel(@RequestParam("token") String token, @RequestParam("PayerID") String PayerID) {
        log.info("cancel param ,token={},PayerID={}",token, PayerID);
        ConsumeRecord entity = consumeRecordManager.getConsumeRecordByExtendId(token);
        //如果成功则执行后面的逻辑
        if (entity != null) {
            entity.setStatus(3);
            consumeRecordManager.saveConsumeRecord(entity);
        }
        log.info("cancel param ,token={},PayerID={},return = {}",token, PayerID,JSONObject.toJSONString(entity));
        return JSONMessage.failure("paypal扣款失败");
    }
}