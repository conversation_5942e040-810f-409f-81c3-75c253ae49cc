package com.tig.im.controller;

import java.util.List;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.tig.commons.constants.KConstants.Result;
import cn.tig.commons.utils.StringUtil;
import cn.tig.commons.vo.JSONMessage;
import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.CommonText;
/**
 * <AUTHOR> @Package: com.tig.im.controller.CustomerController
 * @FileName: CustomerController.java
 * @Date 2019/7/3 15:37
 * @Description 客服访问接口控制类
 **/
@RestController
@RequestMapping("/CustomerService")
public class CustomerController extends AbstractController {
	private static Logger logger = LoggerFactory.getLogger(CustomerController.class);
	
	/**
	 * <AUTHOR> @ModuleName: customerRegister
	 * @Date 2019/7/3 15:39
	 * @Param [companyId, departmentId]
	 * @Return cn.tig.commons.vo.JSONMessage
	 * @Description 客服模块-客户注册
	 **/
	@RequestMapping(value = "/register")
	public JSONMessage customerRegister(@RequestParam String companyId,@RequestParam String departmentId) {
		String requestIp = getRequestIp(); //获取注册用户ip地址
		//String macAddress = getMACAddress(requestIp);   //根据ip获取用户mac地址
		Object data = TigBeanUtils.getCustomerManager().registerUser(companyId,departmentId,requestIp);
		return JSONMessage.success(null, data);
	}
	
	/**
	 * <AUTHOR> @ModuleName: commonTextAdd
	 * @Date 2019/7/3 15:38
	 * @Param [commonText]
	 * @Return cn.tig.commons.vo.JSONMessage
	 * @Description 创建常用语
	 **/
	@RequestMapping("/commonText/add")
	public JSONMessage commonTextAdd(@Valid CommonText commonText){
		JSONMessage jsonMessage = Result.ParamsAuthFail;
		try {
			if (!StringUtil.isEmpty(commonText.toString())) {
				commonText = TigBeanUtils.getCustomerManager().commonTextAdd(commonText);
				return JSONMessage.success("", commonText);
			}else{
				return jsonMessage;
			}
		} catch (Exception e) {
			logger.error("添加关键字失败！");
			return jsonMessage;
		}
	}

	/**
	 * <AUTHOR> @ModuleName: deleteCommonText
	 * @Date 2019/7/3 15:38
	 * @Param [commonTextId]
	 * @Return cn.tig.commons.vo.JSONMessage
	 * @Description 删除常用语
	 **/
	@RequestMapping("/commonText/delete")
	public JSONMessage deleteCommonText(@RequestParam String commonTextId){
		JSONMessage jsonMessage = Result.ParamsAuthFail;
		try {
			if (!StringUtil.isEmpty(commonTextId)) {
				TigBeanUtils.getCustomerManager().deleteCommonTest(commonTextId);
				return JSONMessage.success("", null);
			}else{
				return jsonMessage;
			}
		} catch (Exception e) {
			logger.error("删除关键字失败！");
			return jsonMessage;
		}
	}
	
	/**
	 * <AUTHOR> @ModuleName: commonTextGet
	 * @Date 2019/7/3 15:38
	 * @Param [companyId, pageIndex, pageSize]
	 * @Return cn.tig.commons.vo.JSONMessage
	 * @Description 查询常用语
	 **/
	@RequestMapping("/commonText/get")
	public JSONMessage commonTextGet(@RequestParam String companyId,@RequestParam(defaultValue = "0") int pageIndex,@RequestParam(defaultValue = "10") int pageSize){
		JSONMessage jsonMessage = Result.ParamsAuthFail;
		try {
			List<CommonText> commonTextList = TigBeanUtils.getCustomerManager().CommonTextGet(companyId, pageIndex, pageSize);
			if (!StringUtil.isEmpty(commonTextList.toString()) && commonTextList.size()>0) {
				return JSONMessage.success("", commonTextList);
			}else{
				return JSONMessage.failure(null);
			}
		} catch (Exception e) {
			logger.error("查询常用语失败！");
			return jsonMessage;
		}
	}

	/**
	 * <AUTHOR> @ModuleName: commonTextModify
	 * @Date 2019/7/3 15:38
	 * @Param [commonText]
	 * @Return cn.tig.commons.vo.JSONMessage
	 * @Description 修改常用语
	 **/
	@RequestMapping("/commonText/modify")
	public JSONMessage commonTextModify(CommonText commonText){
		JSONMessage jsonMessage = Result.ParamsAuthFail;
		try {
			if (!StringUtil.isEmpty(commonText.toString())) {
				TigBeanUtils.getCustomerManager().commonTextModify(commonText);
				return JSONMessage.success("", null);
			}else{
				return jsonMessage;
			}
		} catch (Exception e) {
			logger.error("修改常用语失败！");
			return jsonMessage;
		}
	}
	
	/**
	 * <AUTHOR> @ModuleName: getUser
	 * @Date 2019/7/3 15:39
	 * @Param [customerId]
	 * @Return cn.tig.commons.vo.JSONMessage
	 * @Description 此接口用于将查找customer客户表中的数据，然后封装成 user 返回
	 **/
	@RequestMapping(value = "/getUser")
	public JSONMessage getUser(@RequestParam String customerId) {
		Object data = TigBeanUtils.getCustomerManager().getUser(customerId);
		return JSONMessage.success(null, data);
	}
}
