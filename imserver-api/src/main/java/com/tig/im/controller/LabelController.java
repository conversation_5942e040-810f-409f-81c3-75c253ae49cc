package com.tig.im.controller;


import org.bson.types.ObjectId;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.tig.commons.vo.JSONMessage;
import cn.tig.im.lable.Label;
import cn.tig.im.utils.TigBeanUtils;

/**
 * @author:
 * @package: com.tig.im.controller.LabelController
 * @fileName: LabelController.java
 * @dateTime: 2019/7/3 16:13
 * @description: 标签请求控制类
 **/
@RestController
@RequestMapping("/label")
public class LabelController extends AbstractController {

    /**
     * @author:
     * @moduleName: create
     * @dateTime: 2019/7/3 16:14
     * @param: [userId, logo, name]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 创建群标识码
     **/
    @RequestMapping("/create")
    public JSONMessage create(@RequestParam Integer userId,@RequestParam(defaultValue="") String logo,@RequestParam String name) {
        if(null == userId)
            return JSONMessage.failure("userId is null");
        Label newLabel ;
        if(null == name && null == logo) {
            newLabel =  TigBeanUtils.getLabelManager().createLabel(userId);
        } else {
            Object data = TigBeanUtils.getLabelManager().queryLabelByName(name);
            if(data != null)
                return JSONMessage.failure("群标识码名已经使用");
            newLabel =  TigBeanUtils.getLabelManager().createLabelByParams(userId,logo,name);
        }
        addLabel(userId,newLabel.getCode(),0);
        return JSONMessage.success();
    }

    /**
     * @author:
     * @moduleName: getLabel
     * @dateTime: 2019/7/3 16:14
     * @param: [labelId]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 获取群标识码
     **/
    @RequestMapping("/getlabel")
    public JSONMessage getLabel(@RequestParam ObjectId labelId) {
        if(null == labelId)
            return JSONMessage.failure("id is null");
       Object data =  TigBeanUtils.getLabelManager().getLabel(labelId);
        return JSONMessage.success(null,data);
    }
    /**
     * @author:
     * @moduleName: getLabelList
     * @dateTime: 2019/7/3 16:15
     * @param: [userId]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 获取群标识码列表
     **/
    @RequestMapping("/getlabels")
    public JSONMessage getLabelList(@RequestParam Integer userId) {
        if(null == userId)
            return JSONMessage.failure("userId is null");
        Object data =TigBeanUtils.getLabelManager().getLabelList(userId);
        return JSONMessage.success(null,data);
    }

    /**
     * @author:
     * @moduleName: updateLabel
     * @dateTime: 2019/7/3 16:15
     * @param: [label]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 更新群标识码
     **/
    @RequestMapping("/update")
    public JSONMessage updateLabel(@ModelAttribute Label label) {
        if(null == label.getCode())
            return JSONMessage.failure("label code  is null");

        Object data =TigBeanUtils.getLabelManager().updateLabel(label);
        if(null == data)
            return JSONMessage.failure("label can not find");
        else
             return JSONMessage.success(null,data);
    }

    /**
     * @author:
     * @moduleName: saveLabel
     * @dateTime: 2019/7/3 16:15
     * @param: [id, name, logo]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 更新群标识码
     **/
    @RequestMapping("/save")
    public JSONMessage saveLabel(@RequestParam ObjectId id,String name, String logo) {
       if(null == id)
         return JSONMessage.failure("id is null");

        Object data =TigBeanUtils.getLabelManager().saveLabel(id,name,logo);
        return JSONMessage.success(null,data);
    }

    /**
     * @author:
     * @moduleName: addLabel
     * @dateTime: 2019/7/3 16:15
     * @param: [userId, code, date]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 添加群标识码
     **/
    @RequestMapping("/add")
    public JSONMessage addLabel(@RequestParam Integer userId,@RequestParam(defaultValue="") String code,@RequestParam long date) {
        if(null == userId)
            return JSONMessage.failure("user Id is null");
        if(null == code)
            return JSONMessage.failure("label code is null");
       Label label =TigBeanUtils.getLabelManager().getLabelByCode(code);
        if(label == null)//无效群标识码
              return JSONMessage.failure("无效群标识码");
       Object object =TigBeanUtils.getUserLabelManager().queryUserLabel(userId,label.getId().toString());
       if(object != null)
           return JSONMessage.failure("群标识码已添加");
        Object data =TigBeanUtils.getUserLabelManager().addLabel(userId,label.getId().toString(),label.getName(),label.getLogo(),code,date);
        if(data == null)
            return JSONMessage.failure(" 群标识码添加失败");
        else
            return JSONMessage.success(null,data);
    }

    /**
     * @author:
     * @moduleName: addByName
     * @dateTime: 2019/7/3 16:16
     * @param: [userId, name, date]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 通过群标识码名称添加
     **/
    @RequestMapping("/addByName")
    public JSONMessage addByName(@RequestParam Integer userId,
    		@RequestParam(defaultValue="") String name,@RequestParam(defaultValue="0") long date) {
        if(null == userId)
            return JSONMessage.failure("user Id is null");
        if(null == name)
            return JSONMessage.failure("群标识码名不能为空");
        Label label =TigBeanUtils.getLabelManager().queryLabelByName(name);
        if(label == null)
            return JSONMessage.failure("群标识码不存在");
        Object object =TigBeanUtils.getUserLabelManager().queryUserLabel(userId,label.getId().toString());
        if(object != null)
            return JSONMessage.failure("群标识码已添加");
        Object data =TigBeanUtils.getUserLabelManager().addLabel(userId,label.getId().toString(),label.getName(),label.getLogo(),label.getCode(),date);
        if(data == null)
            return JSONMessage.failure(" 群标识码添加失败");
        else
            return JSONMessage.success(null,data);
    }



    /**
     * @author:
     * @moduleName: getUserLabels
     * @dateTime: 2019/7/3 16:16
     * @param: [userId]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 获取群标识码列表
     **/
    @RequestMapping("/getUserLabels")
    public JSONMessage getUserLabels(@RequestParam Integer userId) {
        if (null == userId)
            return JSONMessage.failure("user id is null");
        Object data =TigBeanUtils.getUserLabelManager().getUserLabels(userId);
        return JSONMessage.success(null,data);
    }

    /**
     * @author:
     * @moduleName: openLabel
     * @dateTime: 2019/7/3 16:16
     * @param: [userId, code]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 查收群标识码
     **/
    @RequestMapping("/open")
    public JSONMessage  openLabel(Integer userId,String code){
       Object data = TigBeanUtils.getUserLabelManager().queryUserLabelByCode(userId,code);
        return  JSONMessage.success(null,data);
    }

    /**
     * @author:
     * @moduleName: queryLabelByName
     * @dateTime: 2019/7/3 16:16
     * @param: [userId, name]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 查询群标识码名是否已用
     **/
    @RequestMapping("/queryLabelByName")
    public JSONMessage  queryLabelByName(Integer userId,String name){
        Object data = TigBeanUtils.getLabelManager().queryLabelByName(name);
        if(data == null)
            return JSONMessage.success();
        else
            return JSONMessage.failure("群标识码名已添加");
    }

    /**
     * @author:
     * @moduleName: isBuyLabel
     * @dateTime: 2019/7/3 16:16
     * @param: [userId, name]
     * @return: cn.tig.commons.vo.JSONMessage
     * @description: 判断是否已经添加
     **/
    @RequestMapping("/isBuyLabel")
    public JSONMessage  isBuyLabel(Integer userId,String name){
        Label label =TigBeanUtils.getLabelManager().queryLabelByName(name);
        if(label == null)//无效群标识码
            return JSONMessage.failure("群标识码不存在");
        Object object =TigBeanUtils.getUserLabelManager().queryUserLabel(userId,label.getId().toString());
        if(object != null)
            return JSONMessage.failure("群标识码已添加");
        return JSONMessage.success();
    }
}
