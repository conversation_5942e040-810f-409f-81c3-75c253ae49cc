package com.tig.im.controller;

import cn.tig.commons.support.mongo.MongoOperator;
import cn.tig.commons.utils.IpAccessObjectUtil;
import cn.tig.commons.utils.RSAUtils;
import cn.tig.commons.utils.StringUtil;
import cn.tig.commons.vo.JSONMessage;
import cn.tig.im.model.PageResult;
import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.Config;
import cn.tig.im.vo.RedReceive;
import cn.tig.im.vo.Room;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @Description //TODO 邀请业务请求控制类
 * @Date 2019/7/3 15:08
 * @Param
 * @Return
 */
@RestController
@RequestMapping("/api")
public class ApiController extends AbstractController {

    /**
     * 通证使用API
     * @param code 通证code
     * @return
     */
    @RequestMapping(value = "/invite/usedPassCard")
    public JSONMessage usedPassCard(HttpServletRequest request, @RequestParam String code,@RequestParam String useId,@RequestParam String useName){
        Config config = TigBeanUtils.getAdminManager().getConfig();
        String ip = IpAccessObjectUtil.getIpAddress(request);
        if (config.getIpWhiteList() == null){
            return JSONMessage.failure("缺少IP白名单数据,请在后台管理设置");
        }
        if (config.getIpWhiteList().contains(ip)){
            Map<String,String> resultMap = TigBeanUtils.getUserInviteManager().usedPassCard(code,useId,useName);
            if (resultMap.get("status").equals("true")){
                return JSONMessage.success(resultMap.get("msg"));
            }
            return JSONMessage.failure(resultMap.get("msg"));
        }
        return JSONMessage.failure("无权限调用");
    }

    @RequestMapping(value = "/findRoomListByUser")
    public JSONMessage findRoomListByUser(HttpServletRequest request, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "15") int limit,@RequestParam(defaultValue = "1")String userId) {
        if (StringUtil.isNullOrEmpty(userId)){
            return JSONMessage.failure("缺少用户ID");
        }
        if (!StringUtil.isNumeric(userId)){
            return JSONMessage.failure("用户ID错误");
        }
        Config config = TigBeanUtils.getAdminManager().getConfig();
        String ip = IpAccessObjectUtil.getIpAddress(request);
        logger.info(ip);
        if (config.getIpWhiteList() == null){
            return JSONMessage.failure("缺少IP白名单数据,请在后台管理设置");
        }
        if (config.getIpWhiteList().contains(ip)){
            Query<Room.Member> memberQuery = TigBeanUtils.getImRoomDatastore().createQuery(Room.Member.class).field("userId").equal(Integer.valueOf(userId)).order("-createTime");
            //分页
            List<Room.Member> pageData = memberQuery.asList(TigBeanUtils.getRoomManagerImplForIM().pageFindOption(page, limit, 1));
            List<ObjectId> roomIds = new ArrayList<>();
            Map<ObjectId,String> memberInfo = new HashMap<>();
            for (Room.Member member : pageData) {
                logger.error("群成员昵称："+member.getNickname());
                roomIds.add(member.getRoomId());
                memberInfo.put(member.getRoomId(),member.getNickname());
            }
            Query<Room> roomQuery = TigBeanUtils.getImRoomDatastore().createQuery(Room.class).field("id").in(roomIds);
            List<Room> roomList = roomQuery.asList();
            List<Map<String,String>> resultList = new ArrayList<>();
            for (Room room : roomList) {
                Map<String,String> roomMap = new HashMap<>();
                roomMap.put("roomId", String.valueOf(room.getId()));
                roomMap.put("roomJId", String.valueOf(room.getJid()));
                roomMap.put("roomName",room.getName());
                String userName = memberInfo.get(room.getId());
                roomMap.put("userId",userId);
                roomMap.put("nickName",userName);
                resultList.add(roomMap);
            }
            PageResult result = new PageResult<>();
            result.setData(resultList);
            result.setCount(memberQuery.count());
            long pageCount = result.getCount() / limit + (result.getCount() % limit > 0 ? 1 : 0);
            result.setTotal(pageCount);
            return JSONMessage.success(result);
        }
        return JSONMessage.failure("无权限调用");
    }

    @RequestMapping(value = "/findRoomList")
    public JSONMessage findRoomList(HttpServletRequest request, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "15") int limit) {
        Config config = TigBeanUtils.getAdminManager().getConfig();
        String ip = IpAccessObjectUtil.getIpAddress(request);
        logger.info(ip);
        if (config.getIpWhiteList() == null){
            return JSONMessage.failure("缺少IP白名单数据,请在后台管理设置");
        }
        if (config.getIpWhiteList().contains(ip)){
            PageResult result = new PageResult<>();
            Query<Room> query = TigBeanUtils.getImRoomDatastore().createQuery(Room.class);
            List<Room> list = query.order("-userSize,-createTime").asList(TigBeanUtils.getRoomManagerImplForIM().pageFindOption(page, limit, 1));
            List<Map<String,String>> roomList = new ArrayList<>();
            for (Room room : list) {
                Map<String,String> roomMap = new HashMap<>();
                roomMap.put("roomId", String.valueOf(room.getId()));
                roomMap.put("roomJId", String.valueOf(room.getJid()));
                roomMap.put("roomName",room.getName());
                roomList.add(roomMap);
            }
            result.setData(roomList);
            result.setCount(query.count());
            long pageCount = result.getCount() / limit + (result.getCount() % limit > 0 ? 1 : 0);
            result.setTotal(pageCount);
            return JSONMessage.success(result);
        }
        return JSONMessage.failure("无权限调用");
    }

    @RequestMapping(value = "/findRoomManager")
    public JSONMessage findRoomManager(HttpServletRequest request,@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit, @RequestParam(defaultValue = "") String roomId) {
        Config config = TigBeanUtils.getAdminManager().getConfig();
        String ip = IpAccessObjectUtil.getIpAddress(request);
        logger.info(ip);
        if (config.getIpWhiteList() == null){
            return JSONMessage.failure("缺少IP白名单数据,请在后台管理设置");
        }
        if (config.getIpWhiteList().contains(ip)){
            try {
                if (!StringUtil.isEmpty(roomId)) {
                    PageResult<Room.Member> result = TigBeanUtils.getRoomManagerImplForIM().getMemberListByPage(new ObjectId(roomId), page, limit,"");
                    List<Map<String,String>> memberList = new ArrayList<>();
                    for (Room.Member member:result.getData()) {
                        Map<String,String> memberMap = new HashMap<>();
                        memberMap.put("userId", String.valueOf(member.getUserId()));
                        memberMap.put("nickName",member.getNickname());
                        memberList.add(memberMap);
                    }
                    PageResult resultData = new PageResult<>();
                    resultData.setData(memberList);
                    resultData.setCount(result.getCount());
                    long pageCount = result.getCount() / limit + (result.getCount() % limit > 0 ? 1 : 0);
                    resultData.setTotal(pageCount);
                    return JSONMessage.success(resultData);
                }
                PageResult<Room.Member> result = null;
                return JSONMessage.success(result);
            } catch (Exception e) {
                return JSONMessage.failure(e.getMessage());
            }
        }
        return JSONMessage.failure("无权限调用");
    }

    @RequestMapping("/sendRoomMsg")
    public JSONMessage sendRoomMsg(HttpServletRequest request,@RequestParam(defaultValue = "") String roomJId, @RequestParam(defaultValue = "1") int userId, @RequestParam(defaultValue = "1") int type, @RequestParam(defaultValue = "") String content) {
        if (StringUtil.isNullOrEmpty(roomJId)){
            return JSONMessage.failure("缺少群组JId");
        }
        if (StringUtil.isNullOrEmpty(content)){
            return JSONMessage.failure("缺少发送内容");
        }
        Config config = TigBeanUtils.getAdminManager().getConfig();
        String ip = IpAccessObjectUtil.getIpAddress(request);
        logger.info(ip);
        if (config.getIpWhiteList() == null){
            return JSONMessage.failure("缺少IP白名单数据,请在后台管理设置");
        }
        if (config.getIpWhiteList().contains(ip)){
            try {
                Query<Room> query = TigBeanUtils.getImRoomDatastore().createQuery(Room.class).field("jid").equal(roomJId);
                if (query.asList().size() == 0){
                    return JSONMessage.failure("群组不存在");
                }
                Room room = query.get();
                Query<Room.Member> queryMember = TigBeanUtils.getImRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(room.getId()).field("userId").equal(userId);
                if (queryMember.asList().size() == 0){
                    return JSONMessage.failure("该群成员不在此群");
                }
                String[] split = {roomJId};
                TigBeanUtils.getRoomManagerImplForIM().sendMsgToRooms(split, userId, type, content);
                return JSONMessage.success();
            } catch (Exception e) {
                return JSONMessage.failure(e.getMessage());
            }
        }
        return JSONMessage.failure("无权限调用");

    }

    /**
     * 群聊记录
     *
     * @param startTime
     * @param roomJId
     * @param page
     * @param limit
     * @param request
     * @return
     */
    @RequestMapping(value = "/collectGroupChatMsg")
    public JSONMessage collectGroupChatMsg(@RequestParam(defaultValue = "0") long startTime,@RequestParam(defaultValue = "") String roomJId,
                                           @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit,
                                           @RequestParam(defaultValue = "") String keyWord,HttpServletRequest request) {
        Config config = TigBeanUtils.getAdminManager().getConfig();
        String ip = IpAccessObjectUtil.getIpAddress(request);
        logger.info(ip);
        if (config.getIpWhiteList() == null){
            return JSONMessage.failure("缺少IP白名单数据,请在后台管理设置");
        }
        if (config.getIpWhiteList().contains(ip)){
            DBCollection dbCollection = TigBeanUtils.getImRoomDatastore().getDB().getCollection("mucmsg_" + roomJId);
            BasicDBObject query = new BasicDBObject();
            if (0 != startTime){
                query.put("ts", new BasicDBObject("$gte", startTime));
            }
            if (!StringUtil.isEmpty(keyWord)){
                query.put("content", new BasicDBObject(MongoOperator.REGEX, keyWord));
            }
            long total = dbCollection.count(query);
            java.util.List<DBObject> pageData = Lists.newArrayList();
            PageResult<DBObject> result = new PageResult<>();
            DBCursor cursor = dbCollection.find(query).sort(new BasicDBObject("ts", -1)).skip((page - 1) * limit).limit(limit);
            while (cursor.hasNext()) {
                BasicDBObject dbObj = (BasicDBObject) cursor.next();
                try {
                    Map<?, ?> params = JSON.parseObject(dbObj.getString("body").replace("&quot;", "\""), Map.class);
                    dbObj.put("fromUserName", params.get("fromUserName"));
                    dbObj.put("content", params.get("content"));
                } catch (Exception e) {
                    dbObj.put("content", "--");
                }
                dbObj.put("body",null);
                dbObj.put("message",null);
                dbObj.put("event_type",null);
                dbObj.put("public_event",null);
                pageData.add(dbObj);
            }
            result.setData(pageData);
            result.setCount(total);
            long pageCount = result.getCount() / limit + (result.getCount() % limit > 0 ? 1 : 0);
            result.setTotal(pageCount);
            return JSONMessage.success(result);
        }
        return JSONMessage.failure("无权限调用");
    }

    /**
     * 群聊红包记录
     * @param page
     * @param limit
     * @param request
     * @return
     */
    @RequestMapping(value = "/getGroupAllRedReceive")
    public JSONMessage getGroupAllRedReceive(@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit,
                                             @RequestParam(defaultValue = "")String roomJId, HttpServletRequest request) {
        Config config = TigBeanUtils.getAdminManager().getConfig();
        String ip = IpAccessObjectUtil.getIpAddress(request);
        logger.info(ip);
        if (config.getIpWhiteList() == null){
            return JSONMessage.failure("缺少IP白名单数据,请在后台管理设置");
        }
        if (config.getIpWhiteList().contains(ip)){
            DBCollection dbCollection = TigBeanUtils.getRedPacketManager().getCollection();
            BasicDBObject query = new BasicDBObject();
            List paramList = new ArrayList<>();
            paramList .add(null);
            paramList .add("");
            query.put("roomJid",new BasicDBObject("$nin", paramList));
            if (!StringUtil.isNullOrEmpty(roomJId)){
                query.put("roomJid",roomJId);
            }
            long total = dbCollection.count(query);
            List<Map> pageData = Lists.newArrayList();
            PageResult<Map> result = new PageResult<>();
            DBCursor cursor = dbCollection.find(query).sort(new BasicDBObject("sendTime", -1)).skip((page - 1) * limit).limit(limit);
            while (cursor.hasNext()) {
                BasicDBObject dbObj = (BasicDBObject) cursor.next();
                Map<String,Object> dataMap = new HashMap<>();
                dataMap.put("redReceiveId",dbObj.getString("_id"));//红包ID
                dataMap.put("sendUserId",dbObj.getInt("userId")); //发送人用户ID
                dataMap.put("sendNickname",dbObj.getString("userName"));//发送用户昵称
                dataMap.put("greetings",dbObj.getString("greetings"));//发送祝福语
                dataMap.put("sendTime",dbObj.getLong("sendTime"));//发送时间
                dataMap.put("type",dbObj.getInt("type"));//红包类型 1：普通红包  2：拼手气红包  3:口令红包
                dataMap.put("count",dbObj.getInt("count"));//红包个数
                dataMap.put("receiveCount",dbObj.getInt("receiveCount"));//已领取个数
                dataMap.put("money",dbObj.getDouble("money"));//红包金额
                dataMap.put("overMoney",dbObj.getDouble("over"));//红包剩余金额
                dataMap.put("outTime",dbObj.getLong("outTime"));//超时时间
                dataMap.put("status",dbObj.getInt("status"));//1:发出   2：已领完 -1：已退款 3:未领完退款
                dataMap.put("userIds",dbObj.get("userIds") == null?new ArrayList<>():dbObj.get("userIds"));//领取该红包的 userId数组
                dataMap.put("roomJId",dbObj.getString("roomJid"));//群组房间ID
                pageData.add(dataMap);
            }
            result.setData(pageData);
            result.setCount(total);
            long pageCount = result.getCount() / limit + (result.getCount() % limit > 0 ? 1 : 0);
            result.setTotal(pageCount);
            return JSONMessage.success(result);
        }
        return JSONMessage.failure("无权限调用");
    }

    /**
     * 群聊红包记录
     * @param page
     * @param limit
     * @param request
     * @return
     */
    @RequestMapping(value = "/getRedReceiveInfo")
    public JSONMessage getRedReceiveInfo(@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int limit,
                                         @RequestParam(defaultValue = "")String redReceiveId, HttpServletRequest request) {
        Config config = TigBeanUtils.getAdminManager().getConfig();
        String ip = IpAccessObjectUtil.getIpAddress(request);
        logger.info(ip);
        if (config.getIpWhiteList() == null){
            return JSONMessage.failure("缺少IP白名单数据,请在后台管理设置");
        }
        if (config.getIpWhiteList().contains(ip)){
            PageResult<RedReceive> result = TigBeanUtils.getRedPacketManager().receiveWater(redReceiveId,page,limit);
            return JSONMessage.success(result);
        }
        return JSONMessage.failure("无权限调用");
    }

    @RequestMapping(value = "/serverCount")
    public JSONMessage serverCount(@RequestParam String apiKey,@RequestParam(defaultValue = "2") short timeUnit,
                                   @RequestParam(defaultValue = "") String startDate, @RequestParam(defaultValue = "") String endDate){
        RSAUtils.PRIVATE_KEY = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKrvGWCem8MnQFlTGbHe2i8bjBGRA7VE5Li6eaRE65trfzeDm3eqsYNBz1B3dP1IXQrpEn6KM7PmqI2ux9M289Cg8GF465gsSYsoJ3NVmgJ560Kz7iHuvPN7aLNLZG6IGlXOr5Um146n+I4ad9kDnN++S6CispzNyjMZvsKcDPQXAgMBAAECgYA1y/5+rCx1pRrZgg/qLSsZ/F9+/9bc7AvYH8g87PwB9Dkbra32YaBwoPuwZYhKVsYBsw0BClnvWr7dqI53aSoekRLqGcLbD2NaYqpCy3k1XHYCv7rFGRO9XWEuWx3C7REid/uEAy1ozTBfLcJzKlJ3fiqcsYswZu86bLe69iTYgQJBAOJbZlHnEtYFGcTuEappel7oxFt4zE0dE3jl0IXqfOuLHOGmlImS35HUhkalwp1ZswkHzRtVL5d036bHXXCOzHcCQQDBUaV6zvFKEvkxuA9okIMSDnF3Mfq02hq6K4/Ixr7pol3foYPlEE69qCi3YMieinGiP8tV0dKQLCcKlWXXeB1hAkBwvdluvcrx8kRtg64k1LhwgTsb3ETVu7+xHbYInUxu/TD7az7cbt0QA2yFz5XpTX4Q52PPU1SK+VlT9kSiBV3tAkAS0cv2RKcguvAfcvetCkR4QYeyPt9VM9ZeQVdiCFcVj4Tr/GFGr2u68t/j2jwtkvS1AtjcpeyFtIaodRrQ+xSBAkAOUZ8Rpmhj4R0CD63F4XSDovKqAmudmbJVGbDBEykOFKzz6dCwCutemuHF+YZFJmUojpMyBkzFuDubR41rNuDQ";
        try {
            String serverName = RSAUtils.decryptPrivateWithBase64(apiKey).trim();
            if (StringUtil.isNullOrEmpty(apiKey)){
                return JSONMessage.failure("无权限调用");
            }
            String systemServerName = TigBeanUtils.getLocalSpringBeanManager().getAppConfig().getApiKey().trim();
            if (serverName.equals(systemServerName)){
                Object registerCount = TigBeanUtils.getUserManager().getUserRegisterCount(startDate.trim(), endDate.trim(), timeUnit);
                Object chatMsgCount = TigBeanUtils.getTigaseManager().getChatMsgCount(startDate.trim(), endDate.trim(), timeUnit);
                Object addFriendsCount = TigBeanUtils.getFriendsManager().getAddFriendsCount(startDate.trim(), endDate.trim(), timeUnit);
                Object userOnlineStatusCount = TigBeanUtils.getUserManager().userOnlineStatusCount(startDate.trim(), endDate.trim(), timeUnit);
                Object addRoomsCount = TigBeanUtils.getRoomManager().addRoomsCount(startDate.trim(), endDate.trim(), timeUnit);
                long userNum = TigBeanUtils.getUserManager().count();
                long roomNum = TigBeanUtils.getRoomManagerImplForIM().countRoomNum();
                long msgNum = TigBeanUtils.getTigaseManager().getMsgCountNum();
                long friendsNum = TigBeanUtils.getFriendsManager().count();
                Map<String, Object> dataMap = new HashMap();
                dataMap.put("userNum", userNum);
                dataMap.put("roomNum", roomNum);
                dataMap.put("msgNum", msgNum);
                dataMap.put("friendsNum", friendsNum);
                dataMap.put("registerCount", registerCount);
                dataMap.put("chatMsgCount", chatMsgCount);
                dataMap.put("addFriendsCount", addFriendsCount);
                dataMap.put("userOnlineStatusCount", userOnlineStatusCount);
                dataMap.put("addRoomsCount", addRoomsCount);
                return JSONMessage.success("调用成功", dataMap);
            }
            return JSONMessage.failure("无权限调用");
        }catch (Exception e) {
            e.printStackTrace();
        }
        return JSONMessage.failure("无权限调用");
    }

//    @RequestMapping(value = "/serverUserApi")
//    public JSONMessage serverUserApi() throws UnsupportedEncodingException {
//        Config config = TigBeanUtils.getSystemConfig();
//        String proName = config.getProjectName();
//        if (StringUtil.isNullOrEmpty(proName)){
//            proName = "IM";
//        }else {
//            proName = PinYin4jUtils.checkStr(PinYin4jUtils.getHeadByString(proName));
//        }
//        QueryResults<User> userList = TigBeanUtils.getUserManager().find();
//        List<User> userList1 = userList.asList();
//        for (User user:userList1){
//            if (!StringUtil.isNullOrEmpty(user.getTelephone()) && !StringUtil.isNullOrEmpty(user.getAccount())){
//                if (user.getAccount().length() == 6 && StringUtil.isNumeric(user.getAccount())){
//                    user.setAccount(proName+"_"+ FriendlyId.toFriendlyId(UUID.nameUUIDFromBytes(user.getTelephone().getBytes("utf-8"))));
//                    user.setUserAccountKey(DigestUtils.md5Hex(user.getAccount()));
//                    TigBeanUtils.getUserManager().update(user.getUserId(),user);
//                    KSessionUtil.deleteUserByUserId(user.getUserId());
//                    // 维护redis 用户相关数据
//                    TigBeanUtils.getUserRepository().updateUserRelevantInfo(user.getUserId());
//                }
//            }
//        }
//        QueryResults<SdkLoginInfo> sdkLoginInfos = TigBeanUtils.getDatastore().createQuery(SdkLoginInfo.class);
//        List<SdkLoginInfo> infoList = sdkLoginInfos.asList();
//        for (SdkLoginInfo info:infoList){
//            User user = TigBeanUtils.getUserManager().getUser(info.getUserId());
//            if (!StringUtil.isNullOrEmpty(user.getAccount())){
//                if (user.getAccount().length() == 6 && StringUtil.isNumeric(user.getAccount())){
//                    String tel = user.getUserId()+StringUtil.randomCode();
//                    user.setAccount(proName+"_"+ FriendlyId.toFriendlyId(UUID.nameUUIDFromBytes(tel.getBytes("utf-8"))));
//                    user.setUserAccountKey(DigestUtils.md5Hex(user.getAccount()));
//                    TigBeanUtils.getUserManager().update(user.getUserId(),user);
//                    KSessionUtil.deleteUserByUserId(user.getUserId());
//                    // 维护redis 用户相关数据
//                    TigBeanUtils.getUserRepository().updateUserRelevantInfo(user.getUserId());
//                }
//            }
//        }
//        return JSONMessage.success("处理出完成");
//    }
//    int thread = 0;
//    @RequestMapping("/sendRoomMsgApi")
//    public JSONMessage sendRoomMsgApi1(@RequestParam("userId")Integer userId) throws InterruptedException {
//        thread ++;
//        int abc = thread;
//        String roomJId = "a4a72414f8634aa08c893b9526e37920";
//        for (int i = 0; i < 10000; i++) {
//            String content = String.valueOf("测试线程"+getThreadName(abc)+"--->>>"+(i+1));
//            String[] split = {roomJId};
//            TigBeanUtils.getRoomManagerImplForIM().sendMsgToRooms(split, userId, 1, content);
//            sleep(1000);
//        }
//        return JSONMessage.failure("无权限调用");
//    }
//
//    static char[] cnArr = new char [] {'壹','贰','叁','肆','伍','陆','柒','捌','玖'};
//    static char[] chArr = new char [] {'十','百','千','万','亿'};
//
//    private String getThreadName(int thread){
//        String si = String.valueOf(thread);
//        String sd = "";
//        if (si.length() == 1) {
//            if (thread == 0) {
//                return sd;
//            }
//            sd += cnArr[thread - 1];
//            return sd;
//        } else if (si.length() == 2) {
//            if (si.substring(0, 1).equals("1")) {
//                sd += "十";
//                if (thread % 10 == 0) {
//                    return sd;
//                }
//            }else{
//                sd += (cnArr[thread / 10 - 1] + "拾");
//            }
//            sd += getThreadName(thread % 10);
//        } else if (si.length() == 3) {
//            sd += (cnArr[thread / 100 - 1] + "佰");
//            if (String.valueOf(thread % 100).length() < 2) {
//                if (thread % 100 == 0) {
//                    return sd;
//                }
//                sd += "零";
//            }
//            sd += getThreadName(thread % 100);
//        } else if (si.length() == 4) {
//            sd += (cnArr[thread / 1000 - 1] + "仟");
//            if (String.valueOf(thread % 1000).length() < 3) {
//                if (thread % 1000 == 0) {
//                    return sd;
//                }
//                sd += "零";
//            }
//            sd += getThreadName(thread % 1000);
//        } else if (si.length() == 5) {
//            sd += (cnArr[thread / 10000 - 1] + "卐");
//            if (String.valueOf(thread % 10000).length() < 4) {
//                if (thread % 10000 == 0) {
//                    return sd;
//                }
//                sd += "零";
//            }
//            sd += getThreadName(thread % 10000);
//        }
//        return sd;
//
//    }
//

}
