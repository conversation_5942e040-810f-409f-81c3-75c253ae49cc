spring.config.name=application-local
spring.config.location=classpath:application-local.properties


#imapi\u9879\u76EE\u7AEF\u53E3
server.port=8092

##\u5F00\u542Fhttps
server.openHttps=false
#http.port=8092
#server.ssl.key-store=classpath:imapi.p12
#server.ssl.key-store-password=4bYKAC15
#server.ssl.key-store-type=PKCS12


#\u8BBE\u7F6EUTF-8\u683C\u5F0F
#\u89E3\u51B3\u7A0B\u5E8F\u8BFB\u914D\u7F6E\u6587\u4EF6\u4E71\u7801\u95EE\u9898
spring.messages.encoding=UTF-8
#IP\u5730\u5740\u89E3\u6790
geoip.isOpen=false
geoip.path=D:\\GeoIP\\GeoLite2-City.mmdb

###tomcat \u8BF7\u6C42\u8BBE\u7F6E
server.max-http-header-size=1048576
server.tomcat.max-connections=3000
server.tomcat.max-http-post-size=1048576
server.tomcat.max-threads=1000

#\u7528\u6237\u9080\u8BF7\u7801\u901A\u8BC1\u529F\u80FD
im.inviteConfig.passCardIsOpen=false
#API \u8BBF\u95EE\u767D\u540D\u5355\u8BBE\u7F6E
im.apiAccessConfig.AccessIsSet=true
##OBS key\u8BBE\u7F6E\u516C\u94A5\u79C1\u94A5
##RSA\u52A0\u5BC6\u79C1\u94A5
im.obsConfig.RSAPrivateKey=
##RSA\u52A0\u5BC6\u516C\u94A5
im.obsConfig.RSAPublicKey=
#\u7528\u6237\u901A\u8054\u652F\u4ED8\u529F\u80FD\u662F\u5426\u5F00\u542F
im.sysPayConfig.TlPayIsOpen=false


#Mongodb Properties\uFF08\u6570\u636E\u5E93\u914D\u7F6E\uFF09
#im.mongoConfig.uri=mongodb://*************:27017
im.mongoConfig.uri=mongodb://127.0.0.1:27017
#im.mongoConfig.uri=mongodb://rwuser:jMv6sApz^1jhcySS@**************:8635
im.mongoConfig.dbName=imapi
im.mongoConfig.roomDbName=imRoom
im.mongoConfig.username=
im.mongoConfig.password=
im.mongoConfig.connectTimeout=20000
im.mongoConfig.socketTimeout=20000
im.mongoConfig.maxWaitTime=20000



##APP Properties
im.appConfig.buildTime=2019-06-12
im.appConfig.uploadDomain=http://*************:8088
im.appConfig.downloadDomain=http://*************:8088
im.appConfig.apiKey=123456
im.appConfig.openTask=1
im.appConfig.distance=20
im.appConfig.qqzengPath=
im.appConfig.apns=/opt/spring-boot-imapi/voippush.p12
im.appConfig.languages[0].key=zh
im.appConfig.languages[0].name=\u4E2D\u6587 
im.appConfig.languages[0].value=\u7B80\u4F53\u4E2D\u6587
im.appConfig.languages[1].key=en
im.appConfig.languages[1].name=\u82F1\u6587
im.appConfig.languages[1].value=English
im.appConfig.languages[2].key=big5
im.appConfig.languages[2].name=\u7E41\u4F53
im.appConfig.languages[2].value=\u7E41\u4F53\u4E2D\u6587
im.appConfig.wxChatUrl=
## SMS Properties(\u77ED\u4FE1\u914D\u7F6E)

##\u5929\u5929\u56FD\u9645\u77ED\u4FE1
im.smsConfig.openSMS=1
#im.smsConfig.host=m.isms360.com
#im.smsConfig.port=8085
#im.smsConfig.api=/mt/MT3.ashx
#im.smsConfig.username=username
#im.smsConfig.password=password
#im.smsConfig.templateChineseSMS=\u3010Tig IM\u3011,\u60A8\u7684\u9A8C\u8BC1\u7801\u4E3A:
#im.smsConfig.templateEnglishSMS=[Tig  IM], Your verification code is:
## \u963F\u91CC\u4E91\u77ED\u4FE1\u670D\u52A1
#im.smsConfig.product=Dysmsapi
#im.smsConfig.domain=dysmsapi.aliyuncs.com
#im.smsConfig.accesskeyid=
#im.smsConfig.accesskeysecret=
#im.smsConfig.signname=\u89C5\u4FE1
#im.smsConfig.chinase_templetecode=
#im.smsConfig.english_templetecode=
##\u4E91\u7247\u77ED\u4FE1\u670D\u52A1
im.smsConfig.appKey=
im.smsConfig.signname=
im.smsConfig.englishSignName=
im.smsConfig.templateChineseSMS=\u60A8\u7684\u9A8C\u8BC1\u7801\u662F#code#\u3002\u5982\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5FFD\u7565\u672C\u77ED\u4FE1\u3002
im.smsConfig.templateEnglishSMS=Your verification code is #code#.If you do not operate, please ignore this short message.


#mq \u914D\u7F6E
im.mqConfig.nameAddr=http://***********:9876
##mq \u6D88\u8D39\u6700\u5C0F\u7A0B\u6570\u91CF \u9ED8\u8BA4 cup \u6570\u91CF
#im.mqConfig.threadMin=4
##mq \u6D88\u8D39\u6700\u5927\u7A0B\u6570\u91CF \u9ED8\u8BA4 cup \u6570\u91CF*2
#im.mqConfig.threadMax=8
##mq \u6279\u91CF\u6D88\u8D39\u6570\u91CF  \u9ED8\u8BA4 20
#im.mqConfig.batchMaxSize=30

#XMPP Properties\uFF08XMPP\u4E3B\u673A\u548C\u7AEF\u53E3\u4EE5\u53CA\u63A8\u9001\u7528\u6237\u914D\u7F6E\uFF09
im.xmppConfig.host=***********
im.xmppConfig.serverName=***********
im.xmppConfig.port=5222
im.xmppConfig.username=998
im.xmppConfig.password=998
im.xmppConfig.dbUri=mongodb://127.0.0.1:27017
im.xmppConfig.dbName=tigase
im.xmppConfig.dbUsername=
im.xmppConfig.dbPassword=
#Tigase XMPP\u5BC6\u7801\u52A0\u5BC6\u72B6\u6001
im.xmppConfig.passwordEncryptStatus=true
#\u7CFB\u7EDF\u53D1\u9001\u6D88\u606F\u7528\u6237\u4FE1\u606F\u914D\u7F6E
im.xmppConfig.serverConfig[0].host=*************
im.xmppConfig.serverConfig[0].serverName=*************
im.xmppConfig.serverConfig[0].port=5222
im.xmppConfig.serverConfig[0].tigaseUserInfo=configAdmin100001

#Redis Properties\uFF08\u7F13\u5B58\u914D\u7F6E\uFF09
im.redisConfig.address=redis://127.0.0.1:6379
im.redisConfig.database=0
im.redisConfig.password=123456

###\u5FAE\u4FE1\u652F\u4ED8\u76F8\u5173\u914D\u7F6E
im.wxConfig.appid=
im.wxConfig.mchid=
im.wxConfig.secret=
im.wxConfig.apiKey=
im.wxConfig.callBackUrl=http://imapi.server.com/user/recharge/wxPayCallBack
im.wxConfig.pkPath=D:\\tig-im\\config\\wechat\\apiclient_cert.p12

#\u652F\u4ED8\u5B9D\u652F\u4ED8\u76F8\u5173\u914D\u7F6E
im.aliPayConfig.appid=
im.aliPayConfig.app_private_key=
im.aliPayConfig.charset=utf-8
im.aliPayConfig.signType=RSA2
im.aliPayConfig.alipay_public_key=
im.aliPayConfig.callBackUrl=http://host:port
im.aliPayConfig.callBackTranUrl=http://host:port/alipay/callBackTrans
im.aliPayConfig.pid=

web.upload-path=E:/tmp/
spring.mvc.static-path-pattern=/**
spring.resources.static-locations=classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${web.upload-path}

#\u901A\u8054\u652F\u4ED8\u76F8\u5173\u914D\u7F6E
im.tlPayConfig.signPkPath=
im.tlPayConfig.signCerPath=

#\u52A0\u8F7D\u7684\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
im.quartzConfig.filePath=D:\\tig-im\\config\\quartz\\quartz.properties

#\u4E91\u652F\u4ED8\u914D\u7F6E\u4FE1\u606F
im.yunPayConfig.callBackUrl=/pay/callbackUrl

#\u4E0D\u9700\u8981\u8BBF\u95EE\u4EE4\u724C(access-token)\u5373\u53EF\u8BBF\u95EE\u7684\u63A5\u53E3
authorizationfilter.requestUriList[0]=/user/register
authorizationfilter.requestUriList[1]=/company/register
authorizationfilter.requestUriList[2]=/user/login
authorizationfilter.requestUriList[3]=/verify/telephone
authorizationfilter.requestUriList[4]=/basic/randcode/sendSms
authorizationfilter.requestUriList[5]=/config
authorizationfilter.requestUriList[6]=/user/password/reset
authorizationfilter.requestUriList[7]=/config/set
authorizationfilter.requestUriList[8]=/tigase/notify
authorizationfilter.requestUriList[9]=/user/getUserStatusCount
authorizationfilter.requestUriList[10]=/basic/randcode/sendSms
authorizationfilter.requestUriList[11]=/getImgCode
authorizationfilter.requestUriList[12]=/user/recharge/wxPayCallBack
authorizationfilter.requestUriList[13]=/user/recharge/aliPayCallBack
authorizationfilter.requestUriList[14]=/user/wxUserOpenId
authorizationfilter.requestUriList[15]=/user/getUserInfo
authorizationfilter.requestUriList[16]=/user/getWxUser
authorizationfilter.requestUriList[17]=/user/getWxUserbyId
authorizationfilter.requestUriList[18]=/tigase/tig_muc_msgs
authorizationfilter.requestUriList[19]=/CustomerService/register
authorizationfilter.requestUriList[20]=/user/getUserRegisterCount
authorizationfilter.requestUriList[21]=/clientConfig/set
authorizationfilter.requestUriList[22]=/user/getWxOpenId
authorizationfilter.requestUriList[23]=/user/registerSDK
authorizationfilter.requestUriList[24]=/user/sdkLogin
authorizationfilter.requestUriList[25]=/user/bindingTelephone
authorizationfilter.requestUriList[26]=/alipay/callBack
authorizationfilter.requestUriList[27]=/alipay/getAliUser
authorizationfilter.requestUriList[28]=/wxmeet
authorizationfilter.requestUriList[29]=/user/checkReportUrl
authorizationfilter.requestUriList[30]=/api/invite/usedPassCard
authorizationfilter.requestUriList[31]=/special/sendGroupPlanMsg
#authorizationfilter.requestUriList[32]=/user/transferToAdmin
authorizationfilter.requestUriList[32]=/basic/media/startup
authorizationfilter.requestUriList[33]=/verify/inviteCode
authorizationfilter.requestUriList[34]=/api/serverCount
authorizationfilter.requestUriList[35]=/newVersion
authorizationfilter.requestUriList[36]=/projectConfig
authorizationfilter.requestUriList[37]=/mp/articleList
authorizationfilter.requestUriList[38]=/tl/callBack
authorizationfilter.requestUriList[39]=/user/otherLogin
authorizationfilter.requestUriList[40]=/kaptcha
authorizationfilter.requestUriList[41]=/api/registerUser
authorizationfilter.requestUriList[42]=/pay/callbackUrl
authorizationfilter.requestUriList[43]=/question/list
authorizationfilter.requestUriList[44]=/question/check
authorizationfilter.requestUriList[45]=/question/set
authorizationfilter.requestUriList[46]=/pay/getOrderDetails
authorizationfilter.requestUriList[47]=/pay/getPayMethod
authorizationfilter.requestUriList[48]=/appQCCodeShare
authorizationfilter.requestUriList[49]=/user/login/QCCode
authorizationfilter.requestUriList[50]=/user/login/scan
authorizationfilter.requestUriList[51]=/getRoomDetailsQcCode
authorizationfilter.requestUriList[52]=/user/setUserAccountKey
authorizationfilter.requestUriList[53]=/room/updateIsCreateRoomByAPI
authorizationfilter.requestUriList[54]=/friends/addFriendsByAPI
authorizationfilter.requestUriList[55]=/logReport
#authorizationfilter.requestUriList[56]=/pay/jzCallbackUrl
authorizationfilter.requestUriList[56]=/pay/hmCallbackUrl
authorizationfilter.requestUriList[57]=/user/outtime
authorizationfilter.requestUriList[58]=/paypal/payPalNotify
authorizationfilter.requestUriList[59]=/paypal/cancel
authorizationfilter.requestUriList[60]=/paypal/rechargeSuccessNotify
authorizationfilter.requestUriList[61]=/user/goods/edit
authorizationfilter.requestUriList[62]=/user/purchaseVip
authorizationfilter.requestUriList[63]=/note/add
authorizationfilter.requestUriList[64]=/note/list
#authorizationfilter.requestUriList[61]=/user/buyOrder/createOrder

#\u4E0D\u9700\u8981\u7ECF\u8FC7\u7ECF\u8FC7\u767B\u5F55\u53CA\u767B\u5F55IP\u767D\u540D\u5355\u9A8C\u8BC1\u7684\u8FDE\u63A5
authorizationfilter.excludeIpWhiteUriList[0]=/console/articlePreview
authorizationfilter.excludeIpWhiteUriList[1]=/console/appDiscoverList
authorizationfilter.excludeIpWhiteUriList[2]=/image



#\u63A7\u5236\u4E0D\u540C\u5305\u4E0B\u7684\u65E5\u5FD7\u7EA7\u522B(\u65E5\u5FD7\u7EA7\u522B\u7531\u4F4E\u5230\u9AD8 trace < debug < info < warn < error)
##logging.level.helloWorldController=warn
 
#\u5728\u5F53\u524D\u78C1\u76D8\u7684\u6839\u8DEF\u5F84\u4E0B\u521B\u5EFAspring\u6587\u4EF6\u5939\u548C\u91CC\u9762\u7684log\u6587\u4EF6\u5939\uFF1B\u4F7F\u7528 spring.log \u4F5C\u4E3A\u9ED8\u8BA4\u6587\u4EF6
##logging.path=/spring/log
 
#\u53EF\u4EE5\u6307\u5B9A\u5B8C\u6574\u7684\u8DEF\u5F84(logging.path\u548Clogging.file \u914D\u7F6E\u4E00\u4E2A\u5373\u53EF)
#logging.file=E:/springboot.log

# \u5728\u63A7\u5236\u53F0\u8F93\u51FA\u7684\u65E5\u5FD7\u683C\u5F0F\uFF08\u4F7F\u7528\u9ED8\u8BA4\u683C\u5F0F\u5373\u53EF\uFF09
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{50} %msg%n
# \u6307\u5B9A\u6587\u4EF6\u4E2D\u65E5\u5FD7\u8F93\u51FA\u7684\u683C\u5F0F\uFF08\u4F7F\u7528\u9ED8\u8BA4\u683C\u5F0F\u5373\u53EF\uFF09
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{50} %msg%n

## %d{HH: mm:ss.SSS}\u2014\u2014\u65E5\u5FD7\u8F93\u51FA\u65F6\u95F4
## %thread\u2014\u2014\u8F93\u51FA\u65E5\u5FD7\u7684\u8FDB\u7A0B\u540D\u5B57\uFF0C\u8FD9\u5728Web\u5E94\u7528\u4EE5\u53CA\u5F02\u6B65\u4EFB\u52A1\u5904\u7406\u4E2D\u5F88\u6709\u7528
## %-5level\u2014\u2014\u65E5\u5FD7\u7EA7\u522B\uFF0C\u5E76\u4E14\u4F7F\u75285\u4E2A\u5B57\u7B26\u9760\u5DE6\u5BF9\u9F50
## %logger{36}\u2014\u2014\u65E5\u5FD7\u8F93\u51FA\u8005\u7684\u540D\u5B57
## %msg\u2014\u2014\u65E5\u5FD7\u6D88\u606F
## %n\u2014\u2014\u5E73\u53F0\u7684\u6362\u884C\u7B26 

#\u5728\u6D4F\u89C8\u5668\u4E0A\u6253\u5F00app\u7684\u8DEF\u5F84\uFF0C\u9700\u8981\u548C\u5BA2\u6237\u7AEF\u76F8\u4E00\u81F4
im.roomScanConfig.androidSchema=schema://realmName
im.roomScanConfig.iphoneSchema=schema://name
#\u4E0D\u9700\u8981\u7ECF\u8FC7XSS\u8FC7\u6EE4\u7684\u8FDE\u63A5
xss.url.excludePatterns[0]=/special/sendGroupPlanMsg
xss.url.excludePatterns[1]=/user/recharge/wxPayCallBack
xss.url.excludePatterns[2]=/console/getGoogleQRValueByAccount
xss.url.excludePatterns[3]=/console/addVersionInfo
xss.url.excludePatterns[4]=/mp/saveAndPushNow

pay.paypal.clientId=ARUHP68LLe06FKAlDPNjPwO-NBCt1upj-yWHayzPgRZ2DRb6_jVttckumlufNRlHahlHr_a3ZWu700u_
pay.paypal.clientSecret=ENQvx1XDoA02Sfud8et276J-VvMbQRb1iHzrn_ZO5bihBGY-uWS7rjHIns7SSBBDDV2tkllqEYiuwZaH
pay.paypal.mode=live