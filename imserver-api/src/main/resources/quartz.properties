org.quartz.scheduler.instanceName=MyScheduler
org.quartz.scheduler.instanceId=AUTO
org.quartz.scheduler.rmi.export=false
org.quartz.scheduler.rmi.proxy=false
org.quartz.scheduler.wrapJobExecutionInUserTransaction=false
#\u7EBF\u7A0B\u6C60\u914D\u7F6E
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount=10
org.quartz.threadPool.threadPriority=5
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true
#\u652F\u6301\u96C6\u7FA4
org.quartz.jobStore.isClustered=true
org.quartz.jobStore.clusterCheckinInterval=15000
# JobStore Properties
org.quartz.jobStore.class=com.novemberain.quartz.mongodb.MongoDBJobStore
org.quartz.jobStore.mongoUri=mongodb://127.0.0.1:27017
org.quartz.jobStore.dbName=quartzJobs
org.quartz.jobStore.collectionPrefix=job
org.quartz.jobStore.misfireThreshold=60000