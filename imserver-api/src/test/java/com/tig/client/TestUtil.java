package com.tig.client;

import cn.tig.commons.utils.Md5Util;
import com.alibaba.fastjson.JSONObject;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.City;
import com.maxmind.geoip2.record.Country;
import com.maxmind.geoip2.record.Location;
import com.maxmind.geoip2.record.Subdivision;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.InetAddress;
import java.nio.charset.Charset;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class TestUtil {
    private static Logger logger = LoggerFactory.getLogger(TestUtil.class);
    public static void main(String[] args) throws Exception {
//        new TestUtil().test();2020-04-07T14:19:52.358422236Z
        FileInputStream fis = new FileInputStream("C:\\Users\\\\<USER>\\imapi-20200412.log");
        // 防止路径乱码   如果utf-8 乱码  改GBK     eclipse里创建的txt  用UTF-8，在电脑上自己创建的txt  用GBK
        InputStreamReader isr = new InputStreamReader(fis, "UTF-8");
        BufferedReader br = new BufferedReader(isr);
        String line = null;
        try {
            while ((line = br.readLine()) != null) {
                if (line.length() > 0 && (line.contains("10000926") || line.contains("xx868686"))){
                    System.out.println(line.substring(line.indexOf("请求参数： ")+"请求参数： ".length(),line.indexOf(",")-1).replace("\\u0026","&").replace("&\\r\\n",""));

                }else
                // 如果 t x t文件里的路径 不包含---字符串       这里是对里面的内容进行一个筛选
                if (line.length() > 0 && line.contains("access_token=") && line.contains("time=") && line.contains("secret=")){
                    String string = line.substring(line.indexOf("?")+"?".length(),line.indexOf(",")-1);
                    string = string.replace("\\u0026","&").replace("&\\r\\n","");
                    String[] params = string.split("&");
                    String access_token = null;
                    String time = null;
                    String secret = null;
                    for (String param : params) {
                        if (param.contains("access_token") && param.length() > "access_token=".length()){
                            access_token = param.split("=").length > 0 ? param.split("=")[1] : "";
                            if (access_token == null || access_token.length()==0){
                                access_token = null;
                            }
                        }
                        if (param.contains("time") && param.length() > "time=".length()){
                            time = param.split("=")[1];
                        }
                        if (param.contains("secret") && param.length() > "secret=".length()){
                            secret = param.split("=")[1];
                        }
                    }
                    if (access_token != null && time != null && secret != null){
                        String key =new StringBuffer().append("%In9AXC0#Za8kd&U").append(time).append("10000032").append(access_token).toString();
                        if (Md5Util.md5Hex(key).equals(secret)){
                            System.out.println(line.substring(line.indexOf("请求参数： ")+"请求参数： ".length(),line.indexOf(",")-1).replace("\\u0026","&").replace("&\\r\\n",""));
                        }
                    }
                }
            }
            br.close();
            isr.close();
            fis.close();
        }catch (Exception e){
            System.out.println(line);
        }

    }

    public void test() throws Exception {


        File file = new File("D:\\GeoIP\\GeoLite2-City.mmdb");
        InputStream inputStream = null;
        DatabaseReader reader = null;
        try {
//            Resource resource = new ClassPathResource("classpath:GeoLite2-City.mmdb");
//            inputStream = resource.getInputStream();
            inputStream = new FileInputStream(file);
            reader = new DatabaseReader.Builder(inputStream).build();
        } catch (IOException e) {
        }finally {
            if (inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        try {
            JSONObject json = new JSONObject();
            InetAddress ipAddress = InetAddress.getByName("***************");
            CityResponse response = reader.city(ipAddress);
            Country country = response.getCountry();
            String countryStr = country.getNames().get("zh-CN");
            Subdivision subdivision = response.getLeastSpecificSubdivision();
            String subdivisionStr = subdivision.getNames().get("zh-CN");
            City city = response.getCity();
            String cityStr = city.getNames().get("zh-CN");
            Location location = response.getLocation();
            json.put("country",countryStr == null ? country.getName():countryStr);
            json.put("city",cityStr == null ? city.getName():cityStr);
            json.put("subdivision",subdivisionStr == null ? subdivision.getName():subdivisionStr);
            json.put("lat",location.getLatitude());
            json.put("lon",location.getLongitude());
            logger.info(json.toJSONString());
        }catch (Exception e){
        }

//        DatabaseReader reader = new DatabaseReader.Builder(file).build();
//        JSONObject json = new JSONObject();
//        InetAddress ipAddress = InetAddress.getByName("***************");
//        CityResponse response = reader.city(ipAddress);
//        Country country = response.getCountry();
//        String countryStr = country.getNames().get("zh-CN");
//        Subdivision subdivision = response.getLeastSpecificSubdivision();
//        String subdivisionStr = subdivision.getNames().get("zh-CN");
//        City city = response.getCity();
//        String cityStr = city.getNames().get("zh-CN");
//        Location location = response.getLocation();
//        json.put("country",countryStr == null ? country.getName():countryStr);
//        json.put("city",cityStr == null ? city.getName():cityStr);
//        json.put("subdivision",subdivisionStr == null ? subdivision.getName():subdivisionStr);
//        json.put("lat",location.getLatitude());
//        json.put("lon",location.getLongitude());
//        logger.info(json);
//        String content = "9606b072a2d54f68a873684c288df9c9|10000002";
//        String password = "12345678";
//        // 加密
//        logger.info("加密前：" + content);
//        String str = AESUtil.encrypt(content, AESUtil.PLAN_PASSWORD);
//        logger.info("加密后："+str);
//        logger.info("原字符串：" + str);
//        logger.info("原长度：" + str.length());
//        String compress = TestUtil.compress(str);
//        logger.info("压缩后字符串：" + compress);
//        logger.info("压缩后字符串长度：" + compress.length());
//        String string = TestUtil.uncompress(compress);
//        logger.info("解压缩后字符串：" + string);
//        logger.info("解压缩后字符串：" + str);
//        // 解密
//        String s1 = AESUtil.decrypt(string, AESUtil.PLAN_PASSWORD);
//        logger.info("解密后：" +s1);

    }

    /**
     * 使用gzip进行压缩
     */
    public static String compress(String primStr) {
        if (primStr == null || primStr.length() == 0) {
            return primStr;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip = null;
        try {
            gzip = new GZIPOutputStream(out);
            gzip.write(primStr.getBytes(Charset.defaultCharset()));
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (gzip != null) {
                try {
                    gzip.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return new sun.misc.BASE64Encoder().encode(out.toByteArray());
    }

    /**
     * 使用gzip进行解压缩
     */
    public static String uncompress(String compressedStr) {
        if (compressedStr == null) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = null;
        GZIPInputStream ginzip = null;
        byte[] compressed = null;
        String decompressed = null;
        try {
            compressed = new sun.misc.BASE64Decoder().decodeBuffer(compressedStr);
            in = new ByteArrayInputStream(compressed);
            ginzip = new GZIPInputStream(in);

            byte[] buffer = new byte[1024];
            int offset = -1;
            while ((offset = ginzip.read(buffer)) != -1) {
                out.write(buffer, 0, offset);
            }
            decompressed = out.toString();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (ginzip != null) {
                try {
                    ginzip.close();
                } catch (IOException e) {
                }
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                }
            }
            try {
                out.close();
            } catch (IOException e) {
            }
        }
        return decompressed;
    }

}
