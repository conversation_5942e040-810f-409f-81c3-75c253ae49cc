package com.tig.client.db;

import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.AddressBook;
import com.alibaba.fastjson.JSON;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.client.MongoCollection;
import com.tig.im.Application;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mongodb.morphia.Morphia;
import org.mongodb.morphia.mapping.MappedClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes=Application.class)
public class MongoDBTest {
	private static Logger logger = LoggerFactory.getLogger(MongoDBTest.class);
	@Autowired
	protected Morphia morphia;
	
	@Test
	public void testAddressDB() {
		TigBeanUtils.getAddressBookManger().notifyBook("8618316603690");
		while (true) {
			
		}
	}
	
	
	@Test
	public void testRoomFenBiao() {
		Integer userId=10004251;
		ObjectId id = new ObjectId();
		logger.info(userId+"_"+id.getCounter());
	}
	@Test
	public void testDBObject() {
		DBObject dbObject=new BasicDBObject("aa",111);
		MongoCollection collection = TigBeanUtils.getLocalSpringBeanManager().getMongoClient().getDatabase("test1").getCollection("test",DBObject.class);
		collection.insertOne(dbObject);
	}
	
	
	@Test
	public void testDB() {
		
		logger.info(String.valueOf((10004541/1000)/10000));
		
		AddressBook book=new AddressBook();
		ObjectId id = new ObjectId();
		book.setId(id);
		book.setRegisterEd(1);
		book.setStatus(1);
		book.setTelephone("86183456123");
		book.setToTelephone("861584299254");
		MappedClass mappedClass = morphia.getMapper().getMappedClass(AddressBook.class);
		logger.info("getCollectionName "+mappedClass.getCollectionName());
		DBObject dbObject = morphia.toDBObject(book);
		logger.info(dbObject.toString());
		AddressBook fromDBObject = morphia.fromDBObject(TigBeanUtils.getDatastore(), AddressBook.class, dbObject);
		
		logger.info(JSON.toJSONString(fromDBObject));
		logger.info("id "+fromDBObject.getId().toString());
		
	}

}
