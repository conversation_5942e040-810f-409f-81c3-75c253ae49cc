package com.alipay.util;

import cn.tig.commons.autoconfigure.KApplicationProperties.AliPayConfig;
import cn.tig.commons.constants.KConstants;
import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.PayConfig;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.alipay.config.AlipayConfig;
import com.alipay.sign.RSA;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.TreeMap;

public class AliPayUtil {
	private static final Logger logger = LoggerFactory.getLogger(AliPayUtil.class);
	static AliPayConfig aliPayConfig = TigBeanUtils.getLocalSpringBeanManager().getApplicationConfig().getAliPayConfig();
//	public static String APP_ID=aliPayConfig.getAppid();
//	public static String APP_PRIVATE_KEY=aliPayConfig.getApp_private_key();
	public static String CHARSET=aliPayConfig.getCharset();
//	public static String ALIPAY_PUBLIC_KEY=aliPayConfig.getAlipay_public_key();
	public static String callBackUrl=aliPayConfig.getCallBackUrl();
	// 支付宝签名方式
	public static String SIGN_TYPE = aliPayConfig.getSignType();
	// 支付宝PID
//	public static String PID = aliPayConfig.getPid();
	
//	static AlipayClient alipayClient;

	
//	public static AlipayClient getAliPayClient(){
//		if(alipayClient != null){
//			return alipayClient;
//		}else{
//			PayConfig aliPayConfig = TigBeanUtils.getAdminManager().getPayConfig(KConstants.PayType.ALIPAY);
////			alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", APP_ID, APP_PRIVATE_KEY, "json", CHARSET, ALIPAY_PUBLIC_KEY, SIGN_TYPE);
//			alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", aliPayConfig.getAppId(), aliPayConfig.getPrivateKey(), "json", CHARSET, aliPayConfig.getPublicKey(), SIGN_TYPE);
//		}
//		return alipayClient;
//	}
	
//	public static String getOutTradeNo() {
////		int r1 = (int) (Math.random() * (10));// 产生2个0-9的随机数
////		int r2 = (int) (Math.random() * (10));
//		int r1 = new Random().nextInt(10);
//		int r2 = new Random().nextInt(10);
//		long now = System.currentTimeMillis();// 一个13位的时间戳
//		String id = String.valueOf(r1) + String.valueOf(r2) + String.valueOf(now);// 订单ID
//		return id;
//	}
	 /**
	 * create the order info. 创建订单信息
	 */
	 public static String getOrderInfo(String subject, String body, String price,String orderNo) {
		//实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.app.pay
		AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
		//SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式(model和biz_content同时存在的情况下取biz_content)。
		AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
		model.setBody(body);
		model.setSubject(subject);
		model.setOutTradeNo(orderNo);
		model.setTimeoutExpress("30m");
		model.setTotalAmount(price);
		model.setProductCode("QUICK_MSECURITY_PAY");
//			model.setGoodsType("0");
		request.setBizModel(model);
		 PayConfig aliPayConfig = TigBeanUtils.getAdminManager().getPayConfig(KConstants.PayType.ALIPAY);
		/* if (aliPayConfig.getPayCallBackUrl() != null || callBackUrl.charAt(0) == '/'){
		 	request.setNotifyUrl(aliPayConfig.getPayCallBackUrl()+callBackUrl);
		 }else {
			 request.setNotifyUrl(callBackUrl);
		 }*/
		 request.setNotifyUrl("http://**************:8092/alipay/callBack");
		try {
			//这里和普通的接口调用不同，使用的是sdkExecute
			AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", aliPayConfig.getAppId(), aliPayConfig.getPrivateKey(), "json", CHARSET, aliPayConfig.getPublicKey(), SIGN_TYPE);
			AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
			logger.info("返回order  "+response.getBody());//就是orderString 可以直接给客户端请求，无需再做处理。

			return response.getBody();
		} catch (AlipayApiException e) {
			e.printStackTrace();
			return null;
		}
	 }
	   
	   
	   /**
	    * sign the order info. 对订单信息进行签名
	    * @param content 待签名订单信息
	    */
	 public static String sign(String content) {
		 PayConfig aliPayConfig = TigBeanUtils.getAdminManager().getPayConfig(KConstants.PayType.ALIPAY);
//		 return RSA.sign(content, AlipayConfig.private_key,AlipayConfig.input_charset);
		 return RSA.sign(content, aliPayConfig.getPrivateKey(),AlipayConfig.input_charset);
	 }

	   /**
	    * get the sign type we use. 获取签名方式
	    */
	  /* private String getSignType() {
	      return "sign_type=\"RSA\"";
	   }*/
	 
	 /**
	 * 解析支付宝支付成功后返回的数据
	 *
	 * @param request
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@SuppressWarnings("rawtypes")
	public static Map<String, String> getAlipayResult(javax.servlet.http.HttpServletRequest request) {
		// 获取支付宝POST过来反馈信息
		Map<String, String> params = new TreeMap<>();
		Map<String,String[]> requestParams = request.getParameterMap();
		for(Map.Entry<String,String[]> entry : requestParams.entrySet()){
			String[] values = entry.getValue();
			StringBuffer valueStr = new StringBuffer();
			for (int i = 0; i < values.length; i++) {
				if (i == values.length - 1){
					valueStr.append(values[i]);
				}else {
					valueStr.append(values[i]+",");
				}
			}
			// 乱码解决，这段代码在出现乱码时使用。如果MySign和sign不相等也可以使用这段代码转化
			// valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
			params.put(entry.getKey(), valueStr.toString());
		}
//		for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
//			String name = (String) iter.next();
//			String[] values = (String[]) requestParams.get(name);
//			StringBuffer valueStr = new StringBuffer();
//			for (int i = 0; i < values.length; i++) {
//				if (i == values.length - 1){
//					valueStr.append(values[i]);
//				}else {
//					valueStr.append(values[i]+",");
//				}
//			}
//			// 乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
//			// valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
//			params.put(name, valueStr.toString());
//		}
		return params;
	}

	public static AlipayClient getAliPayClient(PayConfig aliPayConfig) {
		AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", aliPayConfig.getAppId(), aliPayConfig.getPrivateKey(), "json", CHARSET, aliPayConfig.getPublicKey(), SIGN_TYPE);
		return alipayClient;
	}

	public String getAuthInfo() throws AlipayApiException{
		AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
		PayConfig aliPayConfig = TigBeanUtils.getAdminManager().getPayConfig(KConstants.PayType.ALIPAY);
		AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", aliPayConfig.getAppId(), aliPayConfig.getPrivateKey(), "json", CHARSET, aliPayConfig.getPublicKey(), SIGN_TYPE);
//		getAliPayClient().execute(request);
		alipayClient.execute(request);
		return null;
	}
}
