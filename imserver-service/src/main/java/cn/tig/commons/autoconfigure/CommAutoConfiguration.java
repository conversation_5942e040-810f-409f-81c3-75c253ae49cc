package cn.tig.commons.autoconfigure;


import cn.tig.commons.autoconfigure.KApplicationProperties.*;
import cn.tig.commons.support.spring.converter.MappingFastjsonHttpMessageConverter;
import cn.tig.commons.utils.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author:
 * @package: cn.tig.commons.autoconfigure.CommAutoConfiguration
 * @fileName: CommAutoConfiguration.java
 * @dateTime: 2019/7/4 15:57
 * @description: 配置文件控制类
 **/
@Configuration
public class CommAutoConfiguration {
	private static final Logger logger = LoggerFactory.getLogger(CommAutoConfiguration.class);
	@Autowired
	private KApplicationProperties config;
	
	@Bean
	public HttpMessageConverters customConverters() {
		return new HttpMessageConverters(new MappingFastjsonHttpMessageConverter());
	}
	

	@Bean(name="appConfig")
	public AppConfig appConfig(){
		AppConfig appConfig=config.getAppConfig();
		logger.info("appConfig  ---->>>"+JSONObject.toJSONString(appConfig));
		if(!StringUtil.isEmpty(appConfig.getQqzengPath()))
			IpSearch.getInstance(appConfig.getQqzengPath());
		return appConfig;
	}
	
	@Bean(name="smsConfig")
	public SmsConfig smsConfig(){
		SmsConfig smsConfig=config.getSmsConfig();
		return smsConfig;
	}
	@Bean(name="xmppConfig")
	public XMPPConfig xmppConfig(){
		XMPPConfig xmppConfig=config.getXmppConfig();
		logger.info("xmppConfig  ---->>>"+JSONObject.toJSONString(xmppConfig));
		return xmppConfig;
	}
	@Bean(name="mongoConfig")
	public MongoConfig mongoConfig(){
		MongoConfig mongoConfig=config.getMongoConfig();
		logger.info("mongoConfig  ---->>>"+JSONObject.toJSONString(mongoConfig));
		return mongoConfig;
	}
	@Bean(name="redisConfig")
	public RedisConfig redisConfig(){
		RedisConfig redisConfig=config.getRedisConfig();
		logger.info("redisConfig  ---->>>"+JSONObject.toJSONString(redisConfig));
		return redisConfig;
	}
	
	
	
	@Bean(name="wxConfig")
	public WXConfig wxConfig(){
		WXConfig wxConfig=config.getWxConfig();
		return wxConfig;
	}

	@Bean(name="mqConfig")
	public MQConfig mqConfig(){
		MQConfig mqConfig=config.getMqConfig();
		logger.info("mqConfig  ---->>>"+JSONObject.toJSONString(mqConfig));
		return mqConfig;
	}

	@Bean(name="inviteConfig")
	public InviteConfig inviteConfig(){
		InviteConfig inviteConfig = config.getInviteConfig();
		logger.info("inviteConfig  ---->>>"+JSONObject.toJSONString(inviteConfig));
		return inviteConfig;
	}

	@Bean(name="apiAccessConfig")
	public ApiAccessConfig apiAccessConfig(){
		ApiAccessConfig apiAccessConfig = config.getApiAccessConfig();
		logger.info("apiAccessConfig  ---->>>"+JSONObject.toJSONString(apiAccessConfig));
		return apiAccessConfig;
	}

	@Bean(name="obsConfig")
	public ObsConfig obsConfig(){
		ObsConfig obsConfig = config.getObsConfig();
		logger.info("obsConfig  ---->>>"+JSONObject.toJSONString(obsConfig));
		return obsConfig;
	}

	@Bean(name="TlPayConfig")
	public KApplicationProperties.TlPayConfig tlPayConfig(){
		KApplicationProperties.TlPayConfig tlPayConfig = config.getTlPayConfig();
		logger.info("TlPayConfig  ---->>>"+JSONObject.toJSONString(tlPayConfig));
		return tlPayConfig;
	}

	@Bean(name="sysPayConfig")
	public KApplicationProperties.SysPayConfig sysPayConfig(){
		KApplicationProperties.SysPayConfig sysPayConfig = config.getSysPayConfig();
		logger.info("SysPayConfig  ---->>>"+JSONObject.toJSONString(sysPayConfig));
		return sysPayConfig;
	}

	@Bean(name="quartzConfig")
	public KApplicationProperties.QuartzConfig quartzConfig(){
		KApplicationProperties.QuartzConfig quartzConfig = config.getQuartzConfig();
		logger.info("QuartzConfig  ---->>>"+JSONObject.toJSONString(quartzConfig));
		return quartzConfig;
	}
	@Bean(name = "roomScanConfig")
	public KApplicationProperties.RoomScanConfig roomScanConfig(){
		KApplicationProperties.RoomScanConfig roomScanConfig = config.getRoomScanConfig();
		logger.info("RoomScanConfig  ---->>>"+JSONObject.toJSONString(roomScanConfig));
		return roomScanConfig;
	}
	@Bean(name = "dHConfig")
	public KApplicationProperties.DHConfig dHConfig(){
		KApplicationProperties.DHConfig dHConfig = config.getDHConfig();
		logger.info("DHConfig  ---->>>"+JSONObject.toJSONString(dHConfig));
		return dHConfig;
	}
}
