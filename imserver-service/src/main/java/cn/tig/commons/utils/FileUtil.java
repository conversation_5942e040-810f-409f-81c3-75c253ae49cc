package cn.tig.commons.utils;

import cn.tig.commons.ex.ServiceException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author:
 * @package: cn.tig.commons.utils.FileUtil
 * @fileName: FileUtil.java
 * @dateTime: 2019/7/4 17:45
 * @description: 文件帮助类
 **/
//@Log4j2
public final class FileUtil {
    public static final String DEFAULT_CHARSET = "UTF-8";
    public static final int DEFAULT_TIME_OUT = 60 * 1000;
    private static Logger logger = LoggerFactory.getLogger(FileUtil.class);
    public static String readAll(InputStream in) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(in, Charset.defaultCharset()));
        StringBuffer sb = new StringBuffer();
        String ln = null;
        while (null != (ln = reader.readLine())) {
            sb.append(ln);
        }
        return sb.toString();
    }

    public static String readAll(InputStream in, String charsetName) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(in, charsetName));
        StringBuffer sb = new StringBuffer();
        String ln = null;
        while (null != (ln = reader.readLine()))
            sb.append(ln);
        return sb.toString();
    }

    public static String readAll(BufferedReader reader) {
        try {
            StringBuffer sb = new StringBuffer();
            String ln = null;
            while (null != (ln = reader.readLine()))
                sb.append(ln);
            return sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public final static String doGetRequestForFile(String urlStr) {

        InputStream is = null;
        ByteArrayOutputStream os = null;
        byte[] buff = new byte[1024];
        int len = 0;
        try {
            URL url = new URL(UriUtils.encodePath(urlStr, DEFAULT_CHARSET));
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestProperty("Content-Type", "plain/text;charset=" + DEFAULT_CHARSET);
            conn.setRequestProperty("charset", DEFAULT_CHARSET);
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setRequestMethod("GET");
            conn.setReadTimeout(DEFAULT_TIME_OUT);
            conn.connect();
            is = conn.getInputStream();
            os = new ByteArrayOutputStream();
            while ((len = is.read(buff)) != -1) {
                os.write(buff, 0, len);
            }
            return os.toString(DEFAULT_CHARSET);

        } catch (IOException e) {
            logger.info("发起请求出现异常:" + e);
            return null;
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.info("【关闭流异常】");
                }
            }
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    logger.info("【关闭流异常】");
                }
            }
        }
    }


    /**
     * 删除文件(图片、视频、语音)的方法
     *
     * @param paths 文件路径（支持多个）
     * @return
     */
    public static String deleteFileToUploadDomain(String domain, String... paths) throws Exception {
        try {
            new Thread(() -> {
                Map<String, Object> params = null;
                String url = "/upload/deleteFileServlet";
                String path = null;
                for (int i = 0; i < paths.length; i++) {
                    logger.info("删除文件  ===> " + paths[i]);
                    path = paths[i];
                    if (null == path) {
                        return;
                    }
                        //-1 表示空地址，不执行删除操作
                    else if (path.equals("-1")) {
                        return;
                    }

                    params = new HashMap<>();
                    url = domain + url; //拼接URl
                    logger.info(" domain ===> " + domain + " deleteDomain ===>" + url);
                    params.put("paths", paths[i]);
                    HttpUtil.URLPost(url, params);
                }
            }).start();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 复制文件(图片、视频、语音)的方法
     *
     * @param paths 文件路径（支持多个）
     * @return
     */

    public static String copyFileToUploadDomain(String domain, int validTime, String... paths) {
        String newUrl;
//		try {
        Map<String, Object> params;
        String url = "/upload/copyFileServlet";
        String path;
        for (int i = 0; i < paths.length; i++) {
            logger.info("复制文件  ===> " + paths[i]);
            path = paths[i];
            //-1 表示空地址，不执行删除操作
            if (path.equals("-1")) {
                return null;
            }
            params = new HashMap<>();
            url = domain + url; //拼接URl
            logger.info(" domain ===> " + domain + " deleteDomain ===>" + url);
            params.put("paths", paths[i]);
            params.put("validTime", validTime);
            String resultStr = HttpUtil.URLPost(url, params);
            if (StringUtil.isEmpty(resultStr)) {
                throw new ServiceException("连接文件服务器超时");
            }
            JSONObject resultObj = JSON.parseObject(resultStr);
            JSONObject resultData = resultObj.getJSONObject("data");
            if (null == resultData) {
                throw new ServiceException("源文件不存在");
            }
            newUrl = resultData.getString("url");
            if (newUrl == null || newUrl.equals("")) {
                newUrl = resultData.getString("oUrl");
            }
            if (newUrl == null || newUrl.equals("")) {
                newUrl = resultData.getString("tUrl");
            }
            logger.info(" copy new Url =====>" + newUrl);
            return newUrl;
        }
        return null;
    }

    public static String fileToUploadDomain(String domain, MultipartFile multipartFile) {
        String newUrl = null;
        OutputStream out = null;
        DataInputStream in = null;
        BufferedReader reader = null;
        try {
            // 换行符
            final String newLine = "\r\n";
            final String boundaryPrefix = "--";
            // 定义数据分隔线
            String BOUNDARY = "========7d4a6d158c9";
            // 服务器的域名
            URL url;
            if(domain.substring(domain.length()-1).equals("/")){
                url = new URL(domain + "upload/UploadServlet");
            }else {
                url = new URL(domain + "/upload/UploadServlet");
            }
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // 设置为POST情
            conn.setRequestMethod("POST");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            // 设置请求头参数
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Charsert", "UTF-8");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
            out = new DataOutputStream(conn.getOutputStream());
            // 上传文件
            String fileRealName = multipartFile.getOriginalFilename();//获得原始文件名;
            StringBuilder sb = new StringBuilder();
            sb.append(boundaryPrefix);
            sb.append(BOUNDARY);
            sb.append(newLine);
            // 文件参数,photo参数名可以随意修改
            sb.append("Content-Disposition: form-data;name=\"photo\";filename=\"" + fileRealName + "\"" + newLine);
            sb.append("Content-Type:application/octet-stream");
            // 参数头设置完以后需要两个换行，然后才是参数内容
            sb.append(newLine);
            sb.append(newLine);
            // 将参数头的数据写入到输出流中
            out.write(sb.toString().getBytes(Charset.defaultCharset()));
            // 数据输入流,用于读取文件数据
            in = new DataInputStream(multipartFile.getInputStream());
            byte[] bufferOut = new byte[1024];
            int bytes = 0;
            // 每次读1KB数据,并且将文件数据写入到输出流中
            while ((bytes = in.read(bufferOut)) != -1) {
                out.write(bufferOut, 0, bytes);
            }
            // 最后添加换行
            out.write(newLine.getBytes(Charset.defaultCharset()));
            in.close();
            in = null;
            // 定义最后数据分隔线，即--加上BOUNDARY再加上--。
            byte[] end_data = (newLine + boundaryPrefix + BOUNDARY + boundaryPrefix + newLine).getBytes(Charset.defaultCharset());
            // 写上结尾标识
            out.write(end_data);
            out.flush();
            out.close();
            out = null;
//          定义BufferedReader输入流来读取URL的响应
            reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), Charset.defaultCharset()));
            JSONObject resultObj = JSON.parseObject(reader.readLine());
            JSONObject resultData = resultObj.getJSONObject("data");
            if (null == resultData) {
                reader.close();
                reader = null;
                throw new ServiceException("源文件不存在");
            }
            JSONArray imagesData = null;
            JSONArray  videosData= null;
            JSONArray audiosData = null;
            JSONArray othersData = null;
            if(null!=resultData){
                imagesData = resultData.getJSONArray("images");
                videosData = resultData.getJSONArray("videos");
                audiosData = resultData.getJSONArray("audios");
                othersData = resultData.getJSONArray("others");
            }
            if (imagesData.size() > 0) {
                JSONObject imageData = imagesData.getJSONObject(0);
                newUrl = imageData.getString("oUrl");
            }else if(videosData.size()>0){
                JSONObject videoData = videosData.getJSONObject(0);
                newUrl = videoData.getString("oUrl");
            }else if(audiosData.size()>0){
                JSONObject audioData = audiosData.getJSONObject(0);
                newUrl = audioData.getString("oUrl");
            }else {
                JSONObject otherData = othersData.getJSONObject(0);
                newUrl = otherData.getString("oUrl");
            }
            logger.info(" upload new Url =====>" + newUrl);
            reader.close();
            reader = null;
            return newUrl;

        } catch (Exception e) {
            logger.info("发送POST请求出现异常！" + e);
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return newUrl;
    }

    public static String getUploadFilePath(String domain, MultipartFile multipartFile) {
        String newUrl = null;
        OutputStream out = null;
        DataInputStream in = null;
        BufferedReader reader = null;
        try {
            // 换行符
            final String newLine = "\r\n";
            final String boundaryPrefix = "--";
            // 定义数据分隔线
            String BOUNDARY = "========7d4a6d158c9";
            // 服务器的域名
            URL url = new URL(domain + "/upload/UploadServlet");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // 设置为POST情
            conn.setRequestMethod("POST");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            // 设置请求头参数
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Charsert", "UTF-8");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
            out = new DataOutputStream(conn.getOutputStream());
            // 上传文件
            String fileRealName = multipartFile.getOriginalFilename();//获得原始文件名;
            StringBuilder sb = new StringBuilder();
            sb.append(boundaryPrefix);
            sb.append(BOUNDARY);
            sb.append(newLine);
            // 文件参数,photo参数名可以随意修改
            sb.append("Content-Disposition: form-data;name=\"file\";filename=\"" + fileRealName + "\"" + newLine);
            sb.append("Content-Type:application/octet-stream");
            // 参数头设置完以后需要两个换行，然后才是参数内容
            sb.append(newLine);
            sb.append(newLine);
            // 将参数头的数据写入到输出流中
            out.write(sb.toString().getBytes(Charset.defaultCharset()));
            // 数据输入流,用于读取文件数据
            in = new DataInputStream(multipartFile.getInputStream());
            byte[] bufferOut = new byte[1024];
            int bytes = 0;
            // 每次读1KB数据,并且将文件数据写入到输出流中
            while ((bytes = in.read(bufferOut)) != -1) {
                out.write(bufferOut, 0, bytes);
            }
            // 最后添加换行
            out.write(newLine.getBytes(Charset.defaultCharset()));
            in.close();
            in = null;
            // 定义最后数据分隔线，即--加上BOUNDARY再加上--。
            byte[] end_data = (newLine + boundaryPrefix + BOUNDARY + boundaryPrefix + newLine).getBytes(Charset.defaultCharset());
            // 写上结尾标识
            out.write(end_data);
            out.flush();
            out.close();
            out = null;
//          定义BufferedReader输入流来读取URL的响应
            reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), Charset.defaultCharset()));
            JSONObject resultObj = JSON.parseObject(reader.readLine());
            JSONObject resultData = resultObj.getJSONObject("data");
            if (null == resultData) {
                reader.close();
                reader = null;
                throw new ServiceException("源文件不存在");
            }
            JSONArray imagesData = resultData.getJSONArray("images");
            if (imagesData.size() > 0) {
                JSONObject imageData = imagesData.getJSONObject(0);
                String newUrlO = imageData.getString("oUrl");
                String newUrlT = imageData.getString("tUrl");
                logger.info(" upload new Url =====>" + newUrlO);
                logger.info(" upload new Url =====>" + newUrlT);
                newUrl = newUrlO + "," + newUrlT;
                reader.close();
                reader = null;
                return newUrl;
            }

        } catch (Exception e) {
            logger.info("发送POST请求出现异常！" + e);
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return newUrl;
    }

    public static String fileToUploadDomainPlist(String domain, InputStream inputStream) {
        String newUrl = null;
        OutputStream out = null;
        DataInputStream in = null;
        BufferedReader reader = null;
        try {
            // 换行符
            final String newLine = "\r\n";
            final String boundaryPrefix = "--";
            // 定义数据分隔线
            String BOUNDARY = "========7d4a6d158c9";
            // 服务器的域名
            URL url = new URL(domain + "/upload/UploadServlet");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // 设置为POST情
            conn.setRequestMethod("POST");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            // 设置请求头参数
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Charsert", "UTF-8");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
            out = new DataOutputStream(conn.getOutputStream());
            // 上传文件
            String fileRealName = "appPlistFile.plist";//获得原始文件名;
            StringBuilder sb = new StringBuilder();
            sb.append(boundaryPrefix);
            sb.append(BOUNDARY);
            sb.append(newLine);
            // 文件参数,photo参数名可以随意修改
            sb.append("Content-Disposition: form-data;name=\"photo\";filename=\"" + fileRealName + "\"" + newLine);
            sb.append("Content-Type:application/octet-stream");
            // 参数头设置完以后需要两个换行，然后才是参数内容
            sb.append(newLine);
            sb.append(newLine);
            // 将参数头的数据写入到输出流中
            out.write(sb.toString().getBytes(Charset.defaultCharset()));
            // 数据输入流,用于读取文件数据
            in = new DataInputStream(inputStream);
            byte[] bufferOut = new byte[1024];
            int bytes = 0;
            // 每次读1KB数据,并且将文件数据写入到输出流中
            while ((bytes = in.read(bufferOut)) != -1) {
                out.write(bufferOut, 0, bytes);
            }
            // 最后添加换行
            out.write(newLine.getBytes(Charset.defaultCharset()));
            in.close();
            in = null;
            // 定义最后数据分隔线，即--加上BOUNDARY再加上--。
            byte[] end_data = (newLine + boundaryPrefix + BOUNDARY + boundaryPrefix + newLine).getBytes(Charset.defaultCharset());
            // 写上结尾标识
            out.write(end_data);
            out.flush();
            out.close();
            out = null;
//          定义BufferedReader输入流来读取URL的响应
            reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), Charset.defaultCharset()));
            JSONObject resultObj = JSON.parseObject(reader.readLine());
            JSONObject resultData = resultObj.getJSONObject("data");
            if (null == resultData) {
                reader.close();
                reader = null;
                throw new ServiceException("源文件不存在");
            }

            JSONArray imagesData = resultData.getJSONArray("others");
            if (imagesData.size() > 0) {
                JSONObject imageData = imagesData.getJSONObject(0);
                newUrl = imageData.getString("oUrl");
                logger.info(" upload new Url  =====> " + newUrl);
                reader.close();
                reader = null;
                return newUrl;
            }

        } catch (Exception e) {
            logger.info("发送POST请求出现异常！" + e);
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return newUrl;
    }

    public static Map getUploadZipPath(String domain, MultipartFile multipartFile) {
        String newUrl = null;
        Map mapImags = new HashMap();
        OutputStream out = null;
        try {
            // 换行符
            final String newLine = "\r\n";
            final String boundaryPrefix = "--";
            // 定义数据分隔线
            String BOUNDARY = "========7d4a6d158c9";
            // 服务器的域名
            URL url = new URL(domain + "/upload/UploadZipServlet");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // 设置为POST情
            conn.setRequestMethod("POST");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            // 设置请求头参数
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Charsert", "UTF-8");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
            out = new DataOutputStream(conn.getOutputStream());
            // 上传文件
            String fileRealName = multipartFile.getOriginalFilename();//获得原始文件名;
            StringBuilder sb = new StringBuilder();
            sb.append(boundaryPrefix);
            sb.append(BOUNDARY);
            sb.append(newLine);
            // 文件参数,photo参数名可以随意修改
            sb.append("Content-Disposition: form-data;name=\"file\";filename=\"" + fileRealName + "\"" + newLine);
            sb.append("Content-Type:application/octet-stream");
            // 参数头设置完以后需要两个换行，然后才是参数内容
            sb.append(newLine);
            sb.append(newLine);
            // 将参数头的数据写入到输出流中
            out.write(sb.toString().getBytes(Charset.defaultCharset()));
            // 数据输入流,用于读取文件数据
            DataInputStream in = new DataInputStream(multipartFile.getInputStream());
            byte[] bufferOut = new byte[1024];
            int bytes = 0;
            // 每次读1KB数据,并且将文件数据写入到输出流中
            while ((bytes = in.read(bufferOut)) != -1) {
                out.write(bufferOut, 0, bytes);
            }
            // 最后添加换行
            out.write(newLine.getBytes(Charset.defaultCharset()));
            in.close();
            // 定义最后数据分隔线，即--加上BOUNDARY再加上--。
            byte[] end_data = (newLine + boundaryPrefix + BOUNDARY + boundaryPrefix + newLine).getBytes(Charset.defaultCharset());
            // 写上结尾标识
            out.write(end_data);
            out.flush();
            out.close();
//          定义BufferedReader输入流来读取URL的响应
            BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), Charset.defaultCharset()));
            JSONObject resultObj = JSON.parseObject(reader.readLine());
            JSONObject resultData = resultObj.getJSONObject("data");
            if (null == resultData) {
                throw new ServiceException("源文件不存在");
            }
            JSONArray imagesData = resultData.getJSONArray("images");
            JSONArray imagesDataOthers = resultData.getJSONArray("zip");
            List zipImags = Lists.newArrayList();
            List zip = Lists.newArrayList();
            if (imagesData.size() > 0) {
                for (int i = 0; i < imagesData.size(); i++) {
                    JSONObject imageData = imagesData.getJSONObject(i);
                    String newUrlO = imageData.getString("oUrl");
                    String newUrlT = imageData.getString("tUrl");
                    logger.info(" upload new Url =====>" + newUrlO);
                    logger.info(" upload new Url =====>" + newUrlT);
                    newUrl = newUrlO + "," + newUrlT;
                    zipImags.add(newUrl);
                }
                mapImags.put("imags", zipImags);
            }
            if (imagesDataOthers.size() > 0) {
                JSONObject imageDataOthers = imagesDataOthers.getJSONObject(0);
                String newUrlOthers = imageDataOthers.getString("oUrl");
                zip.add(newUrlOthers);
                mapImags.put("zip", zip);
            }

        } catch (Exception e) {
            logger.info("发送POST请求出现异常！" + e);
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.flush();
                    out.close();
                }
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
        return mapImags;
    }

    public static String fileToUploadDomainHeadUrl(String domain, String headUrl, Long userId) {
        String url = "/upload/OtherAvatarServlet";
        Map<String, Object> params = new HashMap<>();
        url = domain + url; //拼接URl
        logger.info(" domain ===> " + domain + " deleteDomain ===>" + url);
        params.put("imgUrl", headUrl);
        params.put("userId", userId);
        String resultStr = HttpUtil.URLPost(url, params);
        if (StringUtil.isEmpty(resultStr)) {
            throw new ServiceException("连接文件服务器超时");
        }
        JSONObject resultObj = JSON.parseObject(resultStr);
        JSONObject resultData = resultObj.getJSONObject("data");
        if (null == resultData) {
            throw new ServiceException("源文件不存在");
        }
        String newUrl = resultData.getString("oUrl");
        logger.info(" uploadOtherUser new Url =====>" + newUrl);
        return newUrl;
    }

    public static void flushStream (byte[] inputStream, HttpServletResponse response) throws IOException {
        if (inputStream != null){
            response.setContentLength(inputStream.length);
            response.setContentType("multipart/form-data");
            OutputStream toClient = response.getOutputStream();
            toClient.write(inputStream);
            toClient.flush();
        }
    }

}
