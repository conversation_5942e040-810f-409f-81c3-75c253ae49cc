package cn.tig.commons.utils;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * @titel: RSA加密解密帮助类
 * @author:
 * @package: cn.tig.commons.utils.RSAUtils
 * @fileName: RSAUtils.java
 * @dateTime: 2019/7/4 18:12
 * @description:
 **/
public class RSAUtils {
    public static final String KEY_ALGORITHM = "RSA";
    public static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    public static final int KEY_SIZE = 1024;
    public static String PUBLIC_KEY;
    public static String PRIVATE_KEY;
    private static final int MAX_ENCRYPT_BLOCK = 117;
    private static final int MAX_DECRYPT_BLOCK = 128;

    public RSAUtils() {
    }

    public static Map<String, Object> genKeyPair() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        keyPairGen.initialize(KEY_SIZE, new SecureRandom());
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey)keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey)keyPair.getPrivate();
        Map<String, Object> keyMap = new HashMap(2);
        keyMap.put(PUBLIC_KEY, publicKey);
        keyMap.put(PRIVATE_KEY, privateKey);
        return keyMap;
    }

    public static String sign(String dataStr) throws Exception {
        byte[] data = (new BASE64Decoder()).decodeBuffer(dataStr);
        RSAPrivateKey privateKeyRsa = loadPrivateKey(PRIVATE_KEY);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateKeyRsa);
        signature.update(data);
        return (new BASE64Encoder()).encodeBuffer(signature.sign());
    }

    public static boolean verify(String dataStr, String sign) throws Exception {
        byte[] data = (new BASE64Decoder()).decodeBuffer(dataStr);
        RSAPublicKey publicKeyRsa = loadPublicKey(PUBLIC_KEY);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(publicKeyRsa);
        signature.update(data);
        return signature.verify((new BASE64Decoder()).decodeBuffer(sign));
    }

    public static byte[] decryptByPrivateKey(byte[] encryptedData, RSAPrivateKey privateKeyRsa) throws Exception {
        if (privateKeyRsa == null) {
            throw new Exception("解密私钥为空，请设置");
        } else {
            try {
                Cipher cipher = Cipher.getInstance("RSA");
                cipher.init(2, privateKeyRsa);
                return partitionAlgorithm(encryptedData, cipher, MAX_DECRYPT_BLOCK);
            } catch (NoSuchAlgorithmException var3) {
                throw new Exception("无此解密算法");
            } catch (NoSuchPaddingException var4) {
                var4.printStackTrace();
                return null;
            } catch (InvalidKeyException var5) {
                throw new Exception("解密私钥非法,请检查");
            } catch (IllegalBlockSizeException var6) {
                throw new Exception("密文长度非法");
            } catch (BadPaddingException var7) {
                throw new Exception("密文数据已损坏");
            }
        }
    }

    public static byte[] decryptByPublicKey(byte[] encryptedData, RSAPublicKey publicKeyRsa) throws Exception {
        if (publicKeyRsa == null) {
            throw new Exception("解密公钥为空，请设置");
        } else {
            try {
                Cipher cipher = Cipher.getInstance("RSA");
                cipher.init(2, publicKeyRsa);
                return partitionAlgorithm(encryptedData, cipher, MAX_DECRYPT_BLOCK);
            } catch (NoSuchAlgorithmException var3) {
                throw new Exception("无此解密算法");
            } catch (NoSuchPaddingException var4) {
                var4.printStackTrace();
                return null;
            } catch (InvalidKeyException var5) {
                throw new Exception("解密公钥非法,请检查");
            } catch (IllegalBlockSizeException var6) {
                throw new Exception("密文长度非法");
            } catch (BadPaddingException var7) {
                throw new Exception("密文数据已损坏");
            }
        }
    }

    public static byte[] encryptByPublicKey(byte[] data, RSAPublicKey publicKeyRsa) throws Exception {
        if (publicKeyRsa == null) {
            throw new Exception("加密公钥为空，请设置");
        } else {
            try {
                Cipher cipher = Cipher.getInstance("RSA");
                cipher.init(1, publicKeyRsa);
                return partitionAlgorithm(data, cipher, MAX_ENCRYPT_BLOCK);
            } catch (NoSuchAlgorithmException var3) {
                throw new Exception("无此加密算法");
            } catch (NoSuchPaddingException var4) {
                var4.printStackTrace();
                return null;
            } catch (InvalidKeyException var5) {
                throw new Exception("加密公钥非法,请检查");
            } catch (IllegalBlockSizeException var6) {
                throw new Exception("明文长度非法");
            } catch (BadPaddingException var7) {
                throw new Exception("明文数据已损坏");
            }
        }
    }

    public static byte[] encryptByPrivateKey(byte[] data, RSAPrivateKey privateKeyRsa) throws Exception {
        if (privateKeyRsa == null) {
            throw new Exception("加密私钥为空，请设置");
        } else {
            try {
                Cipher cipher = Cipher.getInstance("RSA");
                cipher.init(1, privateKeyRsa);
                return partitionAlgorithm(data, cipher, MAX_ENCRYPT_BLOCK);
            } catch (NoSuchAlgorithmException var3) {
                throw new Exception("无此加密算法");
            } catch (NoSuchPaddingException var4) {
                var4.printStackTrace();
                return null;
            } catch (InvalidKeyException var5) {
                throw new Exception("加密私钥非法,请检查");
            } catch (IllegalBlockSizeException var6) {
                throw new Exception("明文长度非法");
            } catch (BadPaddingException var7) {
                throw new Exception("明文数据已损坏");
            }
        }
    }

    public static byte[] partitionAlgorithm(byte[] data, Cipher cipher, int maxBlock) throws IOException, BadPaddingException, IllegalBlockSizeException {
        int inputLen = data.length;

        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            Throwable var5 = null;

            byte[] var10;
            try {
                int offSet = 0;

                for(int i = 0; inputLen - offSet > 0; offSet = i * maxBlock) {
                    byte[] cache;
                    if (inputLen - offSet > maxBlock) {
                        cache = cipher.doFinal(data, offSet, maxBlock);
                    } else {
                        cache = cipher.doFinal(data, offSet, inputLen - offSet);
                    }

                    out.write(cache, 0, cache.length);
                    ++i;
                }

                byte[] encryptedData = out.toByteArray();
                var10 = encryptedData;
            } catch (Throwable var20) {
                var5 = var20;
                throw var20;
            } finally {
                if (out != null) {
                    if (var5 != null) {
                        try {
                            out.close();
                        } catch (Throwable var19) {
                            var5.addSuppressed(var19);
                        }
                    } else {
                        out.close();
                    }
                }

            }

            return var10;
        } catch (IllegalBlockSizeException | IOException | BadPaddingException var22) {
            throw var22;
        }
    }

    public static String getPrivateKey(Map<String, Object> keyMap) throws Exception {
        Key key = (Key)keyMap.get(PRIVATE_KEY);
        return (new BASE64Encoder()).encodeBuffer(key.getEncoded());
    }

    public static String getPublicKey(Map<String, Object> keyMap) throws Exception {
        Key key = (Key)keyMap.get(PUBLIC_KEY);
        return (new BASE64Encoder()).encodeBuffer(key.getEncoded());
    }

    public static RSAPublicKey loadPublicKey(String publicKeyStr) throws Exception {
        try {
            BASE64Decoder base64Decoder = new BASE64Decoder();
            byte[] buffer = base64Decoder.decodeBuffer(publicKeyStr);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(buffer);
            return (RSAPublicKey)keyFactory.generatePublic(keySpec);
        } catch (NoSuchAlgorithmException var5) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException var6) {
            throw new Exception("公钥非法");
        } catch (IOException var7) {
            throw new Exception("公钥数据内容读取错误");
        } catch (NullPointerException var8) {
            throw new Exception("公钥数据为空");
        }
    }

    public static RSAPrivateKey loadPrivateKey(String privateKeyStr) throws Exception {
        try {
            BASE64Decoder base64Decoder = new BASE64Decoder();
            byte[] buffer = base64Decoder.decodeBuffer(privateKeyStr);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(buffer);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return (RSAPrivateKey)keyFactory.generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException var5) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException var6) {
            var6.printStackTrace();
            throw new Exception("私钥非法");
        } catch (IOException var7) {
            throw new Exception("私钥数据内容读取错误");
        } catch (NullPointerException var8) {
            throw new Exception("私钥数据为空");
        }
    }

    public static String decryptPrivateWithBase64(String base64String) throws Exception {
        RSAPrivateKey privateKeyRsa = loadPrivateKey(PRIVATE_KEY);
        byte[] binaryData = decryptByPrivateKey((new BASE64Decoder()).decodeBuffer(base64String), privateKeyRsa);
        String string = new String(binaryData, Charset.defaultCharset());
        return string;
    }

    public static String encryptPublicWithBase64(String string) throws Exception {
        RSAPublicKey publicKeyRsa = loadPublicKey(PUBLIC_KEY);
        byte[] binaryData = encryptByPublicKey(string.getBytes("utf-8"), publicKeyRsa);
        String base64String = (new BASE64Encoder()).encodeBuffer(binaryData);
        return base64String;
    }

    public static String decryptPublicWithBase64(String base64String) throws Exception {
        RSAPublicKey publicKeyRsa = loadPublicKey(PUBLIC_KEY);
        byte[] binaryData = decryptByPublicKey((new BASE64Decoder()).decodeBuffer(base64String), publicKeyRsa);
        String string = new String(binaryData, Charset.defaultCharset());
        return string;
    }

    public static String encryptPrivateWithBase64(String string) throws Exception {
        RSAPrivateKey privateKeyRsa = loadPrivateKey(PRIVATE_KEY);
        byte[] binaryData = encryptByPrivateKey(string.getBytes(Charset.defaultCharset()), privateKeyRsa);
        String base64String = (new BASE64Encoder()).encodeBuffer(binaryData);
        return base64String;
    }
    public static String decryptPrivateWithBase64(String base64String,String privateKey) throws Exception {
        RSAPrivateKey privateKeyRsa = loadPrivateKey(privateKey);
        byte[] binaryData = decryptByPrivateKey((new BASE64Decoder()).decodeBuffer(base64String), privateKeyRsa);
        String string = new String(binaryData,Charset.defaultCharset());
        return string;
    }

    public static String encryptPublicWithBase64(String string,String publicKey) throws Exception {
        RSAPublicKey publicKeyRsa = loadPublicKey(publicKey);
        byte[] binaryData = encryptByPublicKey(string.getBytes(Charset.defaultCharset()), publicKeyRsa);
        String base64String = (new BASE64Encoder()).encodeBuffer(binaryData);
        return base64String;
    }

    public static String decryptPublicWithBase64(String base64String,String publicKey) throws Exception {
        RSAPublicKey publicKeyRsa = loadPublicKey(publicKey);
        byte[] binaryData = decryptByPublicKey((new BASE64Decoder()).decodeBuffer(base64String), publicKeyRsa);
        String string = new String(binaryData,Charset.defaultCharset());
        return string;
    }

    public static String encryptPrivateWithBase64(String string,String privateKey) throws Exception {
        RSAPrivateKey privateKeyRsa = loadPrivateKey(privateKey);
        byte[] binaryData = encryptByPrivateKey(string.getBytes(Charset.defaultCharset()), privateKeyRsa);
        String base64String = (new BASE64Encoder()).encodeBuffer(binaryData);
        return base64String;
    }
}

