package cn.tig.repository;

import cn.tig.im.model.PageResult;
import cn.tig.im.vo.EmojiStore;
import org.bson.types.ObjectId;

import java.util.List;


public interface EmojiStoreRepository {
    PageResult<EmojiStore> findByPage(String emojiName, int pageIndex, int pageSize);
    List<EmojiStore> findAll();
    boolean add(String zipPath, String zipName, String zipProfile, String nameStr);
    EmojiStore deleteEmoji(ObjectId emoPackId);
    void deleteEmojiByUserId(int userId);
}
