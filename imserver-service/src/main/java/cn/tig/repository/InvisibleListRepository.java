package cn.tig.repository;

import cn.tig.im.vo.InvisibleList;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * @titel:
 * @author:
 * @package: cn.tig.repository.InvisibleListRepository
 * @fileName: InvisibleListRepository.java
 * @dateTime: 2019/7/5 14:51
 * @description:
 **/
public interface InvisibleListRepository {
    //插入
    InvisibleList add(InvisibleList invisibleList);
    //更新
    InvisibleList update(InvisibleList invisibleList);
    //根据id查找
    InvisibleList findOneById(ObjectId invisibleId);
    //根据区域查找
    List<InvisibleList> findByRegion(String region);
    //根据网段或者IP查找
    List<InvisibleList> findByIp(String ip);
    //查询所有
    List<InvisibleList> findAll();
    List<InvisibleList> findByPage(String mobileDevices, String ip, int pageIndex, int pageSize);
    //删除
    InvisibleList delete(ObjectId invisibleId);

    List<InvisibleList> findAllByRedis();
}
