package cn.tig.im.lable;

import org.springframework.stereotype.Service;

/**
 * @titel:
 * @author:
 * @package: cn.tig.im.lable.UserLabelManager
 * @fileName: UserLabelManager.java
 * @dateTime: 2019/7/4 18:36
 * @description:
 **/
@Service
public interface UserLabelManager {
    Object addLabel(Integer userId,String labelId,String name,String logo,String code,long date);
    Object getUserLabels(Integer userId);
    UserLabel queryUserLabel(Integer userId,String labelId);
    UserLabel queryUserLabelByCode(Integer userId, String code);
}
