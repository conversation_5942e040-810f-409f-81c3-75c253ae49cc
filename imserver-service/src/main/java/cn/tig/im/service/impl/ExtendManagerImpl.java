package cn.tig.im.service.impl;

import cn.tig.commons.constants.KConstants;
import cn.tig.commons.utils.DateUtil;
import cn.tig.commons.utils.StringUtil;
import cn.tig.im.model.PageResult;
import cn.tig.im.service.ExtendManager;
import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.*;
import cn.tig.repository.UserSignInfoRepository;
import cn.tig.repository.UserSignRepository;
import cn.tig.repository.mongo.UserSignInfoRepositoryImpl;
import cn.tig.repository.mongo.UserSignRepositoryImpl;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.tig.im.utils.TigBeanUtils.getUserManager;

/**
 * @titel: 扩展业务处理实体类
 * @author:
 * @package: cn.tig.im.service.impl.ExtendManagerImpl
 * @fileName: ExtendManagerImpl.java
 * @dateTime: 2019/7/4 19:16
 * @description:
 **/
@Service
public class ExtendManagerImpl implements ExtendManager {

    public static UserSignInfoRepository getUserSignInfoRepository(){
        UserSignInfoRepositoryImpl userSignInfoRepository = (UserSignInfoRepositoryImpl) TigBeanUtils.getUserSignInfoRepository();
        return userSignInfoRepository;
    }

    public static UserSignRepository getUserSignRepository(){
        UserSignRepositoryImpl userSignRepository = (UserSignRepositoryImpl) TigBeanUtils.getUserSignRepository();
        return userSignRepository;
    }

    @Override
    public Float userSign(Integer userId, String device, String IP) throws ParseException {
        // 核验用户是否存在
        if (null == getUserManager().getUser(userId)) {
            return -1f;
        }
        //获取签到随机数字
        Config config = TigBeanUtils.getSystemConfig();
        if (config == null){
            return -1f;
        }
        Float addMoney = null;
        if (StringUtil.isNullOrEmpty(config.getMinSignRedPacket()) && StringUtil.isNullOrEmpty(config.getMaxSignRedPacket())){
            addMoney = null;
        }else if (StringUtil.isNullOrEmpty(config.getMinSignRedPacket()) && !StringUtil.isNullOrEmpty(config.getMaxSignRedPacket())){
            addMoney = Float.valueOf(config.getMaxSignRedPacket());
        }else if (!StringUtil.isNullOrEmpty(config.getMinSignRedPacket()) && StringUtil.isNullOrEmpty(config.getMaxSignRedPacket())){
            addMoney = Float.valueOf(config.getMinSignRedPacket());
        }else if (!StringUtil.isNullOrEmpty(config.getMinSignRedPacket()) && !StringUtil.isNullOrEmpty(config.getMaxSignRedPacket())){
            if (config.getMinSignRedPacket().equals(config.getMaxSignRedPacket())){
                addMoney = Float.valueOf(config.getMinSignRedPacket());
            }else {
                addMoney = StringUtil.randomFloat(Float.valueOf(config.getMinSignRedPacket()),Float.valueOf(config.getMaxSignRedPacket()));
            }
        }
        //记录用户签到的记录
        UserSign userSign = new UserSign();
        userSign.setCreateDate(new Date());
        userSign.setDevice(device);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        userSign.setSignDate(df.parse(df.format(new Date())));
        userSign.setSignIP(IP);
        userSign.setStatus(KConstants.STATUS_YES);
        userSign.setId(new ObjectId());
        String signAward = "";
        if (addMoney == null){
            if (signAward.length() == 0){
                signAward = "money:0";
            }else {
                signAward = ",money:0";
            }
        }else {
            if (signAward.length() == 0){
                signAward = "money:"+String.valueOf(addMoney);
            }else {
                signAward = ",money:"+String.valueOf(addMoney);
            }
        }
        userSign.setSignAward(signAward);
        userSign.setUpdateDate(new Date());
        userSign.setUserId(String.valueOf(userId));
        getUserSignRepository().saveUserSign(userSign);
        //处理校验用户签到的信息
        List<UserSignInfo> signInfoList = getUserSignInfoRepository().getUserSignInfoByUserId(userId);
        UserSignInfo userSignInfo = null;
        if (signInfoList != null && signInfoList.size() > 0){
            userSignInfo = signInfoList.get(0);
        }
        //签到无初始化
        if (userSignInfo == null){
            userSignInfo = new UserSignInfo();
            userSignInfo.setCreateDate(new Date());
            userSignInfo.setDialCount(0);
            userSignInfo.setSeriesSignCount(1);
            userSignInfo.setSignCount(1);
            userSignInfo.setSevenCount(1);
            userSignInfo.setStartSignDate(userSign.getSignDate());
            userSignInfo.setUpdateDate(new Date());
            userSignInfo.setUserId(String.valueOf(userId));
            userSignInfo.setId(new ObjectId());
            getUserSignInfoRepository().saveUserSignInfo(userSignInfo);
        }else {
            //判断签到信息只初始化了并没有签到的情况
            if (userSignInfo.getStartSignDate() == null){
                this.updateUserSignInfo(userSignInfo,1,1,1,0,userSign.getSignDate());
            }else {
                Date yesDate = DateUtil.getNestDayZeroTime(new Date(),-1);
                SimpleDateFormat format =  new SimpleDateFormat( "yyyy-MM-dd" );
                List<UserSign> signList = getUserSignRepository().getUserSignByUserIdAndSignDate(userId,format.parse(format.format(yesDate)));
                //用户前一天没有签到
                if (signList == null || signList.size() == 0){
                    this.updateUserSignInfo(userSignInfo,1,userSignInfo.getSignCount()+1,1,userSignInfo.getDialCount(),userSign.getSignDate());
                }else{
                    if (userSignInfo.getSevenCount() == 0){
                        //用户已签到等于0天
                        this.updateUserSignInfo(userSignInfo,userSignInfo.getSeriesSignCount()+1,userSignInfo.getSignCount()+1,1,userSignInfo.getDialCount(),userSign.getSignDate());
                    }else if (userSignInfo.getSevenCount() < 6 && userSignInfo.getSevenCount() > 0){
                        //用户已连续签到小于6天大于0天
                        this.updateUserSignInfo(userSignInfo,userSignInfo.getSeriesSignCount()+1,userSignInfo.getSignCount()+1,userSignInfo.getSevenCount()+1,userSignInfo.getDialCount(),userSignInfo.getStartSignDate());
                    }else if (userSignInfo.getSevenCount() == 6){
                        //用户已签到大于等于6天
                        this.updateUserSignInfo(userSignInfo,userSignInfo.getSeriesSignCount()+1,userSignInfo.getSignCount()+1,userSignInfo.getSevenCount()+1,userSignInfo.getDialCount()+1,userSignInfo.getStartSignDate());
                    }else if (userSignInfo.getSevenCount() > 6){
                        //用户已签到大于等于6天
                        this.updateUserSignInfo(userSignInfo,userSignInfo.getSeriesSignCount()+1,userSignInfo.getSignCount()+1,1,userSignInfo.getDialCount(),userSignInfo.getStartSignDate());
                    }
                }
            }
        }
        if (addMoney != null){
            String tradeNo = StringUtil.getOutTradeNo();
            // 创建充值记录getUserStatusCount
            ConsumeRecord record = new ConsumeRecord();
            record.setUserId(userId);
            record.setTradeNo(tradeNo);
            record.setMoney(Double.valueOf(addMoney));
            record.setStatus(KConstants.OrderStatus.END);
            record.setType(KConstants.ConsumeType.USER_SIGN_PACKET);
            record.setPayType(KConstants.PayType.SYSTEMPAY); // type = 3 ：管理后台充值
            record.setDesc("签到红包");
            record.setTime(DateUtil.currentTimeSeconds());
            TigBeanUtils.getConsumeRecordManager().save(record);
            try {
                //增加余额
                Double resultDou = getUserManager().rechargeUserMoeny(userId, Double.valueOf(addMoney), KConstants.MOENY_ADD);
                if (resultDou > 0){
                    return addMoney;
                }
                return -1f;
            } catch (Exception e) {
                return -1f;
            }
        }
        return 0f;
    }

    @Override
    public void updateUserSignInfo(UserSignInfo userSignInfo, Integer seriesSignCount, Integer signCount, Integer sevenCount, Integer dialCount, Date startSignDate) {
        userSignInfo.setStartSignDate(startSignDate);
        userSignInfo.setSeriesSignCount(seriesSignCount);
        userSignInfo.setSevenCount(sevenCount);
        userSignInfo.setSignCount(signCount);
        userSignInfo.setDialCount(dialCount);
        userSignInfo.setUpdateDate(new Date());
        getUserSignInfoRepository().updateUserSignInfo(userSignInfo);
    }

    @Override
    public Map<String, Object> getUserSignDateByWeek(Integer userId) throws ParseException {
        //处理校验用户签到的信息
        List<UserSignInfo> signInfoList = getUserSignInfoRepository().getUserSignInfoByUserId(userId);
        UserSignInfo userSignInfo = null;
        if (signInfoList != null && signInfoList.size() > 0){
            userSignInfo = signInfoList.get(0);
        }else {
            userSignInfo = new UserSignInfo();
            userSignInfo.setCreateDate(new Date());
            userSignInfo.setDialCount(0);
            userSignInfo.setSeriesSignCount(0);
            userSignInfo.setSignCount(0);
            userSignInfo.setSevenCount(0);
            userSignInfo.setStartSignDate(null);
            userSignInfo.setUpdateDate(new Date());
            userSignInfo.setUserId(String.valueOf(userId));
            userSignInfo.setId(new ObjectId());
            getUserSignInfoRepository().saveUserSignInfo(userSignInfo);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("seriesSignCount",userSignInfo.getSeriesSignCount());
        resultMap.put("sevenCount",userSignInfo.getSevenCount());
        resultMap.put("signCount",userSignInfo.getSignCount());
        Date yesDate = DateUtil.getNestDayZeroTime(new Date(),0);
        SimpleDateFormat format =  new SimpleDateFormat( "yyyy-MM-dd" );
        List<UserSign> signList = getUserSignRepository().getUserSignByUserIdAndSignDate(userId,format.parse(format.format(yesDate)));
        if (signList == null || signList.size() == 0){
            resultMap.put("signStatus", KConstants.STATUS_NO);
            Date oldDate = DateUtil.getNestDayZeroTime(new Date(),-1);
            SimpleDateFormat oldFormat =  new SimpleDateFormat( "yyyy-MM-dd" );
            List<UserSign> oldSignList = getUserSignRepository().getUserSignByUserIdAndSignDate(userId,oldFormat.parse(oldFormat.format(oldDate)));
            //用户前一天没有签到
            if (oldSignList == null || oldSignList.size() == 0){
                Query<UserSignInfo> query = TigBeanUtils.getDatastore().createQuery(UserSignInfo.class).field("id").equal(userSignInfo.getId());
                UpdateOperations<UserSignInfo> updateOperations = TigBeanUtils.getDatastore().createUpdateOperations(UserSignInfo.class);
                updateOperations.set("seriesSignCount", 0);
                updateOperations.set("sevenCount", 0);
                TigBeanUtils.getDatastore().update(query,updateOperations);
                resultMap.put("seriesSignCount",0);
                resultMap.put("sevenCount",0);
            }
        }else {
            resultMap.put("signStatus",KConstants.STATUS_YES);
            resultMap.put("signAward",signList.get(0).getSignAward());
        }
        return resultMap;
    }

    @Override
    public List<UserSign> getUserSignDateByMonth(Integer userId, String monthStr) throws ParseException {
        if (StringUtil.isEmpty(monthStr)){
            return new ArrayList<>();
        }
        Date month = DateUtil.toDate(monthStr);
        Calendar monthCal = Calendar.getInstance();
        monthCal.setTime(month);
        Date firstDay = DateUtil.getFirstDayOfMonth(monthCal.get(Calendar.YEAR),monthCal.get(Calendar.MONTH));
        Date lastDay = DateUtil.getLastDayOfMonth(monthCal.get(Calendar.YEAR),monthCal.get(Calendar.MONTH));
        SimpleDateFormat format =  new SimpleDateFormat( "yyyy-MM-dd" );
        List<UserSign> userSignList = getUserSignRepository().findUserSignByMouth(userId,format.parse(format.format(firstDay)),format.parse(format.format(lastDay)));
        return userSignList;
    }

    @Override
    public Map<String, String> getUserSignCount(Integer userId) {
        return getUserSignRepository().getUserSignCount(userId);
    }

    @Override
    public PageResult<UserSign> signInfoCount(String searchParam, int page, int limit, String startDate, String endDate) throws ParseException {
        PageResult<UserSign> result = new PageResult<>();
        List<UserSign> signList = getUserSignRepository().signInfoCount(result,page,limit,startDate,endDate,searchParam);
        Map<String,String> countMap = new HashMap<>(2);
        if (signList == null || signList.size() <= 0){
            countMap.put("signCount","0");
            countMap.put("signMoneyCount","0.00");
        }else {
            countMap.put("signCount", String.valueOf(signList.size()));
            final BigDecimal[] bigDecimal = {new BigDecimal("0")};
            signList.forEach(userSign -> {
                if (!StringUtil.isNullOrEmpty(userSign.getSignAward())) {
                    String[] money = userSign.getSignAward().split(",");
                    bigDecimal[0] = bigDecimal[0].add(new BigDecimal(money[0].replace("money:", "")));
                }
            });
            countMap.put("signMoneyCount", String.valueOf(bigDecimal[0]));
        }
        result.getData().forEach(userSign -> {
            User user = TigBeanUtils.getUserManager().getUser(Integer.parseInt(userSign.getUserId()));
            if(user == null){
                userSign.setUserName("");
                userSign.setUserAccount("");
                userSign.setUserPhone("");
            }else {
                userSign.setUserName(user.getNickname());
                userSign.setUserAccount(user.getAccount());
                userSign.setUserPhone(user.getTelephone());
            }
        });
        result.setCountMap(countMap);
        return result;
    }

    @Override
    public List<UserSign> exportSignData(int index, int maxNum, String searchParam, String startDate, String endDate) {
        List<UserSign> signList = getUserSignRepository().exportSignData(index,maxNum,startDate,endDate,searchParam);
        signList.forEach(userSign -> {
            User user = TigBeanUtils.getUserManager().getUser(Integer.parseInt(userSign.getUserId()));
            if(user != null){
                userSign.setUserName(user.getNickname());
                userSign.setUserAccount(user.getAccount());
                userSign.setUserPhone(user.getTelephone());
            }else {
                userSign.setUserName("");
                userSign.setUserAccount("");
                userSign.setUserPhone("");
            }
        });
        return signList;
    }
}
