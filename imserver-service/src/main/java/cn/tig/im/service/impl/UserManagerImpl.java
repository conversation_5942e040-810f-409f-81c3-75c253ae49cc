package cn.tig.im.service.impl;

import cn.tig.commons.constants.KConstants;
import cn.tig.commons.constants.MsgType;
import cn.tig.commons.ex.ServiceException;
import cn.tig.commons.support.Callback;
import cn.tig.commons.support.mongo.MongoOperator;
import cn.tig.commons.utils.*;
import cn.tig.commons.vo.JSONMessage;
import cn.tig.commons.vo.PhoneModel;
import cn.tig.im.invite.UserInviteManager;
import cn.tig.im.model.*;
import cn.tig.im.service.UserManager;
import cn.tig.im.utils.ConstantUtil;
import cn.tig.im.utils.KSessionUtil;
import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.*;
import cn.tig.im.vo.User.DeviceInfo;
import cn.tig.im.vo.User.UserLoginLog;
import cn.tig.im.vo.User.UserSettings;
import cn.tig.repository.mongo.UserRepositoryImpl;
import cn.tig.service.KSMSServiceImpl;
import cn.tig.service.KXMPPServiceImpl;
import cn.tig.service.KXMPPServiceImpl.MessageBean;
import cn.tig.service.RedisServiceImpl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserInfoShareResponse;
import com.google.common.collect.Maps;
import com.mongodb.*;
import com.other.AliPayUtil;
import com.other.QQ.QqUserInfoEntity;
import com.other.QQUtil;
import com.other.WeChatUtil;
import com.other.wechat.WeChatAuthInfo;
import com.other.wechat.WeChatUserInfo;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Criteria;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;
import java.util.regex.Pattern;

import static cn.tig.im.utils.TigBeanUtils.getUserManager;

/**
 * @titel: 用户业务实体类
 * @author:
 * @package: cn.tig.im.service.impl.UserManagerImpl
 * @fileName: UserManagerImpl.java
 * @dateTime: 2019/7/4 19:26
 * @description:
 **/
@Service(UserManagerImpl.BEAN_ID)
public class UserManagerImpl extends MongoRepository<User, Integer> implements UserManager {

    public static final Logger log = LoggerFactory.getLogger(UserManagerImpl.class);

    @Override
    public Datastore getDatastore() {
        return TigBeanUtils.getLocalSpringBeanManager().getDatastore();
    }

    @Override
    public Class<User> getEntityClass() {
        return User.class;
    }

    public static final String BEAN_ID = "UserManagerImpl";

    private static UserRepositoryImpl getUserRepository() {
        return TigBeanUtils.getUserRepository();
    }

    private static RedisServiceImpl getRedisServiceImpl() {
        return TigBeanUtils.getRedisService();
    }

    /**
     * 获取邀请码业务实例
     *
     * @return
     */
    private static UserInviteManager getUserInviteManager() {
        return TigBeanUtils.getUserInviteManager();
    }

    @Override
    public User createUser(String telephone, String password) {
        User user = new User();
        user.setUserId(createUserId());
        user.setUserKey(DigestUtils.md5Hex(telephone));
        user.setPassword(DigestUtils.md5Hex(password));
        user.setTelephone(telephone);
        Boolean encryptStatus = TigBeanUtils.getLocalSpringBeanManager().getXMPPConfig().isPasswordEncryptStatus();
        if (encryptStatus && StringUtil.isNullOrEmpty(user.getSalt())) {
            String salt = StringUtil.randomString(16);
            user.setSalt(salt);
        }
        getUserRepository().addUser(user);
        return user;
    }

    @Override
    public void createUser(User user) {
        Boolean encryptStatus = TigBeanUtils.getLocalSpringBeanManager().getXMPPConfig().isPasswordEncryptStatus();
        if (encryptStatus && StringUtil.isNullOrEmpty(user.getSalt())) {
            String salt = StringUtil.randomString(16);
            user.setSalt(salt);
        }
        getUserRepository().addUser(user);
    }

    @Override
    public User.UserSettings getSettings(int userId) {
        User user = getUser(userId);
        if (null == user) {
            return null;
        }
        UserSettings settings = user.getSettings();
        return null != settings ? settings : new UserSettings();
    }

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId
     * @return
     */
    @Override
    public User getUser(int userId) {
        //先从 Redis 缓存中获取
        User user = KSessionUtil.getUserByUserId(userId);
        if (null == user) {
            user = getUserRepository().getUser(userId);
            if (null == user) {
                logger.info("id为" + userId + "的用户不存在");
                return null;
            }
            KSessionUtil.saveUserByUserId(userId, user);
        }

        return user;
    }


    /* (non-Javadoc)
     * @see cn.xyz.mianshi.service.UserManager#getNickName(int)
     */
    @Override
    public String getNickName(int userId) {
        User user = KSessionUtil.getUserByUserId(userId);
        if (user != null) {
            return user.getNickname();
        }
        return (String) queryOneFieldById("nickname", userId);
    }

    public String getAccount(int userId) {
        User user = KSessionUtil.getUserByUserId(userId);
        if (user != null) {
            if (user.getAccount() == null || "".equals(user.getAccount())) {
                return user.getPhone();
            }
            return user.getAccount();
        } else {
            if ("".equals(String.valueOf(queryOneFieldById("account", userId))) || queryOneFieldById("account", userId) == null) {
                return String.valueOf(queryOneFieldById("phone", userId));
            }
            return String.valueOf(queryOneFieldById("account", userId));
        }
    }

    public User getSerInviteCode(String serInviteCode) {
        return queryOne("serInviteCode", serInviteCode);
    }

    public synchronized int getMsgNum(int userId) {
        int userMsgNum = KSessionUtil.getUserMsgNum(userId);
        if (0 != userMsgNum) {
            return userMsgNum;
        }
        List names = distinct("msgNum", new BasicDBObject("_id", userId));
        if (null == names || 0 == names.size()) {
            updateAttributeByIdAndKey(userId, "msgNum", 0);
            return 0;
        }
        userMsgNum = Integer.parseInt(names.get(0).toString());
        KSessionUtil.saveUserMsgNum(userId, userMsgNum);
        return userMsgNum;
    }

    public synchronized void changeMsgNum(int userId, int num) {
        KSessionUtil.saveUserMsgNum(userId, num);
        UpdateOperations<User> operations = createUpdateOperations();
        operations.set("msgNum", num);
        updateAttributeByOps(userId, operations);
    }

    /**
     * @param userId
     * @param status
     * @Description:（锁定解锁用户状态）
     **/
    public void changeStatus(int userId, int status) {
        UpdateOperations<User> operations = createUpdateOperations();
        operations.set("status", status);
        operations.set("forbidUsersTime",DateUtil.currentTimeSeconds());
        if (status == -1) {
//            KXMPPServiceImpl.getInstance().send();
        }
        updateAttributeByOps(userId, operations);
        //维护redis中的数据
        KSessionUtil.removeAccessToken(userId);
        KSessionUtil.deleteUserByUserId(userId);
    }


	/*public String getUserName(int userId) {
		List distinct = userRepository.getCollection().distinct("nickname", new BasicDBObject("_id", userId));
		if(null!=distinct&&0<distinct.size())
			return distinct.get(0).toString();
		return null;
	}*/

    //不经过Redis 直接从数据库获取数据
    public User getUserFromDB(int userId) {
        //先从 Redis 缓存中获取
        User user = getUserRepository().getUser(userId);
        if (null == user) {
            logger.info("id为" + userId + "的用户不存在");
            return null;
        } else {
            KSessionUtil.saveUserByUserId(userId, user);
        }
        return user;
    }


    @Override
    public User getUser(int userId, int toUserId) {
        User user = getUser(toUserId);
        if (null != user) {
            Friends friends = TigBeanUtils.getFriendsManager().getFriends(new Friends(userId, toUserId));
            user.setFriends(null == friends ? null : friends);
            if (userId == toUserId) {
                List<Integer> userRoles = TigBeanUtils.getRoleManager().getUserRoles(userId);
                user.setRole(userRoles);
            }
            user.setPayPassword("");
            // 隐私设置数据
            setUserSettingInfo(user, userId, toUserId);
        } else {
            throw new ServiceException("该用户不存在!");
        }
        return user;
    }

    private void setUserSettingInfo(User user, Integer userId, Integer toUserId) {
        String phone = getUser(userId).getPhone();
        // 上线时间显示
        UserLoginLog loginLog = getDatastore().createQuery(UserLoginLog.class).field("userId").equal(toUserId).get();
        if(loginLog != null){
            user.setShowLastLoginTime(loginLog.getLoginLog() == null ? 0L : loginLog.getLoginLog().getLoginTime());
        }

        if (!StringUtil.isEmpty(phone) && !phone.equals("18938880001")) {

            if (null != user.getSettings()) {
           /*     if (-1 != user.getSettings().getShowLastLoginTime()) {
                    boolean flag = TigBeanUtils.getFriendsManager().isAddressBookOrFriends(userId, toUserId,
                            user.getSettings().getShowLastLoginTime());
                    if (flag && null != loginLog && null != loginLog.getLoginLog()) {
                        user.setShowLastLoginTime(loginLog.getLoginLog().getLoginTime());
                    }
                } else if (-1 == user.getSettings().getShowLastLoginTime() && userId.equals(toUserId)) {
                    if (null != loginLog && null != loginLog.getLoginLog()) {
                        user.setShowLastLoginTime(loginLog.getLoginLog().getLoginTime());
                    }
                }*/
                // 手机号显示
                if (-1 == user.getSettings().getShowTelephone() && !userId.equals(toUserId)) {
                    user.setAreaCode("");
                    user.setTelephone("");
                    user.setPhone("");
                } else if (2 == user.getSettings().getShowTelephone() || 3 == user.getSettings().getShowTelephone()) {
                    if (userId.equals(toUserId)) {
                        return;
                    }
                    boolean flag = TigBeanUtils.getFriendsManager().isAddressBookOrFriends(userId, toUserId, user.getSettings().getShowTelephone());
                    if (!flag) {
                        user.setAreaCode("");
                        user.setTelephone("");
                        user.setPhone("");
                    }
                }
            }
        }

    }

    @Override
    public User getUser(String telephone) {
        //Integer userId=KSessionUtil.getUserIdByTelephone(telephone);
        User user = getUserRepository().getUser(telephone);
        return user;
    }

    @Override
    public User getInviteCode(String regInviteCode) {
        if(StringUtil.isNullOrEmpty(regInviteCode)){
            return  null;
        }
        return getUserRepository().getUserByInviteCode(regInviteCode);
    }

    /**
     * 根据传过来的用户ID，和传过来的品牌ID，选择符合条件的，筛选出userIdList
     *
     * @param userIdList
     * @param appBrandList
     * @return
     */
    @Override
    public List<User.UserLoginLog> getUserIdListByAppBrand(List<Integer> userIdList, List<String> appBrandList) {
        Query<User.UserLoginLog> query = TigBeanUtils.getDatastore().createQuery(User.UserLoginLog.class);
        query.filter("_id in", userIdList);
        query.filter("loginLog.appBrand in", appBrandList);
        List<User.UserLoginLog> findQuery = query.asList();

        return findQuery;
    }

    /**
     * @param @return 参数
     * @Description: TODO(获取登陆过的设备列表)
     */
    public Map<String, DeviceInfo> getLoginDeviceMap(Integer userId) {
        Query<UserLoginLog> query = getDatastore().createQuery(UserLoginLog.class);
        UserLoginLog userLoginLog = query.filter("_id", userId).get();
        if (null == userLoginLog) {
            return null;
        }
        return userLoginLog.getDeviceMap();

    }

    public BigDecimal sumUserBalance() {
      return  getUserRepository().sumUserBalance();
    }

    @Override
    public int getUserId(String accessToken) {
        return 0;
    }

    @Override
    public boolean isRegister(String telephone) {
        return 1 == getUserRepository().getCount(telephone);
    }

    @Override
    public boolean isRegisterForAccount(String account) {
        return 1 == getUserRepository().getCountForAccount(account);
    }

    @Override
    public User login(String telephone, String password) {
        String userKey = DigestUtils.md5Hex(telephone);
        User user = getUserRepository().getUserv1(userKey, null);
        if (null == user) {
            //当他们用账号登录时用到这个
            user = getUserRepository().getUserByAccountAndPwd(telephone, null);
        }
        if (null == user) {
            throw new ServiceException("帐号不存在");
        } else {
            user.setPayPassword("");
            String _md5 = DigestUtils.md5Hex(password);
//			String _md5_md5 = DigestUtils.md5Hex(_md5);

            if (password.equals(user.getPassword()) || _md5.equals(user.getPassword())) {
                return user;
            } else {
                throw new ServiceException("帐号或密码错误");
            }
        }
    }

    public User mpLogin(String telephone, String password) {
        String userKey = DigestUtils.md5Hex(telephone);
        User user = getUserRepository().getUserv1(userKey, null);
        if (null == user) {
            user = getUserRepository().getUserByAccountKeyAndPwd(userKey, null);
        }
        if (null == user) {
            throw new ServiceException("帐号不存在");
        } else {
            if (2 != user.getUserType()) {
                throw new ServiceException("您不是公众号，暂时无法登陆");
            }
            user.setPayPassword("");
            String _md5 = DigestUtils.md5Hex(password);
            String topMsg = this.topUserLock(user);
            if (topMsg != null){
                throw new ServiceException(topMsg);
            }
            if (password.equals(user.getPassword()) || _md5.equals(user.getPassword())) {
                this.isUserLock(user,true,null);
                return user;
            } else {
                String msgStr = this.isUserLock(user,false,"帐号或密码不正确");
                if (msgStr != null){
                    throw new ServiceException(msgStr);
                }
                throw new ServiceException("帐号或密码错误");
            }
        }
    }


    public User withdrawLogin(String telephone, String password) {
        String userKey = DigestUtils.md5Hex(telephone);
        User user = getUserRepository().getUserv1(userKey, null);
        if (null == user) {
            user = getUserRepository().getUserByAccountKeyAndPwd(userKey, null);
        }
        if (null == user) {
            throw new ServiceException("帐号不存在");
        } else {
            if (3 != user.getUserType()) {
                throw new ServiceException("您没有权限登陆");
            }
            user.setPayPassword("");
            String _md5 = DigestUtils.md5Hex(password);
            String topMsg = this.topUserLock(user);
            if (topMsg != null){
                throw new ServiceException(topMsg);
            }
            if (password.equals(user.getPassword()) || _md5.equals(user.getPassword())) {
                this.isUserLock(user,true,null);
                return user;
            } else {
                String msgStr = this.isUserLock(user,false,"帐号或密码不正确");
                if (msgStr != null){
                    throw new ServiceException(msgStr);
                }
                throw new ServiceException("帐号或密码错误");
            }
        }
    }

    @Override
    public Map<String, Object> login(UserExample example) {
        User user = null;
        if (0 != example.getUserId()) {
            user = getUserRepository().getUser(example.getUserId());
        }
//        else
//            user = getUserRepository().getUser(example.getAreaCode(), example.getTelephone(), null);
        if (null == user) {
            user = getUserRepository().getUser(example.getAreaCode(), example.getTelephone(), null);//客户端传来已加密的电话号
        }
        if (null == user) {
            user = getUserRepository().getUserByAccountKeyAndPwd(example.getTelephone(), null);//客户端用户名传过来的值是进行MD5加密
        }
        if (null == user) {
            user = getUserRepository().getUserByAccount(example.getTelephone());//客户端用户名传过来的值是未进行MD5加密
        }
        if (null == user) {
            user = getUserRepository().getUserByPhone(example.getTelephone());//客户端传来未加密的电话号
        }
        if (null == user) {
            throw new ServiceException(KConstants.ResultCode.AccountNotExist, "帐号不存在, 请注册!");
        } else if (-1 == user.getStatus()) {
            throw new ServiceException(KConstants.ResultCode.ACCOUNT_IS_LOCKED, "您的账号已被锁定");
        } else{
            //登录成功后维护客服模块当前用户的会话人数和会话状态
            if (null == user.getUserId()) {
                throw new ServiceException("获取用户信息失败！");
            } else {
                //用户登录加锁
//                boolean loginLimitStatus = TigBeanUtils.getRedisService().acquireUserLogin(String.valueOf(user.getUserId()),KConstants.API_TYPE_APP,null);
//                if (loginLimitStatus){
//                    throw new ServiceException(KConstants.ResultCode.ACCOUNT_IS_LOCKED, "操作过于频繁，请稍后再试");
//                }
//                isForbidUser(user,example.getSerial(),example.getLoginIp());
                String topMsg = this.topUserLock(user);
                if (topMsg != null){
                    TigBeanUtils.getRedisService().releaseUserLogin(String.valueOf(user.getUserId()),KConstants.API_TYPE_APP);
                    throw new ServiceException(KConstants.ResultCode.AccountOrPasswordIncorrect, topMsg);
                }
                if (0 == example.getLoginType()) {
                    // 账号密码登录
                    String password = example.getPassword();
                    if (!password.equals(user.getPassword())) {
                        TigBeanUtils.getRedisService().releaseUserLogin(String.valueOf(user.getUserId()),KConstants.API_TYPE_APP);
                        String msgStr = this.isUserLock(user,false,"帐号或密码不正确");
                        if (msgStr != null){
                            throw new ServiceException(KConstants.ResultCode.AccountOrPasswordIncorrect, msgStr);
                        }
                        throw new ServiceException(KConstants.ResultCode.AccountOrPasswordIncorrect, "帐号或密码错误");
                    }
                } else if (1 == example.getLoginType()) {
                    KSMSServiceImpl smsService = TigBeanUtils.getSMSService();
                    // 短信验证码登录
                    if (null == example.getVerificationCode()) {
                        TigBeanUtils.getRedisService().releaseUserLogin(String.valueOf(user.getUserId()),KConstants.API_TYPE_APP);
                        throw new ServiceException("短信验证码不能为空!");
                    }
                    if (!smsService.isAvailable(user.getTelephone(), example.getVerificationCode())) {
                        TigBeanUtils.getRedisService().releaseUserLogin(String.valueOf(user.getUserId()),KConstants.API_TYPE_APP);
                        String msgStr = this.isUserLock(user,false,"短信验证码不正确");
                        if (msgStr != null){
                            throw new ServiceException(KConstants.ResultCode.AccountOrPasswordIncorrect, msgStr);
                        }
                        throw new ServiceException("短信验证码不正确!");
                    }
                    // 清除短信验证码
                    smsService.deleteSMSCode(user.getTelephone());
                }
                //将用户的客服模式置为关闭
                UserSettings settings = user.getSettings();
                settings.setOpenService(0);
                user.setSettings(settings);
                getUserManager().updateSettings(user.getUserId(), user.getSettings());
                //将客服模块分配状态置为不分配
                TigBeanUtils.getCompanyManager().modifyEmployeesByUserId(user.getUserId());
            }
            return loginSuccess(user, example);
        }

    }
    public void isForbidUser(User user, String serial, String loginIp){
        DeviceHistory deviceHistory = TigBeanUtils.getDatastore().createQuery(DeviceHistory.class).field("userId").equal(user.getUserId()).order("-updateTime").get();
        if(null!=deviceHistory){
            TigBeanUtils.getAdminManager().isForbidDevice(null,user,serial);
            if(1 == user.getIsForbidDevice()){
                TigBeanUtils.getRedisService().releaseUserLogin(String.valueOf(user.getUserId()),KConstants.API_TYPE_APP);
                throw new ServiceException("您的设备已被封禁");
            }
            TigBeanUtils.getAdminManager().isForbidIp(null,user,loginIp);
            if(1 == user.getIsForbidIp()){
                TigBeanUtils.getRedisService().releaseUserLogin(String.valueOf(user.getUserId()),KConstants.API_TYPE_APP);
                throw new ServiceException("您的IP已被封禁");
            }
        }
    }
    public String topUserLock(User user){
        if (user == null){
            return null;
        }
        long lgtime=(System.currentTimeMillis() - user.getLasterrTime())/1000/60;
        if(user.getLasterrTime()!=0 && (lgtime>=KConstants.USER_LOGIN_LOCK_TIME_24)){
            user.setLockStatus(KConstants.STATUS_NO);
            user.setLoginFailCount(0);
            user.setLockTime(Long.valueOf(0));
            user.setLasterrTime(Long.valueOf(0));
            this.updateUserLockInfo(user);
            return null;
        }
        if (user.getLockStatus().equals(KConstants.STATUS_YES)){
            if (user.getLoginFailCount() >= KConstants.USER_LOGIN_FAIL_COUNT_5 && user.getLoginFailCount()<KConstants.USER_LOGIN_FAIL_COUNT_10){
                long time = (System.currentTimeMillis() - user.getLockTime())/1000/60;
                if (time < KConstants.USER_LOGIN_LOCK_TIME_30){
                    return "账号已被锁定，请"+(KConstants.USER_LOGIN_LOCK_TIME_30-time)+"分钟后再试";
                }
            }
            if (user.getLoginFailCount() >= KConstants.USER_LOGIN_FAIL_COUNT_10){
                long time = (System.currentTimeMillis() - user.getLockTime())/1000/60;
                if (time < KConstants.USER_LOGIN_LOCK_TIME_24){
                    if (KConstants.USER_LOGIN_LOCK_TIME_24-time < 60){
                        return "账号已被锁定，请"+(KConstants.USER_LOGIN_LOCK_TIME_24-time)+"分钟后再试";
                    }else {
                        int hours = (int) Math.floor((KConstants.USER_LOGIN_LOCK_TIME_24-time) / 60);
                        long minute = (KConstants.USER_LOGIN_LOCK_TIME_24-time) % 60;
                        if(hours == 0){
                            return "账号已被锁定，请"+minute+"分钟后再试";
                        }
                        if(minute == 0){
                            return "账号已被锁定，请"+hours+"小时后再试";
                        }
                        return "账号已被锁定，请"+hours+"小时"+minute+"分钟后再试";
                    }
                }else {
                    user.setLockStatus(KConstants.STATUS_NO);
                    user.setLoginFailCount(0);
                    user.setLockTime(Long.valueOf(0));
                    user.setLasterrTime(Long.valueOf(0));
                    this.updateUserLockInfo(user);
                }
            }
        }
        return null;
    }
    //用户登录失败次数统计
    public String isUserLock(User user,Boolean status,String failMsg){
        if (user == null){
            return null;
        }

        if (status){
            user.setLockStatus(KConstants.STATUS_NO);
            user.setLoginFailCount(0);
            user.setLockTime(Long.valueOf(0));
            user.setLasterrTime(Long.valueOf(0));
            this.updateUserLockInfo(user);
            return null;
        }else {
            long lgtime=(System.currentTimeMillis() - user.getLasterrTime())/1000/60;
            if(user.getLasterrTime()!=0 && (lgtime>=KConstants.USER_LOGIN_LOCK_TIME_24)){
                user.setLockStatus(KConstants.STATUS_NO);
                user.setLoginFailCount(0);
                user.setLockTime(Long.valueOf(0));
                user.setLasterrTime(Long.valueOf(0));
                this.updateUserLockInfo(user);
            }
            user.setLoginFailCount(user.getLoginFailCount()+1);
            user.setLoginFailTotal(user.getLoginFailTotal()+1);
            user.setLasterrTime(System.currentTimeMillis());
            if (user.getLoginFailCount() == KConstants.USER_LOGIN_FAIL_COUNT_5){
                user.setLockStatus(KConstants.STATUS_YES);
                user.setLockTime(System.currentTimeMillis());
                this.updateUserLockInfo(user);
                return "账号已被锁定，请30分钟后再试";
            }
            if (user.getLoginFailCount() == KConstants.USER_LOGIN_FAIL_COUNT_10){
                user.setLockStatus(KConstants.STATUS_YES);
                user.setLockTime(System.currentTimeMillis());
                this.updateUserLockInfo(user);
                return "账号已被锁定，请"+(KConstants.USER_LOGIN_LOCK_TIME_24/60)+"小时后再试";
            }
            user.setLockStatus(KConstants.STATUS_NO);
            this.updateUserLockInfo(user);
            if (user.getLoginFailCount() < KConstants.USER_LOGIN_FAIL_COUNT_3){
                return failMsg;
            }
            if (user.getLoginFailCount() <= KConstants.USER_LOGIN_FAIL_COUNT_5){
                return failMsg+",还有"+(KConstants.USER_LOGIN_FAIL_COUNT_5-user.getLoginFailCount())+"次重试机会";
            }
            if (user.getLoginFailCount() <= KConstants.USER_LOGIN_FAIL_COUNT_10){
                return failMsg+",还有"+(KConstants.USER_LOGIN_FAIL_COUNT_10-user.getLoginFailCount())+"次重试机会";
            }
        }
        return null;
    }
    public void updateUserLockInfo(User user){
        Query<User> query = getDatastore().createQuery(getEntityClass()).field("_id").equal(user.getUserId());
        UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
        ops.set("lockStatus", user.getLockStatus());
        ops.set("loginFailCount", user.getLoginFailCount());
        ops.set("loginFailTotal", user.getLoginFailTotal());
        ops.set("lockTime", user.getLockTime());
        ops.set("lasterrTime", user.getLasterrTime());
        getDatastore().update(query, ops);
    }


    //查询邀请列表
    @Override
    public PageResult<User> selectInviteUserList(int page, int limit, String serInviteCode) {
        PageResult<User> result = new PageResult<>();
        Query<User> query = getDatastore().createQuery(User.class).field("regInviteCode").equal(serInviteCode);
        query.order("createTime");
        result.setCount(query.count());
        List<User> users = query.asList(pageFindOption(page, limit, 1));
        result.setData(users);
        return result;
    }

    //登陆成功方法
    public Map<String, Object> loginSuccess(User user, UserExample example) {
        if (user == null) {
            return null;
        }
        isForbidUser(user,example.getSerial(),example.getLoginIp());
        //处理用户手机号无归属地问题
        if (user != null && user.getPhoneToLocation() == null && user.getPhone() != null && user.getAreaCode() != null) {
            checkUserPhone(user);
        }
        KSession session = new KSession(user.getTelephone() == null ? "" : user.getTelephone(), user.getUserId() == null ? null : user.getUserId());
        // 获取上次登录日志
        User.LoginLog login = getUserRepository().getLogin(user.getUserId());
        // 保存登录日志
        getUserRepository().updateUserLoginLog(user.getUserId(), example);
//        log.info("login user userid: {} , userKey: {}",user.getUserId(),user.getUserKey());
        // f1981e4bd8a0d6d8462016d2fc6276b3
//        Map<String, Object> data = KSessionUtil.loginSaveAccessToken(user.getUserId(), user.getUserId(), null);
        Map<String, Object> data = KSessionUtil.loginSaveAccessToken(user.getPassword(), user.getUserId(), null);
        Object token = data.get("access_token");
        log.info("login user userid: {} , userKey: {}, access_token: {}", user.getUserId(), user.getUserKey(), token.toString());
        KSessionUtil.saveSession(token.toString(), session);
        data.put("userId", user.getUserId());
        data.put("nickname", user.getNickname());
        data.put("hasRealNameAuth", user.getHasRealNameAuth());
        if (StringUtil.isEmpty(user.getPayPassword())) {
            data.put("payPassword", 0);
        } else {
            data.put("payPassword", 1);
        }
        // 判断如果是第三方sdk登录,返回客户端
        if (example != null && example.getIsSdkLogin() == 1) {
            data.put("telephone", user.getPhone());
            data.put("areaCode", user.getAreaCode());
            data.put("password", user.getPassword());
        }
        if (example != null && 1 == example.getLoginType()) {
            data.put("password", user.getPassword());
        }
        data.put("sex", user.getSex());
        data.put("birthday", user.getBirthday());
        data.put("offlineNoPushMsg", user.getOfflineNoPushMsg());
        data.put("multipleDevices", user.getSettings().getMultipleDevices());
        data.put("login", login);
        data.put("settings", getSettings(user.getUserId()));
        if (StringUtil.isEmpty(login.getSerial())) {
            data.put("isupdate", 1);//用户登陆不同设备，通知客户端更新用户
        } else if (example != null && !login.getSerial().equals(example.getSerial())) {
            data.put("isupdate", 1);
        } else {
            data.put("isupdate", 0);
        }
        Query<Friends> q = getDatastore().createQuery(Friends.class).field("userId").equal(user.getUserId());
        //好友关系数量
        data.put("friendCount", q.countAll());
        // 用户角色
        List<Integer> userRoles = TigBeanUtils.getRoleManager().getUserRoles(user.getUserId());
        if (null != userRoles && userRoles.size() > 0) {
            data.put("role", (0 == userRoles.size() ? "" : userRoles));
        }
        ///检查该用户  是否注册到 Tigase
        examineTigaseUser(user.getUserId(), user.getPassword());
        destroyMsgRecord(user.getUserId());
        // 保存用户登录位置信息
        if (example != null) {
            user.setArea(example.getArea());
            // 地理位置
            User.Loc loc = new User.Loc(example.getLongitude(), example.getLatitude());
            user.setLoc(loc);
            if (example.getLoginIp() != null) {
                user.setLoginIp(example.getLoginIp());
            }
        }
        save(user);
        //查找出该用户的推广形(一码多用)邀请码
        InviteCode myInviteCode = TigBeanUtils.getAdminManager().findUserPopulInviteCode(user.getUserId());
        data.put("myInviteCode", (myInviteCode == null ? "" : myInviteCode.getInviteCode()));
        //saveIosAppId(user.getUserId(), example.getAppId());
        data.put("serInviteCode", user.getSerInviteCode());
        TigBeanUtils.getRoomManagerImplForIM().updateMemberOfflineTime(user.getUserId());
        //给app端扫码登录web端新加的
        if (example != null && example.getLoginType() == 2) {
            data.put("telephone", user.getPhone());
            data.put("password", user.getPassword());
        }
        Boolean encryptStatus = TigBeanUtils.getLocalSpringBeanManager().getXMPPConfig().isPasswordEncryptStatus();
        String password;
        if (encryptStatus) {
            //校验用户是否有密码盐值
            if (StringUtil.isNullOrEmpty(user.getSalt())) {
                String salt = StringUtil.randomString(16);
                user.setSalt(salt);
                Query<User> userQuery = getDatastore().createQuery(getEntityClass()).field("_id").equal(user.getUserId());
                UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
                ops.set("salt", salt);
                getDatastore().findAndModify(userQuery, ops);
            }
            data.put("salt", user.getSalt());
            password = Md5Util.md5Hex(Md5Util.md5Hex(user.getSalt() + Md5Util.md5Hex(user.getPassword() + user.getSalt())));
        } else {
            password = user.getPassword();
        }
        if (!StringUtil.isNullOrEmpty(password)) {
            //修改用户Tigase密码
            DBCollection collection = TigBeanUtils.getTigaseDatastore().getDB().getCollection("tig_users");
            ClientConfig clientConfig = TigBeanUtils.getAdminManager().getClientConfig();
            BasicDBObject tigaseQuery = new BasicDBObject();
            tigaseQuery.put("user_id", user.getUserId() + "@" + clientConfig.getXMPPDomain());
            DBCursor cursor = collection.find(tigaseQuery).limit(1);
            while (cursor.hasNext()) {
                BasicDBObject dbObj = (BasicDBObject) cursor.next();
                if (!password.equals(dbObj.getString("password"))) {
                    collection.update(tigaseQuery, new BasicDBObject("$set", new BasicDBObject("password", password)));
                }
            }
        }
        if((example.getLoginIp()!=null&&!"".equals(example.getLoginIp()))||(example.getSerial()!=null&&!"".equals(example.getSerial()))){
            ThreadUtil.executeInThread(obj -> {
                Query<DeviceHistory> deviceHistory = getDatastore().createQuery(DeviceHistory.class).field("loginIp").equal(example.getLoginIp()).field("deviceInfo").equal(example.getSerial()).field("userId").equal(user.getUserId());
                if (null == deviceHistory.get()) {
                    DeviceHistory history = new DeviceHistory();
                    history.setLoginIp(example.getLoginIp());
                    history.setDeviceInfo(example.getSerial());
                    history.setLoginNum(1);
                    history.setAccount(user.getAccount());
                    history.setDevice(example.getAppBrand());
                    history.setCreateTime(DateUtil.currentTimeSeconds());
                    history.setUpdateTime(DateUtil.currentTimeSeconds());
                    history.setUserId(user.getUserId());
                    ObjectId id = (ObjectId) getDatastore().save(history).getId();
                    history.setId(id);
                } else {
                    UpdateOperations<DeviceHistory> ops = getDatastore().createUpdateOperations(DeviceHistory.class);
                    ops.set("account", user.getAccount());
                    ops.set("loginNum", deviceHistory.get().getLoginNum() + 1);
                    ops.set("updateTime", DateUtil.currentTimeSeconds());
                    getDatastore().findAndModify(deviceHistory, ops);
                }
            });
        }
        this.isUserLock(user,true,null);
        TigBeanUtils.getRedisService().releaseUserLogin(String.valueOf(user.getUserId()),KConstants.API_TYPE_APP);
        return data;
    }

    @Override
    public Map<String, Object> loginAuto(String access_token, int userId, String serial, String appId, double latitude, double longitude, String loginIp,String appBrand) {
        User user = getUserFromDB(userId);
        if (null == user) {
            throw new ServiceException(1040101, "帐号不存在, 请重新注册!");
        } else if (-1 == user.getStatus()) {
            throw new ServiceException(KConstants.ResultCode.ACCOUNT_IS_LOCKED, "您的账号已被锁定");
        }
        isForbidUser(user,serial,loginIp);
        //处理用户手机号无归属地问题
        if (user.getPhoneToLocation() == null && user.getPhone() != null && user.getAreaCode() != null) {
            checkUserPhone(user);
        }
        User.LoginLog loginLog = getUserRepository().getLogin(userId);
        String atKey = KSessionUtil.getUserIdBytoken(access_token);
        boolean exists = TigBeanUtils.getRedisCRUD().keyExists(atKey);
        // 1=没有设备号、2=设备号一致、3=设备号不一致
        int serialStatus = null == loginLog ? 1 : (serial.equals(loginLog.getSerial()) ? 2 : 3);
        // 1=令牌存在、0=令牌不存在
        int tokenExists = exists ? 1 : 0;
        try {
            Map<String, Object> result = Maps.newHashMap();
            result.put("serialStatus", serialStatus);
            result.put("tokenExists", tokenExists);
            result.put("userId", userId);
            result.put("nickname", user.getNickname());
            result.put("name", user.getName());
            result.put("login", loginLog);
            result.put("settings", getSettings(userId));
            result.put("serialStatus", serialStatus);
            result.put("multipleDevices", user.getSettings().getMultipleDevices());
            // 用户角色
            List<Integer> userRoles = TigBeanUtils.getRoleManager().getUserRoles(user.getUserId());
            if (null != userRoles && userRoles.size() > 0) {
                result.put("role", (0 == userRoles.size() ? "" : userRoles));
            }
            if (StringUtil.isEmpty(user.getPayPassword())) {
                result.put("payPassword", "0");
            } else {
                result.put("payPassword", "1");
            }
            //查找出该用户的推广型邀请码(一码多用)
            InviteCode myInviteCode = TigBeanUtils.getAdminManager().findUserPopulInviteCode(user.getUserId());
            result.put("myInviteCode", (myInviteCode == null ? "" : myInviteCode.getInviteCode()));
            updateLoc(latitude, longitude, userId);
            //更新用户登录IP
            updateUserLoginIp(userId, loginIp);
            getUserRepository().updateLoginLogTime(userId);
            examineTigaseUser(userId, user.getPassword());
            destroyMsgRecord(userId);
            TigBeanUtils.getRoomManagerImplForIM().updateMemberOfflineTime(userId);
            Boolean encryptStatus = TigBeanUtils.getLocalSpringBeanManager().getXMPPConfig().isPasswordEncryptStatus();
            String password;
            if (encryptStatus) {
                //校验用户是否有密码盐值
                if (StringUtil.isNullOrEmpty(user.getSalt())) {
                    String salt = StringUtil.randomString(16);
                    user.setSalt(salt);
                    UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
                    ops.set("salt", salt);
                    Query<User> userQuery = getDatastore().createQuery(getEntityClass()).field("_id").equal(user.getUserId());
                    getDatastore().findAndModify(userQuery, ops);
                }
                //用户秘钥
                result.put("salt", user.getSalt());
                password = Md5Util.md5Hex(Md5Util.md5Hex(user.getSalt() + Md5Util.md5Hex(user.getPassword() + user.getSalt())));
            } else {
                password = user.getPassword();
            }
            if (!StringUtil.isNullOrEmpty(password)) {
                ClientConfig clientConfig = TigBeanUtils.getAdminManager().getClientConfig();
                BasicDBObject tigaseQuery = new BasicDBObject();
                tigaseQuery.put("user_id", user.getUserId() + "@" + clientConfig.getXMPPDomain());
                //修改用户Tigase密码
                DBCollection collection = TigBeanUtils.getTigaseDatastore().getDB().getCollection("tig_users");
                DBCursor cursor = collection.find(tigaseQuery).limit(1);
                while (cursor.hasNext()) {
                    BasicDBObject dbObj = (BasicDBObject) cursor.next();
                    if (!password.equals(dbObj.getString("password"))) {
                        collection.update(tigaseQuery, new BasicDBObject("$set", new BasicDBObject("password", password)));
                    }
                }
            }
            if((loginIp!=null&&!"".equals(loginIp))||(serial!=null&&!"".equals(serial))){
                ThreadUtil.executeInThread(obj -> {
                    Query<DeviceHistory> deviceHistory = getDatastore().createQuery(DeviceHistory.class).field("loginIp").equal(loginIp).field("deviceInfo").equal(serial).field("userId").equal(user.getUserId());
                    if (null == deviceHistory.get()) {
                        DeviceHistory history = new DeviceHistory();
                        history.setLoginIp(loginIp);
                        history.setDeviceInfo(serial);
                        history.setLoginNum(1);
                        history.setAccount(user.getAccount());
                        history.setDevice(appBrand);
                        history.setCreateTime(DateUtil.currentTimeSeconds());
                        history.setUpdateTime(DateUtil.currentTimeSeconds());
                        history.setUserId(user.getUserId());
                        ObjectId id = (ObjectId) getDatastore().save(history).getId();
                        history.setId(id);
                    } else {
                        UpdateOperations<DeviceHistory> ops = getDatastore().createUpdateOperations(DeviceHistory.class);
                        ops.set("loginNum", deviceHistory.get().getLoginNum() + 1);
                        ops.set("updateTime", DateUtil.currentTimeSeconds());
                        ops.set("account", user.getAccount());
                        getDatastore().findAndModify(deviceHistory, ops);
                    }
                });
            }
            return result;
        } catch (NullPointerException e) {
            throw new ServiceException("帐号不存在");
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    private void updateLoc(double latitude, double longitude, Integer userId) {
        User.Loc loc = new User.Loc(longitude, latitude);
        UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
        ops.set("active", DateUtil.currentTimeSeconds());
        ops.set("loc", loc);
        updateAttributeByOps(userId, ops);
    }

    public void savePushToken(Integer userId, DeviceInfo info) {
        log.info("userid: {}, device info ===> {}", userId, JSON.toJSONString(info));
        PushInfo pushInfo;
        try {
            Query<PushInfo> query1 = getDatastore().createQuery(PushInfo.class);
            query1.filter("userId", userId);
            getDatastore().delete(query1);
            Query<PushInfo> query2 = getDatastore().createQuery(PushInfo.class);
            query2.filter("pushServer", info.getPushServer());
            query2.filter("pushToken", info.getPushToken());
            query2.filter("deviceKey", info.getDeviceKey());
            List<PushInfo> pushInfos = query2.asList();
            if (pushInfos != null && pushInfos.size() > 0) {
                getDatastore().delete(query2);
                pushInfos.forEach(e -> {
                    if (KConstants.DeviceKey.Android.equals(e.getDeviceKey())) {
                        KSessionUtil.removeAndroidPushToken(e.getUserId());
                    } else if (KConstants.DeviceKey.IOS.equals(e.getDeviceKey())) {
                        KSessionUtil.removeIosPushToken(e.getUserId());
                    }
                    KSessionUtil.removeUserPushToken(e.getUserId());
                });
            }
        } catch (Exception e) {
            log.info("device info save error===> {}", e);
        }
//        //去除所有绑定有该设备信息的用户
//        if (pushInfos == null || pushInfos.size() > 1){
//            pushInfo = null;
//            if (pushInfos != null || pushInfos.size() > 0){
//                getDatastore().delete(query1);
//                pushInfos.forEach(pushInfoData -> {
//                    if (KConstants.DeviceKey.Android.equals(pushInfoData.getDeviceKey())) {
//                        KSessionUtil.removeAndroidPushToken(pushInfoData.getUserId());
//                    } else if (KConstants.DeviceKey.IOS.equals(pushInfoData.getDeviceKey())) {
//                        KSessionUtil.removeIosPushToken(pushInfoData.getUserId());
//                    }
//                    KSessionUtil.removeUserPushToken(pushInfoData.getUserId());
//                });
//            }
//        }else{
//            pushInfo = pushInfos.get(0);
//        }
//        if (null != pushInfo) {
//            if (!userId.equals(pushInfo.getUserId())) {
//                cleanPushToken(pushInfo.getUserId(), info.getDeviceKey());
//                UpdateOperations<PushInfo> ops = getDatastore().createUpdateOperations(PushInfo.class);
//                ops.set("userId", userId);
//                ops.set("time", DateUtil.currentTimeSeconds());
//                getDatastore().update(query1, ops);
//            } else {
//                UpdateOperations<PushInfo> ops = getDatastore().createUpdateOperations(PushInfo.class);
//                ops.set("time", DateUtil.currentTimeSeconds());
//                getDatastore().update(query1, ops);
//            }
//        } else {
        pushInfo = new PushInfo();
        pushInfo.setUserId(userId);
        pushInfo.setPushServer(info.getPushServer());
        pushInfo.setPushToken(info.getPushToken());
        pushInfo.setDeviceKey(info.getDeviceKey());
        pushInfo.setTime(DateUtil.currentTimeSeconds());
        getDatastore().save(pushInfo);
//        }

        Query<UserLoginLog> query = getDatastore().createQuery(UserLoginLog.class);
        query.filter("_id", userId);
        UpdateOperations<UserLoginLog> ops = getDatastore().createUpdateOperations(UserLoginLog.class);
        try {
            if (!StringUtil.isEmpty(info.getDeviceKey())) {
                ops.set("deviceMap." + info.getDeviceKey() + ".pushServer", info.getPushServer());
                ops.set("deviceMap." + info.getDeviceKey() + ".pushToken", info.getPushToken());
            }
            if (KConstants.DeviceKey.IOS.equals(info.getDeviceKey())) {
                if (!StringUtil.isEmpty(info.getAppId())) {
                    ops.set("deviceMap." + info.getDeviceKey() + ".appId", info.getAppId());
                }
            }
            getDatastore().update(query, ops);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void saveVoipPushToken(Integer userId, String token) {
		/*Query<UserLoginLog> query=getDatastore().createQuery(UserLoginLog.class);
		query.filter("_id", userId);
		UpdateOperations<UserLoginLog> ops = getDatastore().createUpdateOperations(UserLoginLog.class);
		try {
				//ops.set("deviceMap."+KConstants.DeviceKey.IOS+".pushServer",info.getPushServer());
				ops.set("deviceMap."+KConstants.DeviceKey.IOS+".voipToken",token);
				//ops.set("deviceMap."+KConstants.DeviceKey.IOS+".appId",appId);

			getDatastore().update(query, ops);
		} catch (Exception e) {
			e.printStackTrace();
		}*/
        DeviceInfo deviceInfo = KSessionUtil.getIosPushToken(userId);
        if (null != deviceInfo) {
            deviceInfo.setVoipToken(token);
            KSessionUtil.saveIosPushToken(userId, deviceInfo);
        }
    }

    public void saveIosAppId(Integer userId, String appId) {
        if (StringUtil.isEmpty(appId)) {
            return;
        }
        Query<UserLoginLog> query = getDatastore().createQuery(UserLoginLog.class);
        query.filter("_id", userId);
        UpdateOperations<UserLoginLog> ops = getDatastore().createUpdateOperations(UserLoginLog.class);
        try {
            ops.set("deviceMap." + KConstants.DeviceKey.IOS + ".appId", appId);
            getDatastore().update(query, ops);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void cleanPushToken(Integer userId, String devicekey) {
        Query<UserLoginLog> query = getDatastore().createQuery(UserLoginLog.class);
        query.field("_id").equal(userId);
        UpdateOperations<UserLoginLog> ops = getDatastore().createUpdateOperations(UserLoginLog.class);
        ops.set("loginLog.offlineTime", DateUtil.currentTimeSeconds());
        TigBeanUtils.getRoomManagerImplForIM().updateMemberOfflineTime(userId);
        try {
            if (KConstants.DeviceKey.Android.equals(devicekey)) {
                KSessionUtil.removeAndroidPushToken(userId);
            } else if (KConstants.DeviceKey.IOS.equals(devicekey)) {
                KSessionUtil.removeIosPushToken(userId);
            }
            if (!StringUtil.isEmpty(devicekey)) {
                ops.set("deviceMap." + devicekey + ".pushServer", "");
                ops.set("deviceMap." + devicekey + ".pushToken", "");
            }
            getDatastore().update(query, ops);
            KSessionUtil.removeUserPushToken(userId);
            Query<User> query1 = getDatastore().createQuery(User.class).field("userId").equal(userId);
            UpdateOperations<User> ops1 = getDatastore().createUpdateOperations(User.class);
            ops1.set("onlinestate", 0);
            getDatastore().findAndModify(query1, ops1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void logout(String access_token, String areaCode, String userKey, String devicekey) {

        cleanPushToken(ReqUtil.getUserId(), devicekey);
//		KSessionUtil.removeAccessToken(ReqUtil.getUserId());
    }

    @Override
    public List<DBObject> query(UserQueryExample param) {
        return getUserRepository().queryUser(param);
    }

    public List<User> queryPublicUser(int page, int limit, String keyWorld) {
        Query<User> query = createQuery();
        query.filter("userType", 2);
        if (!StringUtil.isEmpty(keyWorld)) {
            // 是否为数字
            if (StringUtil.isNumeric(keyWorld)) {
                int userId = Integer.parseInt(keyWorld);
                query.or(query.criteria("_id").equal(userId), query.criteria("nickname").containsIgnoreCase(keyWorld));
            } else {
                query.criteria("nickname").containsIgnoreCase(keyWorld);
            }
        }
        return query.offset(page * limit).limit(limit).asList();
    }

    @Override
    public Map<String, Object> register(UserExample example) {
        if (isRegister(example.getTelephone())) {
            throw new ServiceException("手机号已被注册");
        }
        //生成userId
        Integer userId = createUserId();
        //新增用户
        try {
            Map<String, Object> data = getUserRepository().addUser(userId, example);
            if (null != data) {
                KXMPPServiceImpl.getInstance().registerByThread(userId.toString(), example.getPassword());
                return data;
            }
        } catch (UnsupportedEncodingException e) {
            throw new ServiceException("用户注册失败");
        }
        throw new ServiceException("用户注册失败");
    }

    /**
     * @titel:
     * @author:
     * @moduleName: initUserData
     * @dateTime: 2019/8/8 16:02
     * @param: [example, userId, data]
     * @return: void
     * @description: 初始化用户数据
     **/
    public void initUserData(UserExample example, Integer userId, Map<String, Object> data) {
        Boolean encryptStatus = TigBeanUtils.getLocalSpringBeanManager().getXMPPConfig().isPasswordEncryptStatus();
        if (encryptStatus && !StringUtil.isNullOrEmpty(example.getSalt())) {
            String slatPass = example.getSalt() + Md5Util.md5Hex(example.getPassword() + example.getSalt());
            String md5Pass = Md5Util.md5Hex(Md5Util.md5Hex(slatPass));
            KXMPPServiceImpl.getInstance().registerByThread(userId.toString(), md5Pass);
        } else {
            KXMPPServiceImpl.getInstance().registerByThread(userId.toString(), example.getPassword());
        }
        TigBeanUtils.getFriendsManager().followUser(userId, 10000, 0);
        ThreadUtil.executeInThread(obj -> {
            // 默认成为好友
            defaultTelephones(example, userId);
            //默认加入群组
            defaultRoom(example, userId);
        });
        // 调用组织架构功能示例方法
        TigBeanUtils.getCompanyManager().autoJoinCompany(userId);
        // 自动创建 好友标签
        TigBeanUtils.getFriendGroupManager().autoCreateGroup(userId);
        if (example.getUserType() != null) {
            if (example.getUserType() == 3) {
                TigBeanUtils.getRoomManager().join(userId, new ObjectId("5a2606854adfdc0cd071485e"), 3);
            }
        }
        //更新通讯录好友
        Object creteTime = data.get("createTime");
        String valueOf = String.valueOf(creteTime);
        Long time = Long.valueOf(valueOf);
        TigBeanUtils.getLocalSpringBeanManager().getAddressBookManger().notifyBook(example.getTelephone(), userId, example.getNickname(), time);
        // 清除redis中没有系统号的表
        TigBeanUtils.getRedisService().deleteNoSystemNumUserIds();
        // 清除短信验证码
        TigBeanUtils.getSMSService().deleteSMSCode(example.getTelephone());
        // 维护公众号角色
        this.defendPublicNumber(example, userId);
    }

    /**
     * 注册IM用户
     *
     * @param example
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, Object> registerIMUser(UserExample example, String apiType) throws Exception {
        try {
//            TigBeanUtils.getRedisService().acquireRegisterUserTelephone(example.getTelephone());
            if (example.getRegisterType() == 0 && example.getTelephone() != null && !"".equals(example.getTelephone())) {
                TigBeanUtils.getRedisService().acquireRegisterUserTelephone(example.getTelephone());//手机号注册加锁
                if (isRegister(example.getTelephone())) {
                    throw new ServiceException("手机号已被注册");
                }
            }
            // 生成userId
            else if (example.getRegisterType() == 1 && example.getAccount() != null && !"".equals(example.getAccount())) {
                TigBeanUtils.getRedisService().acquireRegisterUserAccount(example.getAccount());//用户名注册加锁
                if (isRegisterForAccount(example.getAccount()) || isRegister(example.getAreaCode() + example.getAccount())) {
                    throw new ServiceException("用户名已被注册");
                }
            }
            // 生成userId
            Integer userId = createUserId();
            // 核验邀请码,及相关操作
            User user = this.checkInviteCode(example, userId, apiType);
            //example.setAccount(userId+StringUtil.randomCode());
            //设置手机号归属地
            try {
                if (example.getTelephone() != null && !"".equals(example.getTelephone())) {
                    checkUserPhone(example);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 新增用户
            String inviteCodeStr = RandomUtil.getRandomByIndex(4);
            example.setSerInviteCode(inviteCodeStr);
            Map<String, Object> data = getUserRepository().addUser(userId, example);
            if (null != data) {
                this.initUserData(example, userId, data);
//                if(user != null){
//                    addFriend(user.getUserId(),userId);
//                }
                return data;
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (example.getRegisterType() == 0 && example.getTelephone() != null && !"".equals(example.getTelephone())) {
                TigBeanUtils.getRedisService().releaseRegisterUserTelephone(example.getTelephone());//手机号注册解锁
            } else if (example.getRegisterType() == 1 && example.getAccount() != null && !"".equals(example.getAccount())) {
                TigBeanUtils.getRedisService().releaseRegisterUserAccount(example.getAccount());//用户名注册解锁
            }
        }
        throw new ServiceException("用户注册失败");
    }

    private void addFriend(Integer inviteUserId,Integer userSelfId){
        if(inviteUserId == null){
            return;
        }
        TigBeanUtils.getFriendsManager().consoleaddFriends(userSelfId,inviteUserId);// 过滤好友验证直接成为好友
        TigBeanUtils.getRedisService().deleteFriends(inviteUserId);
        TigBeanUtils.getRedisService().deleteFriends(userSelfId);
    }

    /**
     * 注册IM用户（第三方账号）
     *
     * @param example
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, Object> registerIMUser(UserExample example, int type, String loginInfo, String apiType) throws Exception {
        try {
            // 检查手机号是否已经注册
            // 检查手机号是否已经注册
            if (example.getRegisterType() == 0 && example.getTelephone() != null && !"".equals(example.getTelephone()) && isRegister(example.getTelephone())) {
                throw new ServiceException("手机号已被注册");
            }
            // 生成userId
            else if (example.getRegisterType() == 1 && example.getAccount() != null && !"".equals(example.getAccount()) && isRegisterForAccount(example.getAccount()) || isRegister(example.getAreaCode() + example.getAccount())) {
                throw new ServiceException("用户名已被注册");
            }
            // 生成userId
            Integer userId = createUserId();
            // 核验邀请码,及相关操作
            this.checkInviteCode(example, userId, apiType);
            //设置手机号归属地
            if (example.getTelephone() != null && !"".equals(example.getTelephone())) {
                this.checkUserPhone(example);
            }
            // 新增用户
            Map<String, Object> data = getUserRepository().addUser(userId, example);
            if (null != data) {
                this.initUserData(example, userId, data);
                // 新增第三方登录
                ThreadUtil.executeInThread(obj -> {
                    SdkLoginInfo entity = new SdkLoginInfo();
                    entity.setUserId(userId);
                    entity.setType(type);
                    entity.setLoginInfo(loginInfo);
                    getDatastore().save(entity);
                });
                return data;
            }
        } catch (Exception e) {
            throw e;
        }
        throw new ServiceException("用户注册失败");
    }

    /**
     * @titel: 维护公众号角色
     * @author:
     * @moduleName: defendPublicNumber
     * @dateTime: 2019/8/8 16:51
     * @param: [example, userId]
     * @return: void
     * @description:
     **/
    public void defendPublicNumber(UserExample example, Integer userId) {
        if (example.getUserType() != null && example.getUserType() == 2) {
            ThreadUtil.executeInThread(obj -> {
                Role role = new Role(userId, example.getTelephone(), (byte) 2, (byte) 1, 0);
                getDatastore().save(role);
                TigBeanUtils.getRoleManager().updateFriend(userId, 2);
            });
        }
    }

    /**
     * 屏蔽 某人的 朋友圈
     */
    public void filterCircleUser(int toUserId) {
        Integer userId = ReqUtil.getUserId();
        UserSettings settings = getSettings(userId);
        if (null == settings.getFilterCircleUserIds()) {
            settings.setFilterCircleUserIds(new HashSet<>());
        }
        settings.getFilterCircleUserIds().add(toUserId);
        updateSettings(userId, settings);
    }

    /**
     * 取消 屏蔽 某人的 朋友圈
     */
    public void cancelFilterCircleUser(int toUserId) {
        Integer userId = ReqUtil.getUserId();
        UserSettings settings = getSettings(userId);
        if (null != settings.getFilterCircleUserIds()) {
            settings.getFilterCircleUserIds().remove(toUserId);
            updateSettings(userId, settings);
        }

    }

    /**
     * 检查注册邀请码的及相关处理
     *
     * @param example 用户信息
     * @param userId  用户ID
     * @param apiType
     * @return
     */
    private User checkInviteCode(UserExample example, int userId, String apiType) {
        //获取系统当前的邀请码模式 0:关闭   1:开启一对一邀请(一码一用)    2:开启一对多邀请(一码多用)
        int inviteCodeMode = TigBeanUtils.getAdminManager().getConfig().getRegisterInviteCode();
        if (inviteCodeMode == 0) { //关闭
            return null;
        }
        if(StringUtil.isEmpty(example.getInviteCode())){
            log.info("注册接口，邀请码不能为空 inviteCode:{}",example.getInviteCode());
            throw new ServiceException("邀请码不能为空");
        }

        User user = TigBeanUtils.getUserManager().getInviteCode(example.getInviteCode());
        if(user == null){
            log.info("注册接口，邀请码无效 inviteCode:{}",example.getInviteCode());
            throw new ServiceException("邀请码无效");
        }
        return user;

       /* InviteCode inviteCode = TigBeanUtils.getAdminRepository().findInviteCodeByCode(example.getInviteCode() == null ? "" : example.getInviteCode().replace(" ", ""));
        if (inviteCodeMode == 1) { //开启一对一邀请
            //该模式下邀请码为必填项
            if (!KConstants.API_TYPE_ADMIN.equals(apiType)) {
                if (StringUtil.isEmpty(example.getInviteCode())) {
                    throw new ServiceException("请填写邀请码");
                }
                //检查用户填写的邀请码的合法性
                if (inviteCode == null || !(inviteCode.getTotalTimes() == 1 && inviteCode.getStatus() == 0)) { //status = 0; //状态值 0,为初始状态未使用   1:已使用  -1 禁用
                    throw new ServiceException("邀请码无效或已被使用");
                }
                //更新邀请码数据
                //将邀请码的使用次数加1
                inviteCode.setUsedTimes(inviteCode.getUsedTimes() + 1);
                inviteCode.setStatus((short) 1);
                inviteCode.setLastuseTime(System.currentTimeMillis());
                TigBeanUtils.getAdminRepository().saveInviteCode(inviteCode);
            }
            //给注册用户生成一个自己的一对一邀请码
            String inviteCodeStr = RandomUtil.idToSerialCode(DateUtil.currentTimeSeconds() + getUserManager().createInviteCodeNo(1) + 1); //生成邀请码
            TigBeanUtils.getAdminRepository().saveInviteCode(new InviteCode(userId, inviteCodeStr, System.currentTimeMillis(), 1));
            example.setMyInviteCode(inviteCodeStr);
        } else if (inviteCodeMode == 2) { //开启一对多邀请
            //该模式下邀请码为选填项,不强制要求填写
            //检查用户填写的邀请码的合法性
            if (!StringUtil.isNullOrEmpty(example.getInviteCode()) && inviteCode == null) {
                throw new ServiceException("邀请码无效");
            }
            if (KConstants.API_TYPE_ADMIN.equals(apiType) || inviteCode == null) {
                //生成自已一对多的推广码
                String inviteCodeStr = RandomUtil.getRandomByIndex(8);
                InviteCode myInviteCode = new InviteCode(userId, inviteCodeStr, System.currentTimeMillis(), -1);
                TigBeanUtils.getAdminRepository().saveInviteCode(myInviteCode);
                return;
            }
            if (inviteCode != null && inviteCode.getTotalTimes() != 1) { //邀请码合法
                //推广邀请码绑定用户
                boolean status = TigBeanUtils.getUserInviteManager().addUserInviteMember(inviteCode, String.valueOf(userId));
                //判断用户是否加入成功
                InviteConfig inviteConfig = TigBeanUtils.getLocalSpringBeanManager().getApplicationConfig().getInviteConfig();
                if (status && inviteConfig.isPassCardIsOpen()) {
                    inviteCode.setInviteCount(inviteCode.getInviteCount() + 1);
                    //处理邀请通证业务
                    Integer resultCount = TigBeanUtils.getUserInviteManager().handleUserPassCard(inviteCode, String.valueOf(userId), example.getNickname());
                    inviteCode.setPassCardCount(inviteCode.getPassCardCount() + resultCount);
                }
                //更新邀请码数据
                //将邀请码的使用次数加1
                inviteCode.setUsedTimes(inviteCode.getUsedTimes() + 1);
                inviteCode.setStatus((short) 1);
                inviteCode.setLastuseTime(System.currentTimeMillis());
                TigBeanUtils.getAdminRepository().saveInviteCode(inviteCode);
                //生成自已一对多的推广码
                String inviteCodeStr = RandomUtil.getRandomByIndex(8);
                InviteCode myInviteCode = new InviteCode(userId, inviteCodeStr, System.currentTimeMillis(), -1);
                TigBeanUtils.getAdminRepository().saveInviteCode(myInviteCode);
                example.setSerInviteCode(inviteCodeStr);
            }*/
           /* if (inviteCode == null) {
                throw new ServiceException("邀请码无效");
            }*/
    }

    /**
     * 处理用户手机号
     *
     * @param example
     */
    public void checkUserPhone(UserExample example) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        if (!StringUtil.isNullOrEmpty(example.getPhone()) && pattern.matcher(example.getPhone()).matches()) {
            //设置手机号归属地
            PhoneModel phoneModel = PhoneUtils.getPhoneModel(example.getPhone(), Integer.parseInt(example.getAreaCode()));
            //处理归属地信息
            if (phoneModel != null) {
                if (phoneModel.getProvinceName() == null) {
                    String phoneToLocation = phoneModel.getCityName() == null ? "" : phoneModel.getCityName();
                    example.setPhoneToLocation(phoneToLocation);
                } else {
                    String phoneToLocation = phoneModel.getProvinceName() + (phoneModel.getCityName() == null ? "" : "-" + phoneModel.getCityName());
                    example.setPhoneToLocation(phoneToLocation);
                }
                example.setCarrier(phoneModel.getCarrier());
            }
        } else {
            example.setPhoneToLocation("未知区域");
            example.setCarrier("未知");
        }
    }

    /**
     * 处理用户手机号
     *
     * @param user
     */
    public void checkUserPhone(User user) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        if (pattern.matcher(user.getPhone()).matches()) {
            //设置手机号归属地
            PhoneModel phoneModel = PhoneUtils.getPhoneModel(user.getPhone(), Integer.parseInt(user.getAreaCode()));
            //处理归属地信息
            if (phoneModel != null) {
                getUserRepository().updatePhoneToLocation(user.getUserId(), phoneModel);
            }
        } else {
            PhoneModel phoneModel = new PhoneModel();
            phoneModel.setCarrier("未知");
            phoneModel.setProvinceName("未知区域");
            if (phoneModel != null) {
                getUserRepository().updatePhoneToLocation(user.getUserId(), phoneModel);
            }
        }

    }

    /**
     *
     * @param example
     * @param userId
     * @Description:加入系统指定默认房间
     */
    private void defaultRoom(UserExample example, Integer userId){
        String rooms = TigBeanUtils.getSystemConfig().getDefaultRooms();

        if (!StringUtil.isEmpty(rooms)) {
            String[] roomids = StringUtil.getStringList(rooms);
            for (int i = 0; i < roomids.length; i++) {
                Room room = getRoomDatastore().createQuery(Room.class).field("_id").equal(new ObjectId(roomids[i])).get();
                if (null == room) {
                    continue;
                }
                User user = new User();
                user.setUserId(1);
                user.setNickname("系统");
                List userlist=new ArrayList();
                userlist.add(userId);
                try {
                    TigBeanUtils.getRoomManagerImplForIM().consoleJoinRoom(user, new ObjectId(roomids[i]), userlist);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

    }

    /**
     * @param example
     * @param userId
     * @Description:（注册默认成为好友）
     **/
    private void defaultTelephones(UserExample example, Integer userId) {
        try {
            // 注册默认成为好友
            String telephones = TigBeanUtils.getSystemConfig().getDefaultTelephones();
            log.info(" config defaultTelephones : " + telephones);
            if (!StringUtil.isEmpty(telephones)) {
                String[] phones = StringUtil.getStringList(telephones);
                for (int i = 0; i < phones.length; i++) {
                    User user = getUser(Integer.parseInt(phones[i]));
//                    User user = getUser(new StringBuffer().append(phones[i]).toString());
                    if (null == user) {
                        continue;
                    }
//					TigBeanUtils.getFriendsManager().followUser(userId, user.getUserId(),0);
                    TigBeanUtils.getFriendsManager().consoleaddFriends(userId, user.getUserId());// 过滤好友验证直接成为好友
                    TigBeanUtils.getRedisService().deleteFriends(userId);
                    TigBeanUtils.getRedisService().deleteFriends(user.getUserId());
                }
            }
        } catch (ServiceException e) {
            e.printStackTrace();
        }
    }


    /**
     * 管理后台自动创建用户 或者 群组
     *
     * @param userNum
     * @param roomId
     * @return
     */
    //@Override
    public void autoCreateUserOrRoom(int userNum, String roomId) {

        ThreadUtil.executeInThread(obj -> {
            boolean isJoinRoom = false;
            ObjectId objRoomId = null;
            if (roomId != null && !roomId.isEmpty()) {
                objRoomId = new ObjectId(roomId);
                isJoinRoom = true;
            }
            addRobot(userNum, isJoinRoom, objRoomId);
        });
    }

    public List<Integer> addRobot(int userNum, boolean isJoinRoom, ObjectId objRoomId) {
        Random rand = new Random();
        List<Integer> userIds = new ArrayList<Integer>();
        UserExample userExample = new UserExample();
        //3=机器账号，由系统自动生成
        userExample.setAreaCode("86");
        userExample.setBirthday(DateUtil.currentTimeSeconds());
        userExample.setCountryId(ValueUtil.parse(0));
        userExample.setProvinceId(ValueUtil.parse(0));
        userExample.setCityId(ValueUtil.parse(400300));
        userExample.setAreaId(ValueUtil.parse(0));
        for (int i = 1; i <= userNum; i++) {
            //生成userId
            Integer userId = createUserId();
            userIds.add(userId);
            String name = i % 3 == 0 ? RandomUtil.getRandomZh(rand.nextInt(3) + 2) : RandomUtil.getRandomEnAndNum(rand.nextInt(4) + 2);
            userExample.setPassword(DigestUtils.md5Hex("" + (userId - 1000) / 2));
            userExample.setTelephone("86" + String.valueOf(userId));
            userExample.setPhone(String.valueOf(userId));
            userExample.setName(name);
            userExample.setNickname(name);
            userExample.setDescription(String.valueOf(userId));
            userExample.setSex(userId % 2 == 0 ? 0 : 1);
            userExample.setUserType(3);
            //设置手机号归属地
            checkUserPhone(userExample);
            try {
                if (userId != 0 && getUserRepository().addUser(userId, userExample) != null) {
                    try {
                        KXMPPServiceImpl.getInstance().registerAndXmppVersion(userId.toString(), userExample.getPassword());
                        logger.info("第" + i + "条用户数据已经生成");

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    throw new ServiceException("自动生成用户数据失败");
                }
            } catch (UnsupportedEncodingException e) {
                throw new ServiceException("自动生成用户数据失败");
            }
            if (isJoinRoom) {
                Integer userSize = TigBeanUtils.getRoomManager().getRoom(objRoomId).getUserSize();//1 - 5
                int maxUserSize = TigBeanUtils.getSystemConfig().getMaxUserSize();
                if (userSize + 1 > maxUserSize) {
                    log.info("群人数已达到上限，不能继续加入。当前上限人数" + maxUserSize);
                    throw new ServiceException("群人数已达到上限，不能继续加入");
                }
                TigBeanUtils.getRoomManager().joinRoom(userId, name, objRoomId, 2);
            }
            // 角色信息
            Role role = new Role(userId, String.valueOf(userId), (byte) 3, (byte) 1, 0);
            getDatastore().save(role);
        }


        return userIds;
    }

    @Override
    public void resetPassword(String telephone, String newPassword) {
        User user = getUser(telephone);
        getUserRepository().updatePassword(telephone, newPassword);
        KSessionUtil.removeAccessToken(user.getUserId());
        KXMPPServiceImpl.getInstance().changePassword(user.getUserId() + "", user.getPassword(), newPassword);
    }

    public void resetPassword(int userId, String newPassword) {
        if (get(userId).getPassword().equals(newPassword)) {
            throw new ServiceException("重置的密码不能与旧密码相同");
        }
        User user = getUserFromDB(userId);
        getUserRepository().updatePassWord(userId, newPassword);
        KSessionUtil.removeAccessToken(userId);
        KXMPPServiceImpl.getInstance().changePassword(userId + "", user.getPassword(), newPassword);
    }

    @Override
    public void updatePassword(int userId, String oldPassword, String newPassword) {
        User user = getUserFromDB(userId);
		/*String _md5 = DigestUtils.md5Hex(oldPassword);
		String _md5_md5 = DigestUtils.md5Hex(_md5);*/
		/*|| _md5.equals(user.getPassword())
		|| _md5_md5.equals(user.getPassword())*/
        if (oldPassword.equals(user.getPassword())) {
            getUserRepository().updatePassWord(userId, newPassword);
            KSessionUtil.removeAccessToken(userId);
            KXMPPServiceImpl.getInstance().changePassword(userId + "", user.getPassword(), newPassword);
        } else {
            throw new ServiceException("旧密码错误");
        }
    }

    @Override
    public User updateSettings(int userId, User.UserSettings userSettings) {
        User user = getUserRepository().updateSettings(userId, userSettings);
        user.setPayPassword("");
        KSessionUtil.deleteUserByUserId(userId);
        return user;
    }

    public void sendMessage(String jid, int chatType, int type, String content, String fileName) {
        Integer userId = ReqUtil.getUserId();
        MessageBean messageBean = new MessageBean();
        messageBean.setType(type);
        messageBean.setFromUserId(userId.toString());
        messageBean.setFromUserName(getNickName(userId));
        messageBean.setToUserId(jid);
        messageBean.setTo(jid);

        if (1 == chatType) {
            messageBean.setMsgType(0);
            messageBean.setToUserName(getNickName(Integer.parseInt(jid)));
        } else {
            messageBean.setMsgType(1);
            messageBean.setRoomJid(jid);
        }

        messageBean.setContent(content);
        messageBean.setFileName(fileName);
        messageBean.setTimeSend(DateUtil.currentTimeSeconds());
        messageBean.setMessageId(StringUtil.randomCode());
        KXMPPServiceImpl.getInstance().send(messageBean);

        /**
         * 发送给自己
         */
        messageBean.setMsgType(0);
        messageBean.setTo(userId.toString());
        KXMPPServiceImpl.getInstance().send(messageBean);

    }

    /**
     * 用户 绑定微信 openId
     *
     * @param userId
     * @param code
     */
    public Object bindWxopenid(int userId, String code) {
        if (StringUtil.isEmpty(code)) {
            return null;
        }
        JSONObject jsonObject = WXUserUtils.getWxOpenId(code);
        String openid = jsonObject.getString("openid");
        if (StringUtil.isEmpty(openid)) {
            return null;
        }
        logger.info(String.format("======> bindWxopenid  userId %s  openid  %s", userId, openid));
        updateAttribute(userId, "openid", openid);
        return jsonObject;
    }

    public void bindAliUserId(int userId, String aliUserId) {
        if (StringUtil.isEmpty(aliUserId)) {
            return;
        }
        updateAttribute(userId, "aliUserId", aliUserId);
    }

    /**
     * 修改用户信息
     *
     * @param userId 用户ID
     * @param param  用户更新后的数据
     * @return
     */
    @Override
    public User updateUser(int userId, UserExample param) {
        User user = getUserRepository().updateUser(userId, param);
        if (StringUtil.isEmpty(user.getPayPassword())) {
            user.setPayPassword("0");
        } else {
            user.setPayPassword("1");
        }

        return user;
    }

    /**
     * 实名认证
     *
     * @param userId 用户ID
     * @param param  用户更新后的数据
     * @return
     */
    @Override
    public User realNameAuth(int userId, UserExample param) {
        if(StringUtil.isNullOrEmpty(param.getName())){
           throw new ServiceException("姓名不能为空");
        }
        if(StringUtil.isNullOrEmpty(param.getIdcard())){
            throw new ServiceException("身份证号码不能为空");
        }
        if(StringUtil.isNullOrEmpty(param.getIdcardUrl())){
            throw new ServiceException("身份证正面不能为空");
        }
        if(StringUtil.isNullOrEmpty(param.getIdcardBackUrl())){
            throw new ServiceException("身份证反面不能为空");
        }
        User user = getUserRepository().realNameAuth(userId, param);
        if (StringUtil.isEmpty(user.getPayPassword())) {
            user.setPayPassword("0");
        } else {
            user.setPayPassword("1");
        }

        return user;
    }

    /**
     * 审核实名认证
     *
     * @param userId 用户ID
     * @return
     */
    @Override
    public User checkRealNameAuth(int userId, Integer status) {
        if(status == null || status.equals("0")){
           throw new ServiceException("状态不能为空");
        }
        User user = getUserRepository().checkRealNameAuth(userId, status);
        if (StringUtil.isEmpty(user.getPayPassword())) {
            user.setPayPassword("0");
        } else {
            user.setPayPassword("1");
        }

        return user;
    }

    public List<User> findUserList(int pageIndex, int pageSize, Integer notId) {
        Query<User> query = createQuery();
        List<Integer> ids = new ArrayList<Integer>() {{
            add(10000);
            add(10005);
            add(10006);
            add(notId);
        }};
        query.field("_id").notIn(ids);
        return query.order("-_id").offset(pageIndex * pageSize).limit(pageSize).asList();
    }

    /**
     * 查找对应类型的用户数据
     *
     * @param pageIndex
     * @param pageSize
     * @param userType
     * @return
     */
    public List<User> findUserList(int pageIndex, int pageSize, String keyworld, short userType) throws ServiceException {

        return getUserRepository().searchUsers(pageIndex, pageSize, keyworld, userType);
    }


    @Override
    public List<DBObject> findUser(int pageIndex, int pageSize) {
        return getUserRepository().findUser(pageIndex, pageSize);
    }


    @Override
    public List<Integer> getAllUserId() {
        return getDatastore().getCollection((getClass())).distinct("_id");
    }


    @Override
    public void outtime(String access_token, int userId) {
        Query<UserLoginLog> q = getDatastore().createQuery(UserLoginLog.class).field("_id").equal(userId);
        UserLoginLog loginLog = q.get();
        if (null == q.get()) {
            return;
        }
        if (null == loginLog.getLoginLog()) {
            return;
        }
        UpdateOperations<UserLoginLog> ops = getDatastore().createUpdateOperations(UserLoginLog.class);
        ops.set("loginLog.offlineTime", DateUtil.currentTimeSeconds());
        getDatastore().findAndModify(q, ops);
        TigBeanUtils.getRoomManagerImplForIM().updateMemberOfflineTime(userId);
    }

    @Override
    public void addUser(int userId, String password) {
        getUserRepository().addUser(userId, password);
    }

    /**
     * @param @param userId    参数
     * @Description: TODO(销毁该用户 已过期的 聊天记录)
     */
    public void destroyMsgRecord(int userId) {
        ThreadUtil.executeInThread(obj -> {
//            DBCursor cursor = null;
            try {
                DBCollection dbCollection = TigBeanUtils.getTigaseDatastore().getDB().getCollection("tig_msgs");
                DBCollection lastdbCollection = TigBeanUtils.getTigaseDatastore().getDB().getCollection("tig_lastChats");
                BasicDBObject query = new BasicDBObject();
                BasicDBObject lastquery = new BasicDBObject();
                query.append("sender", userId)
                        .append("deleteTime", new BasicDBObject(MongoOperator.GT, 0)
                                .append(MongoOperator.LT, DateUtil.currentTimeSeconds()))
                        .append("isRead", 1);
                BasicDBObject base = (BasicDBObject) dbCollection.findOne(query);
                BasicDBList queryOr = new BasicDBList();
                if (base != null) {
                    queryOr.add(new BasicDBObject("jid", String.valueOf(base.get("sender"))).append("userId", base.get("receiver").toString()));
                    queryOr.add(new BasicDBObject("userId", String.valueOf(base.get("sender"))).append("jid", base.get("receiver").toString()));
                    lastquery.append(MongoOperator.OR, queryOr);
                }
                // 删除文件
                query.append("contentType", new BasicDBObject(MongoOperator.IN, MsgType.FileTypeArr));
                List<String> fileList = dbCollection.distinct("content", query);
                for (String url : fileList) {
                    ConstantUtil.deleteFile(url);
                }
                query.remove("contentType");
                dbCollection.remove(query); //将消息记录中的数据删除
                // 重新查询一条消息记录插入
                BasicDBList baslist = new BasicDBList();
                if (base != null) {
                    baslist.add(new BasicDBObject("receiver", base.get("sender")));
                    baslist.add(new BasicDBObject("sender", base.get("sender")));
                    query.append(MongoOperator.OR, baslist);
                }
                query.remove("sender");
                query.remove("deleteTime");
                query.remove("isRead");
                DBObject lastMsgObj = dbCollection.find(query).sort(new BasicDBObject("timeSend", -1)).limit(1).one();

                if (lastMsgObj != null) {
                    BasicDBObject values = new BasicDBObject();
                    values.put("messageId", lastMsgObj.get("messageId"));
                    values.put("timeSend", new Double(lastMsgObj.get("timeSend").toString()).longValue());
                    values.put("content", lastMsgObj.get("content"));
                    if (!lastquery.isEmpty()) {
                        lastdbCollection.update(lastquery, new BasicDBObject(MongoOperator.SET, values), false, true);
                    }
                } else {
                    if (!lastquery.isEmpty()) {
                        lastdbCollection.remove(lastquery);
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            } finally {
//                if (cursor != null) {
//                    cursor.close();
//                }
            }
        });


    }
	/*@Override
	public User getfUser(int userId) {
		User user = userRepository.getUser(userId);
		if (null == user)
			return null;
		if (0 != user.getCompanyId())
			user.setCompany(companyManager.get(user.getCompanyId()));
			return user;
	}*/

    //用户充值 type 1 充值  2 消费
    public synchronized Double rechargeUserMoeny(Integer userId, Double money, int type) {
        try {
            Query<User> q = getDatastore().createQuery(getEntityClass());
            q.field("_id").equal(userId);
            UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
            User user = getUser(userId);
            if (null == user || money == null || money == 0) {
                logger.info("用户不存在或操作金额为空---->用户ID："+userId+"+,操作金额："+money);
                return 0.0;
            }
//            money = new BigDecimal(money).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//            double balance = new BigDecimal(user.getBalance()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            if (KConstants.MOENY_ADD == type) {
                double balance = new BigDecimal(money).add(new BigDecimal(user.getBalance())).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                logger.info("操作余额---->用户ID："+userId+",当前积分："+user.getScore()+",操作金额："+money+",操作后的金额："+balance);
//                user.setScore(String.valueOf(balance));
//                ops.set("score",user.getScore());
                user.setBalance(balance);
                ops.set("balance",balance);
                ops.inc("totalRecharge", money);
            } else {
                double balance = BigDecimal.valueOf(user.getBalance()).subtract(new BigDecimal(money)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                logger.info("操作余额---->用户ID："+userId+",当前积分："+user.getScore()+",操作金额："+money+",操作后的金额："+balance);
//                user.setScore(String.valueOf(balance));
//                ops.set("score",user.getScore());
                user.setBalance(balance);
                ops.set("balance",balance);
                ops.inc("totalConsume", money);
            }
            getDatastore().update(q, ops);
            KSessionUtil.saveUserByUserId(userId, user);
            return Double.parseDouble(q.get().getScore());
        } catch (Exception e) {
            logger.error("操作余额异常",e);
            e.printStackTrace();
            return 0.0;
        }
    }

    //用户充值 type 1 充值  2 消费
    public Double getUserMoeny(Integer userId) {
        try {
            Query<User> q = getDatastore().createQuery(getEntityClass());
            q.field("_id").equal(userId);

            return q.get().getBalance();
        } catch (Exception e) {
            e.printStackTrace();
            return 0.0;
        }
    }


    public int getOnlinestateByUserId(Integer userId) {
        return (int) queryOneFieldById("onlinestate", userId);
    }


    public void examineTigaseUser(Integer userId, String password) {
        new Thread(() -> {
            try {
                DBObject query = new BasicDBObject("user_id", userId + "@" + TigBeanUtils.getXMPPConfig().getServerName());
                BasicDBObject result = (BasicDBObject) getTigaseDatastore().getDB().getCollection("tig_users").findOne(query);
                if (null == result) {
                    KXMPPServiceImpl.getInstance().registerByThread(String.valueOf(userId), password);
                }
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }).start();


    }

    public void report(Integer userId, Integer toUserId, int reason, String roomId, String webUrl) {

        if (toUserId == null && StringUtil.isEmpty(roomId) && StringUtil.isEmpty(webUrl)) {
            throw new ServiceException(KConstants.ResultCode.ParamsAuthFail);
        }
        Report report = new Report();
        report.setUserId(userId);
        if (toUserId != null) {
            report.setToUserId(toUserId);
        }
        report.setReason(reason);
        report.setRoomId(roomId);

        if (!StringUtil.isEmpty(webUrl)) {
            report.setWebUrl(webUrl);
        }
        report.setWebStatus(1);
        report.setTime(DateUtil.currentTimeSeconds());
        saveEntity(report);

    }

    public boolean checkReportUrlImpl(String webUrl) {
        try {
            URL requestUrl = new URL(webUrl);
            webUrl = requestUrl.getHost();
        } catch (Exception e) {
            throw new ServiceException("参数对应的URL格式错误");
        }
        logger.info("URL HOST :" + webUrl);
        List<Report> reportList = getDatastore().createQuery(Report.class).field("webUrl").contains(webUrl).asList();
        if (null != reportList && reportList.size() > 0) {
            reportList.forEach(report -> {
                if (null != report && -1 == report.getWebStatus()) {
                    throw new ServiceException("该网页地址已被举报");
                }
            });
        }
        return true;
    }


    /**
     * @param type      0：用户相关，1：群组相关  2：web网页
     * @param sender
     * @param receiver
     * @param pageIndex
     * @param pageSize
     * @return
     * @Description: 获取举报列表
     **/
    public Map<String, Object> getReport(int type, int sender, String receiver, int pageIndex, int pageSize) {
        Map<String, Object> dataMap = Maps.newConcurrentMap();
        List<Report> data = null;
        try {
            if (type == 0) {
                Query<Report> q = getDatastore().createQuery(Report.class);
                if (0 != sender) {
                    q.field("userId").equal(sender);
                }
                if (!StringUtil.isEmpty(receiver)) {
                    q.field("toUserId").equal(Long.valueOf(receiver));
                } else {
                    q.field("toUserId").notEqual(0);
                }
                q.field("roomId").equal("");
                q.order("-time");
                q.offset(pageSize * pageIndex);
                data = q.limit(pageSize).asList();
                for (Report report : data) {
                    report.setUserName(getNickName((int) report.getUserId()));
                    report.setToUserName(getNickName((int) report.getToUserId()));
                    if (KConstants.ReportReason.reasonMap.containsKey(report.getReason())) {
                        report.setInfo(KConstants.ReportReason.reasonMap.get(report.getReason()));
                    }
                    if (null == getUser(Integer.parseInt(String.valueOf(report.getToUserId())))) {
                        report.setToUserStatus(-1);
                    } else {
                        Integer status = getUser(Integer.parseInt(String.valueOf(report.getToUserId()))).getStatus();
                        report.setToUserStatus(status);
                    }
                }
                dataMap.put("count", q.count());
            } else if (type == 1) {
                Query<Report> q = getDatastore().createQuery(Report.class);
                if (0 != sender) {
                    q.field("userId").equal(sender);
                }
                if (!StringUtil.isEmpty(receiver)) {
                    q.field("roomId").equal(receiver);
                }
                q.field("roomId").notEqual("");
                q.order("-time");
                q.offset(pageSize * pageIndex);
                for (Report report : q.asList()) {
                    report.setUserName(getNickName((int) report.getUserId()));
                    report.setRoomName(TigBeanUtils.getRoomManager().getRoomName(new ObjectId(report.getRoomId())));
                    Integer roomStatus = TigBeanUtils.getRoomManager().getRoomStatus(new ObjectId(report.getRoomId()));
                    if (null != roomStatus) {
                        report.setRoomStatus(roomStatus);
                    }
                    if (KConstants.ReportReason.reasonMap.containsKey(report.getReason())) {
                        report.setInfo(KConstants.ReportReason.reasonMap.get(report.getReason()));
                    }
                }
                data = q.limit(pageSize).asList();
                dataMap.put("count", q.count());
            } else if (type == 2) {
                Query<Report> q = getDatastore().createQuery(Report.class);
                if (0 != sender) {
                    q.field("userId").equal(sender);
                }
                if (!StringUtil.isEmpty(receiver)) {
                    q.field("webUrl").equal(receiver);
                }
                q.field("webUrl").notEqual(null);
                q.field("toUserId").equal(0);
                q.order("-time");
                for (Report report : q.asList()) {
                    report.setUserName(getNickName((int) report.getUserId()));
                    if (KConstants.ReportReason.reasonMap.containsKey(report.getReason())) {
                        report.setInfo(KConstants.ReportReason.reasonMap.get(report.getReason()));
                    }
                }
                q.offset(pageSize * pageIndex);
                data = q.limit(pageSize).asList();
                dataMap.put("count", q.count());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        dataMap.put("data", data);
        return dataMap;
    }

    /**
     * @param userId
     * @param roomId
     * @Description: 删除相关的举报信息
     **/
    public void delReport(Integer userId, String roomId) {
        Query<Report> query = getDatastore().createQuery(Report.class);
        if (null != userId) {
            query.or(query.criteria("userId").equal(userId), query.criteria("toUserId").equal(userId));
        } else if (null != roomId) {
            query.field("roomId").equal(roomId);
        }
        getDatastore().delete(query);
    }

    // 删除 被删除的用户得账单记录
    public void delRecord(Integer userId) {
        Query<ConsumeRecord> query = getDatastore().createQuery(ConsumeRecord.class);
        query.field("userId").equal(userId);
        getDatastore().delete(query);
    }

    // 清除被删除用户相关组织架构数据
    public void delCompany(Integer userId) {
        try {
            // 删除对应创建的公司数据
            Query<Company> ownCompanyQuery = getDatastore().createQuery(Company.class).field("createUserId").equal(userId);
            List<Company> companyList = ownCompanyQuery.asList();
            for (Company company : companyList) {
                getDatastore().delete(company);
                // 对应的部门、员工数据
                Query<Department> departMentQuery = getDatastore().createQuery(Department.class).field("companyId").equal(company.getId());
                getDatastore().delete(departMentQuery);
                Query<Employee> ownEmployeeQuery = getDatastore().createQuery(Employee.class).field("companyId").equal(company.getId());
                getDatastore().delete(ownEmployeeQuery);
            }
            // 退出对应部门 删除员工数据
            Query<Employee> employeeQuery = getDatastore().createQuery(Employee.class).field("userId").equal(userId);
            List<Employee> employees = employeeQuery.asList();
            for (Employee employee : employees) {
                // 维护对应部门人数
                Query<Department> departMentQuery = getDatastore().createQuery(Department.class).field("_id").equal(employee.getDepartmentId());
                UpdateOperations<Department> ops = getDatastore().createUpdateOperations(Department.class);
                int empNum = departMentQuery.get().getEmpNum();
                int departEmpNum = empNum - 1;
                ops.set("empNum", departEmpNum);
                getDatastore().findAndModify(departMentQuery, ops);
                // 维护公司员工人数
                Query<Company> companyQuery = getDatastore().createQuery(Company.class).field("_id").equal(employee.getCompanyId());
                int companyEmpNum = companyQuery.get().getEmpNum();
                UpdateOperations<Company> companyOps = getDatastore().createUpdateOperations(Company.class);
                int num = companyEmpNum - 1;
                companyOps.set("empNum", num);
                getDatastore().findAndModify(companyQuery, companyOps);
            }
            getDatastore().delete(employeeQuery);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    //获取用户Id
    public synchronized Integer createUserId() {
        DBCollection collection = getDatastore().getDB().getCollection("idx_user");
        if (null == collection) {
            return createIdxUserCollection(null, 0);
        }
        DBObject obj = collection.findOne();
        if (null != obj) {
            Integer id = Integer.valueOf(obj.get("id").toString());
            id += 1;
            collection.update(new BasicDBObject("_id", obj.get("_id")),
                    new BasicDBObject(MongoOperator.INC, new BasicDBObject("id", 1)));
            return id;
        } else {
            return createIdxUserCollection(collection, 0);
        }

    }

    //获取Call
    public synchronized Integer createCall() {
        DBCollection collection = getDatastore().getDB().getCollection("idx_user");
        if (null == collection) {
            return createIdxUserCollection(null, 0);
        }
        DBObject obj = collection.findOne();
        if (null != obj) {
            if (obj.get("call") == null) {
                obj.put("call", 300000);
            }
            Integer call = Integer.valueOf(obj.get("call").toString());
            call += 1;
            if (call > 349999) {
                call = 300000;
            }
            collection.update(new BasicDBObject("_id", obj.get("_id")), new BasicDBObject(MongoOperator.SET, new BasicDBObject("call", call)));
            return call;
        } else {
            return createIdxUserCollection(collection, 0);
        }
    }

    //获取videoMeetingNo
    public synchronized Integer createvideoMeetingNo() {
        DBCollection collection = getDatastore().getDB().getCollection("idx_user");
        if (null == collection) {
            return createIdxUserCollection(collection, 0);
        }
        DBObject obj = collection.findOne();
        if (null != obj) {
            if (obj.get("videoMeetingNo") == null) {
                obj.put("videoMeetingNo", 350000);
            }
            Integer videoMeetingNo = Integer.valueOf(obj.get("videoMeetingNo").toString());
            videoMeetingNo += 1;
            if (videoMeetingNo > 399999) {
                videoMeetingNo = 350000;
            }
            collection.update(new BasicDBObject("_id", obj.get("_id")), new BasicDBObject(MongoOperator.SET, new BasicDBObject("videoMeetingNo", videoMeetingNo)));
            return videoMeetingNo;
        } else {
            return createIdxUserCollection(collection, 0);
        }
    }

    //获取注册邀请码计数值
    @Override
    public synchronized Integer createInviteCodeNo(int createNum) {
        DBCollection collection = getDatastore().getDB().getCollection("idx_user");
        if (null == collection) {
            createIdxUserCollection(null, 0);
        } else {
            DBObject obj = collection.findOne();
            if (null != obj) {
                if (obj.get("inviteCodeNo") == null) {
                    obj.put("inviteCodeNo", 1001);
                }
                Object inviteCodeNoObj = obj.get("inviteCodeNo");
                Integer inviteCodeNo = Integer.valueOf(inviteCodeNoObj == null ? "0" : inviteCodeNoObj.toString());
                collection.update(new BasicDBObject("_id", obj.get("_id")), new BasicDBObject(MongoOperator.INC, new BasicDBObject("inviteCodeNo", createNum)));
                return inviteCodeNo;
            } else {
                createIdxUserCollection(collection, 0);
            }
        }
        return Integer.valueOf(1);
    }

    //初始化自增长计数表数据
    private Integer createIdxUserCollection(DBCollection collection, long userId) {
        if (null == collection) {
            collection = getDatastore().getDB().createCollection("idx_user", new BasicDBObject());
        }
        BasicDBObject init = new BasicDBObject();
        Integer id = getMaxUserId();
        if (0 == id || id < KConstants.MIN_USERID) {
            id = Integer.valueOf("10000001");
        }
        id += 1;
        init.append("id", id);
        init.append("stub", "id");
        init.append("call", 300000);
        init.append("videoMeetingNo", 350000);
        init.append("inviteCodeNo", 1001);
        collection.insert(init);
        return id;
    }

    public Integer getMaxUserId() {
        BasicDBObject projection = new BasicDBObject("_id", 1);
        DBObject dbobj = getDatastore().getDB().getCollection("user").findOne(null, projection, new BasicDBObject("_id", -1));
        if (null == dbobj) {
            return 0;
        }
        Integer id = Integer.valueOf(dbobj.get("_id").toString());
        return id;
    }

    public Integer getServiceNo(String areaCode) {
        DBCollection collection = getDatastore().getDB().getCollection("sysServiceNo");
        BasicDBObject obj = (BasicDBObject) collection.findOne(new BasicDBObject("areaCode", areaCode));
        if (null != obj) {
            return obj.getInt("userId");
        }
        return createServiceNo(areaCode);
    }

    //获取系统最大客服号
    private Integer getMaxServiceNo() {
        DBCollection collection = getDatastore().getDB().getCollection("sysServiceNo");
        BasicDBObject obj = (BasicDBObject) collection.findOne(null, new BasicDBObject("userId", 1), new BasicDBObject("userId", -1));
        if (null != obj) {
            return obj.getInt("userId");
        } else {
            BasicDBObject query = new BasicDBObject("_id", new BasicDBObject(MongoOperator.LT, 10200));
            query.append("_id", new BasicDBObject(MongoOperator.GT, 10200));
            BasicDBObject projection = new BasicDBObject("_id", 1);
            DBObject dbobj = getDatastore().getDB().getCollection("user").findOne(query, projection, new BasicDBObject("_id", -1));
            if (null == dbobj) {
                return 10200;
            }
            Integer id = Integer.valueOf(dbobj.get("_id").toString());
            return id;
        }
    }

    //创建系统服务号
    private Integer createServiceNo(String areaCode) {
        DBCollection collection = getDatastore().getDB().getCollection("sysServiceNo");
        Integer userId = getMaxServiceNo() + 1;
        BasicDBObject value = new BasicDBObject("areaCode", areaCode);
        value.append("userId", userId);
        collection.save(value);
        addUser(userId, Md5Util.md5Hex(userId + ""));
        return userId;
    }

    //消息免打扰
    @Override
    public User updataOfflineNoPushMsg(int userId, int OfflineNoPushMsg) {
        Query<User> q = getDatastore().createQuery(getEntityClass()).field("_id").equal(userId);
        UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
        ops.set("OfflineNoPushMsg", OfflineNoPushMsg);
        User user = getDatastore().findAndModify(q, ops);
        user.setPayPassword("");
        return user;
    }

    /**
     * @param emoji
     * @Description:（收藏）
     **/
    public Emoji addNewEmoji(String emoji) {
        Emoji newEmoji = null;
        if (StringUtil.isEmpty(emoji)) {
            throw new ServiceException("addNewEmoji emoji is null");
        }
        List<Emoji> emojiList = JSONObject.parseArray(emoji, Emoji.class);
        for (Emoji emojis : emojiList) {
            emojis.setUserId(ReqUtil.getUserId());
            Query<Emoji> query = getDatastore().createQuery(Emoji.class).field("msg").equal(emojis.getMsg()).field("type").equal(emojis.getType()).field("userId").equal(emojis.getUserId());
            if (null != query.get()) {
                throw new ServiceException("不能重复收藏");
            }
            if (!StringUtil.isEmpty(emojis.getMsgId()) && 0 == emojis.getCollectType()) {
                // 添加收藏
                newEmoji = newAddCollection(ReqUtil.getUserId(), emojis);
            } else if (StringUtil.isEmpty(emojis.getMsgId()) && 0 == emojis.getCollectType()) {
                try {
                    String ret = FileUtil.doGetRequestForFile(emojis.getUrl());
                    String fileMD5 = Md5Util.md5Hex(ret);
                    emojis.setFileMD5(fileMD5);
                } catch (ServiceException e) {
                    e.printStackTrace();
                }
//                log.info("userID: {} ,type: {},fileMd5： {}",emojis.getUserId(),emojis.getType(),emojis.getFileMD5());
                Query<Emoji> query1 = getDatastore().createQuery(Emoji.class).field("fileMD5").equal(emojis.getFileMD5()).field("type").equal(emojis.getType()).field("userId").equal(emojis.getUserId());
                if (null != query1.get()) {
                    throw new ServiceException("不能重复收藏");
                }
                // 添加表情
                newEmoji = newAddEmoji(ReqUtil.getUserId(), emojis);
            } else if (StringUtil.isEmpty(emojis.getMsgId()) && -1 == emojis.getCollectType()) {
                // 无关消息的相关收藏
                newEmoji = newAddCollection(ReqUtil.getUserId(), emojis);
            }
            if (StringUtil.isEmpty(emojis.getMsgId()) && 1 == emojis.getCollectType() && StringUtil.isEmpty(emojis.getTitle()) && StringUtil.isEmpty(emojis.getShareURL())) {
                // 朋友圈收藏
                newEmoji = msgCollect(ReqUtil.getUserId(), emojis, 0);
                saveCollect(new ObjectId(newEmoji.getCollectMsgId()), getNickName(emojis.getUserId()), emojis.getUserId());
            }else if (StringUtil.isEmpty(emojis.getMsgId()) && 2 == emojis.getCollectType()) {
                // 笔记收藏
                newEmoji = msgCollect(ReqUtil.getUserId(), emojis, 0);
                saveCollect(new ObjectId(newEmoji.getCollectMsgId()), getNickName(emojis.getUserId()), emojis.getUserId());
            } else if (StringUtil.isEmpty(emojis.getMsgId()) && 1 == emojis.getCollectType() && !StringUtil.isEmpty(emojis.getTitle()) && !StringUtil.isEmpty(emojis.getShareURL())) {
                // SDK分享链接
                newEmoji = msgCollect(ReqUtil.getUserId(), emojis, 1);
                saveCollect(new ObjectId(newEmoji.getCollectMsgId()), getNickName(emojis.getUserId()), emojis.getUserId());
            }
        }
        return newEmoji;
    }


    private Emoji msgCollect(Integer userId, Emoji msgEmoji, int isShare) {
        StringBuffer buffer = new StringBuffer();
        if (msgEmoji.getType() != 5) {
            String[] msgs = msgEmoji.getMsg().split(",");
            String copyFile = "";
            String newCopyFile = null;
            for (int i = 0; i < msgs.length; i++) {
                copyFile = ConstantUtil.copyFile(-1, msgs[i]);
                buffer.append(copyFile).append(",");
            }
            newCopyFile = buffer.deleteCharAt(buffer.length() - 1).toString();
            msgEmoji.setUrl(newCopyFile);
        }
        Emoji emoji = null;
        if (0 == isShare) {
            emoji = new Emoji(msgEmoji.getUserId(), msgEmoji.getType(), (5 == msgEmoji.getType() ? null : msgEmoji.getUrl()), msgEmoji.getMsg(),
                    msgEmoji.getFileName(), msgEmoji.getFileSize(), msgEmoji.getFileLength(), msgEmoji.getCollectType(), msgEmoji.getCollectContent(), msgEmoji.getCollectMsgId());
        } else if (1 == isShare) {
            emoji = new Emoji(msgEmoji.getUserId(), msgEmoji.getType(), (5 == msgEmoji.getType() ? null : msgEmoji.getUrl()), msgEmoji.getMsg(),
                    msgEmoji.getFileName(), msgEmoji.getFileSize(), msgEmoji.getFileLength(), msgEmoji.getCollectType(), msgEmoji.getCollectContent(), msgEmoji.getCollectMsgId(), msgEmoji.getTitle(), msgEmoji.getShareURL());
        }
        getDatastore().save(emoji);
//		// 维护朋友圈收藏
        getRedisServiceImpl().deleteUserCollectCommon(userId);
        return emoji;
    }

    /**
     * @param userId
     * @param emoji
     * @return
     * @Description:（添加收藏）
     **/
    public synchronized Emoji newAddCollection(Integer userId, Emoji emoji) {
        if (emoji.getType() != 5 && emoji.getType() != 8) {
            try {
                String copyFile = ConstantUtil.copyFile(-1, emoji.getMsg());
                emoji.setUrl(copyFile);
            } catch (ServiceException e) {
                throw new ServiceException(e.getMessage());
            }
        } else if (emoji.getType() == 5 || emoji.getType() == 8) {
            emoji.setCollectContent(emoji.getMsg());
        }
        BasicDBObject emojiMsg = emojiMsg(emoji);
        if (null != emojiMsg) {
            JSONObject parseObject = JSONObject.parseObject(emojiMsg.toString());
            // 格式化body 转译&quot;
            String unescapeHtml3 = StringEscapeUtils.unescapeHtml3((String) parseObject.get("body"));
            JSONObject test = JSONObject.parseObject(unescapeHtml3);
            if (emoji.getType() != 5) {
                if (null != test.get("fileName")) {
                    emoji.setFileName(test.get("fileName").toString());
                }
                if (null != test.get("fileSize")) {
                    emoji.setFileSize(Double.valueOf(test.get("fileSize").toString()));
                }
            }
            if (emoji.getType() == 4) {
                emoji.setFileLength(Integer.parseInt(test.get("timeLen").toString()));
            }
        }
		/*if (!StringUtil.isEmpty(emoji.getRoomJid()))
			emoji.setRoomJid(emoji.getRoomJid());
		if(!StringUtil.isEmpty(emoji.getTitle()))
			emoji.setTitle(emoji.getTitle());
		if(!StringUtil.isEmpty(emoji.getShareURL()))
			emoji.setShareURL(emoji.getShareURL());*/
        emoji.setUserId(userId);
        emoji.setCreateTime(DateUtil.currentTimeSeconds());
        getDatastore().save(emoji);
        /**
         * 维护用户收藏的缓存
         */
        getRedisServiceImpl().deleteUserCollectCommon(userId);
        return emoji;
    }

    /**
     * @param emoji
     * @return
     * @Description:（语音、文件特殊处理）
     **/
    private BasicDBObject emojiMsg(Emoji emoji) {
        int isSaveMsg = TigBeanUtils.getSystemConfig().getIsSaveMsg();
        if (0 == isSaveMsg)
            throw new ServiceException("当前设置不保存单聊聊天记录,暂不支持收藏");
        DBCollection dbCollection = null;
        BasicDBObject data = null;
        if (StringUtil.isEmpty(emoji.getRoomJid())) {
            dbCollection = getTigaseDatastore().getDB().getCollection("tig_msgs");
        } else {
            dbCollection = getRoomDatastore().getDB().getCollection("mucmsg_" + emoji.getRoomJid());
        }
        BasicDBObject q = new BasicDBObject();
        q.put("messageId", emoji.getMsgId());
        data = (BasicDBObject) dbCollection.findOne(q);
        logger.info(" emojiMsg  文件：" + JSONObject.toJSONString(data));
        return data;
    }

    /**
     * @param userId
     * @param emoji
     * @return
     * @Description:（添加收藏表情）
     **/
    public Emoji newAddEmoji(Integer userId, Emoji emoji) {
        try {
            String copyFile = ConstantUtil.copyFile(-1, emoji.getUrl());
            emoji.setUserId(userId);
            emoji.setType(emoji.getType());
            if (!StringUtil.isEmpty(copyFile))
                emoji.setUrl(copyFile);
            else
                emoji.setUrl(emoji.getUrl());
            emoji.setCreateTime(DateUtil.currentTimeSeconds());
            getDatastore().save(emoji);
            /**
             * 维护用户自定义表情缓存
             */
            getRedisServiceImpl().deleteUserCollectEmoticon(userId);
        } catch (ServiceException e) {
            throw new ServiceException("文件服务器连接超时");
        }
        return emoji;
    }

    // 收藏详情记录
    public void saveCollect(ObjectId msgId, String nickname, int userId) {
        Collect collect = new Collect(msgId, nickname, userId);
        getDatastore().save(collect);
    }


    /**
     * 旧版收藏 兼容版本
     */
    // 添加收藏
    @Override
    public List<Object> addCollection(int userId, String roomJid, String msgId, String type) {
        int isSaveMsg = TigBeanUtils.getSystemConfig().getIsSaveMsg();
        if (0 == isSaveMsg) {
            throw new ServiceException("当前设置不保存单聊聊天记录,暂不支持收藏");
        }
        Query<Emoji> query = null;
        BasicDBObject data = null;
        List<Object> listEmoji = new ArrayList<>();
        List<String> listMsgId = new ArrayList<>();
        List<String> listType = new ArrayList<>();
        if (!StringUtil.isEmpty(msgId)) {
            listMsgId = Arrays.asList(msgId.split(","));
            listType = Arrays.asList(type.split(","));
            for (int i = 0; i < listMsgId.size(); i++) {
                query = getDatastore().createQuery(Emoji.class).filter("userId", userId).filter("msgId", listMsgId.get(i));
                if (query.get() == null) {
                    Emoji emoji = new Emoji();
                    emoji.setUserId(userId);
                    emoji.setType(Integer.parseInt(listType.get(i)));
                    if (!StringUtil.isEmpty(roomJid)) {
                        emoji.setRoomJid(roomJid);
                    }

                    if (!StringUtil.isEmpty(listMsgId.get(i))) {
                        emoji.setMsgId(listMsgId.get(i));
                        DBCollection dbCollection = null;
                        if (StringUtil.isEmpty(roomJid)) {
                            dbCollection = getTigaseDatastore().getDB().getCollection("tig_msgs");
                        } else {
                            dbCollection = getRoomDatastore().getDB().getCollection("mucmsg_" + roomJid);
                        }

                        BasicDBObject q = new BasicDBObject();
                        q.put("messageId", listMsgId.get(i));
                        data = (BasicDBObject) dbCollection.findOne(q);
                        if (data == null) {
                            continue;
                        }

                    }
                    if (Integer.parseInt(listType.get(i)) != 5) {
                        JSONObject obj = JSONObject.parseObject(data.toString());
                        try {
                            String copyFile = ConstantUtil.copyFile(-1, obj.get("content").toString());
                            data.replace("content", copyFile);
                            emoji.setUrl(copyFile);
                        } catch (Exception e) {
                            // TODO Auto-generated catch block
                            e.printStackTrace();
                        }
                    }
                    emoji.setMsg(data.toString());
                    emoji.setCreateTime(DateUtil.currentTimeSeconds());
                    getDatastore().save(emoji);
                    listEmoji.add(emoji);
                    /**
                     * 维护用户收藏的缓存
                     */
                    getRedisServiceImpl().deleteUserCollectCommon(userId);
                    getRedisServiceImpl().deleteUserCollectEmoticon(userId);
                } else {
                    return null;
                }
            }

        }

        return listEmoji;
    }


    //添加收藏表情
    @Override
    public Object addEmoji(int userId, String url, String type) {
        if (!StringUtil.isEmpty(url)) {
            Query<Emoji> query = getDatastore().createQuery(Emoji.class).filter("userId", userId).filter("url", url);
            String copyFile = ConstantUtil.copyFile(-1, url);
            if (query.get() == null) {
                Emoji emoji = new Emoji();
                emoji.setUserId(userId);
                emoji.setType(Integer.parseInt(type));
                if (!StringUtil.isEmpty(copyFile)) {
                    emoji.setUrl(copyFile);
                } else {
                    emoji.setUrl(url);
                }
                emoji.setCreateTime(DateUtil.currentTimeSeconds());
                getDatastore().save(emoji);
                /**
                 * 维护用户表情缓存
                 */
                getRedisServiceImpl().deleteUserCollectEmoticon(userId);
                return emoji;
            }
        }
        return null;
    }


    //取消收藏
    @Override
    public void deleteEmoji(String emojiId) {
        List<String> list = Arrays.asList(emojiId.split(","));
        for (String emjId : list) {
            Query<Emoji> query = getDatastore().createQuery(Emoji.class).field("_id").equal(new ObjectId(emjId));
            Emoji dbObj = query.get();
            if (dbObj != null && dbObj.getCollectType() == 1) {
                Query<Collect> collectQuery = getDatastore().createQuery(Collect.class).field("msgId").equal(new ObjectId(dbObj.getCollectMsgId()));
                getDatastore().delete(collectQuery);
            }
//            if (null != dbObj)
//                ConstantUtil.deleteFile(dbObj.getUrl());
            if (query.get().getType() != 6) {
                getRedisServiceImpl().deleteUserCollectCommon(ReqUtil.getUserId());
            } else {
                getRedisServiceImpl().deleteUserCollectEmoticon(ReqUtil.getUserId());
            }
            getDatastore().delete(query);
        }
    }


    //收藏列表
    @Override
    public List<Emoji> emojiList(int userId, int type, int pageSize, int pageIndex) {
        // 用户收藏
        List<Emoji> emojiLists = null;
        if (type != 0) {
            Query<Emoji> query = getDatastore().createQuery(Emoji.class).field("userId").equal(userId).filter("type", new BasicDBObject(MongoOperator.LT, 10)).order("-createTime");
            query.filter("type", type);
            emojiLists = query.asList();
            // 兼容旧版文本
            emojiLists = unescapeHtml3(emojiLists);
        } else {
            List<Emoji> userCollectCommon = getRedisServiceImpl().getUserCollectCommon(userId);
            if (null != userCollectCommon && userCollectCommon.size() > 0) {
                emojiLists = userCollectCommon;
            } else {
//					Query<Emoji> query1=getDatastore().createQuery(Emoji.class).field("userId").equal(userId).filter("type", new BasicDBObject(MongoOperator.LT,6)).order("-createTime");
                Query<Emoji> query = getDatastore().createQuery(Emoji.class).field("userId").equal(userId).order("-createTime");
                query.or(query.criteria("type").lessThan(10), query.criteria("type").equal(7));
                emojiLists = query.asList();
                // 兼容旧版文本
                emojiLists = unescapeHtml3(emojiLists);
                getRedisServiceImpl().saveUserCollectCommon(userId, emojiLists);
            }
        }
        return emojiLists;
    }

    /**
     * @param emojiLists
     * @return
     * @Description: 旧版收藏的文本数据格式化
     **/
    public List<Emoji> unescapeHtml3(List<Emoji> emojiLists) {
        if (null == emojiLists) {
            return null;
        }
        emojiLists.forEach(emojis -> {
            if (5 == emojis.getType() && null == emojis.getCollectContent()) {
                BasicDBObject emojiMsg = emojiMsg(emojis);
                if (null != emojiMsg) {
                    JSONObject parseObject = JSONObject.parseObject(emojiMsg.toString());
                    // 格式化body 转译&quot;
                    String unescapeHtml3 = StringEscapeUtils.unescapeHtml3((String) parseObject.get("body"));
                    JSONObject test = JSONObject.parseObject(unescapeHtml3);
                    if (null != test.get("content")) {
                        emojis.setMsg(test.get("content").toString());
                        log.info("旧版转译后的 content:" + test.get("content").toString());
                        emojis.setCollectContent(test.get("content").toString());
                    }
                }
            }
        });
        return emojiLists;
    }


    //收藏表情列表
    @Override
    public List<Emoji> emojiList(int userId) {
        List<Emoji> emojis = null;
        List<Emoji> userCollectEmoticon = getRedisServiceImpl().getUserCollectEmoticon(userId);
        if (null != userCollectEmoticon && userCollectEmoticon.size() > 0)
            emojis = userCollectEmoticon;
        else {
            Query<Emoji> query = getDatastore().createQuery(Emoji.class).filter("userId", userId).filter("type", 6);
            List<Emoji> emojiList = query.order("-createTime").asList();
            emojis = emojiList;
            getRedisServiceImpl().saveUserCollectEmoticon(userId, emojis);
        }
        return emojis;
    }


    //添加课程
    @Override
    public void addMessageCourse(int userId, List<String> messageIds, long createTime, String courseName, String roomJid) {
        Course course = new Course();
        course.setUserId(userId);
        course.setMessageIds(messageIds);
        course.setCreateTime(createTime);
        course.setCourseName(courseName);
        course.setRoomJid(roomJid);
        saveEntity(course);
        ThreadUtil.executeInThread(new Callback() {

            @Override
            public void execute(Object obj) {
                DBCollection dbCollection = null;
                if (course.getRoomJid().equals("0")) {
                    dbCollection = getTigaseDatastore().getDB().getCollection("tig_msgs");
                } else {
                    dbCollection = getRoomDatastore().getDB().getCollection("mucmsg_" + course.getRoomJid());
                }
                BasicDBObject q = new BasicDBObject();
                q.put("messageId", new BasicDBObject(MongoOperator.IN, messageIds));
                q.put("sender", course.getUserId());
                DBCursor dbCursor = dbCollection.find(q);
                DBObject dbObj;
                CourseMessage courseMessage = new CourseMessage();
                while (dbCursor.hasNext()) {
                    dbObj = dbCursor.next();
                    courseMessage.setCourseMessageId(new ObjectId());
                    courseMessage.setUserId(course.getUserId());
                    courseMessage.setCourseId(course.getCourseId().toString());
                    courseMessage.setCreateTime(String.valueOf(dbObj.get("timeSend")));
                    courseMessage.setMessage(String.valueOf(dbObj));
                    courseMessage.setMessageId(String.valueOf(dbObj.get("messageId")));
                    saveEntity(courseMessage);
                }
                dbCursor.close();
            }
        });

    }

    //获取课程列表
    @Override
    public List<Course> getCourseList(int userId) {
        Query<Course> query = getDatastore().createQuery(Course.class).filter("userId", userId);
        List<Course> list = query.order("createTime").asList();
        return list;
    }

    //修改课程
    @Override
    public void updateCourse(Course course, String courseMessageId) {
        Query<Course> q = getDatastore().createQuery(Course.class).filter("courseId", course.getCourseId());
        UpdateOperations<Course> ops = getDatastore().createUpdateOperations(Course.class);
        if (null != course.getMessageIds()) {
            ops.set("messageIds", course.getMessageIds());
        }
        if (0 != course.getUpdateTime()) {
            ops.set("updateTime", course.getUpdateTime());
        }
        if (null != course.getCourseName()) {
            ops.set("courseName", course.getCourseName());
        }
        if (!StringUtil.isEmpty(courseMessageId)) {
            Query<CourseMessage> query = getDatastore().createQuery(CourseMessage.class).filter("messageId", courseMessageId);
            CourseMessage courseMessage = query.get();
            // 兼容IOS旧版本
            if (null == courseMessage) {
                Query<CourseMessage> oldQuery = getDatastore().createQuery(CourseMessage.class).filter("_id", new ObjectId(courseMessageId));
                courseMessage = oldQuery.get();
            }
            getDatastore().delete(courseMessage);
            // 维护讲课messageIds
            List<String> messageIds = q.get().getMessageIds();
            Iterator<String> iterator = messageIds.iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                if (next.equals(courseMessageId)) {
                    iterator.remove();
                }
            }
            if (0 == messageIds.size()) {
                getDatastore().delete(q);
                return;
            }
            ops.set("messageIds", messageIds);
        }
        getDatastore().update(q, ops);

    }

    //删除课程
    @Override
    public void deleteCourse(ObjectId courseId) {
        Query<Course> query = getDatastore().createQuery(Course.class).filter("courseId", courseId);
        Query<CourseMessage> q = getDatastore().createQuery(CourseMessage.class).filter("courseId", String.valueOf(courseId));
        List<CourseMessage> asList = q.asList();
        for (int i = 0; i < asList.size(); i++) {
            getDatastore().delete(asList.get(i));
        }
        getDatastore().delete(query);

    }

    //获取详情
    @Override
    public List<CourseMessage> getCourse(String courseId) {
        List<CourseMessage> listMessage = new ArrayList<CourseMessage>();
        Query<CourseMessage> que = getDatastore().createQuery(CourseMessage.class).filter("courseId", courseId);
        listMessage = que.asList();
        return listMessage;
    }

    @Override
    public WxUser addwxUser(JSONObject jsonObject) {
        WxUser wxUser = new WxUser();
        Integer userId = createUserId();
        wxUser.setWxuserId(userId);
        wxUser.setOpenId(jsonObject.getString("openid"));
        wxUser.setNickname(jsonObject.getString("nickname"));
        wxUser.setImgurl(jsonObject.getString("headimgurl"));
        wxUser.setSex(jsonObject.getIntValue("sex"));
        wxUser.setCity(jsonObject.getString("city"));
        wxUser.setCountry(jsonObject.getString("country"));
        wxUser.setProvince(jsonObject.getString("province"));
        wxUser.setCreatetime(DateUtil.currentTimeSeconds());
        getDatastore().save(wxUser);

        try {
            KXMPPServiceImpl.getInstance().registerByThread(userId.toString(), jsonObject.getString("openid"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return wxUser;
    }


    /**
     * 用户注册统计      时间单位每日，最好可选择：每日、每月、每分钟、每小时
     *
     * @param startDate
     * @param endDate
     * @param timeUnit  统计类型   1: 每个月的数据      2:每天的数据       3.每小时数据   4.每分钟的数据 (小时)
     */
    public List<Object> getUserRegisterCount(String startDate, String endDate, short timeUnit) {
        List<Object> countData = new ArrayList<>();
        long startTime = 0; //开始时间（秒）
        long endTime = 0; //结束时间（秒）,默认为当前时间
        /**
         * 如时间单位为月和天，默认开始时间为当前时间的一年前 ; 时间单位为小时，默认开始时间为当前时间的一个月前;
         * 时间单位为分钟，则默认开始时间为当前这一天的0点
         */
        long defStartTime = timeUnit == 4 ? DateUtil.getTodayMorning().getTime() / 1000 : timeUnit == 3 ? DateUtil.getLastMonth().getTime() / 1000 : DateUtil.getLastYear().getTime() / 1000;
        startTime = StringUtil.isEmpty(startDate) ? defStartTime : DateUtil.toDate(startDate).getTime() / 1000;
        endTime = StringUtil.isEmpty(endDate) ? DateUtil.currentTimeSeconds() : DateUtil.toDate(endDate).getTime() / 1000;
        BasicDBObject queryTime = new BasicDBObject("$ne", null);
        if (startTime != 0 && endTime != 0) {
            queryTime.append("$gt", startTime);
            queryTime.append("$lt", endTime);
        }
        BasicDBObject query = new BasicDBObject("createTime", queryTime);
        //获得用户集合对象
        DBCollection collection = TigBeanUtils.getDatastore().getCollection(getEntityClass());
//        int diffTime = 0;
//        if (timeUnit == 3) {//小时数据
//            diffTime = 3600 * Integer.parseInt(DateUtil.getTimeZone());
//        } else if (timeUnit == 4) {//分钟数据
//            diffTime = 3600 * Integer.parseInt(DateUtil.getTimeZone());
//        }
        String mapStr = "function Map() { "
//                + "var date = new Date((this.createTime+" + diffTime + ")*1000);"
                + "var date = new Date((this.createTime)*1000);"
                + "var year = date.getFullYear();"
                + "var month = (\"0\" + (date.getMonth()+1)).slice(-2);"  //month 从0开始，此处要加1
                + "var day = (\"0\" + date.getDate()).slice(-2);"
                + "var hour = (\"0\" + date.getHours()).slice(-2);"
                + "var minute = (\"0\" + date.getMinutes()).slice(-2);"
                + "var dateStr = date.getFullYear()" + "+'-'+" + "(parseInt(date.getMonth())+1)" + "+'-'+" + "date.getDate();";
        if (timeUnit == 1) { // counType=1: 每个月的数据
            mapStr += "var key= year + '-'+ month;";
        } else if (timeUnit == 2) { // counType=2:每天的数据
            mapStr += "var key= year + '-'+ month + '-' + day;";
        } else if (timeUnit == 3) { //counType=3 :每小时数据
            mapStr += "var key= year + '-'+ month + '-' + day + '  ' + hour +' : 00';";
        } else if (timeUnit == 4) { //counType=4 :每分钟的数据
            mapStr += "var key= year + '-'+ month + '-' + day + '  ' + hour + ':'+ minute; ";
        }
        mapStr += "emit(key,1);}";
        String reduce = "function Reduce(key, values) {" +
                "return Array.sum(values);" +
                "}";
        MapReduceCommand.OutputType type = MapReduceCommand.OutputType.INLINE;//
        MapReduceCommand command = new MapReduceCommand(collection, mapStr, reduce, null, type, query);
        MapReduceOutput mapReduceOutput = collection.mapReduce(command);
        Iterable<DBObject> results = mapReduceOutput.results();
        Map<String, Double> map = new HashMap<String, Double>();
        for (Iterator iterator = results.iterator(); iterator.hasNext(); ) {
            DBObject obj = (DBObject) iterator.next();
            map.put((String) obj.get("_id"), (Double) obj.get("value"));
            countData.add(JSON.toJSON(map));
            map.clear();
        }
        return countData;
    }

    // 1: 每个月的数据      2:每天的数据       3.每小时数据   4.每分钟的数据
    public List<Object> userOnlineStatusCount(String startDate, String endDate, short timeUnit) {
        List<Object> countData = new ArrayList<>();
        long startTime = 0; //开始时间（秒）
        long endTime = 0; //结束时间（秒）,默认为当前时间
        /**
         * 如时间单位为月和天，默认开始时间为当前时间的一年前 ; 时间单位为小时，默认开始时间为当前时间的一个月前;
         * 时间单位为分钟，则默认开始时间为当前这一天的0点
         */
        long defStartTime = timeUnit == 4 ? DateUtil.getTodayMorning().getTime() / 1000 : timeUnit == 3 ? DateUtil.getLastMonth().getTime() / 1000 : DateUtil.getLastYear().getTime() / 1000;
//		logger.info("defStartTime: {},{}",defStartTime);
        startTime = StringUtil.isEmpty(startDate) ? defStartTime : DateUtil.toDate(startDate).getTime() / 1000;
        endTime = StringUtil.isEmpty(endDate) ? DateUtil.currentTimeSeconds() : DateUtil.toDate(endDate).getTime() / 1000;
        BasicDBObject queryTime = new BasicDBObject("$ne", null);
        if (startTime != 0 && endTime != 0) {
            queryTime.append("$gte", startTime);
            queryTime.append("$lt", endTime);
        }
        //用户在线采样标识, 对应 UserStatusCount 表的type 字段     1零时统计   2:小时统计   3:天数统计
        short minute_sampling = 1, hour_sampling = 2, day_sampling = 3;
        BasicDBObject queryType = new BasicDBObject("$eq", day_sampling); //默认筛选天数据
        /*int diffTime = 0;*/
        if (1 == timeUnit) { //月数据
            queryType.append("$eq", day_sampling);
        } else if (2 == timeUnit) {//天数据
//            diffTime = 86400;
            queryType.append("$eq", day_sampling);
        } else if (timeUnit == 3) {//小时数据
//            diffTime = 3600 * Integer.parseInt(DateUtil.getTimeZone());
            queryType.append("$eq", hour_sampling);
        } else if (timeUnit == 4) {//分钟数据
//            diffTime = 3600 * Integer.parseInt(DateUtil.getTimeZone());
            queryType.append("$eq", minute_sampling);
        }
        BasicDBObject query = new BasicDBObject("time", queryTime).append("type", queryType);
        //获得用户集合对象
        DBCollection collection = TigBeanUtils.getDatastore().getCollection(UserStatusCount.class);
        String mapStr = "function Map() { "
//                + "var date = new Date((this.time"+diffTime+")*1000);"
                + "var date = new Date((this.time)*1000);"
                + "var year = date.getFullYear();"
                + "var month = (\"0\" + (date.getMonth()+1)).slice(-2);"  //month 从0开始，此处要加1
                + "var day = (\"0\" +  date.getDate()).slice(-2);"
                + "var hour = (\"0\" + date.getHours()).slice(-2);"
                + "var minute = (\"0\" + date.getMinutes()).slice(-2);"
                + "var dateStr = date.getFullYear()" + "+'-'+" + "(parseInt(date.getMonth())+1)" + "+'-'+" + "(date.getDate());";
        if (timeUnit == 1) { // counType=1: 每个月的数据
            mapStr += "var key= year + '-'+ month;";
        } else if (timeUnit == 2) { // counType=2:每天的数据
            mapStr += "var key= year + '-'+ month + '-' + day;";
        } else if (timeUnit == 3) { //counType=3 :每小时数据
            mapStr += "var key= year + '-'+ month + '-' + day + '  ' + hour +' : 00'; ";
        } else if (timeUnit == 4) { //counType=4 :每分钟的数据
            mapStr += "var key= year + '-'+ month + '-' + day + '  ' + hour + ':'+ minute;";
        }
        mapStr += "emit(key,this.count);}";
        String reduce = "function Reduce(key, values) {" +
                "return Array.sum(values);" +
                "}";
        MapReduceCommand.OutputType type = MapReduceCommand.OutputType.INLINE;//
        MapReduceCommand command = new MapReduceCommand(collection, mapStr, reduce, null, type, query);
        MapReduceOutput mapReduceOutput = collection.mapReduce(command);
        Iterable<DBObject> results = mapReduceOutput.results();
        Map<String, Object> map = new HashMap<>();
        for (Iterator iterator = results.iterator(); iterator.hasNext(); ) {
            DBObject obj = (DBObject) iterator.next();
            map.put((String) obj.get("_id"), obj.get("value"));
            countData.add(JSON.toJSON(map));
            map.clear();
        }
        if (timeUnit == 2) {
            queryType = new BasicDBObject("$eq", minute_sampling);
            queryTime = new BasicDBObject("$ne", null);
            queryTime.append("$gte", DateUtil.getYesterdayNight().getTime() / 1000);
            queryTime.append("$lt", endTime);
            query = new BasicDBObject("time", queryTime).append("type", queryType);
            DBObject today = collection.find(query).sort(new BasicDBObject("count", -1)).skip(0).limit(1).one();
            if (null != today) {
                long tamp = (long) today.get("time");
                String key = DateUtil.getDateStr(new Date(tamp * 1000), "yyyy-MM-dd");
                long value = (long) today.get("count");
                map = new HashMap<>(1);
                map.put(key, value);
                countData.add(JSON.toJSON(map));
            }
        }
        return countData;

    }

    /**
     * @param offlineNoPushMsg
     * @return
     * @Description:（设置消息免打扰）
     **/
    public User updatemessagefree(int offlineNoPushMsg) {
        Query<User> q = getDatastore().createQuery(User.class).field("_id").equal(ReqUtil.getUserId());
        UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
        ops.set("offlineNoPushMsg", offlineNoPushMsg);
        User data = getDatastore().findAndModify(q, ops);
        data.setPayPassword("");
        return data;
    }

    /**
     * @param openid
     * @param userId
     * @return
     * @Description:（获取微信用户）
     **/
    public WxUser getWxUser(String openid, Integer userId) {
        WxUser wxUser = null;
        if (!StringUtil.isEmpty(openid)) {
            wxUser = getDatastore().createQuery(WxUser.class).field("openId").equal(openid).get();
        } else if (null != userId) {
            wxUser = getDatastore().createQuery(WxUser.class).field("wxuserId").equal(userId).get();
        }
        return wxUser;
    }

    /**
     * @param pageIndex
     * @param pageSize
     * @return
     * @Description:（按注册时间降序排序用户）
     **/
    public List<User> getUserlimit(int pageIndex, int pageSize, int isAuth) {
        Query<User> q = getDatastore().createQuery(User.class);
        if (1 == isAuth) {
            q.field("isAuth").equal(isAuth);
        }
        q.order("-createTime"); // 按创建时间降序排列
        List<User> dataList = q.offset(pageIndex * pageSize).limit(pageSize).asList();
        return dataList;
    }

    public void initQueryUserParam(Config config, NearbyUser poi, Query<User> q) {
        if (0 == config.getTelephoneSearchUser()) { //手机号搜索关闭
            if (0 == config.getNicknameSearchUser()) { //昵称搜索关闭
//                q.field("account").equal(poi.getNickname());
                return;
            } else if (1 == config.getNicknameSearchUser()) { //昵称精准搜索
                q.or(q.criteria("nickname").equal(poi.getNickname()).criteria("settings.nameSearch").notEqual(0));
            } else if (2 == config.getNicknameSearchUser()) { //昵称模糊搜索
                q.or(q.criteria("nickname").containsIgnoreCase(poi.getNickname()).criteria("settings.nameSearch").notEqual(0));
            }
        } else if (1 == config.getTelephoneSearchUser()) { //手机号精确搜索
            if (0 == config.getNicknameSearchUser()) { //昵称搜索关闭
                q.or(q.criteria("account").equal(poi.getNickname()), q.criteria("phone").equal(poi.getNickname()).criteria("settings.phoneSearch").notEqual(0));
            } else if (1 == config.getNicknameSearchUser()) { //昵称精准搜索
                q.or(q.criteria("account").equal(poi.getNickname()), q.criteria("phone").equal(poi.getNickname()).criteria("settings.phoneSearch").notEqual(0), q.criteria("nickname").equal(poi.getNickname()).criteria("settings.nameSearch").notEqual(0));
            } else if (2 == config.getNicknameSearchUser()) { //昵称模糊搜索
                q.or(q.criteria("account").equal(poi.getNickname()), q.criteria("phone").equal(poi.getNickname()).criteria("settings.phoneSearch").notEqual(0), q.criteria("nickname").containsIgnoreCase(poi.getNickname()).criteria("settings.nameSearch").notEqual(0));
            }
        } else if (2 == config.getTelephoneSearchUser()) { //手机号模糊搜索
            if (0 == config.getNicknameSearchUser()) { //昵称搜索关闭
                q.or(q.criteria("account").containsIgnoreCase(poi.getNickname()), q.criteria("phone").containsIgnoreCase(poi.getNickname()).criteria("settings.phoneSearch").notEqual(0));
            } else if (1 == config.getNicknameSearchUser()) { //昵称精准搜索
                q.or(q.criteria("account").containsIgnoreCase(poi.getNickname()), q.criteria("phone").containsIgnoreCase(poi.getNickname()).criteria("settings.phoneSearch").notEqual(0), q.criteria("nickname").equal(poi.getNickname()).criteria("settings.nameSearch").notEqual(0)
                );
            } else if (2 == config.getNicknameSearchUser()) { //昵称模糊搜索
                q.or(q.criteria("account").containsIgnoreCase(poi.getNickname()), q.criteria("phone").containsIgnoreCase(poi.getNickname()).criteria("settings.phoneSearch").notEqual(0), q.criteria("nickname").containsIgnoreCase(poi.getNickname()).criteria("settings.nameSearch").notEqual(0));
            }
        }
    }

    /**
     * @param poi
     * @return
     * @Description:（附近的用户）
     **/
    public List<User> nearbyUser(NearbyUser poi) {
        try {
            Query<User> q = this.getNearByQuery(poi);
            if (q == null) {
                return null;
            }
            ClientConfig config = TigBeanUtils.getAdminManager().getClientConfig();
            List<User> data = q.asList();
            if (null!=data&&config.getIsOpenPositionService() == 1){
                for(int i = 0; i < data.size(); i++){
                    if(null!=data.get(i).getLoc()){
                        data.get(i).getLoc().setLat(0.0);
                        data.get(i).getLoc().setLng(0.0);
                    }
                }
            }
            return data;
        } catch (Exception e) {
            if (e.getMessage().contains("$geoNear query")) {
                DBObject dbObject = new BasicDBObject().append("loc", "2d");
                this.getDatastore().getDB().getCollection("user").createIndex(dbObject, "loc");
                Query<User> q = this.getNearByQuery(poi);
                if (q == null) {
                    return null;
                }
                ClientConfig config = TigBeanUtils.getAdminManager().getClientConfig();
                List<User> data = q.asList();
                if (null!=data&&config.getIsOpenPositionService() == 1){
                    for(int i = 0; i < data.size(); i++){
                        if(null!=data.get(i).getLoc()){
                            data.get(i).getLoc().setLat(0.0);
                            data.get(i).getLoc().setLng(0.0);
                        }
                    }
                }
                return data;
            } else {
                e.printStackTrace();
                logger.info(e.getMessage());
            }
        }
        return null;
    }

    public PageVO nearbyUserWeb(NearbyUser poi) {
        try {
            Query<User> q = this.getNearByQuery(poi);
            if (q == null) {
                return null;
            }
            List<User> data = q.asList();
            return new PageVO(data, q.count(), poi.getPageIndex(), poi.getPageSize());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Query<User> getNearByQuery(NearbyUser poi) {
        // 过滤隐私设置中关闭手机号和昵称搜索的用户
        Query<User> q = getDatastore().createQuery(User.class);
        if (null != poi.getSex()) {
            q.field("sex").equal(poi.getSex());
        }
        q.disableValidation();
        int distance = poi.getDistance();
        if (0 == distance) {
            distance = ConstantUtil.getAppDefDistance();
        }
        Double d = distance / KConstants.LBS_KM;// 0.180180180.....
        if (0 != poi.getLatitude() && 0 != poi.getLongitude()) {
            q.field("loc").near(poi.getLongitude(), poi.getLatitude(), d);
            // 附近的人排除自己
            q.field("userId").notEqual(ReqUtil.getUserId());
        }
        if (!StringUtil.isEmpty(poi.getNickname())) {
            Config config = TigBeanUtils.getAdminManager().getConfig();
            if (null != config && 0 == config.getTelephoneSearchUser() && 0 == config.getNicknameSearchUser()) {
                return null;
            }
            this.initQueryUserParam(config, poi, q);
        } else if (0 == poi.getLatitude() && 0 == poi.getLongitude()) {
            //搜索关键字为空，且坐标没传的情况下不返回数据
            return null;
        }
        if (null != poi.getUserId()) {
            q.field("_id").equal(poi.getUserId());
        }
        if (null != poi.getSex()) {
            q.field("sex").equal(poi.getSex());
        }
        if (null != poi.getActive() && 0 != poi.getActive()) {
            q.field("active").greaterThanOrEq(DateUtil.currentTimeSeconds() - poi.getActive() * 86400000);
            q.field("active").lessThanOrEq(DateUtil.currentTimeSeconds());
        }
        //排除系统号
        q.field("_id").greaterThan(100050);
        q.offset(poi.getPageIndex() * (poi.getPageSize())).limit(poi.getPageSize());
        return q;
    }

    // 删除用户
    public void deleteUser(String... userIds) {
        try {
            Integer systemUserId = 10005;
            for (String strUserId : userIds) {
                Integer userId = Integer.valueOf(strUserId);
                if (0 != userId) {
                    DBCollection tdbCollection = TigBeanUtils.getTigaseDatastore().getDB().getCollection("tig_users");
                    String xmpphost = TigBeanUtils.getXMPPConfig().getServerName();
                    tdbCollection.remove(new BasicDBObject("user_id", userId + "@" + xmpphost));
                    // 发送xmpp通知 客户端更新本地数据
                    consoleDeleteUserXmpp(systemUserId, userId);
//                    ThreadUtil.executeInThread(obj -> {
//
//                    });
                    // 退出用户加入的群聊
                    List<ObjectId> roomIdList = TigBeanUtils.getRoomManagerImplForIM().getRoomIdList(userId);
                    roomIdList.forEach(roomId -> {
                        TigBeanUtils.getRoomManagerImplForIM().deleteRoomMemberSignDetails(userId.toString(), roomId.toString(), "");//将该用户所有群里面的签到信息删掉
                        TigBeanUtils.getRoomManagerImplForIM().deleteMember(getUser(userId), roomId, userId);
                    });
                    // 删除用户关系
                    TigBeanUtils.getFriendsManager().deleteFansAndFriends(userId);
                    // 删除通讯录好友
                    TigBeanUtils.getAddressBookManger().delete(null, null, userId);
                    // 删除用户的相关举报信息
                    delReport(userId, null);
                    // 删除用户的账单记录
                    delRecord(userId);
                    // 删除用户的角色信息
                    TigBeanUtils.getRoleManager().deleteAllRoles(userId);
                    // 删除用户组织架构相关信息
                    delCompany(userId);
                    //刪除用户已经下载的表情包信息
                    TigBeanUtils.getEmojiStoreRepository().deleteEmojiByUserId(userId);
                    DBCollection dbCollection = getDatastore().getCollection(User.class);
                    dbCollection.remove(new BasicDBObject("_id", userId));
                    KSessionUtil.removeAccessToken(userId);
                    KSessionUtil.deleteUserByUserId(userId);
                    //删除第三方登录的信息
                    deleteOtherInfoMsg(userId);
                    deleteSDKLoginInfoMsg(userId);
                    // 清除redis中没有系统号的表
                    TigBeanUtils.getRedisService().deleteNoSystemNumUserIds();

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void deleteOtherInfoMsg(int userId) {
        Query<UserOtherInfo> otherInfoQuery = getDatastore().createQuery(UserOtherInfo.class).field("userId").equal(userId);
        if (otherInfoQuery != null){
            getDatastore().delete(otherInfoQuery);
        }
    }

    public void deleteSDKLoginInfoMsg(int userId) {
        Query<SdkLoginInfo> sdkLoginInfoQuery = getDatastore().createQuery(SdkLoginInfo.class).field("userId").equal(userId);
        if (sdkLoginInfoQuery != null) {
            getDatastore().delete(sdkLoginInfoQuery);
        }
    }

    private void consoleDeleteUserXmpp(Integer userId, Integer toUserId) {
        List<Integer> friendsUserIdsList;
        List<Integer> friendsUserIds = TigBeanUtils.getRedisService().getFriendsUserIdsList(toUserId);
        if (null != friendsUserIds && friendsUserIds.size() > 0) {
            friendsUserIdsList = friendsUserIds;
        } else {
            List<Integer> friendsUserIdsDB = TigBeanUtils.getFriendsManager().queryFansId(toUserId);
            friendsUserIdsList = friendsUserIdsDB;
        }
        log.info(" delete user  =====> userId : " + toUserId + "   好友friends : " + friendsUserIdsList);
        ThreadUtil.executeInThread(obj -> {
            //以系统号发送删除好友通知
            //xmpp推送消息
            List<MessageBean> messageBeans = Collections.synchronizedList(new ArrayList<MessageBean>());
            friendsUserIdsList.forEach(strToUserId -> {
                MessageBean messageBean = new MessageBean();
                messageBean.setType(KXMPPServiceImpl.consoleDeleteUsers);
                messageBean.setFromUserId(userId.toString());
                messageBean.setFromUserName(("10005".equals(userId) ? "后台管理员" : getNickName(userId)));
                messageBean.setToUserId(strToUserId.toString());
                messageBean.setToUserName(getNickName(strToUserId));
                messageBean.setContent("后台管理员解除了你们好友关系");
                messageBean.setObjectId(toUserId);
                messageBean.setMessageId(StringUtil.randomUUID());
                messageBeans.add(messageBean);
                messageBean.setMsgType(0);
            });
            try {
                KXMPPServiceImpl.getInstance().send(friendsUserIdsList, messageBeans);
            } catch (Exception e) {
            }
        });
    }

    @Override
    public SdkLoginInfo addSdkLoginInfo(int type, Integer userId, String loginInfo) {
        SdkLoginInfo entity = new SdkLoginInfo();
        entity.setType(type);
        entity.setLoginInfo(loginInfo);
        entity.setUserId(userId);
        entity.setCreateTime(DateUtil.currentTimeSeconds());
        getDatastore().save(entity);
        return entity;
    }

    /**
     * 获取用户绑定信息
     *
     * @param userId
     * @return
     */
    public List<SdkLoginInfo> getBindInfo(Integer userId) {
        Query<SdkLoginInfo> query = getDatastore().createQuery(SdkLoginInfo.class).field("userId").equal(userId);
        return query.asList();
    }

    /**
     * 解除绑定
     *
     * @param type
     * @param userId
     * @return
     */
    public JSONMessage unbind(int type, Integer userId) {
        User user = getUser(userId);
        if (StringUtil.isNullOrEmpty(user.getTelephone())) {
            return JSONMessage.failure("请先绑定手机号");
        }
        Query<SdkLoginInfo> query = getDatastore().createQuery(SdkLoginInfo.class).field("type").equal(type).field("userId").equal(userId);
        if (null != query.get()) {
//            JSONMessage result = getUserManager().unbind(type, ReqUtil.getUserId());
            Query<UserOtherInfo> otherInfoQuery;
            if (type == KConstants.LoginType.WXH5LOGIN || type == KConstants.LoginType.WXLOGIN) {
                List<Integer> otherTypeList = new ArrayList<>();
                otherTypeList.add(KConstants.LoginType.WXH5LOGIN);
                otherTypeList.add(KConstants.LoginType.WXLOGIN);
                otherInfoQuery = getDatastore().createQuery(UserOtherInfo.class).field("userId").equal(userId).field("otherType").in(otherTypeList);
            } else {
                otherInfoQuery = getDatastore().createQuery(UserOtherInfo.class).field("userId").equal(userId).field("otherType").equal(type);
            }
            if (query.get() != null) {
                getDatastore().delete(otherInfoQuery);
            }
            getDatastore().delete(query);
            return JSONMessage.success();
        } else {
            return JSONMessage.failure("绑定关系不存在");
        }
    }

    @Override
    public SdkLoginInfo findSdkLoginInfo(int type, String loginInfo) {
        Query<SdkLoginInfo> query = getDatastore().createQuery(SdkLoginInfo.class).field("type").equal(type).field("loginInfo").equal(loginInfo);
        return query.get();
    }

    /**
     * 修改用户登录IP
     *
     * @param userId
     * @param ip
     * @return
     */
    @Override
    public boolean updateUserLoginIp(int userId, String ip) {
        Query<User> query = getDatastore().createQuery(getEntityClass()).field("_id").equal(userId);
        UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
        ops.set("loginIp", ip);
        getDatastore().update(query, ops);
        return false;
    }

    @Override
    public JSONObject getWxOpenId(String code) {
        if (StringUtil.isEmpty(code)) {
            return null;
        }
        JSONObject jsonObject = WXUserUtils.getWxOpenId(code);
        String openid = jsonObject.getString("openid");
        if (StringUtil.isEmpty(openid)) {
            return null;
        }
        return jsonObject;
    }

    @Override
    public String getWxToken() {
        JSONObject jsonObject = WXUserUtils.getWxToken();
        String token = jsonObject.getString("access_token");
        return token;
    }

    /**
     * 初始化用户业务邀请码
     *
     * @param userId
     * @return
     */
    @Override
    public boolean initUserSerInviteCode(int userId) {
        InviteCode inviteCode = TigBeanUtils.getAdminRepository().findUserInviteCode(userId);
        if (inviteCode == null) { //如果用户没有一对多，推广型邀请码则生成一个
            //当前邀请码标识号
            String inviteCodeStr = RandomUtil.getRandomByIndex(8);
            inviteCode = new InviteCode(userId, inviteCodeStr, System.currentTimeMillis(), -1);
            TigBeanUtils.getAdminRepository().saveInviteCode(inviteCode);
            UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
            ops.set("serInviteCode", inviteCodeStr);
            updateAttributeByOps(userId, ops);
        } else {
            UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
            ops.set("serInviteCode", inviteCode.getInviteCode());
            updateAttributeByOps(userId, ops);
        }
        return true;
    }

    public Map<String, Object> otherLogin(String otherToken, String code, Integer otherType, UserExample example) throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        if (otherType.equals(KConstants.LoginType.QQLOGIN)) {
            LoginConfig loginConfig = TigBeanUtils.getAdminManager().getLoginConfig(KConstants.LoginType.QQLOGIN);
            if (loginConfig == null || loginConfig.getStatus().equals(KConstants.STATUS_NO)) {
                resultMap.put("code", "5");
                resultMap.put("msg", "QQ登录暂未开放");
                return resultMap;
            }
            QqUserInfoEntity userInfoResponse = QQUtil.getSNSUserInfo(otherToken, code, loginConfig.getAppId());
            if (userInfoResponse == null) {
                resultMap.put("code", "4");
                resultMap.put("msg", "用户QQ用户信息失败");
                return resultMap;
            }
            UserOtherInfo otherInfo = QQUtil.getQQToUserOtherInfo(userInfoResponse);
            return this.otherUser(otherInfo, otherType, example);
        } else if (otherType.equals(KConstants.LoginType.WXLOGIN) || otherType.equals(KConstants.LoginType.WXH5LOGIN)) {
            LoginConfig loginConfig;
            String otherName;
            if (otherType.equals(KConstants.LoginType.WXLOGIN)) {
                otherName = "微信";
                loginConfig = TigBeanUtils.getAdminManager().getLoginConfig(KConstants.LoginType.WXLOGIN);
            } else {
                otherName = "微信公众号";
                loginConfig = TigBeanUtils.getAdminManager().getLoginConfig(KConstants.LoginType.WXH5LOGIN);
            }
            if (loginConfig == null || loginConfig.getStatus().equals(KConstants.STATUS_NO)) {
                resultMap.put("code", "5");
                resultMap.put("msg", otherName + "登录暂未开放");
                return resultMap;
            }
            WeChatAuthInfo tokenResponse = WeChatUtil.getOauth2AccessToken(loginConfig.getAppId(), loginConfig.getAppKey(), code);
            if (tokenResponse == null) {
                resultMap.put("code", "3");
                resultMap.put("msg", "获取TOKEN授权失败");
                return resultMap;
            }
            WeChatUserInfo userInfoResponse = WeChatUtil.getSNSUserInfo(tokenResponse.getAccessToken(), tokenResponse.getOpenId());
            if (userInfoResponse == null) {
                resultMap.put("code", "4");
                resultMap.put("msg", "获取" + otherName + "用户信息失败");
                return resultMap;
            }
            if (otherType.equals(KConstants.LoginType.WXH5LOGIN) && StringUtil.isNullOrEmpty(userInfoResponse.getUnionId())) {
                resultMap.put("code", "5");
                resultMap.put("msg", "微信公众号登录未绑定开放平台");
                return resultMap;
            }
            UserOtherInfo otherInfo = WeChatUtil.getWeChatToUserOtherInfo(userInfoResponse);
            return this.otherUser(otherInfo, otherType, example);
        } else if (otherType.equals(KConstants.LoginType.ALLOGIN)) {
            LoginConfig loginConfig = TigBeanUtils.getAdminManager().getLoginConfig(KConstants.LoginType.ALLOGIN);
            if (loginConfig == null || loginConfig.getStatus().equals(KConstants.STATUS_NO)) {
                resultMap.put("code", "5");
                resultMap.put("msg", "支付宝登录暂未开放");
                return resultMap;
            }
            AlipaySystemOauthTokenResponse tokenResponse = AliPayUtil.getAliAccessToken(loginConfig.getAppId(), loginConfig.getPrivateKey(), loginConfig.getPublicKey(), code);
            if (tokenResponse == null) {
                resultMap.put("code", "3");
                resultMap.put("msg", "获取TOKEN授权失败");
                return resultMap;
            }
            AlipayUserInfoShareResponse userInfoResponse = AliPayUtil.getAliUserInfo(loginConfig.getAppId(), loginConfig.getPrivateKey(), loginConfig.getPublicKey(), tokenResponse.getAccessToken());
            if (userInfoResponse == null) {
                resultMap.put("code", "4");
                resultMap.put("msg", "获取支付宝用户信息失败");
                return resultMap;
            }
            UserOtherInfo otherInfo = AliPayUtil.getAliToUserOtherInfo(userInfoResponse);
            return this.otherUser(otherInfo, otherType, example);
        } else {
            resultMap.put("code", "2");
            resultMap.put("msg", "第三方登录类型异常");
        }
        return resultMap;
    }

    public Map<String, Object> otherUser(UserOtherInfo otherInfo, Integer otherType, UserExample example) throws Exception {
        Query<UserOtherInfo> otherInfoQuery;
        if (otherType.equals(KConstants.LoginType.WXH5LOGIN) || otherType.equals(KConstants.LoginType.WXLOGIN)) {
            otherInfoQuery = getDatastore().createQuery(UserOtherInfo.class).field("otherUnionId").equal(otherInfo.getOtherUnionId());
        } else {
            otherInfoQuery = getDatastore().createQuery(UserOtherInfo.class).field("otherId").equal(otherInfo.getOtherId()).field("otherType").equal(otherType);
        }
        List<UserOtherInfo> otherInfoList = otherInfoQuery.asList();
        Boolean otherStatus = false;
        int registerStatus = 0;
        if (otherInfoList != null && otherInfoList.size() > 0) {
            otherInfo.setUserId(otherInfoList.get(0).getUserId());
            if (otherInfoList.get(0).getInviteStatus() == null) {
                otherInfo.setInviteStatus(null);
            } else {
                otherInfo.setInviteStatus(otherInfoList.get(0).getInviteStatus());
            }
            if (otherInfo.getUserId() == null) {
                registerStatus = 1;
            } else {
                User user = getUserManager().getUser(otherInfo.getUserId());
                if (user == null) {
                    otherInfo.setUserId(null);
                    registerStatus = 1;
                }
            }
        } else {
            //设置三方类型保存信息
            otherInfo.setOtherType(otherType);
            getDatastore().save(otherInfo);
            otherInfo.setUserId(null);
            registerStatus = 1;
        }
        if (otherInfo.getUserId() == null) {
            otherStatus = true;
            // 生成userId
            Integer userId = createUserId();
            try {
                String domain = TigBeanUtils.getLocalSpringBeanManager().getAppConfig().getUploadDomain();
                FileUtil.fileToUploadDomainHeadUrl(domain, otherInfo.getHeadUrl(), Long.valueOf(userId));
            } catch (ServiceException e) {
                logger.error(e.getErrMessage());
            }
            // 新增用户
            example.setName(otherInfo.getNickName());
            example.setNickname(otherInfo.getNickName());
            example.setSex(otherInfo.getSex());
            example.setPassword(Md5Util.md5Hex(otherInfo.getOtherId()));
            example.setUserType(1);
            // 新增用户
            Map<String, Object> data = getUserRepository().addUser(userId, example);
            otherInfo.setUserId(userId);
            if (null != data) {
                this.initUserData(example, userId, data);
                getUserManager().welcomeMsg(userId);
            }
        }
        User user = this.get(otherInfo.getUserId());
        //判断账号是否锁定
        if (-1 == user.getStatus()) {
            Map<String, Object> data = new HashMap<>();
            data.put("loginLock", "1");
            return data;
        }
//        updateUserLoginIp(user.getUserId(),example.getLoginIp());
        Integer sdkType;
        String loginInfo;
        if (otherType.equals(KConstants.LoginType.WXH5LOGIN) || otherType.equals(KConstants.LoginType.WXLOGIN)) {
            sdkType = KConstants.LoginType.WXLOGIN;
            loginInfo = otherInfo.getOtherUnionId();
        } else {
            sdkType = otherType;
            loginInfo = otherInfo.getOtherId();
        }
        SdkLoginInfo sdkLoginInfo = this.findSdkLoginInfo(sdkType, loginInfo);
        if (sdkLoginInfo == null) {
            this.addSdkLoginInfo(sdkType, user.getUserId(), loginInfo);
        }
//        example.setPassword(user.getPassword());
        example.setUserId(user.getUserId());
        example.setIsSdkLogin(1);
        Map<String, Object> data = this.loginSuccess(user, example);
        //校验邀请码状态
        int inviteCodeMode = TigBeanUtils.getAdminManager().getConfig().getRegisterInviteCode();
        if (inviteCodeMode == 0 || (otherInfo.getInviteStatus() != null && otherInfo.getInviteStatus() == KConstants.STATUS_YES)) { //关闭
            if (otherInfo.getInviteStatus() == null) {
                otherInfo.setInviteStatus(KConstants.STATUS_YES);
            }
            UpdateOperations<UserOtherInfo> ops = getDatastore().createUpdateOperations(UserOtherInfo.class);
            if (otherStatus) {
                ops.set("userId", otherInfo.getUserId());
            }
            ops.set("inviteStatus", otherInfo.getInviteStatus());
            getDatastore().update(otherInfoQuery, ops);
            data.put("inviteStatus", KConstants.STATUS_YES);
        } else {
            UpdateOperations<UserOtherInfo> ops = getDatastore().createUpdateOperations(UserOtherInfo.class);
            if (otherStatus) {
                ops.set("userId", otherInfo.getUserId());
            }
            ops.set("inviteStatus", KConstants.STATUS_NO);
            getDatastore().update(otherInfoQuery, ops);
            data.put("inviteStatus", KConstants.STATUS_NO);
        }
        data.put("registerStatus", registerStatus);
        data.put("code", "1");
        data.put("msg", "登录成功");
        return data;
    }

    public boolean otherSetInviteCode(String inviteCode) {
        // 核验邀请码,及相关操作
        UserExample example = new UserExample();
        example.setInviteCode(inviteCode);
        Integer userId = ReqUtil.getUserId();
        this.checkInviteCode(example, userId, KConstants.API_TYPE_APP);
        // 保存第三方登录信息邀请码状态信息
        Query<UserOtherInfo> otherInfoQuery = getDatastore().createQuery(UserOtherInfo.class).field("userId").equal(userId);
        UpdateOperations<UserOtherInfo> ops = getDatastore().createUpdateOperations(UserOtherInfo.class);
        ops.set("inviteStatus", KConstants.STATUS_YES);
        getDatastore().update(otherInfoQuery, ops);
        //获取用户信息
        User user = getUser(userId);
        user.setRegInviteCode(inviteCode);
        user.setSerInviteCode(example.getSerInviteCode());
        this.update(userId, user);
        KSessionUtil.deleteUserByUserId(userId);
        return true;
    }

    public boolean setUserInviteCode(String inviteCode) {
        // 核验邀请码,及相关操作
        UserExample example = new UserExample();
        example.setInviteCode(inviteCode);
        Integer userId = ReqUtil.getUserId();
        this.checkInviteCode(example, userId, KConstants.API_TYPE_APP);
        //获取用户信息
        User user = getUser(userId);
        user.setRegInviteCode(inviteCode);
        user.setSerInviteCode(example.getSerInviteCode());
        this.update(userId, user);
        KSessionUtil.deleteUserByUserId(userId);
        return true;
    }

    public Map<String, Object> otherBindUserInfo(String otherToken, String code, Integer otherType, User user) throws AlipayApiException {
        Map<String, Object> resultMap = new HashMap<>();
        if (otherType.equals(KConstants.LoginType.QQLOGIN)) {
            LoginConfig loginConfig = TigBeanUtils.getAdminManager().getLoginConfig(KConstants.LoginType.QQLOGIN);
            if (loginConfig == null || loginConfig.getStatus().equals(KConstants.STATUS_NO)) {
                resultMap.put("code", "5");
                resultMap.put("msg", "QQ登录暂未开放");
                return resultMap;
            }
            QqUserInfoEntity userInfoResponse = QQUtil.getSNSUserInfo(otherToken, code, loginConfig.getAppId());
            if (userInfoResponse == null) {
                resultMap.put("msg", "用户QQ用户信息失败");
                resultMap.put("code", "4");
                return resultMap;
            }
            UserOtherInfo otherInfo = QQUtil.getQQToUserOtherInfo(userInfoResponse);
            return this.otherUser(otherInfo, otherType, user);
        } else if (otherType.equals(KConstants.LoginType.WXLOGIN) || otherType.equals(KConstants.LoginType.WXH5LOGIN)) {
            LoginConfig loginConfig;
            String otherName;
            if (otherType.equals(KConstants.LoginType.WXH5LOGIN)) {
                otherName = "微信公众号";
                loginConfig = TigBeanUtils.getAdminManager().getLoginConfig(KConstants.LoginType.WXH5LOGIN);
            } else {
                otherName = "微信";
                loginConfig = TigBeanUtils.getAdminManager().getLoginConfig(KConstants.LoginType.WXLOGIN);
            }
            if (loginConfig == null || loginConfig.getStatus().equals(KConstants.STATUS_NO)) {
                resultMap.put("code", "5");
                resultMap.put("msg", otherName + "登录暂未开放");
                return resultMap;
            }
            WeChatAuthInfo tokenResponse = WeChatUtil.getOauth2AccessToken(loginConfig.getAppId(), loginConfig.getAppKey(), code);
            if (tokenResponse == null) {
                resultMap.put("msg", "获取TOKEN授权失败");
                resultMap.put("code", "3");
                return resultMap;
            }
            WeChatUserInfo userInfoResponse = WeChatUtil.getSNSUserInfo(tokenResponse.getAccessToken(), tokenResponse.getOpenId());
            if (userInfoResponse == null) {
                resultMap.put("code", "4");
                resultMap.put("msg", "获取" + otherName + "用户信息失败");
                return resultMap;
            }
            if (otherType.equals(KConstants.LoginType.WXH5LOGIN) && StringUtil.isNullOrEmpty(userInfoResponse.getUnionId())) {
                resultMap.put("code", "5");
                resultMap.put("msg", "微信公众号登录未绑定开放平台");
                return resultMap;
            }
            UserOtherInfo otherInfo = WeChatUtil.getWeChatToUserOtherInfo(userInfoResponse);
            return this.otherUser(otherInfo, otherType, user);
        } else if (otherType.equals(KConstants.LoginType.ALLOGIN)) {
            LoginConfig loginConfig = TigBeanUtils.getAdminManager().getLoginConfig(KConstants.LoginType.ALLOGIN);
            if (loginConfig == null || loginConfig.getStatus().equals(KConstants.STATUS_NO)) {
                resultMap.put("code", "5");
                resultMap.put("msg", "支付宝登录暂未开放");
                return resultMap;
            }
            AlipaySystemOauthTokenResponse tokenResponse = AliPayUtil.getAliAccessToken(loginConfig.getAppId(), loginConfig.getPrivateKey(), loginConfig.getPublicKey(), code);
            if (tokenResponse == null) {
                resultMap.put("msg", "获取TOKEN授权失败");
                resultMap.put("code", "3");
                return resultMap;
            }
            AlipayUserInfoShareResponse userInfoResponse = AliPayUtil.getAliUserInfo(loginConfig.getAppId(), loginConfig.getPrivateKey(), loginConfig.getPublicKey(), tokenResponse.getAccessToken());
            if (userInfoResponse == null) {
                resultMap.put("code", "4");
                resultMap.put("msg", "获取支付宝用户信息失败");
                return resultMap;
            }
            UserOtherInfo otherInfo = AliPayUtil.getAliToUserOtherInfo(userInfoResponse);
            return this.otherUser(otherInfo, otherType, user);
        } else {
            resultMap.put("code", "2");
            resultMap.put("msg", "三方登录类型异常");
        }
        return resultMap;
    }

    public Map<String, Object> otherUser(UserOtherInfo otherInfo, Integer otherType, User user) {
        Query<UserOtherInfo> otherInfoQuery;
        if (otherType.equals(KConstants.LoginType.WXH5LOGIN) || otherType.equals(KConstants.LoginType.WXLOGIN)) {
            otherInfoQuery = getDatastore().createQuery(UserOtherInfo.class).field("otherUnionId").equal(otherInfo.getOtherUnionId());
        } else {
            otherInfoQuery = getDatastore().createQuery(UserOtherInfo.class).field("otherId").equal(otherInfo.getOtherId()).field("otherType").equal(otherType);
        }
//        Query<UserOtherInfo> otherInfoQuery = getDatastore().createQuery(UserOtherInfo.class).field("otherId").equal(otherInfo.getOtherId()).field("otherType").equal(otherType);
        List<UserOtherInfo> otherInfoList = otherInfoQuery.asList();
        if (otherInfoList == null || otherInfoList.size() == 0) {
            otherInfo.setOtherType(otherType);
            getDatastore().save(otherInfo);
        } else {
            Map<String, Object> data = new HashMap<>();
            data.put("code", "5");
            data.put("msg", "该账号已绑定其他用户,请选择未绑定账户或联系客服");
            return data;
        }
        Integer sdkType;
        String loginInfo;
        if (otherType.equals(KConstants.LoginType.WXH5LOGIN) || otherType.equals(KConstants.LoginType.WXLOGIN)) {
            sdkType = KConstants.LoginType.WXLOGIN;
            loginInfo = otherInfo.getOtherUnionId();
        } else {
            sdkType = otherType;
            loginInfo = otherInfo.getOtherId();
        }
        SdkLoginInfo sdkLoginInfo = this.findSdkLoginInfo(sdkType, loginInfo);
        if (sdkLoginInfo == null) {
            this.addSdkLoginInfo(sdkType, user.getUserId(), loginInfo);
        }
        UserExample example = new UserExample();
        example.setPassword(user.getPassword());
        example.setUserId(user.getUserId());
        example.setIsSdkLogin(1);
        Map<String, Object> data = this.loginSuccess(user, example);
        UpdateOperations<UserOtherInfo> ops = getDatastore().createUpdateOperations(UserOtherInfo.class);
        ops.set("userId", user.getUserId());
        ops.set("inviteStatus", KConstants.STATUS_YES);
        getDatastore().update(otherInfoQuery, ops);
        data.put("inviteStatus", KConstants.STATUS_YES);
        data.put("code", "1");
        data.put("msg", "绑定成功");
        return data;
    }

    public void welcomeMsg(Integer userId) {
        ThreadUtil.executeInThread(obj -> {
            if (null != userId) {
                //获取保存的欢迎语
                String title = TigBeanUtils.getRedisService().getWelecomeText("welcome");
                //调用向后台推送消息的方法
                if (!"".equalsIgnoreCase(title)) {
                    KXMPPServiceImpl.getInstance().consoleAddBlack(userId, title);
                }
                String uploadDomain = TigBeanUtils.getLocalSpringBeanManager().getAppConfig().getUploadDomain();
                String url = uploadDomain + "/upload/UploadLocalImgServlet";
                Map<String, Object> params = new HashMap<>();
                params.put("userId", userId);
                HttpUtil.URLPost(url, params);
            }
        });
    }

    public void insertGoogleDynamicValue(int userId, String googleValue) {
        if ("".equals(googleValue)) {
            throw new ServiceException("Google动态验证码不能为空");
        }
//        User user = getUserFromDB(userId);
        Query<User> q = getDatastore().createQuery(getEntityClass()).field("_id").equal(userId);
        UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
        ops.set("googleDynamicValue", googleValue);
        getDatastore().findAndModify(q, ops);
        KSessionUtil.removeAccessToken(userId);
    }

    public void deleteGoogleDynamicValue(int userId) {
//        User user = getUserFromDB(userId);
        Query<User> q = getDatastore().createQuery(getEntityClass()).field("_id").equal(userId);
        UpdateOperations<User> ops = getDatastore().createUpdateOperations(getEntityClass());
        ops.set("googleDynamicValue", "");
        getDatastore().findAndModify(q, ops);
        String keySec = String.format(KSessionUtil.GET_GOOGLE_SEC_BY_USERID, String.valueOf(userId));
        KSessionUtil.getRedisCRUD().delete(keySec);
        KSessionUtil.removeAccessToken(userId);
    }

    public boolean getGoogleDynamicValueByAccount(String account) {
        boolean googleVal = false;
        if ("".equals(account)) {
            throw new ServiceException("用户名不能为空");
        }
        User user = null;
        if (null == user) {
            user = getUserRepository().getUserByPhone(account);
        }
        if (null == user) {
            user = getUserRepository().getUserByAccount(account);
        }
        if (null == user) {
            throw new ServiceException("用户不存在");
        }
        if (user.getGoogleDynamicValue() != null && !"".equals(user.getGoogleDynamicValue())) {
            String keySec = String.format(KSessionUtil.GET_GOOGLE_SEC_BY_USERID, String.valueOf(user.getUserId()));
            if (null != KSessionUtil.getRedisCRUD().get(keySec) && !"".equals(KSessionUtil.getRedisCRUD().get(keySec))) {
                if (!KSessionUtil.getRedisCRUD().get(keySec).equals(user.getGoogleDynamicValue())) {
                    KSessionUtil.getRedisCRUD().delete(keySec);
                    KSessionUtil.getRedisCRUD().set(keySec, user.getGoogleDynamicValue());
                }
            } else {
                KSessionUtil.getRedisCRUD().set(keySec, user.getGoogleDynamicValue());
            }
            googleVal = true;
        }
        return googleVal;
    }

    public boolean getGoogleDynamicValueByUserId(int userId) {
        boolean googleVal = false;
        if (userId == 0) {
            throw new ServiceException("账号不能为空");
        }
        User user = null;
        if (null == user) {
            user = getUserRepository().getUser(userId);
        }
        if (null == user) {
            throw new ServiceException("用户不存在");
        }
        if (user.getGoogleDynamicValue() != null && !"".equals(user.getGoogleDynamicValue())) {
            String keySec = String.format(KSessionUtil.GET_GOOGLE_SEC_BY_USERID, String.valueOf(user.getUserId()));
            if (null != KSessionUtil.getRedisCRUD().get(keySec) && !"".equals(KSessionUtil.getRedisCRUD().get(keySec))) {
                if (!KSessionUtil.getRedisCRUD().get(keySec).equals(user.getGoogleDynamicValue())) {
                    KSessionUtil.getRedisCRUD().delete(keySec);
                    KSessionUtil.getRedisCRUD().set(keySec, user.getGoogleDynamicValue());
                }
            } else {
                KSessionUtil.getRedisCRUD().set(keySec, user.getGoogleDynamicValue());
            }
            googleVal = true;
        }
        return googleVal;
    }

    public WithdrawMethod getWithdrawMethod(int userId) {
        Query<WithdrawMethod> query = getDatastore().createQuery(WithdrawMethod.class).field("userId")
                .equal(userId);
        WithdrawMethod withdrawMethod = query.get();
        if (null != withdrawMethod) {
            boolean isAlipay = false;
            boolean isBankCard = false;
            List<WithdrawWayList> withdrawWayList = TigBeanUtils.getDatastore().createQuery(WithdrawWayList.class).field("withdrawWayStatus").equal(1).asList();
            List<WithdrawMethod.OtherMethod> otherMethodList = withdrawMethod.getOtherMethod();
            List<WithdrawMethod.OtherMethod>  otherMethodNewList = new ArrayList<>();
            if(null!=withdrawWayList&&withdrawWayList.size()>0){
                for(int j = 0 ;j <withdrawWayList.size();j++){
                    if(withdrawWayList.get(j).getWithdrawWaySort()==1){
                        isAlipay = true;
                    }else if(withdrawWayList.get(j).getWithdrawWaySort()==5){
                        isBankCard = true;
                    }else{
                        int type = withdrawWayList.get(j).getWithdrawWaySort();
                        if(null!=otherMethodList && otherMethodList.size()!=0){
                            for(int k = 0 ; k < otherMethodList.size(); k++){
                                if(otherMethodList.get(k).getType()==type){
                                    if(null==otherMethodNewList){
                                        otherMethodNewList.add(0,otherMethodList.get(k));
                                    }else{
                                        otherMethodNewList.add(otherMethodNewList.size(),otherMethodList.get(k));
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if(isBankCard){
                List<WithdrawMethod.BankCardMethod> bankCardMethodList = withdrawMethod.getBankCardMethod();
                if (null != bankCardMethodList && bankCardMethodList.size() != 0) {
                    for (int i = 0; i < bankCardMethodList.size(); i++) {
                        String bankCardNo = bankCardMethodList.get(i).getBankCardNo();
                        if (bankCardNo.length() > 4) {
                            bankCardMethodList.get(i).setBankCardNo(bankCardNo.substring(bankCardNo.length() - 4, bankCardNo.length()));
                        }
                        bankCardMethodList.get(i).setBankUserName("");
                    }
                    withdrawMethod.setBankCardMethod(bankCardMethodList);
                }
            }else{
                withdrawMethod.setBankCardMethod(null);
            }
            if(!isAlipay){
                withdrawMethod.setAlipayMethod(null);
            }
            withdrawMethod.setOtherMethod(otherMethodNewList);
        }
        return withdrawMethod;
    }

    public String getAccountDetails(int userId, int type, String id) {
        Query<WithdrawMethod> query = getDatastore().createQuery(WithdrawMethod.class).field("userId").equal(userId);
        WithdrawMethod withdrawMethod = query.get();
        String context = "";
        if (null != withdrawMethod) {
            if (1 == type) {
                List<WithdrawMethod.AlipayMethod> alipayMethodList = withdrawMethod.getAlipayMethod();
                if (null != alipayMethodList && alipayMethodList.size() != 0) {
                    for (int i = 0; i < alipayMethodList.size(); i++) {
                        if (id.equalsIgnoreCase(String.valueOf(alipayMethodList.get(i).getAlipayId()))) {
                            context = alipayMethodList.get(i).getAlipayName() + ":" + alipayMethodList.get(i).getAlipayNumber();
                            break;
                        }
                    }
                }
            } else if (5 == type) {
                List<WithdrawMethod.BankCardMethod> bankCardMethodList = withdrawMethod.getBankCardMethod();
                if (null != bankCardMethodList && bankCardMethodList.size() != 0) {
                    for (int i = 0; i < bankCardMethodList.size(); i++) {
                        if (id.equalsIgnoreCase(String.valueOf(bankCardMethodList.get(i).getBankId()))) {
                            context = bankCardMethodList.get(i).getBankUserName() + ":" + bankCardMethodList.get(i).getBankCardNo() + ":" + bankCardMethodList.get(i).getBankName();
                            break;
                        }
                    }
                }
            }else{
                StringBuffer contentStrBuffer = new StringBuffer();
                List<WithdrawMethod.OtherMethod> otherMethodList = withdrawMethod.getOtherMethod();
                if (null != otherMethodList && otherMethodList.size() != 0) {
                    for (int i = 0; i < otherMethodList.size(); i++) {
                        if (id.equalsIgnoreCase(String.valueOf(otherMethodList.get(i).getOtherId()))) {
                            if(!"".equals(otherMethodList.get(i).getOtherNode1())){
                                contentStrBuffer.append(":" + otherMethodList.get(i).getOtherNode1());
                            }
                            if(!"".equals(otherMethodList.get(i).getOtherNode2())){
                                contentStrBuffer.append(":" + otherMethodList.get(i).getOtherNode2());
                            }
                            if(!"".equals(otherMethodList.get(i).getOtherNode3())){
                                contentStrBuffer.append(":" + otherMethodList.get(i).getOtherNode3());
                            }
                            if(!"".equals(otherMethodList.get(i).getOtherNode4())){
                                contentStrBuffer.append(":" + otherMethodList.get(i).getOtherNode4());
                            }
                            if(!"".equals(otherMethodList.get(i).getOtherNode5())){
                                contentStrBuffer.append(":" + otherMethodList.get(i).getOtherNode5());
                            }
                            context = contentStrBuffer.toString().substring(1);
                            break;
                        }
                    }
                }
            }
        }
        return context;
    }

    public void setWithdrawMethod(int type, String alipayName, String alipayNumber, String bankName
            , String bankUserName, String bankCardNo, String subBankName, String remarks
            , String otherNode1, String otherNode2, String otherNode3, String otherNode4, String otherNode5) {
        Integer userId = ReqUtil.getUserId();
        Query<WithdrawMethod> query = TigBeanUtils.getDatastore().createQuery(WithdrawMethod.class).field("userId")
                .equal(userId);
        if (null != query.get()) {
            WithdrawMethod getWithdrawMethod = query.get();
            UpdateOperations<WithdrawMethod> opsUser = TigBeanUtils.getDatastore().createUpdateOperations(WithdrawMethod.class);
            if (1 == type) {
                List<WithdrawMethod.AlipayMethod> alipayMethodList = getWithdrawMethod.getAlipayMethod();
                if (null == alipayMethodList) {
                    alipayMethodList = new ArrayList();
                }
                WithdrawMethod.AlipayMethod alipayMethod = new WithdrawMethod.AlipayMethod();
                alipayMethod.setAlipayName(alipayName);
                alipayMethod.setAlipayNumber(alipayNumber);
                alipayMethod.setAddAlipayTime(DateUtil.currentTimeSeconds());
                alipayMethod.setType(type);
                alipayMethod.setAlipayId(new ObjectId());
                alipayMethodList.add(alipayMethod);
                getWithdrawMethod.setAlipayMethod(alipayMethodList);
                opsUser.set("alipayMethod", alipayMethodList);
                TigBeanUtils.getDatastore().findAndModify(query, opsUser);
            } else if (5 == type) {
                List<WithdrawMethod.BankCardMethod> bankCardMethodList = getWithdrawMethod.getBankCardMethod();
                if (null == bankCardMethodList) {
                    bankCardMethodList = new ArrayList();
                }
                WithdrawMethod.BankCardMethod bankCardMethod = new WithdrawMethod.BankCardMethod();
                bankCardMethod.setBankName(bankName);
                bankCardMethod.setBankUserName(bankUserName);
                bankCardMethod.setBankCardNo(bankCardNo);
                bankCardMethod.setSubBankName(subBankName);
                bankCardMethod.setRemarks(remarks);
                bankCardMethod.setType(type);
                bankCardMethod.setAddBankTime(DateUtil.currentTimeSeconds());
                bankCardMethod.setBankId(new ObjectId());
                bankCardMethodList.add(bankCardMethod);
                getWithdrawMethod.setBankCardMethod(bankCardMethodList);
                opsUser.set("bankCardMethod", bankCardMethodList);
                TigBeanUtils.getDatastore().findAndModify(query, opsUser);
            }else{
                List<WithdrawMethod.OtherMethod> otherMethodList = getWithdrawMethod.getOtherMethod();
                if (null == otherMethodList) {
                    otherMethodList = new ArrayList();
                }
                WithdrawMethod.OtherMethod otherMethod = new WithdrawMethod.OtherMethod();
                otherMethod.setOtherNode1(otherNode1);
                otherMethod.setOtherNode2(otherNode2);
                otherMethod.setOtherNode3(otherNode3);
                otherMethod.setOtherNode4(otherNode4);
                otherMethod.setOtherNode5(otherNode5);
                otherMethod.setType(type);
                otherMethod.setAddOtherTime(DateUtil.currentTimeSeconds());
                otherMethod.setOtherId(new ObjectId());
                otherMethodList.add(otherMethod);
                getWithdrawMethod.setOtherMethod(otherMethodList);
                opsUser.set("otherMethod", otherMethodList);
                TigBeanUtils.getDatastore().findAndModify(query, opsUser);
            }
        } else {
            WithdrawMethod withdrawMethod = new WithdrawMethod();
            withdrawMethod.setUserId(userId);
//            withdrawMethod.setType(type);
            if (1 == type) {
                List<WithdrawMethod.AlipayMethod> alipayMethodList = new ArrayList();
                WithdrawMethod.AlipayMethod alipayMethod = new WithdrawMethod.AlipayMethod();
                alipayMethod.setAlipayName(alipayName);
                alipayMethod.setAlipayNumber(alipayNumber);
                alipayMethod.setType(type);
                alipayMethod.setAddAlipayTime(DateUtil.currentTimeSeconds());
                alipayMethod.setAlipayId(new ObjectId());
                alipayMethodList.add(alipayMethod);
                withdrawMethod.setAlipayMethod(alipayMethodList);
            } else if (5 == type) {
                List<WithdrawMethod.BankCardMethod> bankCardMethodList = new ArrayList();
                WithdrawMethod.BankCardMethod bankCardMethod = new WithdrawMethod.BankCardMethod();
                bankCardMethod.setBankName(bankName);
                bankCardMethod.setBankUserName(bankUserName);
                bankCardMethod.setBankCardNo(bankCardNo);
                bankCardMethod.setSubBankName(subBankName);
                bankCardMethod.setRemarks(remarks);
                bankCardMethod.setType(type);
                bankCardMethod.setAddBankTime(DateUtil.currentTimeSeconds());
                bankCardMethod.setBankId(new ObjectId());
                bankCardMethodList.add(bankCardMethod);
                withdrawMethod.setBankCardMethod(bankCardMethodList);
            }else{
                List<WithdrawMethod.OtherMethod> otherMethodList = new ArrayList();
                WithdrawMethod.OtherMethod otherMethod = new WithdrawMethod.OtherMethod();
                otherMethod.setOtherNode1(otherNode1);
                otherMethod.setOtherNode2(otherNode2);
                otherMethod.setOtherNode3(otherNode3);
                otherMethod.setOtherNode4(otherNode4);
                otherMethod.setOtherNode5(otherNode5);
                otherMethod.setType(type);
                otherMethod.setAddOtherTime(DateUtil.currentTimeSeconds());
                otherMethod.setOtherId(new ObjectId());
                otherMethodList.add(otherMethod);
                withdrawMethod.setOtherMethod(otherMethodList);
            }
            TigBeanUtils.getDatastore().save(withdrawMethod);
        }
    }

    public void deleteWithdrawMethod(int type, ObjectId id) {
        Integer userId = ReqUtil.getUserId();
        Query<WithdrawMethod> query = TigBeanUtils.getDatastore().createQuery(WithdrawMethod.class).field("userId")
                .equal(userId);
        if (null != query.get()) {
            WithdrawMethod getWithdrawMethod = query.get();
            UpdateOperations<WithdrawMethod> opsUser = TigBeanUtils.getDatastore().createUpdateOperations(WithdrawMethod.class);
            if (1 == type) {
                List<WithdrawMethod.AlipayMethod> alipayMethodList = getWithdrawMethod.getAlipayMethod();
                if (null != alipayMethodList) {
                    for (int i = 0; i < alipayMethodList.size(); i++) {
                        if (alipayMethodList.get(i).getAlipayId().equals(id)) {
                            alipayMethodList.remove(alipayMethodList.get(i));
                            break;
                        }
                    }
                }
                opsUser.set("alipayMethod", alipayMethodList);
                TigBeanUtils.getDatastore().findAndModify(query, opsUser);
            } else if (5 == type) {
                List<WithdrawMethod.BankCardMethod> bankCardMethodList = getWithdrawMethod.getBankCardMethod();
                if (null != bankCardMethodList) {
                    for (int i = 0; i < bankCardMethodList.size(); i++) {
                        if (bankCardMethodList.get(i).getBankId().equals(id)) {
                            bankCardMethodList.remove(bankCardMethodList.get(i));
                            break;
                        }
                    }
                }
                opsUser.set("bankCardMethod", bankCardMethodList);
                TigBeanUtils.getDatastore().findAndModify(query, opsUser);
            }else{
                List<WithdrawMethod.OtherMethod> otherMethodList = getWithdrawMethod.getOtherMethod();
                if (null != otherMethodList) {
                    for (int i = 0; i < otherMethodList.size(); i++) {
                        if (otherMethodList.get(i).getOtherId().equals(id)) {
                            otherMethodList.remove(otherMethodList.get(i));
                            break;
                        }
                    }
                }
                opsUser.set("otherMethod", otherMethodList);
                TigBeanUtils.getDatastore().findAndModify(query, opsUser);
            }
            if ((null == getWithdrawMethod.getAlipayMethod() || getWithdrawMethod.getAlipayMethod().size() == 0)
                    && (null == getWithdrawMethod.getBankCardMethod() || getWithdrawMethod.getBankCardMethod().size() == 0)
                    && (null == getWithdrawMethod.getOtherMethod() || getWithdrawMethod.getOtherMethod().size() == 0)) {
                TigBeanUtils.getDatastore().delete(query);
            }
        }
    }

    public void unlockUserLogin(int userId) {
        User user = new User();
        user.setUserId(userId);
        user.setLockStatus(KConstants.STATUS_NO);
        user.setLoginFailCount(0);
        user.setLockTime(Long.valueOf(0));
        user.setLasterrTime(Long.valueOf(0));
        this.updateUserLockInfo(user);
        //维护redis中的数据
        KSessionUtil.removeAccessToken(userId);
        KSessionUtil.deleteUserByUserId(userId);
    }
}
