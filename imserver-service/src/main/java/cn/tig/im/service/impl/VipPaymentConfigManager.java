package cn.tig.im.service.impl;

import cn.tig.commons.vo.JSONMessage;
import cn.tig.im.model.PageResult;
import cn.tig.im.vo.VipPaymentConfig;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * @titel: 群组收费配置业务处理接口类
 * @author:
 * @package: cn.tig.im.service.RoomManager
 * @fileName: RoomManager.java
 * @dateTime: 2019/7/4 19:11
 * @description:
 **/
public interface VipPaymentConfigManager {
	public static final String BEAN_ID = "VipPaymentConfigManagerImpl";
	void add(VipPaymentConfig vipPaymentConfigList);

	void delete(ObjectId id);

	JSONMessage update(VipPaymentConfig vipPaymentConfigList);

    //查询某个邀请码列表
    PageResult<VipPaymentConfig> selectList1(int page, int limit);

    VipPaymentConfig get(ObjectId id);
	 
	List<VipPaymentConfig> selectList(int pageIndex, int pageSize);
}
