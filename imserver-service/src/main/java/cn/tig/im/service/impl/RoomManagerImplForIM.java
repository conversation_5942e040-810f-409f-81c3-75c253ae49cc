package cn.tig.im.service.impl;

import cn.tig.commons.constants.MsgType;
import cn.tig.commons.ex.ServiceException;
import cn.tig.commons.support.mongo.MongoOperator;
import cn.tig.commons.utils.*;
import cn.tig.commons.vo.JSONMessage;
import cn.tig.im.model.PageResult;
import cn.tig.im.model.PageVO;
import cn.tig.im.model.RoomVO;
import cn.tig.im.service.RoomManager;
import cn.tig.im.service.VipEquityService;
import cn.tig.im.utils.ConstantUtil;
import cn.tig.im.utils.TigBeanUtils;
import cn.tig.im.vo.*;
import cn.tig.im.vo.GroupHelper.KeyWord;
import cn.tig.im.vo.Room.Member;
import cn.tig.im.vo.Room.Notice;
import cn.tig.im.vo.Room.Share;
import cn.tig.service.KXMPPServiceImpl;
import cn.tig.service.KXMPPServiceImpl.MessageBean;
import cn.tig.service.RedisServiceImpl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * @titel: 群组业务实体类
 * @author:
 * @package: cn.tig.im.service.impl.RoomManagerImplForIM
 * @fileName: RoomManagerImplForIM.java
 * @dateTime: 2019/7/4 19:23
 * @description:
 **/
@Service(RoomManager.BEAN_ID)
public class RoomManagerImplForIM extends MongoRepository<Room, ObjectId> implements RoomManager {

    public static final String Tig_ROOMJIDS_USERID = "tig_roomJids_userId";
    public static final String MUCMsg_ = "mucmsg_";

    @Override
    public Datastore getDatastore() {
        return TigBeanUtils.getImRoomDatastore();
    }

    @Override
    public Class<Room> getEntityClass() {
        return Room.class;
    }

    private static UserManagerImpl getUserManager() {
        UserManagerImpl userManager = TigBeanUtils.getUserManager();
        return userManager;
    }

    private static RedisServiceImpl getRedisServiceImpl() {
        return TigBeanUtils.getRedisService();
    }

//	private final String roomMemerList="roomMemerList:";
    public final static String mucMsg = "mucmsg_";
	/*int num=300000;
	int voide=350000;*/
    @Override
    public Room add(User user, Room entity, List<Integer> memberUserIdList) {
        Config config = TigBeanUtils.getSystemConfig();
        //判断全局创建群组是否允许
        if(config.getAllowCreateRoom() == 0){
            if(user.getIsCreateRoom() == 1){
                return this.addRoom(user,entity,memberUserIdList);
            }
            return null;
        }else{
            if(user.getIsCreateRoom() == 0){
                return null;
            }
        }
        return this.addRoom(user,entity,memberUserIdList);
    }

    public Room addRoom(User user, Room entity, List<Integer> memberUserIdList) {
        Config config = TigBeanUtils.getSystemConfig();
       /* if (memberUserIdList != null && memberUserIdList.size()+1> config.getMaxUserSize()){
            throw new ServiceException("当前群成员人数上限"+config.getMaxUserSize());
        }*/
        if (null == entity.getId()) {
            entity.setId(ObjectId.get());
        }
        /*entity.setCall("0"+user.getUserId()+user.getNum());*/
        entity.setCall(String.valueOf(getUserManager().createCall()));
        entity.setVideoMeetingNo(String.valueOf(getUserManager().createvideoMeetingNo()));
        entity.setSubject("");
        entity.setTags(Lists.newArrayList());
        entity.setNotice(new Room.Notice());
        entity.setNotices(Lists.newArrayList());
        entity.setUserSize(0);
        // entity.setMaxUserSize(1000);
        entity.setMembers(Lists.newArrayList());
        entity.setUserId(user.getUserId());
        entity.setNickname(user.getNickname());
        entity.setCreateTime(DateUtil.currentTimeSeconds());
        entity.setModifyTime(entity.getCreateTime());
        entity.setS(1);
        entity.setAllowForceNotice(0);
        entity.setGroupActiveNo(0);
        entity.setGroupMsgNo(0);
       /* if (config.getMaxUserSize() != 0) {
            entity.setMaxUserSize(config.getMaxUserSize());
        }*/
//        user.setNum(user.getNum() + 1);

        Object  rooms = selectHistoryList(user.getUserId(),1);
        int roomNum = 0;
        if(rooms != null){
            List<Room> historyList = (List<Room>) rooms;
            roomNum = historyList.size();
        }
        //用户是否为vip
        if(user.getVip() == 0){
            //普通用户
            entity.setMaxUserSize(Integer.parseInt(VipEquityService.nRoomNumber));
            //是否达到最大建群数
            if(roomNum >= Integer.parseInt(VipEquityService.nCreateRoomNumber)){
                throw new ServiceException("已达到建群上限"+VipEquityService.nCreateRoomNumber+"个");
            }
        }else {
            //vip
            entity.setMaxUserSize(Integer.parseInt(VipEquityService.vipRoomNumber));
            //是否达到最大建群数
            if(roomNum >= Integer.parseInt(VipEquityService.vipCreateRoomNumber)){
                throw new ServiceException("已达到建群上限"+VipEquityService.vipCreateRoomNumber+"个");
            }
        }
        if (config.getIsAttritionNotice() != -1) {
            entity.setIsAttritionNotice(config.getIsAttritionNotice());
        }
        if (config.getIsLook() != -1) {
            entity.setIsLook(config.getIsLook());
        }
        if (config.getShowRead() != -1) {
            entity.setShowRead(config.getShowRead());
        }
        if (config.getIsNeedVerify() != -1) {
            entity.setIsNeedVerify(config.getIsNeedVerify());
        }
        if (config.getShowMember() != -1) {
            entity.setShowMember(config.getShowMember());
        }
        if (config.getAllowSendCard() != -1) {
            entity.setAllowSendCard(config.getAllowSendCard());
        }
        if (config.getAllowInviteFriend() != -1) {
            entity.setAllowInviteFriend(config.getAllowInviteFriend());
        }
        if (config.getAllowUploadFile() != -1) {
            entity.setAllowUploadFile(config.getAllowUploadFile());
        }
        if (config.getAllowConference() != -1) {
            entity.setAllowConference(config.getAllowConference());
        }
        if (config.getAllowSpeakCourse() != -1) {
            entity.setAllowSpeakCourse(config.getAllowSpeakCourse());
        }
        List<Role> userRoles = TigBeanUtils.getRoleManager().getUserRoles(user.getUserId(), null, 0);
        if (null != userRoles && userRoles.size() > 0) {
            for (Role role : userRoles) {
                if (role.getRole() == 4) {
                    entity.setPromotionUrl(role.getPromotionUrl());
                }
            }
        }
        if (null == entity.getCategory()) {
            entity.setCategory(0);
        } else {
            entity.setCategory(entity.getCategory());
        }
        if (null == entity.getName()) {
            entity.setName("我的群组");
        }
        if (null == entity.getDesc()) {
            entity.setDesc("");
        }
        if (null == entity.getCountryId()) {
            entity.setCountryId(0);
        }
        if (null == entity.getProvinceId()) {
            entity.setProvinceId(0);
        }
        if (null == entity.getCityId()) {
            entity.setCityId(0);
        }
        if (null == entity.getAreaId()) {
            entity.setAreaId(0);
        }
        if (null == entity.getLongitude()) {
            entity.setLongitude(0d);
        }
        if (null == entity.getLatitude()) {
            entity.setLatitude(0d);
        }
        // 保存房间配置
        getRoomDatastore().save(entity);
        //初始化消息记录表
        initRoomMucMsg(entity.getJid());
        // 创建者
        Member member = new Member();
        member.setActive(DateUtil.currentTimeSeconds());
        member.setCreateTime(member.getActive());
        member.setModifyTime(0L);
        member.setNickname(user.getNickname());
        member.setRole(1);
        member.setRoomId(entity.getId());
        member.setSub(1);
        member.setTalkTime(0L);
        member.setCall(entity.getCall());
        member.setVideoMeetingNo(entity.getVideoMeetingNo());
        member.setUserId(user.getUserId());
        MessageBean messageBean = null;
        //群组设置属性
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("showRead", entity.getShowRead());
        jsonObject.put("lsLook", entity.getIsLook());
        jsonObject.put("isNeedVerify", entity.getIsNeedVerify());
        jsonObject.put("showMember", entity.getShowMember());
        jsonObject.put("allowSendCard", entity.getAllowSendCard());
        // 初试成员列表
        List<Member> memberList = Lists.newArrayList(member);
        //没有邀请群成员
        if (null == memberUserIdList || memberUserIdList.isEmpty()) {
            messageBean = new MessageBean();
            messageBean.setType(KXMPPServiceImpl.NEW_MEMBER);
            messageBean.setFromUserId(user.getUserId().toString());
            messageBean.setFromUserName(user.getNickname());
            messageBean.setToUserId(user.getUserId().toString());
            messageBean.setFileSize(entity.getShowRead());
            messageBean.setContent(entity.getName());
            messageBean.setToUserName(user.getNickname());
            messageBean.setFileName(entity.getId().toString());
            messageBean.setObjectId(entity.getJid());
            messageBean.setOther(jsonObject.toJSONString());
            messageBean.setMessageId(StringUtil.randomUUID());
            // 发送单聊通知到被邀请人， 群聊
            sendChatToOneGroupMsg(user.getUserId(), entity.getJid(), messageBean);
            /**
             * 删除 用户加入的群组 jid  缓存
             */
            TigBeanUtils.getRedisService().deleteUserRoomJidList(user.getUserId());
            if (0 == TigBeanUtils.getUserManager().getOnlinestateByUserId(user.getUserId())) {
                TigBeanUtils.getRedisService().addRoomPushMember(entity.getJid(), user.getUserId());
            }
        } else if (!memberUserIdList.isEmpty()) {
            // 初试成员列表不为空
            Long currentTimeSeconds = DateUtil.currentTimeSeconds();
            ObjectId roomId = entity.getId();
            //添加群主
            memberUserIdList.add(user.getUserId());
            Member _member;
            for (int userId : memberUserIdList) {
                User _user = getUserManager().getUser(userId);

                //群主在上面已经添加了
                if (userId != member.getUserId()) {
                    //成员
                    _member = new Member();
                    _member.setActive(currentTimeSeconds);
                    _member.setCreateTime(currentTimeSeconds);
                    _member.setModifyTime(0L);
                    _member.setNickname(_user.getNickname());
                    _member.setRole(3);
                    _member.setRoomId(roomId);
                    _member.setSub(1);
                    _member.setCall(entity.getCall());
                    _member.setVideoMeetingNo(entity.getVideoMeetingNo());
                    _member.setTalkTime(0L);
                    _member.setUserId(_user.getUserId());
                    memberList.add(_member);
                }
                //xmpp推送
                messageBean = new MessageBean();
                messageBean.setType(KXMPPServiceImpl.NEW_MEMBER);
                messageBean.setFromUserId(user.getUserId().toString());
                messageBean.setFromUserName(user.getNickname());
                messageBean.setToUserId(_user.getUserId().toString());
                messageBean.setFileSize(entity.getShowRead());
                messageBean.setContent(entity.getName());
                messageBean.setToUserName(_user.getNickname());
                messageBean.setFileName(entity.getId().toString());
                messageBean.setObjectId(entity.getJid());
                messageBean.setOther(jsonObject.toJSONString());
                messageBean.setMsgType(0);// 单聊
                messageBean.setMessageId(StringUtil.randomUUID());
                try {
//                    KXMPPServiceImpl.getInstance().send(messageBean);
                    // 发送单聊通知到被邀请人， 群聊
                    sendChatToOneGroupMsg(_user.getUserId(), entity.getJid(), messageBean);
                } catch (Exception e) {
                    logger.info("add room send xmpp message ex: {}", ExceptionUtils.getStackTrace(e));
                }
                /**
                 * 删除 用户加入的群组 jid  缓存
                 */
                TigBeanUtils.getRedisService().deleteUserRoomJidList(userId);
                if (0 == TigBeanUtils.getUserManager().getOnlinestateByUserId(userId)) {
                    TigBeanUtils.getRedisService().addRoomPushMember(entity.getJid(), userId);
                }
            }
        }
        // 保存成员列表
        getRoomDatastore().save(memberList);
        updateUserSize(entity.getId(), memberList.size());
        // 用户加入的群组
        saveJidsByUserId(user.getUserId(), queryUserRoomsJidList(user.getUserId()));
        return entity;
    }

    public void initRoomMucMsg(String jid){
        DBCollection msgCollection;
        BasicDBObject msgIndex = new BasicDBObject();
        msgIndex.put("background",true);
        if (getRoomDatastore().getDB().collectionExists(MUCMsg_+  jid)){
            msgCollection = getRoomDatastore().getDB().getCollection(MUCMsg_+  jid);
            List<DBObject> indexDBObjList = msgCollection.getIndexInfo();
            boolean senderStatus = true;
            boolean receiverStatus = true;
            boolean senderReceiverStatus = true;
            boolean senderReceiverTsStatus = true;
            boolean messageIdStatus = true;
            boolean timeSendStatus = true;
            boolean timeSendMessageIdStatus = true;
            for (DBObject indexDBObj : indexDBObjList){
                if (indexDBObj.containsField("sender_1")){
                    senderStatus = false;
                }
                if (indexDBObj.containsField("receiver_1")){
                    receiverStatus = false;
                }
                if (indexDBObj.containsField("sender_1_receiver_1")){
                    senderReceiverStatus = false;
                }
                if (indexDBObj.containsField("sender_1_receiver_1_ts_1")){
                    senderReceiverTsStatus = false;
                }
                if (indexDBObj.containsField("messageId_1")){
                    messageIdStatus = false;
                }
                if (indexDBObj.containsField("timeSend_1")){
                    timeSendStatus = false;
                }
                if (indexDBObj.containsField("timeSend_1_messageId_1")){
                    timeSendStatus = false;
                }
            }
            if (senderStatus){
                msgCollection.createIndex(new BasicDBObject("sender", 1),msgIndex);
            }
            if (receiverStatus){
                msgCollection.createIndex(new BasicDBObject("receiver", 1),msgIndex);
            }
            if (senderReceiverStatus){
                msgCollection.createIndex(new BasicDBObject("sender", 1).append("receiver", 1),msgIndex);
            }
            if (senderReceiverTsStatus){
                msgCollection.createIndex(new BasicDBObject("sender", 1).append("receiver", 1).append("ts", 1),msgIndex);
            }
            if (messageIdStatus){
                msgCollection.createIndex(new BasicDBObject("messageId", 1),msgIndex);
            }
            if (timeSendStatus){
                msgCollection.createIndex(new BasicDBObject("timeSend", 1),msgIndex);
            }
            if (timeSendMessageIdStatus){
                msgCollection.createIndex(new BasicDBObject("timeSend", 1).append("messageId", 1),msgIndex);
            }
        }else {
            msgCollection = getRoomDatastore().getDB().createCollection(MUCMsg_+  jid, new BasicDBObject());
            msgCollection.createIndex(new BasicDBObject("timeSend", 1),msgIndex);
            msgCollection.createIndex(new BasicDBObject("messageId", 1),msgIndex);
            msgCollection.createIndex(new BasicDBObject("sender", 1).append("receiver", 1).append("ts", 1),msgIndex);
            msgCollection.createIndex(new BasicDBObject("sender", 1),msgIndex);
            msgCollection.createIndex(new BasicDBObject("receiver", 1),msgIndex);
            msgCollection.createIndex(new BasicDBObject("sender", 1).append("receiver", 1),msgIndex);
            msgCollection.createIndex(new BasicDBObject("timeSend", 1).append("messageId", 1),msgIndex);
        }
    }

    public void queryLocationRoom(double longitude, double latitude, String password, String name) {

    }

    /*private void updateMemerListToRedis(String roomId,List<Integer> memerList){
		String jsonStr = JSON.toJSONString(memerList);
		TigBeanUtils.getRedisCRUD().set(roomMemerList+roomId, jsonStr);
	}*/
	/*public List<Integer> getMermerListIdByRedis(ObjectId roomId){
		String jsonStr=null;
		jsonStr=TigBeanUtils.getRedisCRUD().get(roomMemerList+roomId.toString());
		if(StringUtil.isEmpty(jsonStr)){
			List<Integer> memberIdList = getMemberIdList(roomId, 0);
			//jsonStr = JSON.toJSONString(memberIdList);
			updateMemerListToRedis(roomId.toString(), memberIdList);
			return memberIdList;
		}else{
			List<Integer> memberIdList=JSON.parseArray(jsonStr, Integer.class);
			return memberIdList;
		}
	}*/
    public List<Integer> getRoomPushUserIdList(ObjectId roomId) {
        BasicDBObject query = new BasicDBObject("roomId", roomId);
        query.append("offlineNoPushMsg", new BasicDBObject(MongoOperator.NE, 1));
        List<Integer> memberIdList = distinct("tig_room_member", "userId", query);
        return memberIdList;
    }

    @Override
    public void delete(ObjectId roomId, Integer userId) {
        Query<Room> query = getRoomDatastore().createQuery(getEntityClass()).field("_id").equal(roomId);
        Room room = query.get();
        if (null == room) {
            logger.info("====> RoomManagerImplForIM > delete room is null ");
            return;
        }
        Member member = getMember(roomId, userId);
        List<Integer> userRoles = TigBeanUtils.getRoleManager().getUserRoles(userId);
        if (null != member) {
            if (!userRoles.contains(5) && !userRoles.contains(6)) {
                if (1 != member.getRole()) {
                    throw new ServiceException("暂无权限解散群组");
                }
            }
/*			if(1 != member.getRole() && (!userRoles.contains(5) || !userRoles.contains(6)))
				throw new ServiceException("暂无权限解散群组");*/
        } else {
            if (!userRoles.contains(5) && !userRoles.contains(6)) {
                throw new ServiceException("暂无权限解散群组");
            }

        }
        String roomJid = room.getJid();
        if (room.getUserSize() > 0) {
            MessageBean messageBean = new MessageBean();
            Query<Member> memberQuery = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("userId").equal(userId);
            Member memberInfo = memberQuery.get();
            if (memberInfo == null){
                User user = TigBeanUtils.getUserManager().getUser(room.getUserId());
                if (user!= null){
                    messageBean.setFromUserName(user.getNickname());
                }
            }else {
//                messageBean.setFromUserName(getMemberNickname(roomId, room.getUserId()));
                messageBean.setFromUserName(memberInfo.getNickname());
            }
            messageBean.setFromUserId(room.getUserId() + "");
            messageBean.setType(KXMPPServiceImpl.DELETE_ROOM);
            messageBean.setObjectId(room.getJid());
            messageBean.setContent(room.getName());
            messageBean.setMessageId(StringUtil.randomUUID());
            // 发送单聊群聊
            sendChatGroupMsg(roomId, room.getJid(), messageBean);
        }
//		int createUserId=room.getUserId();
        ThreadUtil.executeInThread(obj -> {
            getRoomDatastore().delete(query);
            List<Integer> memberIdList = getMemberIdList(roomId);
            for (Integer id : memberIdList) {
                // 维护用户加入群组 Jids 缓存
                TigBeanUtils.getRedisService().deleteUserRoomJidList(id);
            }
            //删除群组 清除 群组成员
            Query<Member> merQuery = getRoomDatastore().createQuery(Member.class).field("roomId").equal(roomId);
            getRoomDatastore().delete(merQuery);
            getRoomDatastore().getDB().getCollection(mucMsg + roomJid).drop();
//				TigBeanUtils.getRedisCRUD().del(roomMemerList+roomId);
            //删除公告
            Query<Notice> notQuery = getRoomDatastore().createQuery(Notice.class).field("roomId").equal(roomId);
            getRoomDatastore().delete(notQuery);
            //删除群组离线消息记录
            deleMucHistory(roomJid);
            // 删除 群共享的文件 和 群聊天消息的文件
            destroyRoomMsgFileAndShare(roomId, roomJid);
            // 删除群组相关的举报信息
            getUserManager().delReport(null, roomId.toString());
            deleteRoomMemberSignDetails("",roomId.toString(), roomJid);//删除该群组所有有关签到的信息
            /**
             * tigase 8.0后 废弃 这个方法
             */
            //destroyRoomToIM(roomJid);
            User user = getUserManager().getUserFromDB(userId);
            destroyRoomToIM(user.getUserId() + "", user.getPassword(), roomJid);
            List<Member> memberList = getRedisServiceImpl().getMemberList(roomId.toString());
            memberList.forEach(member1 -> {
                // 维护用户加入的群组jids
                saveJidsByUserId(member1.getUserId(), queryUserRoomsJidList(member1.getUserId()));
            });
            // 维护群组、群成员缓存
            updateRoomInfoByRedis(roomId.toString());
            getRedisServiceImpl().deleteNoticeList(roomId.toString());
        });

    }

    /**
     * @param @param roomId  群主ID
     * @param @param talkTime   禁言到期时间   0 取消禁言
     * @Description: TODO(全员禁言)
     */
    public void roomAllBanned(ObjectId roomId, long talkTime) {
        ThreadUtil.executeInThread(obj -> {
            Query<Member> query = getRoomDatastore().createQuery(Member.class);
            query.filter("roomId", roomId);
            UpdateOperations<Member> operations = getRoomDatastore().createUpdateOperations(Member.class);
            operations.set("talkTime", talkTime);
            getRoomDatastore().update(query, operations);
        });

    }


    public synchronized JSONMessage update(User user, RoomVO roomVO, int isAdmin, int isConsole) {
        JSONMessage jsonMessage = JSONMessage.success();

        Query<Room> query = getRoomDatastore().createQuery(getEntityClass());
        query.filter("_id", roomVO.getRoomId());

        UpdateOperations<Room> operations = getRoomDatastore().createUpdateOperations(getEntityClass());

        Room room = getRoom(roomVO.getRoomId());
        if (0 == isConsole) {
            if (null != room && room.getS() == -1) {
                throw new ServiceException("该群组已被后台锁定");
            }
        }
        if (!StringUtil.isEmpty(roomVO.getRoomName()) && (!room.getName().equals(roomVO.getRoomName()))) {
            UpdateGroupNickname(query, user, roomVO, isAdmin, room, operations);
            return jsonMessage;
        }
        /*全员禁言*/
        if (-2 < roomVO.getTalkTime()) {
            logger.info("all banned speak room: {}", JSONObject.toJSONString(room));
            allBannedSpeak(query, user, roomVO, room, operations);
            return jsonMessage;
        }

        if (!StringUtil.isEmpty(roomVO.getDesc()) || roomVO.getDesc().length() > 0) {
            operations.set("desc", roomVO.getDesc());
        }
        if (!StringUtil.isEmpty(roomVO.getSubject())) {
            operations.set("subject", roomVO.getSubject());
        }
        try {
            if (!StringUtil.isEmpty(roomVO.getNotice())) {
                if (getMember(room.getId(), ReqUtil.getUserId()).getRole() == 3) {
                    return JSONMessage.failure("只有群主和管理员才可以发群公告!");
                }
                String noticeId = newNotice(query, user, roomVO, isAdmin, room, operations);
                return JSONMessage.success(noticeId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (-1 < roomVO.getShowRead() && room.getShowRead() != roomVO.getShowRead()) {
            alreadyReadNums(query, user, roomVO, isAdmin, room, operations);
            return jsonMessage;
        }
        if (-1 != roomVO.getIsNeedVerify()) {
            groupVerification(query, user, roomVO, isAdmin, room, operations);
            return jsonMessage;
        }
        if (-1 != roomVO.getIsLook()) {
            roomIsPublic(query, user, roomVO, isAdmin, room, operations);
            return jsonMessage;
        }
        if (null != roomVO.getMaxUserSize() && roomVO.getMaxUserSize() >= 0) {
            if (roomVO.getMaxUserSize() < room.getUserSize()) {
                throw new ServiceException("群成员上限不能低于当前群成员人数");
            }
            int maxUserSize = TigBeanUtils.getAdminManager().getConfig().getMaxUserSize();
            if (roomVO.getMaxUserSize() > maxUserSize) {
                throw new ServiceException("当前群成员人数上限" + maxUserSize);
            }
            operations.set("maxUserSize", roomVO.getMaxUserSize());
        }
        // 锁定、取消锁定群组
        if (null != roomVO.getS() && 0 != roomVO.getS()) {
            roomIsLocking(query, user, roomVO, isAdmin, room, operations);
            return jsonMessage;
        }

        if (-1 != roomVO.getShowMember()) {
            showMember(query, user, roomVO, isAdmin, room, operations);
            return jsonMessage;
        }
        if (-1 != roomVO.getAllowSendCard()) {
            roomAllowSendCard(query, user, roomVO, isAdmin, room, operations);
            return jsonMessage;
        }

        if (-1 != roomVO.getAllowInviteFriend()) {
            roomAllowInviteFriend(query, user, roomVO, room, operations);
            return jsonMessage;
        }

        if (-1 != roomVO.getAllowUploadFile()) {
            roomAllowUploadFile(query, user, roomVO, room, operations);
            return jsonMessage;
        }

        if (-1 != roomVO.getAllowConference()) {
            roomAllowConference(query, user, roomVO, room, operations);
            return jsonMessage;
        }

        if (-1 != roomVO.getAllowSpeakCourse()) {
            roomAllowSpeakCourse(query, user, roomVO, room, operations);
            return jsonMessage;
        }

        if (-1 != roomVO.getAllowHostUpdate()) {
            operations.set("allowHostUpdate", roomVO.getAllowHostUpdate());
        }

        if (-1 != roomVO.getChatRecordTimeOut())// 聊天记录超时
        {
            ChatRecordTimeOut(query, user, roomVO, room, operations);
        }

        if (-1 != roomVO.getIsAttritionNotice()) {
            operations.set("isAttritionNotice", roomVO.getIsAttritionNotice());
        }

        if (-1 != roomVO.getAllowForceNotice()) {
            operations.set("allowForceNotice", roomVO.getAllowForceNotice());
        }
        if(-1!=roomVO.getIsShowSignIn()){
            updateAllSignIn(query, user, roomVO, room, operations);//群签到
            return jsonMessage;
        }
        operations.set("modifyTime", DateUtil.currentTimeSeconds());

        // 维护群组相关缓存
        getRedisServiceImpl().deleteRoom(roomVO.getRoomId().toString());
        synchronized (query) {
            getRoomDatastore().update(query, operations);
        }

        return jsonMessage;
    }

    /**
     * @param roomId
     * @Description:维护群组、群成员 缓存
     **/
    protected void updateRoomInfoByRedis(String roomId) {
        getRedisServiceImpl().deleteRoom(roomId);
        getRedisServiceImpl().deleteMemberList(roomId);
    }

    // 修改群昵称
    public synchronized void UpdateGroupNickname(Query<Room> query, User user, RoomVO roomVO, int isAdmin, Room room, UpdateOperations<Room> operations) {

        operations.set("name", roomVO.getRoomName());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        if (1 == isAdmin) {
            // IMPORTANT 1-2、改房间名推送-已改
            messageBean.setFromUserId(user.getUserId() + "");
            messageBean.setFromUserName(("10005".equals(user.getUserId().toString()) ? "后台管理员" : getMemberNickname(room.getId(), user.getUserId())));
            messageBean.setType(KXMPPServiceImpl.CHANGE_ROOM_NAME);
            messageBean.setObjectId(room.getJid());
            messageBean.setContent(roomVO.getRoomName());
            messageBean.setMessageId(StringUtil.randomUUID());
        }
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);

    }

    // 全员禁言
    public void allBannedSpeak(Query<Room> query, User user, RoomVO roomVO, Room room, UpdateOperations<Room> operations) {
        operations.set("talkTime", roomVO.getTalkTime());
        updateGroup(query, operations);
//        roomAllBanned(roomVO.getRoomId(), roomVO.getTalkTime());
        /**
         * 维护群组、群成员缓存
         */
        updateRoomInfoByRedis(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.RoomAllBanned);
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
        messageBean.setContent(String.valueOf(roomVO.getTalkTime()));
        messageBean.setObjectId(room.getJid());
        messageBean.setMessageId(StringUtil.randomUUID());
        logger.info("send all banned message: {}", JSONObject.toJSONString(messageBean));
        // 发送群聊通知
        sendGroupMsg(room.getJid(), messageBean);
    }

    // 新公告
    public String newNotice(Query<Room> query, User user, RoomVO roomVO, int isAdmin, Room room, UpdateOperations<Room> operations) {
        Notice notice = new Notice(new ObjectId(), roomVO.getRoomId(), roomVO.getNotice(), user.getUserId(), user.getNickname());
        if (roomVO.getAllowForceNotice() == 1){
            notice.setNoticeType(1);
        }
        // 更新最新公告
        operations.set("notice", notice);
        updateGroup(query, operations);
        // 新增历史公告记录
        getRoomDatastore().save(notice);
        /**
         * 维护公告
         */
        getRedisServiceImpl().deleteNoticeList(room.getId());
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        if (1 == isAdmin) {
            // IMPORTANT 1-5、改公告推送-已改
            messageBean.setFromUserId(user.getUserId() + "");
            messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
            messageBean.setType(KXMPPServiceImpl.NEW_NOTICE);
            messageBean.setObjectId(room.getJid());
//            messageBean.setContent(roomVO.getNotice());
            JSONObject send = new JSONObject();
            send.put("text",notice.getText());
            send.put("noticeType",notice.getNoticeType());
            send.put("roomId",notice.getRoomId().toString());
            send.put("time",notice.getTime());
            send.put("userId",notice.getUserId());
            send.put("nickname",notice.getNickname());
            messageBean.setContent(send);
            messageBean.setMessageId(StringUtil.randomUUID());
        }
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
        return notice.getId().toString();
    }

    // 显示已读人数
    public void alreadyReadNums(Query<Room> query, User user, RoomVO roomVO, int isAdmin, Room room, UpdateOperations<Room> operations) {
        operations.set("showRead", roomVO.getShowRead());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        if (1 == isAdmin) {
            messageBean.setType(KXMPPServiceImpl.SHOWREAD);
            messageBean.setFromUserId(user.getUserId().toString());
            messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
            messageBean.setContent(String.valueOf(roomVO.getShowRead()));
            messageBean.setObjectId(room.getJid());
            messageBean.setMessageId(StringUtil.randomUUID());
        }
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
    }

    // 群组验证
    public void groupVerification(Query<Room> query, User user, RoomVO roomVO, int isAdmin, Room room, UpdateOperations<Room> operations) {
        operations.set("isNeedVerify", roomVO.getIsNeedVerify());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        if (1 == isAdmin) {
            messageBean.setType(KXMPPServiceImpl.RoomNeedVerify);
            messageBean.setFromUserId(user.getUserId().toString());
            messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
            messageBean.setContent(String.valueOf(roomVO.getIsNeedVerify()));
            messageBean.setObjectId(room.getJid());
            messageBean.setMessageId(StringUtil.randomUUID());
        }
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
    }

    // 是否公开群组
    public void roomIsPublic(Query<Room> query, User user, RoomVO roomVO, int isAdmin, Room room, UpdateOperations<Room> operations) {
        operations.set("isLook", roomVO.getIsLook());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        if (1 == isAdmin) {
            messageBean.setType(KXMPPServiceImpl.RoomIsPublic);
            messageBean.setFromUserId(user.getUserId().toString());
            messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
            messageBean.setContent(String.valueOf(roomVO.getIsLook()));
            messageBean.setObjectId(room.getJid());
            messageBean.setMessageId(StringUtil.randomUUID());
        }
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
    }

    // 群组是否被锁定
    public void roomIsLocking(Query<Room> query, User user, RoomVO roomVO, int isAdmin, Room room, UpdateOperations<Room> operations) {
        operations.set("s", roomVO.getS());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        if (1 == isAdmin) {
            messageBean.setType(KXMPPServiceImpl.consoleProhibitRoom);
            messageBean.setFromUserId(user.getUserId().toString());
            messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
            messageBean.setContent(roomVO.getS());
            messageBean.setObjectId(room.getJid());
            messageBean.setMessageId(StringUtil.randomUUID());
        }
        // 发送群聊
        sendChatGroupMsg(roomVO.getRoomId(), room.getJid(), messageBean);
    }


    // 是否允许发送名片
    public void roomAllowSendCard(Query<Room> query, User user, RoomVO roomVO, int isAdmin, Room room, UpdateOperations<Room> operations) {
        operations.set("allowSendCard", roomVO.getAllowSendCard());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        if (1 == isAdmin) {
            messageBean.setType(KXMPPServiceImpl.RoomAllowSendCard);
            messageBean.setFromUserId(user.getUserId().toString());
            messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
            messageBean.setContent(String.valueOf(roomVO.getAllowSendCard()));
            messageBean.setObjectId(room.getJid());
            messageBean.setMessageId(StringUtil.randomUUID());
        }
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
    }

    // 普通成员 是否可以看到 群组内的成员
    public void showMember(Query<Room> query, User user, RoomVO roomVO, int isAdmin, Room room, UpdateOperations<Room> operations) {
        operations.set("showMember", roomVO.getShowMember());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        if (1 == isAdmin) {
            messageBean.setType(KXMPPServiceImpl.RoomShowMember);
            messageBean.setFromUserId(user.getUserId().toString());
            messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
            messageBean.setContent(String.valueOf(roomVO.getShowMember()));
            messageBean.setObjectId(room.getJid());
            messageBean.setMessageId(StringUtil.randomUUID());
        }
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
    }

    // 是否允许群成员邀请好友
    public void roomAllowInviteFriend(Query<Room> query, User user, RoomVO roomVO, Room room, UpdateOperations<Room> operations) {
        operations.set("allowInviteFriend", roomVO.getAllowInviteFriend());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.RoomAllowInviteFriend);
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
        messageBean.setContent(String.valueOf(roomVO.getAllowInviteFriend()));
        messageBean.setObjectId(room.getJid());
        messageBean.setMessageId(UUID.randomUUID().toString());
        messageBean.setMessageId(StringUtil.randomUUID());
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
    }

    // 是否允许群成员上传文件
    public void roomAllowUploadFile(Query<Room> query, User user, RoomVO roomVO, Room room, UpdateOperations<Room> operations) {
        operations.set("allowUploadFile", roomVO.getAllowUploadFile());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.RoomAllowUploadFile);
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
        messageBean.setContent(String.valueOf(roomVO.getAllowUploadFile()));
        messageBean.setObjectId(room.getJid());
        messageBean.setMessageId(StringUtil.randomUUID());
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
    }

    // 群组允许成员召开会议
    public void roomAllowConference(Query<Room> query, User user, RoomVO roomVO, Room room, UpdateOperations<Room> operations) {
        operations.set("allowConference", roomVO.getAllowConference());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.RoomAllowConference);
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
        messageBean.setContent(String.valueOf(roomVO.getAllowConference()));
        messageBean.setObjectId(room.getJid());
        messageBean.setMessageId(StringUtil.randomUUID());
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
    }

    //  群组允许成员开启讲课
    public void roomAllowSpeakCourse(Query<Room> query, User user, RoomVO roomVO, Room room, UpdateOperations<Room> operations) {
        operations.set("allowSpeakCourse", roomVO.getAllowSpeakCourse());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.RoomAllowSpeakCourse);
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
        messageBean.setContent(String.valueOf(roomVO.getAllowSpeakCourse()));
        messageBean.setObjectId(room.getJid());
        messageBean.setMessageId(StringUtil.randomUUID());
        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);
    }

    // 聊天记录超时设置 通知
    public void ChatRecordTimeOut(Query<Room> query, User user, RoomVO roomVO, Room room, UpdateOperations<Room> operations) {
        operations.set("chatRecordTimeOut", roomVO.getChatRecordTimeOut());
        updateGroup(query, operations);
        /**
         * 维护群组缓存
         */
        getRedisServiceImpl().deleteRoom(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.ChatRecordTimeOut);
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
        messageBean.setContent(String.valueOf(roomVO.getChatRecordTimeOut()));
        messageBean.setObjectId(room.getJid());
        messageBean.setMessageId(StringUtil.randomUUID());

        // 发送群聊
        sendGroupMsg(room.getJid(), messageBean);

    }

    public synchronized void updateGroup(Query<Room> query, UpdateOperations<Room> operations) {
        getRoomDatastore().update(query, operations);
    }

    // 单聊通知某个人
    public void sendGroupOne(Integer userIds, MessageBean messageBean) {
        try {
            messageBean.setMsgType(0);
            KXMPPServiceImpl.getInstance().send(messageBean);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 发送群聊通知
    public void sendGroupMsg(String jid, MessageBean messageBean) {
        try {
            KXMPPServiceImpl.getInstance().sendMsgToGroupByJid(jid, messageBean);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 发送单聊通知某个人 ,且发送群聊通知
    public void sendChatToOneGroupMsg(Integer userIds, String jid, MessageBean messageBean) {
        try {
            // 发送单聊
            messageBean.setMsgType(0);
            messageBean.setMessageId(StringUtil.randomUUID());
            KXMPPServiceImpl.getInstance().send(messageBean);
            // 发送群聊
            ThreadUtil.executeInThread(obj -> {
                try {
                    KXMPPServiceImpl.getInstance().sendMsgToGroupByJid(jid, messageBean);
                } catch (Exception e) {
                    logger.info("send chat to someone group message ex: {}", ExceptionUtils.getStackTrace(e));
                }
            });
        } catch (Exception e) {
            logger.info("send chat to someone group message ex: {}", ExceptionUtils.getStackTrace(e));
        }
    }

    // 发送单聊通知群组所有人 ,且 发送群聊通知
    public void sendChatGroupMsg(ObjectId roomId, String jid, MessageBean messageBean) {
        try {
            // 发送单聊
            messageBean.setMsgType(0);
            messageBean.setMessageId(StringUtil.randomUUID());
            KXMPPServiceImpl.getInstance().send(messageBean, getMemberIdList(roomId));
            // 发送群聊
            ThreadUtil.executeInThread(obj -> {
                try {
                    KXMPPServiceImpl.getInstance().sendMsgToGroupByJid(jid, messageBean);
                } catch (Exception e) {
                    logger.info("send chat group message ex: {}", ExceptionUtils.getStackTrace(e));
                }
            });
        } catch (Exception e) {
            logger.info("send chat group message ex: {}", ExceptionUtils.getStackTrace(e));
        }
    }


    /**
     * @param @param roomId  群主ID
     * @param @param toUserId   新群主 用户ID   必须 是 群内成员
     * @Description: TODO(群主 转让)
     */
    public Room transfer(Room room, Integer toUserId) {

        String nickName = getUserManager().getNickName(toUserId);
        Query<Room> roomQuery = getRoomDatastore().createQuery(getEntityClass()).filter("_id", room.getId());
        UpdateOperations<Room> roomOps = getRoomDatastore().createUpdateOperations(getEntityClass());
        roomOps.set("userId", toUserId);
        roomOps.set("nickname", nickName);
        getRoomDatastore().update(roomQuery, roomOps);

        /*修改 旧群主的角色*/
        Query<Member> query = getRoomDatastore().createQuery(Member.class);
        query.filter("roomId", room.getId());
        query.filter("userId", room.getUserId());
        UpdateOperations<Member> operations = getRoomDatastore().createUpdateOperations(Member.class);
        operations.set("role", 3);
        getRoomDatastore().update(query, operations);

        /*赋值新群主的角色*/
        query = getRoomDatastore().createQuery(Member.class);
        query.filter("roomId", room.getId());
        query.filter("userId", toUserId);
//        query.filter("talkTime",0);
        operations = getRoomDatastore().createUpdateOperations(Member.class);
        operations.set("role", 1);
        operations.set("talkTime",0);
        getRoomDatastore().update(query, operations);
        // 更新群组、群成员相关缓存
        updateRoomInfoByRedis(room.getId().toString());
        MessageBean message = new MessageBean();
        message.setType(KXMPPServiceImpl.RoomTransfer);
        message.setFromUserId(room.getUserId().toString());
        message.setFromUserName(getMemberNickname(room.getId(), room.getUserId()));
        message.setObjectId(room.getJid());
        message.setToUserId(toUserId.toString());
        message.setToUserName(getUserManager().getNickName(toUserId));
        message.setMessageId(StringUtil.randomUUID());
        // 发送单聊通知被转让的人、群聊通知
        sendChatToOneGroupMsg(toUserId, room.getJid(), message);
        return get(room.getId());
    }


    @Override
    public Room get(ObjectId roomId, Integer pageIndex, Integer pageSize) {
        // redis room 不包含 members noties
        Room redisRoom = TigBeanUtils.getRedisService().queryRoom(roomId);
        if (null != redisRoom) {
            if (-1 == redisRoom.getS()) {
                throw new ServiceException("该群组已被锁定！");
            }
            Room specialRoom = specialHandleByRoom(redisRoom, roomId, pageIndex, pageSize);
            return specialRoom;
        } else {
            Room room = getRoomDatastore().createQuery(getEntityClass()).field("_id").equal(roomId).get();
            if (null != room && -1 == room.getS()) {
                throw new ServiceException("该群组已被锁定！");
            }
            if (null == room) {
                throw new ServiceException("群组不存在！");
            }
            Room specialRoom = specialHandleByRoom(room, roomId, pageIndex, pageSize);
            return specialRoom;
        }
    }


    /**
     * @param room
     * @param roomId
     * @return
     * @Description: 房间相关特殊处理操作
     **/
    public Room specialHandleByRoom(Room room, ObjectId roomId, Integer pageIndex, Integer pageSize) {
        // 特殊身份处理
        Member member = TigBeanUtils.getRoomManagerImplForIM().getMember(roomId, ReqUtil.getUserId());
        if (null == member) {
            // 主动加群（二维码扫描），该用户不再群组内，需要members
            Room joinRoom = getRoom(roomId);
//			List<Member> members = getMembers(roomId,pageIndex,pageSize);
            List<Member> members = getHeadMemberListByPageImpls(roomId, pageSize);
            joinRoom.setMembers(members);
            return joinRoom;
        }
        int role = member.getRole();
        List<Member> members = null;
//		 监护人和隐身人不能互看  保证每次都有自己
        if (1 != member.getRole()) {
            Query<Member> query = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).order("role").order("createTime").offset(pageIndex * pageSize).limit(pageSize);
            if (role > 1 && role < 4) {
                Query<Member> queryMemberSize = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).offset(pageIndex * pageSize).limit(pageSize);
                query.field("role").lessThan(4).order("role");
                members = query.asList();
                int specialSize = queryMemberSize.field("role").greaterThanOrEq(4).asList().size();// 隐身人监护人
                room.setUserSize(room.getUserSize() - specialSize);
            } else if (role == 4 || role == 5) {
                // 隐身人
                query.or(query.criteria("role").lessThan(4), query.criteria("userId").equal(ReqUtil.getUserId()));
                members = query.asList();
                room.setUserSize(members.size());
            }
//            room.setMembers(this.addLoginLog(members));
            room.setMembers(members);
        } else {
//			List<Member> membersList = getMembers(roomId,pageIndex,pageSize);
            List<Member> membersList = getHeadMemberListByPageImpls(roomId, pageSize);
//            room.setMembers(this.addLoginLog(membersList));
            room.setMembers(membersList);
        }
        // 群公告
        List<Notice> noticesCache = getRedisServiceImpl().getNoticeList(roomId);
        if (null != noticesCache && noticesCache.size() > 0) {
            room.setNotices(noticesCache);
        } else {
            List<Notice> noticesDB = getRoomDatastore().createQuery(Room.Notice.class).field("roomId").equal(roomId).order("-time").asList();
            room.setNotices(noticesDB);
            /**
             * 维护群公告列表缓存
             */
            getRedisServiceImpl().saveNoticeList(roomId, noticesDB);
        }
        room.setMembers(buildVipData(room.getMembers()));
        return room;
    }
    //在群信息列表中添加成员的登录信息
//    public List<Member> addLoginLog(List<Member> members){
//        if (members != null && members.size() != 0){
//            List<Integer> userIds = new ArrayList<>();
//            members.forEach(menber -> {
//                userIds.add(menber.getUserId());
//                Query<User> user = TigBeanUtils.getDatastore().createQuery(User.class).field("_id").equal(menber.getUserId());
//                Query<User.UserLoginLog> userLoginLogs = TigBeanUtils.getDatastore().createQuery(User.UserLoginLog.class).field("_id").equal(menber.getUserId());
//                if (null != userLoginLogs.get() && null != user.get())
//                    user.get().setLoginLog(userLoginLogs.get().getLoginLog());
//                menber.setUser(user.get());
//            });
////			Query<User> user = TigBeanUtils.getDatastore().createQuery(User.class).field("_id").equal(userIds.iterator());
////			Query<User.UserLoginLog> userLoginLogs = TigBeanUtils.getDatastore().createQuery(User.UserLoginLog.class).field("_id").equal(menber.getUserId());
//
//        }
//        return members;
//    }
    /**
     * @param roomId
     * @param pageIndex
     * @param pageSize
     * @return
     * @Description: 首先返回群主、管理员然后按加群时间排序
     **/
    @SuppressWarnings("deprecation")
    public List<Member> getMembers(ObjectId roomId, Integer pageIndex, Integer pageSize) {
        List<Member> members = new ArrayList<Member>();
        // 群成员
        List<Member> memberCacheList = getRedisServiceImpl().getMemberList(roomId.toString(), pageIndex, pageSize);
        if (null != memberCacheList && memberCacheList.size() > 0) {
            members = memberCacheList;
        } else {
            // 群主管理员
            Query<Member> memberQuery = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("role").lessThanOrEq(2).order("role").order("createTime");
            List<Member> adminList = memberQuery.asList();
            // 普通成员
            Query<Member> query = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("role").greaterThan(2).order("createTime");

            List<Member> memberAllList = query.asList();
            int adminSize = adminList.size();
            if (pageSize > adminSize) {
                pageSize -= adminSize;
//				Query<Member> query = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("role").greaterThan(2).order("createTime").offset(pageIndex*pageSize).limit(pageSize);
                query.offset(pageIndex * pageSize).limit(pageSize);
                List<Member> memberList = query.asList();// 普通群成员
                members.addAll(adminList);
                members.addAll(memberList);
            } else {
                Query<Member> limit = memberQuery.offset(pageIndex * pageSize).limit(pageSize);
                members = limit.asList();
            }
            List<Member> dbMembers = new ArrayList<Member>();
            dbMembers.addAll(adminList);
            dbMembers.addAll(memberAllList);
            // 维护群成员缓存数据
            getRedisServiceImpl().saveMemberList(roomId.toString(), dbMembers);
        }

        return members;
    }

    /**
     * @param roomId
     * @param pageSize
     * @return
     * @Description:room/get 和 joinTime 为0时返回群成员 列表
     * // 补全问题 ： 例如 ：pageSize = 100 。  第一种情况 小于pageSize{ 群组 + 管理员 = 80人   返回 80+20普通群成员}
     * 第二种情况 大于等于pageSize{ 群组 + 管理员 = 120人   返回 120人 + 1名最先加群的普通群成员主要拿到createTime}
     **/
    @SuppressWarnings("deprecation")
    public List<Member> getHeadMemberListByPageImpls(ObjectId roomId, Integer pageSize) {
        List<Member> members = new ArrayList<Member>();
//		List<Integer> userIds = new ArrayList<Integer>();
        // 群主管理员
        Query<Member> adminMemberQuery = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("role").lessThanOrEq(2).order("role").order("createTime");
        List<Member> adminList = adminMemberQuery.asList();
        int adminSize = adminList.size();
        if (adminSize < pageSize) {
            // 补全pageSize
            members.addAll(adminMemberQuery.asList());
            Query<Member> lessMemberQuery = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("role").greaterThan(2).order("createTime").limit(pageSize - adminSize);
            members.addAll(lessMemberQuery.asList());
        } else {
            members.addAll(adminMemberQuery.asList());
            Query<Member> lessMemberQuery = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("role").greaterThan(2).order("createTime").limit(1);
            members.addAll(lessMemberQuery.asList());
        }

//		Query<User> q = TigBeanUtils.getUserRepository().getDatastore().createQuery(User.class);
//		members.forEach(e->{
//			userIds.add(e.getUserId());
//		});
//		List<User> users = q.field("_id").hasAnyOf(userIds).asList();
//		members.forEach(e->{
//			for (int i = 0; i < users.size(); i++) {
//				if (e.getUserId() == users.get(i).getUserId()){
//					e.setVip(users.get(i).getVip());
//				}
//			}
//		});
        return buildVipData(members);

    }

    private List<Member> buildVipData(List<Member> members) {
        List<Integer> userIds = new ArrayList<>();
        Query<User> q = TigBeanUtils.getUserRepository().getDatastore().createQuery(User.class);
        Query<User.UserLoginLog> qLog = TigBeanUtils.getUserRepository().getDatastore().createQuery(User.UserLoginLog.class);
        members.forEach(e -> userIds.add(e.getUserId()));
        List<User> users = q.field("_id").hasAnyOf(userIds).asList();
        List<User.UserLoginLog> usersLog = qLog.field("_id").hasAnyOf(userIds).asList();
        members.forEach(e -> {
            for (int i = 0; i < users.size(); i++) {
                if (e.getUserId().longValue() == users.get(i).getUserId().longValue()) {
                    e.setVip(users.get(i).getVip());
                    e.setIsAddFirend(users.get(i).getIsAddFirend());
                    e.setOnLineState(users.get(i).getOnlinestate());
                }
            }
        });
        members.forEach(e -> {
            for (int i = 0; i < usersLog.size(); i++) {
                if (e.getUserId().longValue() == usersLog.get(i).getUserId().longValue()) {
                    if(null!=usersLog.get(i).getLoginLog()){
                        e.setOfflineTime(usersLog.get(i).getLoginLog().getOfflineTime());
                    }

                }
            }
        });
        return members;
    }

    /**
     * @param roomId
     * @param joinTime
     * @param pageSize
     * @return
     * @Description:群成员分页
     **/
    @SuppressWarnings("deprecation")
    public List<Member> getMemberListByPageImpl(ObjectId roomId, long joinTime, Integer pageSize) {
        if (0 == joinTime) {
            return getHeadMemberListByPageImpls(roomId, pageSize);
        }
        Query<Member> memberQuery = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("role").greaterThan(2).field("createTime").greaterThanOrEq(joinTime).order("createTime").limit(pageSize);
        return buildVipData(memberQuery.asList());
    }

    public Room consoleGetRoom(ObjectId roomId) {
        Room room = getRoomDatastore().createQuery(getEntityClass()).field("_id").equal(roomId).get();
        if (null != room) {
            List<Member> members = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).order("createTime").order("role").asList();
            List<Notice> notices = getRoomDatastore().createQuery(Room.Notice.class).field("roomId").equal(roomId).order("-time").asList();

            room.setMembers(members);
            room.setNotices(notices);
            if (0 == room.getUserSize()) {
                room.setUserSize(members.size());
                DBObject q = new BasicDBObject("_id", roomId);
                DBObject o = new BasicDBObject("$set", new BasicDBObject("userSize", members.size()));
                getRoomDatastore().getCollection(getEntityClass()).update(q, o);
            }
        }
        return room;
    }


    /**
     * @param @param  roomId
     * @param @return 参数
     * @Description: TODO(只获取群组详情 ， 群主和管理员信息 ， 不获取普通群成员列表和公告列表, )
     */
    public Room getRoom(ObjectId roomId) {
        Room room;
        Room roomCache = getRedisServiceImpl().queryRoom(roomId);
        if (null != roomCache) {
            room = roomCache;
        } else {
            Room roomDB = getRoomDatastore().createQuery(getEntityClass()).field("_id").equal(roomId).get();
            if (null == roomDB){
                throw new ServiceException("房间不存在");
            }
            room = roomDB;
            /**
             * 缓存 房间
             */
            getRedisServiceImpl().saveRoom(room);
        }
        // 群组和管理员信息
        room.setMembers(getAdministrationMemberList(roomId));
        return room;
    }

    public Integer getCreateUserId(ObjectId roomId) {
        return (Integer) queryOneField("userId", new BasicDBObject("_id", roomId));
    }

    public ObjectId getRoomId(String jid) {
        return (ObjectId) queryOneField("_id", new BasicDBObject("jid", jid));
    }

    public String queryRoomJid(ObjectId roomId) {
        return (String) queryOneFieldById("jid", roomId);
    }

    public Integer queryRoomStatus(ObjectId roomId) {
        return (Integer) queryOneFieldById("s", roomId);
    }

    public String getRoomName(String jid) {
        return (String) queryOneField("name", new BasicDBObject("jid", jid));
    }

    public String getRoomName(ObjectId roomId) {
        return (String) queryOneField("name", new BasicDBObject("_id", roomId));
    }

    // 房间状态
    public Integer getRoomStatus(ObjectId roomId) {
        return (Integer) queryOneField("s", new BasicDBObject("_id", roomId));
    }

    @Override
    public List<Room> selectList(int pageIndex, int pageSize, String roomName) {
        Query<Room> q = getRoomDatastore().createQuery(getEntityClass());
        if (!StringUtil.isEmpty(roomName)) {
            //q.field("name").contains(roomName);
            q.or(q.criteria("name").containsIgnoreCase(roomName),
                    q.criteria("desc").containsIgnoreCase(roomName));
        }
        q.filter("isLook", 0);
        List<Room> roomList = q.offset(pageIndex * pageSize).limit(pageSize).order("-_id").asList();
        return roomList;
    }

    /**
     * @param @param  userId
     * @param @return 参数
     * @Description: TODO(查询用户加入的所有群的jid)
     */
    public List<String> queryUserRoomsJidList(int userId) {
        List<ObjectId> roomIdList = queryUserRoomsIdList(userId);
        BasicDBObject query = new BasicDBObject("_id", new BasicDBObject(MongoOperator.IN, roomIdList));
        return getRoomDatastore().getCollection(getEntityClass()).distinct("jid", query);
    }

    /**
     * @param userId
     * @return
     * @Description:在表Tig_ROOMJIDS_USERID 下查询用户加入的所有群的jid
     **/
    public List<String> queryUserRoomsJidListByDB(int userId) {
        DBCollection collection = getRoomDatastore().getDB().getCollection(Tig_ROOMJIDS_USERID);
        BasicDBObject query = new BasicDBObject("userId", userId);
        return collection.distinct("jids", query);
    }

    /**
     * 查询用户开启免打扰的  群组Jid 列表
     *
     * @param userId
     * @return
     */
    public List<String> queryUserNoPushJidList(int userId) {
        BasicDBObject query = new BasicDBObject("userId", userId);
        query.append("offlineNoPushMsg", 1);
        return getRoomDatastore().getCollection(Member.class).distinct("jid", query);
    }

    /**
     * @param @param  userId
     * @param @return 参数
     * @Description: TODO(查询用户加入的所有群的roomId)
     */
    public List<ObjectId> queryUserRoomsIdList(int userId) {
        BasicDBObject query = new BasicDBObject("userId", userId);
        return getRoomDatastore().getCollection(Member.class).distinct("roomId", query);

    }

    @Override
    public Object selectHistoryList(int userId, int type) {
        List<Object> historyIdList = Lists.newArrayList();

        Query<Room.Member> q = getRoomDatastore().createQuery(Room.Member.class).field("userId").equal(userId);
        if (1 == type) {// 自己的房间
            q.filter("role =", 1);
        } else if (2 == type) {// 加入的房间
            q.filter("role !=", 1);
        }
        DBCursor cursor = getRoomDatastore().getCollection(Room.Member.class).find(q.getQueryObject(),
                new BasicDBObject("roomId", 1));
        while (cursor.hasNext()) {
            DBObject dbObj = cursor.next();
            historyIdList.add(dbObj.get("roomId"));
        }
        if (historyIdList.isEmpty()) {
            return null;
        }
        List<Room> historyList = getRoomDatastore().createQuery(getEntityClass()).field("_id").in(historyIdList).order("-_id").asList();
        historyList.forEach(room -> {
            Member member = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(room.getId()).field("userId").equal(userId).get();
            room.setMember(member);
        });
        return historyList;
    }

    @SuppressWarnings("deprecation")
    @Override
    public Object selectHistoryList(int userId, int type, int pageIndex, int pageSize) {
        List<Object> historyIdList = Lists.newArrayList();
        Query<Room.Member> q = getRoomDatastore().createQuery(Room.Member.class).field("userId").equal(userId);
        if (1 == type) {// 自己的房间
            q.filter("role =", 1);
        } else if (2 == type) {// 加入的房间
            q.filter("role !=", 1);
        }
        DBCursor cursor = getRoomDatastore().getCollection(Room.Member.class).find(q.getQueryObject(), new BasicDBObject("roomId", 1));
        while (cursor.hasNext()) {
            DBObject dbObj = cursor.next();
            historyIdList.add(dbObj.get("roomId"));
        }
        if (historyIdList.isEmpty()) {
            return null;
        }
//		List<Room> historyList = getRoomDatastore().createQuery(getEntityClass()).field("_id").in(historyIdList).order("-_id").offset(pageIndex * pageSize).limit(pageSize).asList();
        Query<Room> limit = getRoomDatastore().createQuery(getEntityClass()).field("_id").in(historyIdList).field("s").equal(1).order("-_id").offset(pageIndex * pageSize).limit(pageSize);
        List<Room> historyList = limit.asList();
        historyList.forEach(room -> {
            Member member = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(room.getId()).field("userId").equal(userId).get();
            room.setMember(member);
            List<Member> members = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(room.getId()).offset(0).limit(9).asList();
            room.setMembers(members);
        });
        return historyList;
    }

    /* (non-Javadoc)
     * @see cn.xyz.mianshi.service.RoomManager#deleteMember(cn.xyz.mianshi.vo.User, org.bson.types.ObjectId, int)
     */
    /* (non-Javadoc)
     * @see cn.xyz.mianshi.service.RoomManager#deleteMember(cn.xyz.mianshi.vo.User, org.bson.types.ObjectId, int)
     */
    @Override
    public void deleteChatLog(User user, ObjectId roomId, int userId) {
        Room room = getRoom(roomId);
        if (-1 == room.getS()) {
            throw new ServiceException("该群已经被锁定！");
        }
        Member roomMember = getMember(roomId, user.getUserId());
        Member member = getMember(roomId, userId);
        if (member == null) {
            throw new ServiceException("该成员不在群组中");
        }
        // 处理后台管理员
        if (null == roomMember) {
            if (room.getUserId().equals(userId)) {
                throw new ServiceException("不能移出群主");
            }
            // 后台管理员
            Query<Role> roleQuery = TigBeanUtils.getDatastore().createQuery(Role.class).field("userId").equal(user.getUserId());
            if (null != roleQuery.get()) {
                if (5 == roleQuery.get().getRole() || 6 == roleQuery.get().getRole()) {
                    if (-1 == roleQuery.get().getStatus()) {
                        throw new ServiceException("该后台管理员状态异常");
                    }
                }
            } else {
                throw new ServiceException("该成员不在该群组中");
            }
        } else {
            // 自己退群
            if (!user.getUserId().equals(userId)) {
                if (roomMember.getRole() >= 3) {
                    throw new ServiceException("暂无踢人权限");
                }
                if (room.getUserId().equals(userId)) {
                    throw new ServiceException("不能移出群主");
                }
                // 处理管理员踢管理员和隐身人监护人的问题
                if (member.getRole() != 1 && member.getRole() != 3) {
                    // 处理群主和后台管理员踢人问题
                    if (2 == roomMember.getRole()) {
                        throw new ServiceException("管理员不能踢出" + (2 == member.getRole() ? "管理员" : 4 == member.getRole() ? "隐身人" : "监护人"));
                    }
                }
            }
        }
        User toUser = getUserManager().getUser(userId);


        // IMPORTANT 1-4、删除成员推送-已改
        MessageBean messageBean = new MessageBean();
        messageBean.setFromUserId(user.getUserId() + "");
        messageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
        messageBean.setType(KXMPPServiceImpl.DELETE_MEMBER);
        // messageBean.setObjectId(roomId.toString());
        messageBean.setObjectId(room.getJid());
        messageBean.setToUserId(userId + "");
        messageBean.setToUserName(toUser.getNickname());
        messageBean.setContent(room.getName());
        messageBean.setMessageId(StringUtil.randomUUID());

        // 群组减员发送通知
        if (1 == room.getIsAttritionNotice()) {
            // 发送单聊通知被踢出本人、群聊
            sendChatToOneGroupMsg(userId, room.getJid(), messageBean);
        } else {
            sendGroupOne(userId, messageBean);
        }
        Query<Room.Member> q = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("userId").equal(userId);
        getRoomDatastore().delete(q);
        updateUserSize(roomId, -1);
//		List<Integer> memberIdList = getMemberIdList(roomId, 0);
//		updateMemerListToRedis(roomId.toString(), memberIdList);
        // 维护用户加入群组的jids
        saveJidsByUserId(userId, queryUserRoomsJidList(userId));
        /**
         * 删除 用户加入的群组 jid  缓存
         */
        TigBeanUtils.getRedisService().deleteUserRoomJidList(userId);
        TigBeanUtils.getRedisService().removeRoomPushMember(room.getJid(), userId);
        /**
         * 维护群组、群成员 缓存
         */
        updateRoomInfoByRedis(roomId.toString());
    }

    @Resource(name = "dsForRoom")
    private Datastore dsForRoom;


    /* (non-Javadoc)
     * @see cn.xyz.mianshi.service.RoomManager#deleteMember(cn.xyz.mianshi.vo.User, org.bson.types.ObjectId, int)
     */
    /* (non-Javadoc)
     * @see cn.xyz.mianshi.service.RoomManager#deleteMember(cn.xyz.mianshi.vo.User, org.bson.types.ObjectId, int)
     */
    @Override
    public void deleteMember(User user, ObjectId roomId, int userId) {
        Room room = getRoom(roomId);
        if (-1 == room.getS()) {
            throw new ServiceException("该群已经被锁定！");
        }
        Member roomMember = getMember(roomId, user.getUserId());
        Member member = getMember(roomId, userId);
        if (member == null) {
            throw new ServiceException("该成员不在群组中");
        }
        // 处理后台管理员
        if (null == roomMember) {
            if (room.getUserId().equals(userId)) {
                throw new ServiceException("不能移出群主");
            }
            // 后台管理员
            Query<Role> roleQuery = TigBeanUtils.getDatastore().createQuery(Role.class).field("userId").equal(user.getUserId());
            if (null != roleQuery.get()) {
                if (5 == roleQuery.get().getRole() || 6 == roleQuery.get().getRole()) {
                    if (-1 == roleQuery.get().getStatus()) {
                        throw new ServiceException("该后台管理员状态异常");
                    }
                }
            } else {
                throw new ServiceException("该成员不在该群组中");
            }
        } else {
            // 自己退群
            if (!user.getUserId().equals(userId)) {
                if (roomMember.getRole() >= 3) {
                    throw new ServiceException("暂无踢人权限");
                }
                if (room.getUserId().equals(userId)) {
                    throw new ServiceException("不能移出群主");
                }
                // 处理管理员踢管理员和隐身人监护人的问题
                if (member.getRole() != 1 && member.getRole() != 3) {
                    // 处理群主和后台管理员踢人问题
                    if (2 == roomMember.getRole()) {
                        throw new ServiceException("管理员不能踢出" + (2 == member.getRole() ? "管理员" : 4 == member.getRole() ? "隐身人" : "监护人"));
                    }
                }
            }
        }
        User toUser = getUserManager().getUser(userId);

        // IMPORTANT 1-4、删除成员推送-已改
        MessageBean messageBean = new MessageBean();
        messageBean.setFromUserId(user.getUserId() + "");
        messageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
        messageBean.setType(KXMPPServiceImpl.DELETE_MEMBER);
        // messageBean.setObjectId(roomId.toString());
        messageBean.setObjectId(room.getJid());
        messageBean.setToUserId(userId + "");
        messageBean.setToUserName(toUser.getNickname());
        messageBean.setContent(room.getName());
        messageBean.setMessageId(StringUtil.randomUUID());


        // 群组减员发送通知
        if (1 == room.getIsAttritionNotice()) {
            // 发送单聊通知被踢出本人、群聊
            sendChatToOneGroupMsg(userId, room.getJid(), messageBean);
        } else {
            sendGroupOne(userId, messageBean);
        }


        //判断当前用户是否VIP，vip则需要删除踢人的群聊天记录，且需要发送全员通知，删除本地消息记录
        if(user.getVip() != 0){
            DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + room.getJid());
            BasicDBObject roomChatLog = new BasicDBObject();
            roomChatLog.put("sender", new BasicDBObject("$eq", userId));
            dbCollection.remove(roomChatLog);

            //删除成员删除本地消息记录推送
            MessageBean removeMessageBean = new MessageBean();
            removeMessageBean.setFromUserId(user.getUserId() + "");
            removeMessageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
            removeMessageBean.setType(MsgType.TYPE_REMOVE);
            removeMessageBean.setObjectId(room.getJid());
            removeMessageBean.setToUserId(userId + "");
            removeMessageBean.setToUserName(toUser.getNickname());
            removeMessageBean.setContent(room.getName());
            removeMessageBean.setMessageId(StringUtil.randomUUID());

            // 群组减员发送通知
            if (1 == room.getIsAttritionNotice()) {
                // 发送单聊群聊
                sendChatGroupMsg(roomId, room.getJid(), removeMessageBean);
            } else {
                sendGroupOne(userId, messageBean);
            }
        }

        Query<Room.Member> q = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("userId").equal(userId);
        getRoomDatastore().delete(q);
        updateUserSize(roomId, -1);
//		List<Integer> memberIdList = getMemberIdList(roomId, 0);
//		updateMemerListToRedis(roomId.toString(), memberIdList);
        // 维护用户加入群组的jids
        saveJidsByUserId(userId, queryUserRoomsJidList(userId));
        /**
         * 删除 用户加入的群组 jid  缓存
         */
        TigBeanUtils.getRedisService().deleteUserRoomJidList(userId);
        TigBeanUtils.getRedisService().removeRoomPushMember(room.getJid(), userId);
        /**
         * 维护群组、群成员 缓存
         */
        updateRoomInfoByRedis(roomId.toString());
    }

    public void deleteMembers(User user, ObjectId roomId, int userId,List deleteFailedList,int j,List deleteFailedNicknameList) {
        Room room = getRoom(roomId);
        if (-1 == room.getS()) {
            throw new ServiceException("该群已经被锁定！");
        }
        Member roomMember = getMember(roomId, user.getUserId());
        Member member = getMember(roomId, userId);
        User toUser = getUserManager().getUser(userId);
        if (member == null) {
//            throw new ServiceException("该成员不在群组中");
            deleteFailedList.add(j,userId);
            deleteFailedNicknameList.add(j,toUser.getNickname());
            j++;
            return;
        }
        // 处理后台管理员
        if (null == roomMember) {
            if (room.getUserId().equals(userId)) {
//                throw new ServiceException("不能移出群主");
                deleteFailedList.add(j,userId);
                deleteFailedNicknameList.add(j,toUser.getNickname());
                j++;
                return;
            }
            // 后台管理员
            Query<Role> roleQuery = TigBeanUtils.getDatastore().createQuery(Role.class).field("userId").equal(user.getUserId());
            if (null != roleQuery.get()) {
                if (5 == roleQuery.get().getRole() || 6 == roleQuery.get().getRole()) {
                    if (-1 == roleQuery.get().getStatus()) {
                        throw new ServiceException("该后台管理员状态异常");
                    }
                }
            } else {
//                throw new ServiceException("该成员不在该群组中");
                deleteFailedList.add(j,userId);
                deleteFailedNicknameList.add(j,toUser.getNickname());
                j++;
                return;
            }
        } else {
            // 自己退群
            if (!user.getUserId().equals(userId)) {
                if (roomMember.getRole() >= 3) {
                    throw new ServiceException("暂无踢人权限");
                }
                if (room.getUserId().equals(userId)) {
//                    throw new ServiceException("不能移出群主");
                    deleteFailedList.add(j,userId);
                    deleteFailedNicknameList.add(j,toUser.getNickname());
                    j++;
                    return;
                }
                // 处理管理员踢管理员和隐身人监护人的问题
                if (member.getRole() != 1 && member.getRole() != 3) {
                    // 处理群主和后台管理员踢人问题
                    if (2 == roomMember.getRole()) {
                        throw new ServiceException("管理员不能踢出" + (2 == member.getRole() ? "管理员" : 4 == member.getRole() ? "隐身人" : "监护人"));
                    }
                }
            }
        }
        // IMPORTANT 1-4、删除成员推送-已改
        MessageBean messageBean = new MessageBean();
        messageBean.setFromUserId(user.getUserId() + "");
        messageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
        messageBean.setType(KXMPPServiceImpl.DELETE_MEMBER);
        // messageBean.setObjectId(roomId.toString());
        messageBean.setObjectId(room.getJid());
        messageBean.setToUserId(userId + "");
        messageBean.setToUserName(toUser.getNickname());
        messageBean.setContent(room.getName());
        messageBean.setMessageId(StringUtil.randomUUID());
        // 群组减员发送通知
        if (1 == room.getIsAttritionNotice()) {
            // 发送单聊通知被踢出本人、群聊
            sendChatToOneGroupMsg(userId, room.getJid(), messageBean);
        } else {
            sendGroupOne(userId, messageBean);
        }
        Query<Room.Member> q = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("userId").equal(userId);
        getRoomDatastore().delete(q);



        //判断当前用户是否VIP，vip则需要删除踢人的群聊天记录，且需要发送全员通知，删除本地消息记录
        if(user.getVip() != 0){
            DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + room.getJid());
            BasicDBObject roomChatLog = new BasicDBObject();
            roomChatLog.put("sender", new BasicDBObject("$eq", userId));
            dbCollection.remove(roomChatLog);

            //删除成员删除本地消息记录推送
            MessageBean removeMessageBean = new MessageBean();
            removeMessageBean.setFromUserId(user.getUserId() + "");
            removeMessageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
            removeMessageBean.setType(MsgType.TYPE_REMOVE);
            removeMessageBean.setObjectId(room.getJid());
            removeMessageBean.setToUserId(userId + "");
            removeMessageBean.setToUserName(toUser.getNickname());
            removeMessageBean.setContent(room.getName());
            removeMessageBean.setMessageId(StringUtil.randomUUID());

            // 群组减员发送通知
            if (1 == room.getIsAttritionNotice()) {
                // 发送单聊群聊
                sendChatGroupMsg(roomId, room.getJid(), removeMessageBean);
            } else {
                sendGroupOne(userId, messageBean);
            }
        }


        updateUserSize(roomId, -1);
//		List<Integer> memberIdList = getMemberIdList(roomId, 0);
//		updateMemerListToRedis(roomId.toString(), memberIdList);
        // 维护用户加入群组的jids
        saveJidsByUserId(userId, queryUserRoomsJidList(userId));
        /**
         * 删除 用户加入的群组 jid  缓存
         */
        TigBeanUtils.getRedisService().deleteUserRoomJidList(userId);
        TigBeanUtils.getRedisService().removeRoomPushMember(room.getJid(), userId);
        /**
         * 维护群组、群成员 缓存
         */
        updateRoomInfoByRedis(roomId.toString());
    }

    @Override
    public void updateMember(User user, ObjectId roomId, List<Integer> userIdList) {
        for (int userId : userIdList) {
            Member _member = new Member();
            _member.setUserId(userId);
            _member.setRole(3);
            updateMember(user, roomId, _member);
        }
    }

    @Override
    public void updateMember(User user, ObjectId roomId, Member member) {
        DBCollection dbCollection = getRoomDatastore().getCollection(Room.Member.class);
        DBObject q = new BasicDBObject().append("roomId", roomId).append("userId", member.getUserId());
        Room room = getRoom(roomId);
        if (null != room && room.getS() == -1) {
            throw new ServiceException("该群组已被后台锁定");
        }
        User toUser = getUserManager().getUser(member.getUserId());
        Member oldMember = getMember(roomId, toUser.getUserId());
        if (1 == dbCollection.count(q)) {
            BasicDBObject values = new BasicDBObject();
            if (0 != member.getRole()) {
                values.append("role", member.getRole());
            }
            if (null != member.getSub()) {
                values.append("sub", member.getSub());
            }
            if (null != member.getTalkTime()) {
                values.append("talkTime", member.getTalkTime());
            }
            if (!StringUtil.isEmpty(member.getNickname())) {
                values.append("nickname", member.getNickname());
            }
            if (!StringUtil.isEmpty(member.getRemarkName())) {
                values.append("remarkName", member.getRemarkName());
            }
            values.append("modifyTime", DateUtil.currentTimeSeconds());
            values.append("call", room.getCall());
            values.append("videoMeetingNo", room.getVideoMeetingNo());

            // 更新成员信息
            dbCollection.update(q, new BasicDBObject("$set", values));

            if (!StringUtil.isEmpty(member.getNickname()) && !oldMember.getNickname().equals(member.getNickname())) {
                // IMPORTANT 1-1、改昵称推送-已改
                MessageBean messageBean = new MessageBean();
                messageBean.setType(KXMPPServiceImpl.CHANGE_NICK_NAME);
                // messageBean.setObjectId(roomId.toString());
                messageBean.setObjectId(room.getJid());
                messageBean.setFromUserId(user.getUserId() + "");
                messageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
                messageBean.setToUserId(toUser.getUserId() + "");
                messageBean.setToUserName(oldMember.getNickname());
                messageBean.setContent(member.getNickname());
                messageBean.setMessageId(StringUtil.randomUUID());
                // 发送群聊
                sendGroupMsg(room.getJid(), messageBean);
            }
            if (null != member.getTalkTime()) {
                // IMPORTANT 1-6、禁言
                MessageBean messageBean = new MessageBean();
                messageBean.setType(KXMPPServiceImpl.GAG);
                // messageBean.setObjectId(roomId.toString());
                messageBean.setObjectId(room.getJid());
                messageBean.setFromUserId(user.getUserId() + "");
                messageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
                messageBean.setToUserId(toUser.getUserId() + "");
                messageBean.setToUserName(oldMember.getNickname());
                messageBean.setContent(member.getTalkTime() + "");
                messageBean.setMessageId(StringUtil.randomUUID());
                // 发送单聊通知被禁言的人,群聊
                sendChatToOneGroupMsg(toUser.getUserId(), room.getJid(), messageBean);
            }
        } else {
            Member invitationMember = getMember(roomId, user.getUserId());
            if (null != invitationMember && 4 == invitationMember.getRole()) {
                throw new ServiceException("隐身人不可以邀请用户加群");
            }
            if (room.getMaxUserSize() < room.getUserSize() + 1) {
                throw new ServiceException("群人数已达到上限，不能继续加入");
            }
            User _user = getUserManager().getUser(member.getUserId());
            Member _member = new Member();
            _member.setActive(DateUtil.currentTimeSeconds());
            _member.setCreateTime(_member.getActive());
            _member.setModifyTime(0L);
            _member.setNickname(_user.getNickname());
            _member.setRole(member.getRole());
            _member.setRoomId(roomId);
            _member.setSub(1);
            _member.setTalkTime(0L);
            _member.setUserId(_user.getUserId());
            _member.setRole(3);
            getRoomDatastore().save(_member);

            updateUserSize(roomId, 1);

            // IMPORTANT 1-7、新增成员
            MessageBean messageBean = new MessageBean();
            messageBean.setType(KXMPPServiceImpl.NEW_MEMBER);
            // messageBean.setObjectId(roomId.toString());
            messageBean.setObjectId(room.getJid());
            messageBean.setFromUserId(user.getUserId() + "");
            messageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
            messageBean.setToUserId(toUser.getUserId() + "");
            messageBean.setToUserName(toUser.getNickname());


            messageBean.setFileSize(room.getShowRead());
            messageBean.setContent(room.getName());
            messageBean.setFileName(room.getId().toString());

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("showRead", room.getShowRead());
            jsonObject.put("lsLook", room.getIsLook());
            jsonObject.put("isNeedVerify", room.getIsNeedVerify());
            jsonObject.put("showMember", room.getShowMember());
            jsonObject.put("allowSendCard", room.getAllowSendCard());
            jsonObject.put("maxUserSize", room.getMaxUserSize());
            messageBean.setOther(jsonObject.toJSONString());
            messageBean.setMessageId(StringUtil.randomUUID());

            // 发送单聊通知到被邀请人， 群聊
            sendChatToOneGroupMsg(toUser.getUserId(), room.getJid(), messageBean);
            // 维护用户加入的群jids
            saveJidsByUserId(toUser.getUserId(), queryUserRoomsJidList(toUser.getUserId()));
            /**
             * 删除 用户加入的群组 jid  缓存
             */
            TigBeanUtils.getRedisService().deleteUserRoomJidList(member.getUserId());
            if (0 == TigBeanUtils.getUserManager().getOnlinestateByUserId(member.getUserId())) {
                TigBeanUtils.getRedisService().addRoomPushMember(room.getJid(), member.getUserId());
            }
        }

        /**
         * 维护群组、群成员缓存
         */
        updateRoomInfoByRedis(roomId.toString());
    }

    @Override
    public Member getMember(ObjectId roomId, int userId) {
//		Member member = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("userId").equal(userId).get();
        List<Member> members = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("userId").equal(userId).asList();
        members = buildVipData(members);
        return members.size() > 0 ? members.get(0) : null;

    }


    @Override
    public void Memberset(Integer offlineNoPushMsg, ObjectId roomId, int userId) {
        Query<Room.Member> q = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("userId").equal(userId);
        UpdateOperations<Room.Member> ops = getDatastore().createUpdateOperations(Room.Member.class);
        ops.set("offlineNoPushMsg", offlineNoPushMsg);
        String jid = queryRoomJid(roomId);
        if (1 == offlineNoPushMsg) {
            TigBeanUtils.getRedisService().addToRoomNOPushJids(userId, jid);
        } else {
            TigBeanUtils.getRedisService().removeToRoomNOPushJids(userId, jid);
        }
        getRoomDatastore().update(q, ops);
        // 维护群组、群成员相关属性
        updateRoomInfoByRedis(roomId.toString());
    }

    @Override
    public List<Member> getMemberList(ObjectId roomId, String keyword) {
        List<Member> list;
        if (!StringUtil.isEmpty(keyword)) {
            Query<Member> query = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId);
            query.field("nickname").containsIgnoreCase(keyword);
            list = query.order("createTime").order("role").asList();
        } else {
            List<Member> memberList = getRedisServiceImpl().getMemberList(roomId.toString());
            if (null != memberList && memberList.size() > 0) {
                list = memberList;
            } else {
                List<Member> memberDBList = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).asList();
                list = memberDBList;
//				getRedisServiceImpl().saveMemberList(roomId.toString(), memberDBList);
            }
        }
        return buildVipData(list);
    }

    /**
     * @param roomId
     * @return
     * @Description:获取群组中指定类型的用户（目前获取群主）
     **/
    public List<Member> getAdminMemberList(ObjectId roomId) {
        Query<Member> query = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).order("createTime");
        List<Member> memberPageList = query.field("role").lessThanOrEq(1).asList();
        return buildVipData(memberPageList);
    }

    /**
     * @param roomId
     * @return
     * @Description:获取群组中的群主和管理员
     **/
    public List<Member> getAdministrationMemberList(ObjectId roomId) {
        List<Member> members = null;
        // 群成员
        List<Member> memberList = getRedisServiceImpl().getMemberList(roomId.toString());
        if (null != memberList && memberList.size() > 0) {
            List<Member> adminMembers = new ArrayList<>();// 群组、管理员
            for (Member member : memberList) {
                if (member.getRole() == 1 || member.getRole() == 2) {
                    adminMembers.add(member);
                }
                members = adminMembers;
            }
        } else {
            Query<Member> query = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).order("createTime");
//            List<Member> membersList = query.asList();
            List<Member> memberPageList = query.field("role").lessThanOrEq(2).asList();
            members = memberPageList;
            /**
             * 维护群成员列表缓存
             */
//			getRedisServiceImpl().saveMemberList(roomId.toString(), membersList);
        }

        return buildVipData(members);
    }

    /**
     * @param roomId
     * @return
     * @Description: 普通群成员userId列表，除了管理员和群主
     **/
    @SuppressWarnings("unchecked")
    public List<Integer> getCommonMemberIdList(ObjectId roomId) {
        List<Integer> members = distinct("tig_room_member", "userId", new BasicDBObject("roomId", roomId).append("role", 3));
        return members;
    }

    /**
     * @param roomId
     * @return
     * @Description: 群成员userId列表
     **/
    @SuppressWarnings("unchecked")
    public List<Integer> getMemberIdList(ObjectId roomId) {
        List<Integer> members = distinct("tig_room_member", "userId", new BasicDBObject("roomId", roomId));
        return members;
    }

    @SuppressWarnings("unchecked")
    public List<ObjectId> getRoomIdList(Integer userId) {
        List<ObjectId> roomIds = distinct("tig_room_member", "roomId", new BasicDBObject("userId", userId));
        return roomIds;
    }

    /**
     * 查询成员是否开启 免打扰
     *
     * @param roomId
     * @param userId
     * @return
     */
    public boolean getMemberIsNoPushMsg(ObjectId roomId, int userId) {
        DBObject query = new BasicDBObject("roomId", roomId).append("userId", userId);
        query.put("offlineNoPushMsg", 1);
        Object field = queryOneField("tig_room_member", "offlineNoPushMsg", query);
        return null != field;
    }

    public String getMemberNickname(ObjectId roomId, Integer userId) {
        String nickname = null;
        Query<Member> query = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId);
        if (query.asList().size() == 0) {
            throw new ServiceException("群组不存在");
        }
        if (0 != userId) {
            query.field("userId").equal(userId);
            if (null == query.get()) {
                // 后台管理员
                Query<Role> roleQuery = TigBeanUtils.getDatastore().createQuery(Role.class).field("userId").equal(userId);
                if (null != roleQuery.get()) {
                    if (5 == roleQuery.get().getRole() || 6 == roleQuery.get().getRole()) {
                        if (1 == roleQuery.get().getStatus()) {
                            nickname = "后台管理员";// 后台管理员操作群设置
                        } else {
                            throw new ServiceException("该管理员状态异常请重试");
                        }
                    }
                } else {
                    throw new ServiceException("该成员不在该群组中");
                }
            } else {
                nickname = query.get().getNickname();
            }
        }
        return nickname;
    }

    /*公告列表*/
    public List<Notice> getNoticeList(ObjectId roomId) {
        List<Notice> notices;
        List<Notice> noticeList = getRedisServiceImpl().getNoticeList(roomId);
        if (null != noticeList && noticeList.size() > 0) {
            notices = noticeList;
        } else {
            List<Notice> noticesDB = getRoomDatastore().createQuery(Room.Notice.class).field("roomId").equal(roomId).asList();
            notices = noticesDB;
        }
        return notices;
    }

    /*公告列表*/
    public PageVO getNoticeList(ObjectId roomId, Integer pageIndex, Integer pageSize) {

        Query<Notice> query = getRoomDatastore().createQuery(Room.Notice.class).field("roomId").equal(roomId).order("-time");
        long total = query.count();
        List<Notice> pageData = query.offset(pageIndex * pageSize).limit(pageSize).asList();
        return new PageVO(pageData, total, pageIndex, pageSize);
    }

    public void deleteNotice(ObjectId roomId, ObjectId noticeId) {
        Query<Notice> query = getRoomDatastore().createQuery(Notice.class);
        query.filter("_id", noticeId);
        query.filter("roomId", roomId);
        getRoomDatastore().delete(query);
        // 维护room最新公告
        Room room = getRoom(roomId);
        if (null != room.getNotice() && noticeId.equals(room.getNotice().getId())) {
            updateAttribute(roomId, "notice", new Notice());
        }
        /**
         * 维护群组信息 、公告缓存
         */
        getRedisServiceImpl().deleteNoticeList(roomId);
        getRedisServiceImpl().deleteRoom(roomId.toString());
    }


    public PageResult<Member> getMemberListByPage(ObjectId roomId, int pageIndex, int pageSize,String keyWord) {
        Query<Room.Member> q = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).order("-createTime");
        if(!StringUtil.isEmpty(keyWord)){
            Pattern pattern = Pattern.compile("^.*" + keyWord + ".*$", Pattern.CASE_INSENSITIVE);
            q.filter("nickname",pattern);
        }
        //分页
        List<Member> pageData = q.asList(pageFindOption(pageIndex, pageSize, 1));
        pageData = buildVipData(pageData);
        for (Member member : pageData) {
            logger.info(member.getNickname());
        }
        return new PageResult<Member>(pageData, q.count()); //(pageData, total, pageIndex, pageSize,total);
    }


    @Override
    public void join(int userId, ObjectId roomId, int type) {
        Room room = getRoom(roomId);
        if (room != null) {
            if (room.getUserSize() + 1 > room.getMaxUserSize()) {
                throw new ServiceException("超过房间最大人数，加入房间失败");
            }
        } else {
            throw new ServiceException("房间不存在");
        }
        Member member = new Member();
        member.setUserId(userId);
        member.setRole(1 == type ? 1 : 3);
        updateMember(getUserManager().getUser(userId), roomId, member);
    }

    public void joinRoom(Integer userId, String name, ObjectId roomId, int type) {
        Room room = getRoom(roomId);
        if (room == null) {
            throw new ServiceException("房间不存在");
        }
        List<Member> memberList = Collections.synchronizedList(new ArrayList<>());
        List<MessageBean> messageList = Collections.synchronizedList(new ArrayList<>());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("showRead", room.getShowRead());
        jsonObject.put("lsLook", room.getIsLook());
        jsonObject.put("isNeedVerify", room.getIsNeedVerify());
        jsonObject.put("showMember", room.getShowMember());
        jsonObject.put("allowSendCard", room.getAllowSendCard());
        jsonObject.put("maxUserSize", room.getMaxUserSize());
        Member member = new Member();
        member.setUserId(userId);
        member.setRole(1 == type ? 1 : 3);
        member.setNickname(name);
        member.setCreateTime(DateUtil.currentTimeSeconds());
        member.setRoomId(roomId);
        member.setOfflineTime(DateUtil.currentTimeSeconds());
        memberList.add(member);
        getDatastore().save(member);
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.NEW_MEMBER);
        messageBean.setObjectId(room.getJid());
        messageBean.setFromUserId(userId + "");
        messageBean.setFromUserName(member.getNickname());
        messageBean.setToUserId(userId + "");
        messageBean.setToUserName(member.getNickname());
        messageBean.setFileSize(room.getShowRead());
        messageBean.setContent(room.getName());
        messageBean.setFileName(room.getId().toString());
        messageBean.setOther(jsonObject.toJSONString());
        messageBean.setMessageId(StringUtil.randomUUID());
        messageList.add(messageBean);
        updateUserSize(room.getId(), 1);
        /**
         * 维护群组、群成员缓存
         */
        updateRoomInfoByRedis(roomId.toString());
        KXMPPServiceImpl.getInstance().sendManyMsgToGroupByJid(room.getJid(), messageList);
    }

    private void updateUserSize(ObjectId roomId, int userSize) {
        DBObject q = new BasicDBObject("_id", roomId);
        DBObject o = new BasicDBObject("$inc", new BasicDBObject("userSize", userSize));
        getRoomDatastore().getCollection(getEntityClass()).update(q, o);
    }


    @Override
    public void leave(int userId, ObjectId roomId) {
    }

    @Override
    public Room exisname(Object roomname, ObjectId roomId) {
        Query<Room> query = getRoomDatastore().createQuery(getEntityClass());
        query.field("name").equal(roomname);
        if (null != roomId) {
            query.field("_id").notEqual(roomId);
        }
        Room room = query.get();
        return room;
    }


    /**
     * @param @param roomId
     * @param @param roomJid    参数
     * @Description: TODO(删除 群共享的文件 和 群聊天消息的文件)
     */
    public void destroyRoomMsgFileAndShare(ObjectId roomId, String roomJid) {
        //删除共享文件
        Query<Share> shareQuery = getRoomDatastore().createQuery(Room.Share.class).field("roomId").equal(roomId);
        List<String> shareList = getRoomDatastore().getCollection(Share.class).distinct("url", shareQuery.getQueryObject());
        for (String url : shareList) {
            try {
                ConstantUtil.deleteFile(url);
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        getRoomDatastore().delete(shareQuery);
        BasicDBObject msgFileQuery = new BasicDBObject("contentType", new BasicDBObject(MongoOperator.IN, MsgType.FileTypeArr));
        List<String> fileList = getRoomDatastore().getDB().getCollection(mucMsg + roomJid).distinct("content", msgFileQuery);
        for (String url : fileList) {
            try {
                ConstantUtil.deleteFile(url);
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        getRoomDatastore().getDB().getCollection(mucMsg + roomJid).drop();
    }

    /**
     * @param @param roomJid    参数  tigase 8.0 后废弃 该 方法
     * @Description: TODO(解散群组后 删除 tigase 的数据)
     */
    @Deprecated()
    public void destroyRoomToIM(String roomJid) {
        /*删除 tig_nodes 群组的配置*/
        DBCollection collection = getTigaseDatastore().getDB().getCollection("tig_nodes");
        String queryJid = "rooms/" + roomJid;
        Pattern regex = Pattern.compile("^" + (queryJid != null ? queryJid : "") + "[^/]*");
        BasicDBObject query = new BasicDBObject("node", regex);
        collection.remove(query);
    }

    /**
     * @param @param username 群主的userid
     * @param @param password  群主的密码
     * @param @param roomJid     房间jid
     * @Description: TODO(群主 通过xmpp 解散群组)
     */
    public void destroyRoomToIM(String username, String password, String roomJid) {
        KXMPPServiceImpl.getInstance().destroyMucRoom(username, password, roomJid);
    }

    /**
     * @param roomJid
     * @Description:（解散群组后删除群组的离线消息）
     **/
    public void deleMucHistory(String roomJid) {
        DBCollection collection = getDatastore().getDB().getCollection("muc_history");
        Pattern regex = Pattern.compile("^" + (roomJid != null ? roomJid : "") + "[^/]*");
        BasicDBObject query = new BasicDBObject("room_jid", regex);
        collection.remove(query);
    }

    //设置/取消管理员
    @Override
    public void setAdmin(ObjectId roomId, int touserId, int type, int userId) {
        Integer status = queryRoomStatus(roomId);
        if (null != status && status == -1) {
            throw new ServiceException("该群组已被后台锁定");
        }
        Query<Room.Member> q = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("userId").equal(touserId);
        if (null == q.get()) {
            throw new ServiceException("该成员不在群组中");
        }
        UpdateOperations<Room.Member> ops = getRoomDatastore().createUpdateOperations(Room.Member.class);
        ops.set("role", type);
        ops.set("talkTime",0);
        getRoomDatastore().findAndModify(q, ops);
        // 更新群组、群成员相关缓存
        updateRoomInfoByRedis(roomId.toString());
        Room room = getRoom(roomId);
        User user = getUserManager().getUser(userId);
        //xmpp推送
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.SETADMIN);
        if (type == 2) {//1为设置管理员
            messageBean.setContent(1);
        } else {
            messageBean.setContent(0);
        }
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
        messageBean.setToUserName(q.get().getNickname());
        messageBean.setToUserId(q.get().getUserId().toString());
        messageBean.setObjectId(room.getJid());
        messageBean.setMessageId(StringUtil.randomUUID());
        // 发送单聊通知被设置的人、群聊
        sendChatToOneGroupMsg(q.get().getUserId(), room.getJid(), messageBean);
    }

    //设置/取消群成员互加好友
    @Override
    public void setAddFriend(ObjectId roomId, Integer type, int userId) {

        Room room = getRoom(roomId);

        if(room == null){
            throw new ServiceException("该群组不存在");
        }

        Member member = TigBeanUtils.getRoomManagerImplForIM().getMember(roomId, userId);
        if(member.getRole() != 1 || member.getRole() != 2) {
            throw new ServiceException("只有群主或者管理员才能操作");
        }

        Integer status = queryRoomStatus(roomId);
        if (null != room.getS() && room.getS() == -1) {
            throw new ServiceException("该群组已被后台锁定");
        }
        room.setAddFriend(type);
        update(room.getId(),room);
        // 更新群组、群成员相关缓存
        updateRoomInfoByRedis(roomId.toString());
    }

    //群成员互加好友
    @Override
    public void addFriend(ObjectId roomId, Integer touserId, Integer userId) {

        Room room = getRoom(roomId);

        if(room == null){
            throw new ServiceException("该群组不存在");
        }

        if(room.getAddFriend() != 1){
            throw new ServiceException("该群组未开启成员互加好友功能");
        }

        User user = getUserManager().getUser(touserId);
        if(user == null){
            throw new ServiceException("该用户不存在");
        }


    }

    public void setInvisibleGuardian(ObjectId roomId, int touserId, int type, int userId) {
        Query<Room.Member> q = getRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(roomId).field("userId").equal(touserId);
        UpdateOperations<Room.Member> ops = getRoomDatastore().createUpdateOperations(Room.Member.class);
        if (type == -1 || type == 0) {
            ops.set("role", 3);// 1=创建者、2=管理员、3=普通成员、4=隐身人、5=监控人
        } else if (type == 4 || type == 5) {
            ops.set("role", type);
        }
        getRoomDatastore().findAndModify(q, ops);
        /**
         * 维护群组、群成员相关缓存
         */
        updateRoomInfoByRedis(roomId.toString());
        Room room = getRoom(roomId);
        //xmpp推送
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.SetRoomSettingInvisibleGuardian);
        if (type == 4) {
            messageBean.setContent(1);
        } else if (type == 5) {
            messageBean.setContent(2);
        } else if (type == -1) {
            messageBean.setContent(-1);
        } else if (type == 0) {
            messageBean.setContent(0);
        }
        messageBean.setFromUserId(String.valueOf(userId));
        messageBean.setFromUserName(getMemberNickname(roomId, userId));
        messageBean.setToUserName(q.get().getNickname());
        messageBean.setToUserId(String.valueOf(touserId));
        messageBean.setObjectId(room.getJid());
        messageBean.setMessageId(StringUtil.randomUUID());
        // 发送单聊通知被设置的人、群聊
//		sendChatToOneGroupMsg(q.get().getUserId(), room.getJid(), messageBean);
        sendGroupOne(q.get().getUserId(), messageBean);
    }

    //添加文件（群共享）
    @Override
    public Share Addshare(ObjectId roomId, long size, int type, int userId, String url, String name) {
        User user = getUserManager().getUser(userId);
        Share share = new Share();
        share.setRoomId(roomId);
        share.setTime(DateUtil.currentTimeSeconds());
        share.setNickname(user.getNickname());
        share.setUserId(userId);
        share.setSize(size);
        share.setUrl(url);
        share.setType(type);
        share.setName(name);
        getRoomDatastore().save(share);
        /**
         * 维护群文件缓存
         */
        getRedisServiceImpl().deleteShareList(roomId);
        Room room = getRoom(roomId);
        //上传文件xmpp推送
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.FILEUPLOAD);
        messageBean.setContent(share.getShareId().toString());
        messageBean.setFileName(share.getName());
        messageBean.setObjectId(room.getJid());
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
        messageBean.setMessageId(StringUtil.randomUUID());
        // 发送群聊通知
        sendGroupMsg(room.getJid(), messageBean);
        return share;
    }

    //查询所有
    @SuppressWarnings("deprecation")
    @Override
    public List<Share> findShare(ObjectId roomId, long time, int userId, int pageIndex, int pageSize) {
        if (userId != 0) {
            Query<Room.Share> q = getRoomDatastore().createQuery(Room.Share.class).field("roomId").equal(roomId).order("-time");
            q.filter("userId", userId);
            return q.offset(pageSize * pageIndex).limit(pageSize).asList();
        } else {
            List<Share> shareList;
            List<Share> redisShareList = getRedisServiceImpl().getShareList(roomId, pageIndex, pageSize);
            if (null != redisShareList && redisShareList.size() > 0) {
                shareList = redisShareList;
            } else {
                Query<Room.Share> q = getRoomDatastore().createQuery(Room.Share.class).field("roomId").equal(roomId).order("-time");
                getRedisServiceImpl().saveShareList(roomId, q.asList());
                shareList = q.offset(pageSize * pageIndex).limit(pageSize).asList();
            }
            return shareList;
        }
    }

    //删除
    @Override
    public void deleteShare(ObjectId roomId, ObjectId shareId, int userId) {
        Query<Room.Share> q = getRoomDatastore().createQuery(Room.Share.class).field("roomId").equal(roomId).field("shareId").equal(shareId);

        User user = getUserManager().getUser(userId);
        Room room = getRoom(roomId);
        Share share = q.get();
        //删除XMpp推送
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.DELETEFILE);
        messageBean.setContent(share.getShareId().toString());
        messageBean.setFileName(share.getName());
        messageBean.setObjectId(room.getJid());
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(roomId, user.getUserId()));
        messageBean.setMessageId(StringUtil.randomUUID());
        // 发送群聊通知
        sendGroupMsg(room.getJid(), messageBean);
        getRoomDatastore().delete(q);
        /**
         * 维护群文件缓存
         */
        getRedisServiceImpl().deleteShareList(roomId);
    }

    //获取单个文件
    @Override
    public Object getShare(ObjectId roomId, ObjectId shareId) {
        Share share = getRoomDatastore().createQuery(Room.Share.class).field("roomId").equal(roomId).field("shareId").equal(shareId).get();
        return share;
    }

    @Override
    public String getCall(ObjectId roomId) {
        Room room = getRoomDatastore().createQuery(getEntityClass()).field("_id").equal(roomId).get();
        return room.getCall();
    }

    @Override
    public String getVideoMeetingNo(ObjectId roomId) {
        Room room = getRoomDatastore().createQuery(getEntityClass()).field("_id").equal(roomId).get();
        return room.getVideoMeetingNo();
    }

    /**
     * 发送消息 到群组中
     *
     * @param jidArr
     * @param userId
     * @param msgType
     * @param content
     */
    public void sendMsgToRooms(String[] jidArr, int userId, int msgType, String content) {
        User user = TigBeanUtils.getUserManager().getUserFromDB(userId);
        MessageBean messageBean = new MessageBean();
        messageBean.setFromUserId(userId + "");
        messageBean.setFromUserName(user.getNickname());
        messageBean.setType(msgType);
        messageBean.setTimeSend(DateUtil.currentTimeSeconds());
        messageBean.setContent(content);
        messageBean.setMessageId(StringUtil.randomUUID());
        TigBeanUtils.getXmppService().sendMsgToMucRoom(messageBean, jidArr);
    }

    /**
     * 发送消息 到群组中（特殊消息）
     * @param jidArr
     * @param userId
     * @param msgType
     * @param content
     */
    public void sendMsgToRoomsSpecial(String[] jidArr, int userId, int msgType, String content) {
        User user = TigBeanUtils.getUserManager().getUserFromDB(userId);
        MessageBean messageBean = new MessageBean();
        messageBean.setFromUserId(userId + "");
        messageBean.setFromUserName(user.getNickname());
        messageBean.setType(msgType);
        messageBean.setTimeSend(DateUtil.currentTimeSeconds());
        messageBean.setContent(content);
        messageBean.setMessageId(StringUtil.randomUUID());
        TigBeanUtils.getXmppService().sendMsgToMucRoomSpecial(messageBean, jidArr);
//        for (String jid:jidArr){
//            PlanMessage planMessage = new PlanMessage();
//            planMessage.setContent(content);
//            planMessage.setFromUserId(String.valueOf(userId));
//            planMessage.setFromUserName(user.getNickname());
//            planMessage.setPlanId(StringUtil.randomUUID());
//            planMessage.setPlanType(msgType);
//            planMessage.setToUserId(jid);
//            MessageBean messageBean = new MessageBean();
//            messageBean.setFromUserId(userId + "");
//            messageBean.setFromUserName(user.getNickname());
//            messageBean.setType(msgType);
//            messageBean.setTimeSend(DateUtil.currentTimeSeconds());
//            messageBean.setContent(content);
//            messageBean.setMessageId(StringUtil.randomUUID());
//            TigBeanUtils.getXmppService().sendMsgToMucRoomSpecial(messageBean, jid);
//        }
    }

    /**
     * 获取房间总数量
     */
    @Override
    public Long countRoomNum() {
        long roomNum = getRoomDatastore().createQuery(getEntityClass()).count();
        return roomNum;
    }

    /**
     * 保存群组apikey信息
     *
     * @param apiKey
     * @return
     */
    @Override
    public boolean saveApiKey(Room.RoomApiKey apiKey) {
        getDatastore().save(apiKey);
        return true;
    }

    /**
     * 获取群组apikey信息
     *
     * @param roomId 房间ID
     * @param keyStr 源key字符串
     * @return
     */
    @Override
    public Room.RoomApiKey getApiKeyByRoomId(String roomId, String keyStr) {
        Room.RoomApiKey roomApiKey = getRoomDatastore().createQuery(Room.RoomApiKey.class).field("roomId").equal(new ObjectId(roomId)).get();
        if (roomApiKey == null) {
            // AES加密
            String encryptStr = AESUtil.encrypt(keyStr, AESUtil.PLAN_PASSWORD);
            // Md5加密
            String keyMd5 = Md5Util.md5Hex(encryptStr);
            Room.RoomApiKey apiKey = new Room.RoomApiKey();
            apiKey.setId(new ObjectId());
            apiKey.setCreateTime(System.currentTimeMillis());
            apiKey.setModifyTime(System.currentTimeMillis());
            apiKey.setRoomId(new ObjectId(roomId));
            apiKey.setPlanSourceStr(keyStr);
            apiKey.setPlanAESStr(encryptStr);
            apiKey.setPlanMD5Str(keyMd5);
            TigBeanUtils.getRoomManagerImplForIM().saveApiKey(apiKey);
            return apiKey;
        }
        return roomApiKey;
    }

    /**
     * 获取群组API KEY信息
     *
     * @param keys
     * @return
     */
    @Override
    public List<Room.RoomApiKey> getApiKeyByMd5Key(List<String> keys) {
        List<Room.RoomApiKey> roomApiKeyList = getRoomDatastore().createQuery(Room.RoomApiKey.class).field("planMD5Str").in(keys).asList();
        return roomApiKeyList;
    }

    /**
     * 添加群组统计      时间单位每日，最好可选择：每日、每月、每分钟、每小时
     *
     * @param startDate
     * @param endDate
     * @param counType  统计类型   1: 每个月的数据      2:每天的数据       3.每小时数据   4.每分钟的数据 (小时)
     */
    public List<Object> addRoomsCount(String startDate, String endDate, short counType) {
        List<Object> countData = new ArrayList<>();
        long startTime = 0; //开始时间（秒）
        long endTime = 0; //结束时间（秒）,默认为当前时间
        /**
         * 如时间单位为月和天，默认开始时间为当前时间的一年前 ; 时间单位为小时，默认开始时间为当前时间的一个月前;
         * 时间单位为分钟，则默认开始时间为当前这一天的0点
         */
        long defStartTime = counType == 4 ? DateUtil.getTodayMorning().getTime() / 1000 : counType == 3 ? DateUtil.getLastMonth().getTime() / 1000 : DateUtil.getLastYear().getTime() / 1000;
        startTime = StringUtil.isEmpty(startDate) ? defStartTime : DateUtil.toDate(startDate).getTime() / 1000;
        endTime = StringUtil.isEmpty(endDate) ? DateUtil.currentTimeSeconds() : DateUtil.toDate(endDate).getTime() / 1000;
        BasicDBObject queryTime = new BasicDBObject("$ne", null);
        if (startTime != 0 && endTime != 0) {
            queryTime.append("$gt", startTime);
            queryTime.append("$lt", endTime);
        }
        BasicDBObject query = new BasicDBObject("createTime", queryTime);
        //获得用户集合对象
        DBCollection collection = TigBeanUtils.getImRoomDatastore().getCollection(getEntityClass());
//        int diffTime = 0;
//        if (counType == 3) {//小时数据
//            diffTime = 3600 * Integer.parseInt(DateUtil.getTimeZone());
//        } else if (counType == 4) {//分钟数据
//            diffTime = 3600 * Integer.parseInt(DateUtil.getTimeZone());
//        }
        String mapStr = "function Map() { "
//                + "var date = new Date((this.createTime+" + diffTime + ")*1000);"
                + "var date = new Date((this.createTime)*1000);"
                + "var year = date.getFullYear();"
                + "var month = (\"0\" + (date.getMonth()+1)).slice(-2);"  //month 从0开始，此处要加1
                + "var day = (\"0\" + date.getDate()).slice(-2);"
                + "var hour = (\"0\" + date.getHours()).slice(-2);"
                + "var minute = (\"0\" + date.getMinutes()).slice(-2);"
                + "var dateStr = date.getFullYear()" + "+'-'+" + "(parseInt(date.getMonth())+1)" + "+'-'+" + "date.getDate();";
        if (counType == 1) { // counType=1: 每个月的数据
            mapStr += "var key= year + '-'+ month;";
        } else if (counType == 2) { // counType=2:每天的数据
            mapStr += "var key= year + '-'+ month + '-' + day; ";
        } else if (counType == 3) { //counType=3 :每小时数据
            mapStr += "var key= year + '-'+ month + '-' + day + '  ' + hour +' : 00';";
        } else if (counType == 4) { //counType=4 :每分钟的数据
            mapStr += "var key= year + '-'+ month + '-' + day + '  ' + hour + ':'+ minute;";
        }
        mapStr += "emit(key,1);}";
        String reduce = "function Reduce(key, values) {" + "return Array.sum(values);" + "}";
        MapReduceCommand.OutputType type = MapReduceCommand.OutputType.INLINE;
        MapReduceCommand command = new MapReduceCommand(collection, mapStr, reduce, null, type, query);
        MapReduceOutput mapReduceOutput = collection.mapReduce(command);
        Iterable<DBObject> results = mapReduceOutput.results();
        Map<String, Double> map = new HashMap<>();
        for (Iterator iterator = results.iterator(); iterator.hasNext(); ) {
            DBObject obj = (DBObject) iterator.next();
            map.put((String) obj.get("_id"), (Double) obj.get("value"));
            countData.add(JSON.toJSON(map));
            map.clear();
            //logger.info(JSON.toJSON(obj));
        }
        return countData;
    }

    /**
     * @param roomId
     * @param request
     * @param response
     * @return
     * @Description: 导出群成员
     **/
    public Workbook exprotExcelGroupMembers(String roomId, HttpServletRequest request, HttpServletResponse response) {
        String name = getRoomName(new ObjectId(roomId)) + " 的群成员明细";
        String fileName = "groupMembers.xlsx";
        List<Member> members = getMemberList(new ObjectId(roomId), null);
        List<String> titles = Lists.newArrayList();
        titles.add("userId");
        titles.add("userName");
        titles.add("remarkName");
        titles.add("telephone");
        titles.add("role");
        titles.add("offlineNoPushMsg");
        titles.add("createTime");
        titles.add("modifyTime");
        List<Map<String, Object>> values = Lists.newArrayList();
        members.forEach(member -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put("userId", member.getUserId());
            map.put("userName", member.getNickname());
            map.put("remarkName", member.getRemarkName());
            map.put("telephone", getUserManager().getUser(member.getUserId()).getPhone());
            map.put("role", member.getRole() == 1 ? "群主" : member.getRole() == 2 ? "管理员" : member.getRole() == 3 ? "普通成员" : member.getRole() == 4 ? "隐身人" : "监护人");// 1=创建者、2=管理员、3=普通成员、4=隐身人、5=监控人
            map.put("offlineNoPushMsg", member.getOfflineNoPushMsg() == 0 ? "否" : "是");
            if(member.getCreateTime()!=null) {
                map.put("createTime", DateUtil.strToDateTime(member.getCreateTime()));
            }
            if(member.getModifyTime()!=null) {
                map.put("modifyTime", DateUtil.strToDateTime(member.getModifyTime()));
            }
            values.add(map);
        });
        Workbook workBook = ExcelUtil.generateWorkbook(name, "xlsx", titles, values);
        response.reset();
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(Charset.defaultCharset()), "utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return workBook;
    }


    /**
     * @param userId
     * @param jids
     * @Description: 用户加入的群组 userId ====> jids
     **/
    public void saveJidsByUserId(Integer userId, List<String> jids) {
        if (null == jids) {
            return;
        } else {
            DBCollection collection = getRoomDatastore().getDB().getCollection(Tig_ROOMJIDS_USERID);
            BasicDBObject query = new BasicDBObject("userId", userId);
            if (0 == jids.size()) {
                collection.remove(query);
                return;
            }
            BasicDBObject values = new BasicDBObject("jids", jids);
            values.append("jids", jids);
            collection.update(query, new BasicDBObject("$set", values), true, false);
        }
    }

    /**
     * @param user
     * @param roomId
     * @param userIdList
     * @Description:
     **/
    public void consoleJoinRoom(User user, ObjectId roomId, List<Integer> userIdList) {
        for (Integer userId : userIdList) {
            Member data = TigBeanUtils.getRoomManagerImplForIM().getMember(roomId, userId);
            if (null != data) {
                throw new ServiceException(userId + " 该成员已经在群组中,不能重复邀请");
            }
            Member member = new Member();
            member.setActive(DateUtil.currentTimeSeconds());
            member.setCreateTime(DateUtil.currentTimeSeconds());
            member.setModifyTime(0L);
            member.setNickname(getUserManager().getNickName(userId));
            member.setRole(3);
            member.setRoomId(roomId);
            member.setSub(1);
            member.setTalkTime(0L);
            member.setUserId(userId);
            getDatastore().save(member);
            // 群组人数
            updateUserSize(roomId, 1);
            Room room = getRoom(roomId);
            // 后台邀请用户加入群组
            MessageBean messageBean = new MessageBean();
            messageBean.setType(KXMPPServiceImpl.NEW_MEMBER);
            messageBean.setObjectId(room.getJid());
            messageBean.setFromUserId(user.getUserId().toString());
            messageBean.setFromUserName(user.getNickname());
            messageBean.setToUserId(userId.toString());
            messageBean.setToUserName(getUserManager().getNickName(userId));
            messageBean.setContent(room.getName());
            messageBean.setMessageId(StringUtil.randomUUID());
            messageBean.setFileSize(room.getShowRead());
            messageBean.setFileName(room.getId().toString());
            // 发送单聊通知到被邀请人， 群聊
            sendChatToOneGroupMsg(userId, room.getJid(), messageBean);
            getRedisServiceImpl().deleteMemberList(roomId.toString());
            // 维护用户加入的群jids
            saveJidsByUserId(userId, queryUserRoomsJidList(userId));
            /**
             * 删除 用户加入的群组 jid  缓存
             */
            TigBeanUtils.getRedisService().deleteUserRoomJidList(member.getUserId());
            if (0 == TigBeanUtils.getUserManager().getOnlinestateByUserId(member.getUserId())) {
                TigBeanUtils.getRedisService().addRoomPushMember(room.getJid(), member.getUserId());
            }
            // 维护群组数据
            getRedisServiceImpl().deleteRoom(String.valueOf(roomId));
        }
    }

    // 面对面创群
    public Room queryLocationRoom(String name, double longitude, double latitude, String password, int isQuery) {
        Integer userId = ReqUtil.getUserId();
        Room room = TigBeanUtils.getRedisService().queryLocationRoom(userId, longitude, latitude, password, name);
        if (1 == isQuery) {
            return room;
        }
        ThreadUtil.executeInThread(obj -> {
            for (Member mem : room.getMembers()) {
                if (userId.equals(mem.getUserId())) {
                    continue;
                }
                MessageBean messageBean = new MessageBean();
                messageBean.setObjectId(room.getJid());
                messageBean.setFromUserId(userId.toString());
                messageBean.setFromUserName(userId.toString());
                messageBean.setType(KXMPPServiceImpl.LocationRoom);
                messageBean.setToUserId(mem.getUserId().toString());
                TigBeanUtils.getXmppService().send(messageBean);
            }
        });
        return room;
    }

    public Room joinLocationRoom(String roomJid) {
        ObjectId roomId = getRoomId(roomJid);
        Integer userId = ReqUtil.getUserId();
        User user = null;
        if (null == roomId) {
            Room room = TigBeanUtils.getRedisService().queryLocationRoom(roomJid);
            if (null == room) {
                throw new ServiceException("群组已过期失效");
            }
            user = TigBeanUtils.getUserManager().getUserFromDB(userId);
            TigBeanUtils.getXmppService().createMucRoom(user.getPassword(), userId.toString(), room.getName(), roomJid, room.getName(), room.getName());
            roomId = new ObjectId();
            room.setId(roomId);
            add(user, room, null);
            TigBeanUtils.getRedisService().saveLocationRoom(roomJid, room);
        } else {
            user = TigBeanUtils.getUserManager().getUser(userId);
            Member member = new Member();
            member.setUserId(userId);
            updateMember(user, roomId, member);
        }
        return get(roomId);
    }

    public void exitLocationRoom(String roomJid) {
        Integer userId = ReqUtil.getUserId();
        TigBeanUtils.getRedisService().exitLocationRoom(userId, roomJid);
        ThreadUtil.executeInThread(obj -> {
            Room room = TigBeanUtils.getRedisService().queryLocationRoom(roomJid);
            for (Member mem : room.getMembers()) {
                if (userId.equals(mem.getUserId())) {
                    continue;
                }
                MessageBean messageBean = new MessageBean();
                messageBean.setObjectId(room.getJid());
                messageBean.setFromUserId(userId.toString());
                messageBean.setFromUserName(userId.toString());
                messageBean.setType(KXMPPServiceImpl.LocationRoom);
                messageBean.setToUserId(mem.getUserId().toString());
                TigBeanUtils.getXmppService().send(messageBean);
            }
        });
    }

    /**
     * 添加群助手
     *
     * @return
     */
    public JSONMessage addGroupHelper(String helperId, String roomId, String roomJid, Integer userId) {
        Query<GroupHelper> query = TigBeanUtils.getDatastore().createQuery(GroupHelper.class).field("roomId").equal(roomId).field("helperId").equal(helperId);
        if (null != query.get()) {
            return JSONMessage.failure("已存在");
        }
        Query<Helper> helQuery = TigBeanUtils.getDatastore().createQuery(Helper.class).field("_id").equal(new ObjectId(helperId));
        if (null == helQuery.get()) {
            return JSONMessage.failure("群助手不存在");
        }
        GroupHelper entity = new GroupHelper();
        entity.setHelperId(helperId);
        entity.setRoomId(roomId);
        entity.setRoomJid(roomJid);
        entity.setUserId(userId);
        if (null == entity.getId()) {
            entity.setId(ObjectId.get());
        }
        TigBeanUtils.getDatastore().save(entity);
        entity.setHelper(helQuery.get());
        return JSONMessage.success(null, entity);
    }

    /**
     * 添加自动回复关键字
     *
     * @param roomId
     * @param helperId
     * @param keyWord
     * @param value
     * @return
     */
    public JSONMessage addAutoResponse(String roomId, String helperId, String keyWord, String value) {
        Query<GroupHelper> query = TigBeanUtils.getDatastore().createQuery(GroupHelper.class).field("roomId").equal(roomId).field("helperId").equal(helperId);
        UpdateOperations<GroupHelper> ops = TigBeanUtils.getDatastore().createUpdateOperations(GroupHelper.class);
        GroupHelper.KeyWord keyword = new GroupHelper.KeyWord();
        keyword.setId(ObjectId.get().toString());
        keyword.setKeyWord(keyWord);
        keyword.setValue(value);
        List<KeyWord> list = new ArrayList<>();
        GroupHelper groupHelper = query.get();
        if (null == groupHelper) {
            return JSONMessage.failure("该群助手不存在");
        }
        if (null != groupHelper.getKeywords()) {
            for (int i = 0; i < groupHelper.getKeywords().size(); i++) {
                if (groupHelper.getKeywords().get(i).getKeyWord().equals(keyWord)) {
                    return JSONMessage.failure("关键字已存在");
                }
            }
            groupHelper.getKeywords().add(keyword);
        } else {
            list.add(keyword);
            groupHelper.setKeywords(list);
        }
        ops.set("keywords", groupHelper.getKeywords());
        TigBeanUtils.getDatastore().update(query, ops);
        return JSONMessage.success(null, keyword);

    }

    /**
     * 修改自动回复关键字和回复
     *
     * @param groupHelperId
     * @param keyWordId
     * @param keyword
     * @param value
     */
    public JSONMessage updateKeyword(String groupHelperId, String keyWordId, String keyword, String value) {
        Query<GroupHelper> query = TigBeanUtils.getDatastore().createQuery(GroupHelper.class).field("_id").equal(new ObjectId(groupHelperId));
        UpdateOperations<GroupHelper> ops = TigBeanUtils.getDatastore().createUpdateOperations(GroupHelper.class);
        GroupHelper groupHelper = query.get();
        if (null == groupHelper) {
            return JSONMessage.failure("该群助手不存在");
        }
        if (null == groupHelper.getKeywords()) {
            return JSONMessage.failure("关键字不存在");
        }
        for (int i = 0; i < groupHelper.getKeywords().size(); i++) {
            if (groupHelper.getKeywords().get(i).getId().equals(keyWordId)) {
                groupHelper.getKeywords().get(i).setKeyWord(keyword);
                groupHelper.getKeywords().get(i).setValue(value);
            }
        }
        ops.set("keywords", groupHelper.getKeywords());
        TigBeanUtils.getDatastore().update(query, ops);
        return JSONMessage.success();
    }

    /**
     * 删除自动回复关键字
     *
     * @param groupHelperId
     * @param keyWordId
     */
    public JSONMessage deleteAutoResponse(String groupHelperId, String keyWordId) {
        Query<GroupHelper> query = TigBeanUtils.getDatastore().createQuery(GroupHelper.class).field("_id").equal(new ObjectId(groupHelperId));
        UpdateOperations<GroupHelper> ops = TigBeanUtils.getDatastore().createUpdateOperations(GroupHelper.class);
        GroupHelper groupHelper = query.get();
        if (null == groupHelper) {
            return JSONMessage.failure("该群助手不存在");
        }
        if (null == groupHelper.getKeywords()) {
            return JSONMessage.failure("关键字不存在");
        }
        for (int i = 0; i < groupHelper.getKeywords().size(); i++) {
            if (groupHelper.getKeywords().get(i).getId().equals(keyWordId)) {
                groupHelper.getKeywords().remove(i);
            }
        }
        ops.set("keywords", groupHelper.getKeywords());
        TigBeanUtils.getDatastore().update(query, ops);
        return JSONMessage.success();
    }

    /**
     * 删除群助手
     *
     * @param id
     */
    public void deleteGroupHelper(String id) {
        Query<GroupHelper> query = TigBeanUtils.getDatastore().createQuery(GroupHelper.class).field("_id").equal(new ObjectId(id));
        TigBeanUtils.getDatastore().delete(query);
    }

    /**
     * 查询群组群助手
     *
     * @param roomId
     * @return
     */
    public List<GroupHelper> queryGroupHelper(String roomId, String helperId) {
        Query<GroupHelper> query = TigBeanUtils.getDatastore().createQuery(GroupHelper.class).field("roomId").equal(roomId);
        List<GroupHelper> list = query.asList();
        List<GroupHelper> newList = new ArrayList<>();
        if (!StringUtil.isEmpty(helperId)) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getHelperId().equals(helperId)) {
                    Query<Helper> q = TigBeanUtils.getDatastore().createQuery(Helper.class).field("_id").equal(new ObjectId(list.get(i).getHelperId()));
                    list.get(i).setHelper(q.get());
                    newList.add(list.get(i));
                }
            }
        } else {
            for (int i = 0; i < list.size(); i++) {
                Query<Helper> q = TigBeanUtils.getDatastore().createQuery(Helper.class).field("_id").equal(new ObjectId(list.get(i).getHelperId()));
                if (null != q.get()) {
                    list.get(i).setHelper(q.get());
                    newList.add(list.get(i));
                }
            }
        }

        return newList;

    }
    @Override
    public void getMsgAllAndActMem(Datastore dsForRoom, List list, PageResult<Room> result) {
        for (int i = 0; i < list.size(); i++) {
            if (result.getData().get(i) != null && !result.getData().get(i).getJid().equals("")) {
                DBCollection dbCollection = dsForRoom.getDB().getCollection("mucmsg_" + result.getData().get(i).getJid());
                BasicDBObject q = new BasicDBObject();
                long total = dbCollection.count(q);
                result.getData().get(i).setGroupMsgNo(Integer.parseInt(String.valueOf(total)));
                long current = DateUtil.currentTimeSeconds();
                int count = 0;
                Query<Room.Member> qMember = TigBeanUtils.getLocalSpringBeanManager().getImRoomDatastore().createQuery(Room.Member.class).field("roomId").equal(result.getData().get(i).getId()).order("-createTime");
                List<Member> data = qMember.asList();
                data = TigBeanUtils.getRoomManagerImplForIM().buildVipData(data);
                for (Member member : data) {
                    if (member.getOfflineTime() != null) {
                        long time = member.getOfflineTime();
                        long ft = (current - time) / (60 * 60 * 24);
                        if (ft <= 7) {
                            count++;
                        }
                    }
                }
                result.getData().get(i).setGroupActiveNo(count);
            }
        }
    }
    public void updateMemberOfflineTime(int userId) {
        Query<Room.Member> qmember = getRoomDatastore().createQuery(Room.Member.class).field("userId").equal(userId);
        UpdateOperations<Room.Member> ops1 = getDatastore().createUpdateOperations(Room.Member.class);
        if (qmember.get() != null) {
            qmember.get().setOfflineTime(DateUtil.currentTimeSeconds());
            ops1.set("offlineTime", DateUtil.currentTimeSeconds());
            getRoomDatastore().update(qmember, ops1);
        }
    }

    @Override
    public void insertSignInDate(String roomId, String roomJid, int userId) {
        try {
            RoomSignInDate roomSignInDate = new RoomSignInDate();
            if (!StringUtil.isEmpty(roomId)) {
                roomSignInDate.setRoomId(roomId);
                Room room = getRoomDatastore().createQuery(getEntityClass()).field("_id").equal(new ObjectId(roomId)).get();
                if(room!=null) {
                    roomSignInDate.setRoomJid(room.getJid());
                }
            }
            if (!StringUtil.isEmpty(roomJid)) {
                roomSignInDate.setRoomJid(roomJid);
                Room room = getRoomDatastore().createQuery(getEntityClass()).field("jid").equal(roomJid).get();
                if(room!=null) {
                    roomSignInDate.setRoomId(room.getId().toString());
                }
            }
            roomSignInDate.setUserId(userId);
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            roomSignInDate.setSignInDate(df.parse(df.format(new Date())));
            ObjectId id = (ObjectId) TigBeanUtils.getDatastore().save(roomSignInDate).getId();
            roomSignInDate.setId(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Override
    public RoomSignIn getSignInUser(String roomId, String roomJid, Integer userId) {
        Query<RoomSignIn> query = TigBeanUtils.getDatastore().createQuery(RoomSignIn.class).field("userId").equal(userId);
        if (!StringUtil.isEmpty(roomId)) {
            query.field("roomId").equal(roomId);
        }
        if (!StringUtil.isEmpty(roomJid)) {
            query.field("roomJid").equal(roomJid);
        }
        RoomSignIn data = query.get();
        return data;
    }
    @Override
    public RoomSignIn getSignInNow(RoomSignIn data, String nickName, String roomId, String roomJid, Integer userId) {
        if (data != null) {
            if (DateUtil.isSameDayOfMillis(data.getSignInTime(), DateUtil.currentTimeMilliSeconds())) {
                throw new ServiceException("一天只能签到一次，请不要连续签到");
            }
            Query<RoomSignIn> query1 = TigBeanUtils.getDatastore().createQuery(RoomSignIn.class).field("id").equal(data.getId());
            UpdateOperations<RoomSignIn> ops = TigBeanUtils.getDatastore().createUpdateOperations(RoomSignIn.class);
            if (Math.abs(DateUtil.currentTimeMilliSeconds()-data.getSignInTime()) / (1000 * 60 * 60 * 24) <= 1) {//当签到的日期不大于一时，定为不是连续签到
                data.setSerialCount(data.getSerialCount() + 1);//
            } else {
                data.setSerialCount(0);//没有连续签到，将连续签到的时间设置为0
            }
            ops.set("serialCount", data.getSerialCount());
            data.setSignInTime(DateUtil.currentTimeMilliSeconds());//最近签到的日期
            ops.set("signInTime", data.getSignInTime());
            data.setCount(data.getCount() + 1);//签到总数加1
            ops.set("count", data.getCount());
            data.setStatus(1);//是说明当天已签到
            ops.set("status", data.getStatus());
            ops.set("nickName", nickName);
            TigBeanUtils.getDatastore().update(query1, ops);
            insertSignInDate(roomId, roomJid, userId);
        } else {//表示该群里一个人第一次签到
            data = new RoomSignIn();
            //房间id
            if (!StringUtil.isEmpty(roomId)) {
                data.setRoomId(roomId);
                Room room = getRoomDatastore().createQuery(getEntityClass()).field("_id").equal(new ObjectId(roomId)).get();
                if(room!=null) {
                    data.setRoomJid(room.getJid());
                }
            }
            if (!StringUtil.isEmpty(roomJid)) {
                data.setRoomJid(roomJid);
                Room room = getRoomDatastore().createQuery(getEntityClass()).field("jid").equal(roomJid).get();
                if(room!=null) {
                    data.setRoomId(room.getId().toString());
                }
            }
            //用户Id
            data.setUserId(userId);
            //签到总数---的service里面判断
            data.setCount(1);//总数默认是1
            //连续签到数---的service里面判断
            //最近签到的日期
            //roomSignIn.setSignInDay(signInDay);
            data.setSerialCount(1);//调用改接口胡时候，默认签到一天
            //本次签到日期（用来判断是否为连续签到）
            data.setSignInTime(DateUtil.currentTimeMilliSeconds());
            data.setNickName(nickName);
            data.setStatus(1);
            ObjectId id = (ObjectId) TigBeanUtils.getDatastore().save(data).getId();
            data.setId(id);
            insertSignInDate(roomId, roomJid, userId);
        }
        return data;
    }
    @Override
    public List<RoomSignInDate> getSignInDate(String roomId, String roomJid, String monthStr) {
        try {
            Integer userId = ReqUtil.getUserId();
            Query<RoomSignInDate> query = TigBeanUtils.getDatastore().createQuery(RoomSignInDate.class).field("userId").equal(userId);
            if (!StringUtil.isEmpty(roomId)) {
                query.field("roomId").equal(roomId);
            }
            if (!StringUtil.isEmpty(roomJid)) {
                query.field("roomJid").equal(roomJid);
            }
            List<RoomSignInDate> userSignInfoList = new ArrayList();
            if (!StringUtil.isEmpty(monthStr)) {
                Date month = DateUtil.toDate(monthStr);
                Calendar monthCal = Calendar.getInstance();
                monthCal.setTime(month);
                Date firstDay = DateUtil.getFirstDayOfMonth(monthCal.get(Calendar.YEAR), monthCal.get(Calendar.MONTH));
                Date lastDay = DateUtil.getLastDayOfMonth(monthCal.get(Calendar.YEAR), monthCal.get(Calendar.MONTH));
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                query.and(query.criteria("signInDate").greaterThanOrEq(format.parse(format.format(firstDay))), query.criteria("signInDate").lessThanOrEq(format.parse(format.format(lastDay))));
                userSignInfoList = query.asList();
            }
            return userSignInfoList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList();
    }
    @Override
    public void exchangeRoomGift(String reqData) {
        try{
            JSONObject reqJson = JSON.parseObject(reqData);
            if (reqJson != null) {
                String roomId = "";
                if (reqJson.containsKey("roomId")) {
                    roomId = reqJson.getString("roomId");
                }
                String roomJid = "";
                if (reqJson.containsKey("roomJid")) {
                    roomJid = reqJson.getString("roomJid");
                }
                JSONArray dataArray = reqJson.getJSONArray("data");
                if (dataArray != null && dataArray.size() > 0) {
                    for (int i = 0; i < dataArray.size(); i++) {
                        RoomSignInGift data = new RoomSignInGift();
                        if (!dataArray.getJSONObject(i).getString("userId").equals("")) {
                            data.setUserId(Integer.parseInt(dataArray.getJSONObject(i).getString("userId")));
                        }
                        if (!dataArray.getJSONObject(i).getString("serialCount").equals("")) {
                            data.setCount(Integer.parseInt(dataArray.getJSONObject(i).getString("serialCount")));
                        }
                        if (!dataArray.getJSONObject(i).getString("nickName").equals("")) {
                            data.setNickName(dataArray.getJSONObject(i).getString("nickName"));
                        }
                        if (!StringUtil.isEmpty(roomId)) {
                            data.setRoomId(roomId);
                            Room room = getRoomDatastore().createQuery(getEntityClass()).field("_id").equal(new ObjectId(roomId)).get();
                            if(room!=null) {
                                data.setRoomJid(room.getJid());
                            }
                        }
                        if (!StringUtil.isEmpty(roomJid)) {
                            data.setRoomJid(roomJid);
                            Room room = getRoomDatastore().createQuery(getEntityClass()).field("jid").equal(roomJid).get();
                            if(room!=null) {
                                data.setRoomId(room.getId().toString());
                            }
                        }
                        data.setStatus(1);
                        SimpleDateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                        Date date = new Date();
                        String todayDate = format1.format(date);
                        data.setUpdateTime(todayDate);
                        ObjectId id = (ObjectId) TigBeanUtils.getDatastore().save(data).getId();
                        data.setId(id);
                        Query<RoomSignIn> query1 = TigBeanUtils.getDatastore().createQuery(RoomSignIn.class).field("userId").equal(data.getUserId());
                        if (!StringUtil.isEmpty(roomId)) {
                            query1.field("roomId").equal(roomId);
                        }
                        if (!StringUtil.isEmpty(roomJid)) {
                            query1.field("roomJid").equal(roomJid);
                        }
                        UpdateOperations<RoomSignIn> ops = TigBeanUtils.getDatastore().createUpdateOperations(RoomSignIn.class);
                        ops.set("serialCount",0);
                        TigBeanUtils.getDatastore().update(query1, ops);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    @Override
    public RoomSignIn getSignInDetails(RoomSignIn data,Integer userId,String roomId, String roomJid){
        if (data == null) {//表示还没有签到的用户，签到状态为0，即今天还没有签到
            data = new RoomSignIn();
            data.setSerialCount(0);//连续签到的天数
            data.setStatus(0);//当前用户还么有签到
            data.setUserId(userId);
            data.setRoomJid(roomJid);
            data.setRoomId(roomId);
        } else {
            Query<RoomSignInGift> query1 = TigBeanUtils.getDatastore().createQuery(RoomSignInGift.class).field("userId").equal(userId);
            if (!StringUtil.isEmpty(roomId)) {
                query1.field("roomId").equal(roomId);
            }
            if (!StringUtil.isEmpty(roomJid)) {
                query1.field("roomJid").equal(roomJid);
            }
            if (DateUtil.isSameDayOfMillis(data.getSignInTime(), DateUtil.currentTimeMilliSeconds())) {//将当前签到最新的时间和进入的时间对比是否为同一天
                data.setStatus(1);//是说明当天已签到
            } else {
                data.setStatus(0);//说明当天没有签到
            }
            data.setRoomSignInGift(query1.asList());
        }
        return data;
    }
    public void deleteRoomMemberSignDetails(String userId,String roomId, String roomJid){
        //删除RoomSignIn表
        Query<RoomSignIn> query = TigBeanUtils.getDatastore().createQuery(RoomSignIn.class);
        if (!StringUtil.isEmpty(userId)) {
            query.field("userId").equal(Integer.parseInt(userId));
        }
        if (!StringUtil.isEmpty(roomId)) {
            query.field("roomId").equal(roomId);
        }
        if (!StringUtil.isEmpty(roomJid)) {
            query.field("roomJid").equal(roomJid);
        }
        getDatastore().findAndDelete(query);
        //删除签到日期的表
        Query<RoomSignInDate> queryDate = TigBeanUtils.getDatastore().createQuery(RoomSignInDate.class);
        if (!StringUtil.isEmpty(userId)) {
            query.field("userId").equal(Integer.parseInt(userId));
        }
        if (!StringUtil.isEmpty(roomId)) {
            queryDate.field("roomId").equal(roomId);
        }
        if (!StringUtil.isEmpty(roomJid)) {
            queryDate.field("roomJid").equal(roomJid);
        }
        getDatastore().findAndDelete(queryDate);
        //删除兑换礼物的表
        Query<RoomSignInGift> queryGift = TigBeanUtils.getDatastore().createQuery(RoomSignInGift.class);
        if (!StringUtil.isEmpty(userId)) {
            query.field("userId").equal(Integer.parseInt(userId));
        }
        if (!StringUtil.isEmpty(roomId)) {
            queryGift.field("roomId").equal(roomId);
        }
        if (!StringUtil.isEmpty(roomJid)) {
            queryGift.field("roomJid").equal(roomJid);
        }
        getDatastore().findAndDelete(queryGift);
    }
    public void updateAllSignIn(Query<Room> query, User user, RoomVO roomVO, Room room, UpdateOperations<Room> operations) {
        operations.set("isShowSignIn", roomVO.getIsShowSignIn());
        updateGroup(query, operations);
        getRedisServiceImpl().deleteRoom(roomVO.getRoomId().toString());
        /**
         * 维护群组、群成员缓存
         */
        updateRoomInfoByRedis(room.getId().toString());
        MessageBean messageBean = new MessageBean();
        messageBean.setType(KXMPPServiceImpl.SignInRoom);
        messageBean.setFromUserId(user.getUserId().toString());
        messageBean.setFromUserName(getMemberNickname(room.getId(), user.getUserId()));
        messageBean.setContent(String.valueOf(roomVO.getIsShowSignIn()));
        messageBean.setObjectId(room.getJid());
        messageBean.setMessageId(StringUtil.randomUUID());
        logger.info("send all banned message: {}", JSONObject.toJSONString(messageBean));
        // 发送群聊通知
        sendGroupMsg(room.getJid(), messageBean);
    }
//    public void roomAllUpdateSignIn(ObjectId roomId, int isShowSignIn ) {
//        ThreadUtil.executeInThread(new Callback() {
//            @Override
//            public void execute(Object obj) {
//                Query<Member> query = getRoomDatastore().createQuery(Member.class);
//                query.filter("roomId", roomId);
//                UpdateOperations<Member> operations = getRoomDatastore().createUpdateOperations(Member.class);
//                operations.set("isShowSignIn", isShowSignIn);
//                getRoomDatastore().update(query, operations);
//            }
//        });
//    }
}
