2025-07-03 00:00:00.001 - cn.tig.im.scheduleds.CommTask =====>>>>> deleteMucMsgRecord 2025-07-03 12:00:00
2025-07-03 00:00:00.018 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/appTabBarConfig.html
2025-07-03 00:00:00.033 - cn.tig.im.scheduleds.CommTask 当前时间:2025-07-03 12:00:00
2025-07-03 00:00:00.045 - cn.tig.im.scheduleds.CommTask 最高在线用户======>>>>>2,执行时间2025-07-03 12:00:00
2025-07-03 00:00:00.050 - cn.tig.im.scheduleds.CommTask 累积清除   2025-06-18 00:00:00  前的  0  条系统日志记录
2025-07-03 00:00:00.056 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:00:00
2025-07-03 00:00:00.056 - cn.tig.im.scheduleds.CommTask =====> deleteChatMsgRecord 2025-07-03 12:00:00
2025-07-03 00:00:00.071 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/appTabBarConfig.js
2025-07-03 00:00:00.084 - cn.tig.im.scheduleds.CommTask =========定时删除直播间========  0
2025-07-03 00:00:00.087 - cn.tig.im.scheduleds.CommTask =======红包退回和转账退回定时任务开启中=======
2025-07-03 00:00:00.091 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/appTabBarList
2025-07-03 00:00:00.099 - cn.tig.im.scheduleds.CommTask 红包超时未领取的数量 ======>>>>> 0
2025-07-03 00:00:00.099 - cn.tig.im.scheduleds.CommTask 刷新红包成功,耗时12毫秒,执行时间2025-07-03 12:00:00
2025-07-03 00:00:00.103 - com.tig.im.SysApiLogAspect 请求参数： /console/appTabBarList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=194953088811dbdf011df8d0a3124394&time=**********086&page=1&user=0&account=1000&
2025-07-03 00:00:00.103 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:00:00.103 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AppTabBarController】类的【appTabBarList】方法
2025-07-03 00:00:00.105 - cn.tig.im.scheduleds.CommTask 转账超时未领取的数量 ======>>>>> 0
2025-07-03 00:00:00.105 - cn.tig.im.scheduleds.CommTask 刷新转账成功,耗时18毫秒
2025-07-03 00:00:00.105 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:00:00
2025-07-03 00:00:00.111 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:00:00.142 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AppTabBarController】类的【appTabBarList】方法，应答参数：{"currentTime":**********142,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:00:00.142 - com.tig.im.SysApiLogAspect 接口【appTabBarList】总耗时(毫秒)：39
2025-07-03 00:00:00.142 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:00:00.896 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/versionManage.html
2025-07-03 00:00:00.903 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/versionManage.js
2025-07-03 00:00:00.916 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/versionInfoList
2025-07-03 00:00:00.929 - com.tig.im.SysApiLogAspect 请求参数： /console/versionInfoList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=10&secret=04fd8d8c3e100b960b891e779c86574f&time=**********909&page=1&account=1000&
2025-07-03 00:00:00.929 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:00:00.929 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【versionInfoList】方法
2025-07-03 00:00:00.965 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【versionInfoList】方法，应答参数：{"currentTime":**********964,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:00:00.965 - com.tig.im.SysApiLogAspect 接口【versionInfoList】总耗时(毫秒)：35
2025-07-03 00:00:00.965 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:00:01.721 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/system_other_login_config.html
2025-07-03 00:00:01.994 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getLoginConfig
2025-07-03 00:00:01.994 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getLoginConfig
2025-07-03 00:00:01.995 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getLoginConfig
2025-07-03 00:00:02.010 - com.tig.im.SysApiLogAspect 请求参数： /console/getLoginConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=68748f5d1a2d743f59518bf5295b8b82&time=*************&type=4&account=1000&
2025-07-03 00:00:02.010 - com.tig.im.SysApiLogAspect 请求参数： /console/getLoginConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=9947ea390f1d667798b6251d260b89c8&time=*************&type=2&account=1000&
2025-07-03 00:00:02.010 - com.tig.im.SysApiLogAspect 请求参数： /console/getLoginConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=08c018001b1f0ca23b178c7048bc34e2&time=*************&type=1&account=1000&
2025-07-03 00:00:02.010 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:00:02.010 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:00:02.010 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法
2025-07-03 00:00:02.011 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:00:02.011 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法
2025-07-03 00:00:02.011 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法
2025-07-03 00:00:02.038 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法，应答参数：{"currentTime":1751472002037,"data":{"id":{"counter":4476394,"date":1751472002000,"machineIdentifier":3999260,"processIdentifier":-17996,"time":1751472002000,"timeSecond":1751472002,"timestamp":1751472002},"name":"QQ","status":2,"type":1},"resultCode":1}
2025-07-03 00:00:02.038 - com.tig.im.SysApiLogAspect 接口【getLoginConfig】总耗时(毫秒)：26
2025-07-03 00:00:02.038 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:00:02.040 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法，应答参数：{"currentTime":1751472002040,"resultCode":1}
2025-07-03 00:00:02.040 - com.tig.im.SysApiLogAspect 接口【getLoginConfig】总耗时(毫秒)：29
2025-07-03 00:00:02.040 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:00:02.042 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法，应答参数：{"currentTime":1751472002042,"data":{"id":{"counter":4476395,"date":1751472002000,"machineIdentifier":3999260,"processIdentifier":-17996,"time":1751472002000,"timeSecond":1751472002,"timestamp":1751472002},"name":"微信","status":2,"type":2},"resultCode":1}
2025-07-03 00:00:02.042 - com.tig.im.SysApiLogAspect 接口【getLoginConfig】总耗时(毫秒)：31
2025-07-03 00:00:02.042 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:00:59.280 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/system_sms_config.html
2025-07-03 00:00:59.301 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/system_sms_config.js
2025-07-03 00:00:59.322 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/smsConfig/get
2025-07-03 00:00:59.336 - com.tig.im.SysApiLogAspect 请求参数： /console/smsConfig/get
2025-07-03 00:00:59.337 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:00:59.337 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminSmsConfigGet】方法
2025-07-03 00:00:59.367 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminSmsConfigGet】方法，应答参数：{"currentTime":1751472059367,"resultCode":1}
2025-07-03 00:00:59.368 - com.tig.im.SysApiLogAspect 接口【adminSmsConfigGet】总耗时(毫秒)：30
2025-07-03 00:00:59.368 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:00.326 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/system_pay_config.html
2025-07-03 00:01:00.347 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/system_pay_config.js
2025-07-03 00:01:00.607 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getTransferConfig
2025-07-03 00:01:00.607 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getPayConfig
2025-07-03 00:01:00.607 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getPayConfig
2025-07-03 00:01:00.608 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getPayConfig
2025-07-03 00:01:00.609 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getPayConfig
2025-07-03 00:01:00.609 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getPayConfig
2025-07-03 00:01:00.616 - com.tig.im.SysApiLogAspect 请求参数： /console/getPayConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=abfb6d5619bf965e108d51280861dedd&time=*************&type=8&account=1000&
2025-07-03 00:01:00.616 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:00.616 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法
2025-07-03 00:01:00.618 - com.tig.im.SysApiLogAspect 请求参数： /console/getTransferConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=b38511866e675b773a89e02df9007d80&time=*************&account=1000&
2025-07-03 00:01:00.619 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:00.619 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getTransferConfig】方法
2025-07-03 00:01:00.621 - com.tig.im.SysApiLogAspect 请求参数： /console/getPayConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=abfb6d5619bf965e108d51280861dedd&time=*************&type=6&account=1000&
2025-07-03 00:01:00.621 - com.tig.im.SysApiLogAspect 请求参数： /console/getPayConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=a14eb2da56400b045d72563490937cb6&time=**********604&type=5&account=1000&
2025-07-03 00:01:00.621 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:00.621 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:00.621 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法
2025-07-03 00:01:00.621 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法
2025-07-03 00:01:00.623 - com.tig.im.SysApiLogAspect 请求参数： /console/getPayConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=ef5304550d070ab6e979c182aa24a6db&time=*************&type=2&account=1000&
2025-07-03 00:01:00.623 - com.tig.im.SysApiLogAspect 请求参数： /console/getPayConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=3ea9333d205495d0259093c387d8f152&time=**********602&type=1&account=1000&
2025-07-03 00:01:00.623 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:00.623 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:00.624 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法
2025-07-03 00:01:00.624 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法
2025-07-03 00:01:00.642 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getTransferConfig】方法，应答参数：{"currentTime":**********642,"data":{"displayRedPacket":1,"id":{"counter":4476400,"date":*************,"machineIdentifier":3999260,"processIdentifier":-17996,"time":*************,"timeSecond":**********,"timestamp":**********},"isWeiBaoStatus":0,"isWithdrawToAdmin":0,"maxSendRedPagesAmount":500,"minTransferAmount":0,"rechargeRate":0,"transferRate":0,"weiBaoMaxRedPacketAmount":0,"weiBaoMaxTransferAmount":0,"weiBaoMinTransferAmount":0,"weiBaoTransferRate":0,"withdrawWayList":[{"withdrawWayKeyId":"49cce27d78d8429a88a5473d9953cfe9","withdrawWayName":"支付宝","withdrawWaySort":1,"withdrawWayStatus":1,"withdrawWayTime":1751471996},{"withdrawWayKeyId":"4995463477eb4d62b694e54cca33de93","withdrawWayName":"银行卡","withdrawWaySort":5,"withdrawWayStatus":1,"withdrawWayTime":1751471996}]},"resultCode":1}
2025-07-03 00:01:00.643 - com.tig.im.SysApiLogAspect 接口【getTransferConfig】总耗时(毫秒)：23
2025-07-03 00:01:00.643 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:00.645 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法，应答参数：{"currentTime":**********645,"data":{"id":{"counter":4476401,"date":*************,"machineIdentifier":3999260,"processIdentifier":-17996,"time":*************,"timeSecond":**********,"timestamp":**********},"name":"支付宝","status":2,"type":1,"withdrawStatus":2},"resultCode":1}
2025-07-03 00:01:00.645 - com.tig.im.SysApiLogAspect 接口【getPayConfig】总耗时(毫秒)：21
2025-07-03 00:01:00.645 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:00.647 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法，应答参数：{"currentTime":**********647,"data":{"id":{"counter":4476402,"date":*************,"machineIdentifier":3999260,"processIdentifier":-17996,"time":*************,"timeSecond":**********,"timestamp":**********},"name":"黑马","status":2,"type":8,"withdrawStatus":2},"resultCode":1}
2025-07-03 00:01:00.647 - com.tig.im.SysApiLogAspect 接口【getPayConfig】总耗时(毫秒)：31
2025-07-03 00:01:00.647 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:00.654 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法，应答参数：{"currentTime":**********653,"data":{"id":{"counter":4476403,"date":*************,"machineIdentifier":3999260,"processIdentifier":-17996,"time":*************,"timeSecond":**********,"timestamp":**********},"name":"云支付","status":2,"type":6,"withdrawStatus":2},"resultCode":1}
2025-07-03 00:01:00.654 - com.tig.im.SysApiLogAspect 接口【getPayConfig】总耗时(毫秒)：31
2025-07-03 00:01:00.654 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:00.654 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法，应答参数：{"currentTime":**********654,"data":{"id":{"counter":4476404,"date":*************,"machineIdentifier":3999260,"processIdentifier":-17996,"time":*************,"timeSecond":**********,"timestamp":**********},"name":"微信","status":2,"type":2,"withdrawStatus":2},"resultCode":1}
2025-07-03 00:01:00.655 - com.tig.im.SysApiLogAspect 接口【getPayConfig】总耗时(毫秒)：30
2025-07-03 00:01:00.655 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:00.657 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法，应答参数：{"currentTime":**********657,"data":{"id":{"counter":4476405,"date":*************,"machineIdentifier":3999260,"processIdentifier":-17996,"time":*************,"timeSecond":**********,"timestamp":**********},"name":"通联","status":2,"type":5,"withdrawStatus":2},"resultCode":1}
2025-07-03 00:01:00.657 - com.tig.im.SysApiLogAspect 接口【getPayConfig】总耗时(毫秒)：36
2025-07-03 00:01:00.658 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:00.660 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getPayConfig
2025-07-03 00:01:00.672 - com.tig.im.SysApiLogAspect 请求参数： /console/getPayConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=ef918fbe583b965b9ddf2690f1dd7bc5&time=**********606&type=9&account=1000&
2025-07-03 00:01:00.672 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:00.672 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法
2025-07-03 00:01:00.701 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getPayConfig】方法，应答参数：{"currentTime":*************,"data":{"id":{"counter":4476406,"date":*************,"machineIdentifier":3999260,"processIdentifier":-17996,"time":*************,"timeSecond":**********,"timestamp":**********},"name":"微包","status":2,"type":9,"withdrawStatus":2},"resultCode":1}
2025-07-03 00:01:00.701 - com.tig.im.SysApiLogAspect 接口【getPayConfig】总耗时(毫秒)：29
2025-07-03 00:01:00.701 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:01.884 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/pushConfig.html
2025-07-03 00:01:01.899 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/pushConfig.js
2025-07-03 00:01:02.152 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/pushConfig/get
2025-07-03 00:01:02.163 - com.tig.im.SysApiLogAspect 请求参数： /console/pushConfig/get
2025-07-03 00:01:02.163 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:02.163 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminPushConfigGet】方法
2025-07-03 00:01:02.180 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminPushConfigGet】方法，应答参数：{"currentTime":1751472062180,"resultCode":1}
2025-07-03 00:01:02.180 - com.tig.im.SysApiLogAspect 接口【adminPushConfigGet】总耗时(毫秒)：17
2025-07-03 00:01:02.180 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:02.618 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/pushTest.html
2025-07-03 00:01:02.634 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/pushTest.js
2025-07-03 00:01:03.274 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/vipPaymentConfigList.html
2025-07-03 00:01:03.285 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/core.js
2025-07-03 00:01:03.285 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/tripledes.js
2025-07-03 00:01:03.286 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/vipPaymentConfigList.js
2025-07-03 00:01:03.309 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/vipConfigList
2025-07-03 00:01:03.328 - com.tig.im.SysApiLogAspect 请求参数： /console/vipConfigList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=b2ab87e5993e5b36feee1fee50294636&time=*************&page=1&account=1000&
2025-07-03 00:01:03.329 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:03.330 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【vipConfigList】方法
2025-07-03 00:01:03.370 - controller roomPaymentConfigList resut :[]
2025-07-03 00:01:03.371 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【vipConfigList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:01:03.371 - com.tig.im.SysApiLogAspect 接口【vipConfigList】总耗时(毫秒)：40
2025-07-03 00:01:03.372 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:03.935 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/userList.html
2025-07-03 00:01:03.953 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/userList.js
2025-07-03 00:01:03.965 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/userList
2025-07-03 00:01:03.980 - com.tig.im.SysApiLogAspect 请求参数： /console/userList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=51e74044e8c1443ea7a0b4766727bb71&time=*************&page=1&account=1000&
2025-07-03 00:01:03.981 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:03.982 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法
2025-07-03 00:01:04.275 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[{"active":0,"anonymous":0,"areaCode":"","areaId":0,"attCount":0,"balance":0,"birthday":1100,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"1100","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"1100","nickname":"系统通知","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"6m0nv7obbpr016by","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":1100,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861100","totalConsume":0,"totalRecharge":0,"userId":1100,"userKey":"1e6e0a04d20f50967c64dac2d639a577","userType":1,"vip":0},{"active":0,"anonymous":0,"attCount":0,"balance":0,"checkRealNameAuthStatus":0,"createTime":**********,"fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"msgNum":0,"nickname":"1000","num":0,"offlineNoPushMsg":0,"onlinestate":0,"password":"","payPassword":"","phone":"1000","redPacketVip":0,"registerType":0,"score":"0.00","setAccountCount":0,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861000","totalConsume":0,"totalRecharge":0,"userId":1000,"userKey":"a9b7ba70783b617e9998dc4dd82eb3c5","userType":1,"vip":0},{"active":0,"anonymous":0,"areaCode":"86","areaId":0,"attCount":0,"balance":0,"birthday":10000,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"10000","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"10000","nickname":"客服公众号","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","phone":"10000","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"qtsx7ae11dbyn5oz","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":10000,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"8610000","totalConsume":0,"totalRecharge":0,"userId":10000,"userKey":"b7a782741f667201b54880c925faec4b","userType":2,"vip":0}],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":3,"totalCreate":0}
2025-07-03 00:01:04.277 - com.tig.im.SysApiLogAspect 接口【userList】总耗时(毫秒)：266
2025-07-03 00:01:04.279 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:05.094 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/roomList.html
2025-07-03 00:01:05.108 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/roomList.js
2025-07-03 00:01:05.120 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/roomList
2025-07-03 00:01:05.134 - com.tig.im.SysApiLogAspect 请求参数： /console/roomList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=987ca0cdf165b2e41741f8528f5f6a80&time=*************&page=1&account=1000&
2025-07-03 00:01:05.134 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:05.135 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【roomList】方法
2025-07-03 00:01:05.174 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【roomList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:01:05.179 - com.tig.im.SysApiLogAspect 接口【roomList】总耗时(毫秒)：38
2025-07-03 00:01:05.179 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:05.616 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/messageList.html
2025-07-03 00:01:05.649 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/messageList.js
2025-07-03 00:01:05.660 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/chat_logs_all
2025-07-03 00:01:05.682 - com.tig.im.SysApiLogAspect 请求参数： /console/chat_logs_all?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=2db7c8078d18c16791141aebedf2d1cd&time=*************&page=1&account=1000&
2025-07-03 00:01:05.686 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:05.689 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【chat_logs_all】方法
2025-07-03 00:01:05.710 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【chat_logs_all】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:01:05.711 - com.tig.im.SysApiLogAspect 接口【chat_logs_all】总耗时(毫秒)：21
2025-07-03 00:01:05.711 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:06.684 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/ipWhiteListManage.html
2025-07-03 00:01:06.699 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getIpWhiteListManage
2025-07-03 00:01:06.716 - com.tig.im.SysApiLogAspect 请求参数： /console/getIpWhiteListManage
2025-07-03 00:01:06.717 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:06.718 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getIpWhiteListManage】方法
2025-07-03 00:01:06.729 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getIpWhiteListManage】方法，应答参数：{"currentTime":1751472066729,"resultCode":1,"resultMsg":""}
2025-07-03 00:01:06.730 - com.tig.im.SysApiLogAspect 接口【getIpWhiteListManage】总耗时(毫秒)：11
2025-07-03 00:01:06.731 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:07.523 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/ipWhiteList.html
2025-07-03 00:01:07.543 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getIpWhiteList
2025-07-03 00:01:07.554 - com.tig.im.SysApiLogAspect 请求参数： /console/getIpWhiteList
2025-07-03 00:01:07.555 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:07.555 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getIpWhiteList】方法
2025-07-03 00:01:07.570 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getIpWhiteList】方法，应答参数：{"currentTime":1751472067569,"resultCode":1,"resultMsg":""}
2025-07-03 00:01:07.586 - com.tig.im.SysApiLogAspect 接口【getIpWhiteList】总耗时(毫秒)：13
2025-07-03 00:01:07.587 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:09.459 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/appRecharge.html
2025-07-03 00:01:09.491 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/appRecharge.js
2025-07-03 00:01:09.504 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/systemRecharge
2025-07-03 00:01:09.532 - com.tig.im.SysApiLogAspect 请求参数： /console/systemRecharge?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=a5477ff2c0a1433b3b3e53e533024b78&time=*************&page=1&account=1000&
2025-07-03 00:01:09.532 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:09.533 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【systemRecharge】方法
2025-07-03 00:01:09.585 - cn.tig.im.service.impl.ConsumeRecordManagerImpl 当前总金额：0.0
2025-07-03 00:01:09.616 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【systemRecharge】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:01:09.617 - com.tig.im.SysApiLogAspect 接口【systemRecharge】总耗时(毫秒)：83
2025-07-03 00:01:09.617 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:09.819 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/raiseCash.html
2025-07-03 00:01:09.832 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/raiseCash.js
2025-07-03 00:01:09.847 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/systemRecharge
2025-07-03 00:01:09.855 - com.tig.im.SysApiLogAspect 请求参数： /console/systemRecharge?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=1919db1d4d020a1d3ad899dee624167c&time=*************&page=1&type=2&account=1000&
2025-07-03 00:01:09.855 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:09.856 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【systemRecharge】方法
2025-07-03 00:01:09.897 - cn.tig.im.service.impl.ConsumeRecordManagerImpl 当前总金额：0.0
2025-07-03 00:01:09.919 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【systemRecharge】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:01:09.920 - com.tig.im.SysApiLogAspect 接口【systemRecharge】总耗时(毫秒)：63
2025-07-03 00:01:09.920 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:10.217 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/transfer.html
2025-07-03 00:01:10.256 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/transfer.js
2025-07-03 00:01:10.265 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/transferList
2025-07-03 00:01:10.283 - com.tig.im.SysApiLogAspect 请求参数： /console/transferList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=f439925e4f56c8b612b996d2642099ff&time=*************&page=1&account=1000&
2025-07-03 00:01:10.284 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:10.284 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【transferList】方法
2025-07-03 00:01:10.341 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【transferList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:01:10.341 - com.tig.im.SysApiLogAspect 接口【transferList】总耗时(毫秒)：56
2025-07-03 00:01:10.342 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:11.182 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/complaint.html
2025-07-03 00:01:11.210 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/complaint.js
2025-07-03 00:01:11.218 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/beReport
2025-07-03 00:01:11.233 - com.tig.im.SysApiLogAspect 请求参数： /console/beReport?access_token=51fa3b039f6846a48a35a4ba1cc2c138&receiver=&sender=&pageIndex=0&pageSize=15&secret=7943cdefbc8fb4af89b58a5ede241f80&time=*************&type=0&account=1000&
2025-07-03 00:01:11.233 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:11.234 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【beReport】方法
2025-07-03 00:01:11.269 - controller 举报详情：[]
2025-07-03 00:01:11.274 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【beReport】方法，应答参数：{"currentTime":*************,"data":{"allPageCount":0,"pageCount":0,"pageData":[],"pageIndex":0,"pageSize":15,"start":0,"total":0},"resultCode":1}
2025-07-03 00:01:11.274 - com.tig.im.SysApiLogAspect 接口【beReport】总耗时(毫秒)：35
2025-07-03 00:01:11.274 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:11.301 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/beReport
2025-07-03 00:01:11.312 - com.tig.im.SysApiLogAspect 请求参数： /console/beReport?access_token=51fa3b039f6846a48a35a4ba1cc2c138&receiver=&sender=&pageIndex=0&pageSize=15&secret=422efa81a6ae5a76354d05b8d622c974&time=*************&type=0&account=1000&
2025-07-03 00:01:11.312 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:11.313 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【beReport】方法
2025-07-03 00:01:11.346 - controller 举报详情：[]
2025-07-03 00:01:11.346 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【beReport】方法，应答参数：{"currentTime":*************,"data":{"allPageCount":0,"pageCount":0,"pageData":[],"pageIndex":0,"pageSize":15,"start":0,"total":0},"resultCode":1}
2025-07-03 00:01:11.347 - com.tig.im.SysApiLogAspect 接口【beReport】总耗时(毫秒)：33
2025-07-03 00:01:11.347 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:13.090 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/keyword.html
2025-07-03 00:01:13.102 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/keyword.js
2025-07-03 00:01:13.113 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/keywordfilter
2025-07-03 00:01:13.131 - com.tig.im.SysApiLogAspect 请求参数： /console/keywordfilter?access_token=51fa3b039f6846a48a35a4ba1cc2c138&pageSize=15&secret=38832c7bb29b3526efdf477aa2c0cd88&time=*************&pageIndex=0&account=1000&
2025-07-03 00:01:13.131 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:13.132 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【keywordfilter】方法
2025-07-03 00:01:13.168 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【keywordfilter】方法，应答参数：{"currentTime":*************,"data":{"allPageCount":0,"pageCount":0,"pageData":[],"pageIndex":0,"pageSize":15,"start":0,"total":0},"resultCode":1}
2025-07-03 00:01:13.168 - com.tig.im.SysApiLogAspect 接口【keywordfilter】总耗时(毫秒)：35
2025-07-03 00:01:13.169 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:13.186 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/keywordfilter
2025-07-03 00:01:13.202 - com.tig.im.SysApiLogAspect 请求参数： /console/keywordfilter?access_token=51fa3b039f6846a48a35a4ba1cc2c138&pageSize=15&secret=26855aee498647fc97b343d1a295b54d&time=*************&pageIndex=0&account=1000&
2025-07-03 00:01:13.202 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:13.202 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【keywordfilter】方法
2025-07-03 00:01:13.237 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【keywordfilter】方法，应答参数：{"currentTime":*************,"data":{"allPageCount":0,"pageCount":0,"pageData":[],"pageIndex":0,"pageSize":15,"start":0,"total":0},"resultCode":1}
2025-07-03 00:01:13.239 - com.tig.im.SysApiLogAspect 接口【keywordfilter】总耗时(毫秒)：34
2025-07-03 00:01:13.243 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:13.512 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/urlWhiteList.html
2025-07-03 00:01:13.525 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/urlWhiteList.js
2025-07-03 00:01:13.539 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getUrlWhiteList
2025-07-03 00:01:13.552 - com.tig.im.SysApiLogAspect 请求参数： /console/getUrlWhiteList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&pageSize=15&secret=6028d47923798a66bc5cccbfce54e9e2&time=*************&pageIndex=0&account=1000&
2025-07-03 00:01:13.553 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:13.553 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getUrlWhiteList】方法
2025-07-03 00:01:13.589 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getUrlWhiteList】方法，应答参数：{"currentTime":*************,"data":{"allPageCount":0,"pageCount":0,"pageData":[],"pageIndex":0,"pageSize":15,"start":0,"total":0},"resultCode":1}
2025-07-03 00:01:13.590 - com.tig.im.SysApiLogAspect 接口【getUrlWhiteList】总耗时(毫秒)：35
2025-07-03 00:01:13.590 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:13.604 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getUrlWhiteList
2025-07-03 00:01:13.618 - com.tig.im.SysApiLogAspect 请求参数： /console/getUrlWhiteList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&pageSize=15&secret=51e28b919e0d1d1bda710fbf752ec9a6&time=*************&pageIndex=0&account=1000&
2025-07-03 00:01:13.619 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:13.619 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getUrlWhiteList】方法
2025-07-03 00:01:13.655 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getUrlWhiteList】方法，应答参数：{"currentTime":*************,"data":{"allPageCount":0,"pageCount":0,"pageData":[],"pageIndex":0,"pageSize":15,"start":0,"total":0},"resultCode":1}
2025-07-03 00:01:13.655 - com.tig.im.SysApiLogAspect 接口【getUrlWhiteList】总耗时(毫秒)：35
2025-07-03 00:01:13.655 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:13.981 - com.tig.im.filter.AuthorizationFilter GET 请求：/mp/login
2025-07-03 00:01:13.982 - com.tig.im.filter.AuthorizationFilter 不包含请求令牌
2025-07-03 00:01:18.805 - com.tig.im.filter.AuthorizationFilter POST 请求：/config
2025-07-03 00:01:18.823 - com.tig.im.SysApiLogAspect 请求参数： /config
2025-07-03 00:01:18.824 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:18.824 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法
2025-07-03 00:01:18.825 - controller ==Client-IP===>  ************  ===Address==>  CN 
2025-07-03 00:01:19.273 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法，应答参数：{"currentTime":1751472079273,"data":{"XMPPDomain":"im.server.com","XMPPHost":"im.server.com","XMPPTimeout":180,"address":"CN","aliLoginStatus":2,"aliPayStatus":2,"aliWithdrawStatus":2,"androidVersion":0,"audioLen":"20","chatRecordTimeOut":-1,"cusServerUrl":"","displayRedPacket":1,"distance":20,"exchangeVipScore":"0","fileValidTime":-1,"firstShareScoreRate":"0","helpUrl":"","hideSearchByFriends":1,"hmPayStatus":2,"hmWithdrawStatus":2,"invisibleList":[],"iosVersion":0,"ipAddress":"************","isAudioStatus":0,"isCommonCreateGroup":0,"isCommonFindFriends":0,"isDelAfterReading":1,"isDiscoverStatus":1,"isEnableCusServer":1,"isNodesStatus":0,"isOpenCluster":0,"isOpenDHRecharge":0,"isOpenGoogleFCM":0,"isOpenOSStatus":0,"isOpenPositionService":1,"isOpenReadReceipt":1,"isOpenReceipt":1,"isOpenRegister":1,"isOpenSMSCode":1,"isOpenTelnum":1,"isOpenTwoBarCode":1,"isQestionOpen":1,"isTabBarStatus":1,"isUserSignRedPacket":1,"isWeiBaoStatus":0,"isWithdrawToAdmin":0,"jiGuangStatus":2,"macVersion":0,"maxSendRedPagesAmount":500,"minTransferAmount":0,"minWithdrawToAdmin":0,"nicknameSearchUser":0,"pcVersion":0,"popularAPP":"{\"lifeCircle\":1,\"videoMeeting\":1,\"liveVideo\":1,\"shortVideo\":1,\"peopleNearby\":1,\"scan\":1}","qqLoginStatus":2,"regeditPhoneOrName":1,"registerInviteCode":0,"secondShareScoreRate":"0","shareUrl":"","showContactsUser":0,"softUrl":"","tabBarConfigList":{"tabBarNum":0,"tabBarStatus":0},"thirdShareScoreRate":"0","tlPayStatus":2,"tlWithdrawStatus":2,"transferRate":0,"uploadMaxSize":20,"videoLen":"20","wechatH5LoginStatus":2,"wechatLoginStatus":2,"wechatPayStatus":2,"wechatWithdrawStatus":2,"weiBaoMaxRedPacketAmount":0,"weiBaoMaxTransferAmount":0,"weiBaoMinTransferAmount":0,"weiBaoTransferRate":0,"weiPayStatus":2,"weiWithdrawStatus":2,"xMPPDomain":"im.server.com","xMPPHost":"im.server.com","xMPPTimeout":180,"xmppPingTime":72,"yunPayStatus":2,"yunWithdrawStatus":2},"resultCode":1}
2025-07-03 00:01:19.280 - com.tig.im.SysApiLogAspect 接口【getConfig】总耗时(毫秒)：448
2025-07-03 00:01:19.281 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:19.557 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/config
2025-07-03 00:01:19.570 - com.tig.im.SysApiLogAspect 请求参数： /console/config?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=60894914c5cc57aaa33160c93270acba&time=*************&account=1000&
2025-07-03 00:01:19.570 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:19.570 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getConfig】方法
2025-07-03 00:01:19.583 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getConfig】方法，应答参数：{"currentTime":*************,"data":{"XMPPTimeout":180,"allowAddFirend":1,"allowConference":1,"allowCreateRoom":1,"allowInviteFriend":1,"allowSendCard":1,"allowSpeakCourse":1,"allowUploadFile":1,"androidVersion":0,"audioLen":"20","chatRecordTimeOut":-1,"distance":20,"exchangeVipScore":"0","fileValidTime":-1,"firstShareScoreRate":"0","giftRatio":0.5,"helpUrl":"","id":10000,"invisibleList":[],"iosPushServer":"apns","iosVersion":0,"isAttritionNotice":1,"isAuthApi":1,"isAutoAddressBook":0,"isEncrypt":0,"isFriendsVerify":1,"isKeepalive":1,"isKeyWord":0,"isLook":1,"isMsgSendTime":0,"isMultiLogin":1,"isNeedVerify":0,"isOpenCluster":0,"isOpenGoogleFCM":0,"isOpenOSStatus":0,"isOpenReceipt":1,"isOpenSMSCode":1,"isOpenVoip":0,"isQuestionOpen":1,"isSaveMsg":1,"isSaveMucMsg":1,"isSaveRequestLogs":0,"isStrongNotice":1,"isTelephoneLogin":1,"isTyping":0,"isUrlWhite":0,"isUseGoogleMap":0,"isUserIdLogin":1,"isUserSignRedPacket":1,"isVibration":0,"language":"zh","maxSignRedPacket":"0.01","maxUserSize":1000,"minSignRedPacket":"0.01","nameSearch":1,"nicknameSearchUser":0,"outTimeDestroy":-1,"phoneSearch":1,"regeditPhoneOrName":1,"registerInviteCode":0,"roamingTime":-1,"sMSType":"aliyun","secondShareScoreRate":"0","shareUrl":"","showLastLoginTime":-1,"showMember":1,"showRead":0,"showTelephone":-1,"softUrl":"","tabBarConfigList":{"tabBarNum":0,"tabBarStatus":0},"telephoneSearchUser":1,"thirdShareScoreRate":"0","videoLen":"20","xMPPTimeout":180},"resultCode":1}
2025-07-03 00:01:19.584 - com.tig.im.SysApiLogAspect 接口【getConfig】总耗时(毫秒)：13
2025-07-03 00:01:19.584 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:29.354 - com.tig.im.filter.AuthorizationFilter POST 请求：/config
2025-07-03 00:01:29.366 - com.tig.im.SysApiLogAspect 请求参数： /config
2025-07-03 00:01:29.366 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:29.366 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法
2025-07-03 00:01:29.367 - controller ==Client-IP===>  ************  ===Address==>  CN 
2025-07-03 00:01:29.821 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法，应答参数：{"currentTime":1751472089820,"data":{"XMPPDomain":"im.server.com","XMPPHost":"im.server.com","XMPPTimeout":180,"address":"CN","aliLoginStatus":2,"aliPayStatus":2,"aliWithdrawStatus":2,"androidVersion":0,"audioLen":"20","chatRecordTimeOut":-1,"cusServerUrl":"","displayRedPacket":1,"distance":20,"exchangeVipScore":"0","fileValidTime":-1,"firstShareScoreRate":"0","helpUrl":"","hideSearchByFriends":1,"hmPayStatus":2,"hmWithdrawStatus":2,"invisibleList":[],"iosVersion":0,"ipAddress":"************","isAudioStatus":0,"isCommonCreateGroup":0,"isCommonFindFriends":0,"isDelAfterReading":1,"isDiscoverStatus":1,"isEnableCusServer":1,"isNodesStatus":0,"isOpenCluster":0,"isOpenDHRecharge":0,"isOpenGoogleFCM":0,"isOpenOSStatus":0,"isOpenPositionService":1,"isOpenReadReceipt":1,"isOpenReceipt":1,"isOpenRegister":1,"isOpenSMSCode":1,"isOpenTelnum":1,"isOpenTwoBarCode":1,"isQestionOpen":1,"isTabBarStatus":1,"isUserSignRedPacket":1,"isWeiBaoStatus":0,"isWithdrawToAdmin":0,"jiGuangStatus":2,"macVersion":0,"maxSendRedPagesAmount":500,"minTransferAmount":0,"minWithdrawToAdmin":0,"nicknameSearchUser":0,"pcVersion":0,"popularAPP":"{\"lifeCircle\":1,\"videoMeeting\":1,\"liveVideo\":1,\"shortVideo\":1,\"peopleNearby\":1,\"scan\":1}","qqLoginStatus":2,"regeditPhoneOrName":1,"registerInviteCode":0,"secondShareScoreRate":"0","shareUrl":"","showContactsUser":0,"softUrl":"","tabBarConfigList":{"tabBarNum":0,"tabBarStatus":0},"thirdShareScoreRate":"0","tlPayStatus":2,"tlWithdrawStatus":2,"transferRate":0,"uploadMaxSize":20,"videoLen":"20","wechatH5LoginStatus":2,"wechatLoginStatus":2,"wechatPayStatus":2,"wechatWithdrawStatus":2,"weiBaoMaxRedPacketAmount":0,"weiBaoMaxTransferAmount":0,"weiBaoMinTransferAmount":0,"weiBaoTransferRate":0,"weiPayStatus":2,"weiWithdrawStatus":2,"xMPPDomain":"im.server.com","xMPPHost":"im.server.com","xMPPTimeout":180,"xmppPingTime":72,"yunPayStatus":2,"yunWithdrawStatus":2},"resultCode":1}
2025-07-03 00:01:29.821 - com.tig.im.SysApiLogAspect 接口【getConfig】总耗时(毫秒)：453
2025-07-03 00:01:29.822 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:30.108 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/clientConfig
2025-07-03 00:01:30.118 - com.tig.im.SysApiLogAspect 请求参数： /console/clientConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=dcb7472ba002274aa84aea0169aba21f&time=*************&account=1000&
2025-07-03 00:01:30.118 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:30.118 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getClientConfig】方法
2025-07-03 00:01:30.141 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getClientConfig】方法，应答参数：{"currentTime":*************,"data":{"XMPPDomain":"im.server.com","XMPPHost":"im.server.com","androidVersion":0,"cusServerUrl":"","displayRedPacket":1,"hideSearchByFriends":1,"id":10000,"iosVersion":0,"isAudioStatus":0,"isCommonCreateGroup":0,"isCommonFindFriends":0,"isDelAfterReading":1,"isDiscoverStatus":1,"isEnableCusServer":1,"isNodesStatus":0,"isOpenPositionService":1,"isOpenReadReceipt":1,"isOpenRegister":1,"isOpenTelnum":1,"isOpenTwoBarCode":1,"isTabBarStatus":1,"isUserSignRedPacket":1,"isWithdrawToAdmin":0,"macVersion":0,"minWithdrawToAdmin":0,"pcVersion":0,"popularAPP":"{\"lifeCircle\":1,\"videoMeeting\":1,\"liveVideo\":1,\"shortVideo\":1,\"peopleNearby\":1,\"scan\":1}","showContactsUser":0,"uploadMaxSize":20,"xMPPDomain":"im.server.com","xMPPHost":"im.server.com"},"resultCode":1}
2025-07-03 00:01:30.141 - com.tig.im.SysApiLogAspect 接口【getClientConfig】总耗时(毫秒)：23
2025-07-03 00:01:30.141 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:31.038 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/media/list
2025-07-03 00:01:31.058 - com.tig.im.SysApiLogAspect 请求参数： /console/media/list?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=10&secret=536b5329ef2459e2a0c2446bec6111d5&time=*************&page=1&type=1&account=1000&
2025-07-03 00:01:31.059 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:31.059 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getMediaList】方法
2025-07-03 00:01:31.078 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getMediaList】方法，应答参数：{"currentTime":*************,"data":[],"resultCode":1}
2025-07-03 00:01:31.079 - com.tig.im.SysApiLogAspect 接口【getMediaList】总耗时(毫秒)：18
2025-07-03 00:01:31.079 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:31.546 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/income/list
2025-07-03 00:01:31.551 - com.tig.im.SysApiLogAspect 请求参数： /console/income/list?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=10&secret=8dac857c51b99b8e084e4f5377d62e22&time=*************&page=1&account=1000&
2025-07-03 00:01:31.552 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:31.552 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getMediaList】方法
2025-07-03 00:01:31.578 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getMediaList】方法，应答参数：{"currentTime":*************,"data":[],"resultCode":1}
2025-07-03 00:01:31.578 - com.tig.im.SysApiLogAspect 接口【getMediaList】总耗时(毫秒)：25
2025-07-03 00:01:31.579 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:32.851 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/anon/list
2025-07-03 00:01:32.862 - com.tig.im.SysApiLogAspect 请求参数： /console/anon/list?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=10&secret=139b8ed0e8259f797a38f9d847084318&time=*************&page=1&account=1000&
2025-07-03 00:01:32.862 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:32.863 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AnonymousController】类的【getAnonymousList】方法
2025-07-03 00:01:32.873 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AnonymousController】类的【getAnonymousList】方法，应答参数：{"currentTime":*************,"data":[],"resultCode":1}
2025-07-03 00:01:32.874 - com.tig.im.SysApiLogAspect 接口【getAnonymousList】总耗时(毫秒)：10
2025-07-03 00:01:32.874 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:33.524 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/appDiscoverList
2025-07-03 00:01:33.525 - com.tig.im.SysApiLogAspect 请求参数： /console/appDiscoverList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=39b135763df8685109da014327f4c887&time=*************&page=1&user=0&account=1000&
2025-07-03 00:01:33.526 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:33.527 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AppDiscoverController】类的【appDiscoverList】方法
2025-07-03 00:01:33.562 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AppDiscoverController】类的【appDiscoverList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[{"discoverId":{"counter":8744943,"date":**********000,"machineIdentifier":3999260,"processIdentifier":2772,"time":**********000,"timeSecond":**********,"timestamp":**********},"discoverImg":"/pages/img/socialCircles.png","discoverLinkURL":"#","discoverName":"生活圈","discoverNum":1,"discoverStatus":1,"discoverType":0,"discoverUpdateTime":**********},{"discoverId":{"counter":8744944,"date":**********000,"machineIdentifier":3999260,"processIdentifier":2772,"time":**********000,"timeSecond":**********,"timestamp":**********},"discoverImg":"/pages/img/video.png","discoverLinkURL":"#","discoverName":"短视频","discoverNum":2,"discoverStatus":1,"discoverType":0,"discoverUpdateTime":**********},{"discoverId":{"counter":8744945,"date":**********000,"machineIdentifier":3999260,"processIdentifier":2772,"time":**********000,"timeSecond":**********,"timestamp":**********},"discoverImg":"/pages/img/videoconferencing.png","discoverLinkURL":"#","discoverName":"视频会议","discoverNum":3,"discoverStatus":1,"discoverType":0,"discoverUpdateTime":**********},{"discoverId":{"counter":8744946,"date":**********000,"machineIdentifier":3999260,"processIdentifier":2772,"time":**********000,"timeSecond":**********,"timestamp":**********},"discoverImg":"/pages/img/videoLive.png","discoverLinkURL":"#","discoverName":"视频直播","discoverNum":4,"discoverStatus":1,"discoverType":0,"discoverUpdateTime":**********},{"discoverId":{"counter":8744947,"date":**********000,"machineIdentifier":3999260,"processIdentifier":2772,"time":**********000,"timeSecond":**********,"timestamp":**********},"discoverImg":"/pages/img/people.png","discoverLinkURL":"#","discoverName":"附近人","discoverNum":5,"discoverStatus":1,"discoverType":0,"discoverUpdateTime":**********},{"discoverId":{"counter":8744948,"date":**********000,"machineIdentifier":3999260,"processIdentifier":2772,"time":**********000,"timeSecond":**********,"timestamp":**********},"discoverImg":"/pages/img/mp.png","discoverLinkURL":"#","discoverName":"公众号","discoverNum":6,"discoverStatus":1,"discoverType":0,"discoverUpdateTime":**********},{"discoverId":{"counter":8744949,"date":**********000,"machineIdentifier":3999260,"processIdentifier":2772,"time":**********000,"timeSecond":**********,"timestamp":**********},"discoverImg":"/pages/img/redPacket.png","discoverLinkURL":"#","discoverName":"签到红包","discoverNum":7,"discoverStatus":1,"discoverType":0,"discoverUpdateTime":**********}],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":7,"totalCreate":0}
2025-07-03 00:01:33.563 - com.tig.im.SysApiLogAspect 接口【appDiscoverList】总耗时(毫秒)：34
2025-07-03 00:01:33.564 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:36.510 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/appTabBarList
2025-07-03 00:01:36.529 - com.tig.im.SysApiLogAspect 请求参数： /console/appTabBarList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=70d574d0c7726eb1246b60d9097a3e99&time=*************&page=1&user=0&account=1000&
2025-07-03 00:01:36.529 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:36.530 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AppTabBarController】类的【appTabBarList】方法
2025-07-03 00:01:36.565 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AppTabBarController】类的【appTabBarList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:01:36.565 - com.tig.im.SysApiLogAspect 接口【appTabBarList】总耗时(毫秒)：35
2025-07-03 00:01:36.565 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:37.136 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/versionInfoList
2025-07-03 00:01:37.163 - com.tig.im.SysApiLogAspect 请求参数： /console/versionInfoList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=10&secret=8f019501fb8b1fa3429b4b348412f07b&time=*************&page=1&account=1000&
2025-07-03 00:01:37.164 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:37.164 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【versionInfoList】方法
2025-07-03 00:01:37.209 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【versionInfoList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:01:37.209 - com.tig.im.SysApiLogAspect 接口【versionInfoList】总耗时(毫秒)：45
2025-07-03 00:01:37.209 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:37.902 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getLoginConfig
2025-07-03 00:01:37.906 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getLoginConfig
2025-07-03 00:01:37.913 - com.tig.im.SysApiLogAspect 请求参数： /console/getLoginConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=88b509997e8b4c85215e29cdd05bbf1f&time=*************&type=2&account=1000&
2025-07-03 00:01:37.913 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:37.913 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法
2025-07-03 00:01:37.917 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getLoginConfig
2025-07-03 00:01:37.917 - com.tig.im.SysApiLogAspect 请求参数： /console/getLoginConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=761d23dc4670ba23f72e391bf35ac2f8&time=*************&type=1&account=1000&
2025-07-03 00:01:37.918 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:37.918 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法
2025-07-03 00:01:37.926 - com.tig.im.SysApiLogAspect 请求参数： /console/getLoginConfig?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=88b509997e8b4c85215e29cdd05bbf1f&time=*************&type=4&account=1000&
2025-07-03 00:01:37.926 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:01:37.926 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法
2025-07-03 00:01:38.053 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法，应答参数：{"currentTime":*************,"resultCode":1}
2025-07-03 00:01:38.054 - com.tig.im.SysApiLogAspect 接口【getLoginConfig】总耗时(毫秒)：126
2025-07-03 00:01:38.054 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:38.057 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法，应答参数：{"currentTime":*************,"data":{"id":{"counter":4476460,"date":*************,"machineIdentifier":3999260,"processIdentifier":-17996,"time":*************,"timeSecond":1751472098,"timestamp":1751472098},"name":"微信","status":2,"type":2},"resultCode":1}
2025-07-03 00:01:38.057 - com.tig.im.SysApiLogAspect 接口【getLoginConfig】总耗时(毫秒)：143
2025-07-03 00:01:38.057 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:01:38.062 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getLoginConfig】方法，应答参数：{"currentTime":1751472098062,"data":{"id":{"counter":4476461,"date":*************,"machineIdentifier":3999260,"processIdentifier":-17996,"time":*************,"timeSecond":1751472098,"timestamp":1751472098},"name":"QQ","status":2,"type":1},"resultCode":1}
2025-07-03 00:01:38.062 - com.tig.im.SysApiLogAspect 接口【getLoginConfig】总耗时(毫秒)：144
2025-07-03 00:01:38.062 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:06.081 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/userList
2025-07-03 00:02:06.094 - com.tig.im.SysApiLogAspect 请求参数： /console/userList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=eb5df1e0cac137d87e79e42f6608662f&time=*************&page=1&account=1000&
2025-07-03 00:02:06.094 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:06.094 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法
2025-07-03 00:02:06.167 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[{"active":0,"anonymous":0,"areaCode":"","areaId":0,"attCount":0,"balance":0,"birthday":1100,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"1100","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"1100","nickname":"系统通知","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"6m0nv7obbpr016by","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":1100,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861100","totalConsume":0,"totalRecharge":0,"userId":1100,"userKey":"1e6e0a04d20f50967c64dac2d639a577","userType":1,"vip":0},{"active":0,"anonymous":0,"attCount":0,"balance":0,"checkRealNameAuthStatus":0,"createTime":**********,"fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"msgNum":0,"nickname":"1000","num":0,"offlineNoPushMsg":0,"onlinestate":0,"password":"","payPassword":"","phone":"1000","redPacketVip":0,"registerType":0,"score":"0.00","setAccountCount":0,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861000","totalConsume":0,"totalRecharge":0,"userId":1000,"userKey":"a9b7ba70783b617e9998dc4dd82eb3c5","userType":1,"vip":0},{"active":0,"anonymous":0,"areaCode":"86","areaId":0,"attCount":0,"balance":0,"birthday":10000,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"10000","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"10000","nickname":"客服公众号","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","phone":"10000","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"qtsx7ae11dbyn5oz","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":10000,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"8610000","totalConsume":0,"totalRecharge":0,"userId":10000,"userKey":"b7a782741f667201b54880c925faec4b","userType":2,"vip":0}],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":3,"totalCreate":0}
2025-07-03 00:02:06.167 - com.tig.im.SysApiLogAspect 接口【userList】总耗时(毫秒)：73
2025-07-03 00:02:06.167 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:08.034 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/admin.html
2025-07-03 00:02:08.053 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/admin.js
2025-07-03 00:02:08.064 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/adminList
2025-07-03 00:02:08.077 - com.tig.im.SysApiLogAspect 请求参数： /console/adminList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&adminId=1000&limit=15&secret=cc78a7cb6101148aee0182164a07d73c&time=*************&page=1&type=0&userId=1000&account=1000&
2025-07-03 00:02:08.077 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:08.077 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法
2025-07-03 00:02:08.099 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:02:08.099 - com.tig.im.SysApiLogAspect 接口【adminList】总耗时(毫秒)：22
2025-07-03 00:02:08.099 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:08.893 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/customer.html
2025-07-03 00:02:08.900 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/customer.js
2025-07-03 00:02:08.912 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/adminList
2025-07-03 00:02:08.921 - com.tig.im.SysApiLogAspect 请求参数： /console/adminList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&adminId=1000&limit=15&secret=4d9bc90017d8ab1f7fa768ebaee4aca8&time=*************&page=1&type=4&userId=1000&account=1000&
2025-07-03 00:02:08.921 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:08.921 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法
2025-07-03 00:02:08.938 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:02:08.938 - com.tig.im.SysApiLogAspect 接口【adminList】总耗时(毫秒)：16
2025-07-03 00:02:08.939 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:09.429 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/finance.html
2025-07-03 00:02:09.436 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/finance.js
2025-07-03 00:02:09.443 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/adminList
2025-07-03 00:02:09.457 - com.tig.im.SysApiLogAspect 请求参数： /console/adminList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&adminId=1000&limit=15&secret=87eaba5f1eef7fd275671a891cf75723&time=*************&page=1&type=7&userId=1000&account=1000&
2025-07-03 00:02:09.457 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:09.457 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法
2025-07-03 00:02:09.474 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:02:09.474 - com.tig.im.SysApiLogAspect 接口【adminList】总耗时(毫秒)：16
2025-07-03 00:02:09.474 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:09.946 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/robot.html
2025-07-03 00:02:09.954 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/robot.js
2025-07-03 00:02:09.962 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/adminList
2025-07-03 00:02:09.978 - com.tig.im.SysApiLogAspect 请求参数： /console/adminList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&adminId=1000&limit=15&secret=b4b9a0ffac8c518004aaf346960366d1&time=*************&page=1&type=3&userId=1000&account=1000&
2025-07-03 00:02:09.978 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:09.978 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法
2025-07-03 00:02:09.996 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:02:09.996 - com.tig.im.SysApiLogAspect 接口【adminList】总耗时(毫秒)：18
2025-07-03 00:02:09.996 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:10.398 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/tourist.html
2025-07-03 00:02:10.405 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/tourist.js
2025-07-03 00:02:10.410 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/adminList
2025-07-03 00:02:10.424 - com.tig.im.SysApiLogAspect 请求参数： /console/adminList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&adminId=1000&limit=15&secret=c3e2d919f9c2c1e8b4cc3afeb3efe7c4&time=*************&page=1&type=1&userId=1000&account=1000&
2025-07-03 00:02:10.424 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:10.424 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法
2025-07-03 00:02:10.443 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:02:10.443 - com.tig.im.SysApiLogAspect 接口【adminList】总耗时(毫秒)：19
2025-07-03 00:02:10.443 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:10.788 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/publicNumber.html
2025-07-03 00:02:10.796 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/js/publicNumber.js
2025-07-03 00:02:10.807 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/adminList
2025-07-03 00:02:10.818 - com.tig.im.SysApiLogAspect 请求参数： /console/adminList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&adminId=1000&limit=15&secret=4c2e5be5537fe670f3296b61fb96ad37&time=*************&page=1&type=2&userId=1000&account=1000&
2025-07-03 00:02:10.818 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:10.818 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法
2025-07-03 00:02:10.883 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[{"account":"10000","createTime":**********,"id":{"counter":8744915,"date":**********000,"machineIdentifier":3999260,"processIdentifier":2772,"time":**********000,"timeSecond":**********,"timestamp":**********},"lastLoginTime":0,"nickName":"客服公众号","phone":"10000","role":2,"status":1,"userId":10000}],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":1,"totalCreate":0}
2025-07-03 00:02:10.884 - com.tig.im.SysApiLogAspect 接口【adminList】总耗时(毫秒)：63
2025-07-03 00:02:10.884 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:11.350 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/roomList
2025-07-03 00:02:11.363 - com.tig.im.SysApiLogAspect 请求参数： /console/roomList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=1935aab9c575e9de3866ee7f05b04b68&time=*************&page=1&account=1000&
2025-07-03 00:02:11.363 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:11.363 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【roomList】方法
2025-07-03 00:02:11.392 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【roomList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:02:11.392 - com.tig.im.SysApiLogAspect 接口【roomList】总耗时(毫秒)：29
2025-07-03 00:02:11.392 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:12.317 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/chat_logs_all
2025-07-03 00:02:12.329 - com.tig.im.SysApiLogAspect 请求参数： /console/chat_logs_all?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=5930dfb64a6af0b788daf54c287ecead&time=*************&page=1&account=1000&
2025-07-03 00:02:12.330 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:12.330 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【chat_logs_all】方法
2025-07-03 00:02:12.460 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【chat_logs_all】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:02:12.460 - com.tig.im.SysApiLogAspect 接口【chat_logs_all】总耗时(毫秒)：129
2025-07-03 00:02:12.460 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:02:13.722 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/getIpWhiteListManage
2025-07-03 00:02:13.737 - com.tig.im.SysApiLogAspect 请求参数： /console/getIpWhiteListManage
2025-07-03 00:02:13.737 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:02:13.737 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getIpWhiteListManage】方法
2025-07-03 00:02:13.747 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getIpWhiteListManage】方法，应答参数：{"currentTime":1751472133747,"resultCode":1,"resultMsg":""}
2025-07-03 00:02:13.747 - com.tig.im.SysApiLogAspect 接口【getIpWhiteListManage】总耗时(毫秒)：10
2025-07-03 00:02:13.747 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:05:00.027 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:05:00
2025-07-03 00:05:00.028 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:05:00
2025-07-03 00:05:00.034 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:10:00.018 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:10:00
2025-07-03 00:10:00.024 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:10:00
2025-07-03 00:10:00.030 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:10:37.634 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/userList
2025-07-03 00:10:37.647 - com.tig.im.SysApiLogAspect 请求参数： /console/userList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=5019d91a7bc95897cb8a87b1363b138a&time=*************&page=1&account=1000&
2025-07-03 00:10:37.647 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:10:37.647 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法
2025-07-03 00:10:37.725 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[{"active":0,"anonymous":0,"areaCode":"","areaId":0,"attCount":0,"balance":0,"birthday":1100,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"1100","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"1100","nickname":"系统通知","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"6m0nv7obbpr016by","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":1100,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861100","totalConsume":0,"totalRecharge":0,"userId":1100,"userKey":"1e6e0a04d20f50967c64dac2d639a577","userType":1,"vip":0},{"active":0,"anonymous":0,"attCount":0,"balance":0,"checkRealNameAuthStatus":0,"createTime":**********,"fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"msgNum":0,"nickname":"1000","num":0,"offlineNoPushMsg":0,"onlinestate":0,"password":"","payPassword":"","phone":"1000","redPacketVip":0,"registerType":0,"score":"0.00","setAccountCount":0,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861000","totalConsume":0,"totalRecharge":0,"userId":1000,"userKey":"a9b7ba70783b617e9998dc4dd82eb3c5","userType":1,"vip":0},{"active":0,"anonymous":0,"areaCode":"86","areaId":0,"attCount":0,"balance":0,"birthday":10000,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"10000","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"10000","nickname":"客服公众号","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","phone":"10000","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"qtsx7ae11dbyn5oz","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":10000,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"8610000","totalConsume":0,"totalRecharge":0,"userId":10000,"userKey":"b7a782741f667201b54880c925faec4b","userType":2,"vip":0}],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":3,"totalCreate":0}
2025-07-03 00:10:37.725 - com.tig.im.SysApiLogAspect 接口【userList】总耗时(毫秒)：76
2025-07-03 00:10:37.725 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:10:40.786 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/adminList
2025-07-03 00:10:40.803 - com.tig.im.SysApiLogAspect 请求参数： /console/adminList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&adminId=1000&limit=15&secret=226bb0bb8400c1a9b9d9fa8098104770&time=*************&page=1&type=0&userId=1000&account=1000&
2025-07-03 00:10:40.803 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:10:40.804 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法
2025-07-03 00:10:40.831 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【adminList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":0,"totalCreate":0}
2025-07-03 00:10:40.831 - com.tig.im.SysApiLogAspect 接口【adminList】总耗时(毫秒)：27
2025-07-03 00:10:40.832 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:10:47.697 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/userList
2025-07-03 00:10:47.704 - com.tig.im.SysApiLogAspect 请求参数： /console/userList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=38485412d12847484f8f46ae36baf1e7&time=*************&page=1&account=1000&
2025-07-03 00:10:47.704 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:10:47.704 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法
2025-07-03 00:10:47.769 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[{"active":0,"anonymous":0,"areaCode":"","areaId":0,"attCount":0,"balance":0,"birthday":1100,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"1100","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"1100","nickname":"系统通知","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"6m0nv7obbpr016by","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":1100,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861100","totalConsume":0,"totalRecharge":0,"userId":1100,"userKey":"1e6e0a04d20f50967c64dac2d639a577","userType":1,"vip":0},{"active":0,"anonymous":0,"attCount":0,"balance":0,"checkRealNameAuthStatus":0,"createTime":**********,"fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"msgNum":0,"nickname":"1000","num":0,"offlineNoPushMsg":0,"onlinestate":0,"password":"","payPassword":"","phone":"1000","redPacketVip":0,"registerType":0,"score":"0.00","setAccountCount":0,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861000","totalConsume":0,"totalRecharge":0,"userId":1000,"userKey":"a9b7ba70783b617e9998dc4dd82eb3c5","userType":1,"vip":0},{"active":0,"anonymous":0,"areaCode":"86","areaId":0,"attCount":0,"balance":0,"birthday":10000,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"10000","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"10000","nickname":"客服公众号","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","phone":"10000","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"qtsx7ae11dbyn5oz","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":10000,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"8610000","totalConsume":0,"totalRecharge":0,"userId":10000,"userKey":"b7a782741f667201b54880c925faec4b","userType":2,"vip":0}],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":3,"totalCreate":0}
2025-07-03 00:10:47.771 - com.tig.im.SysApiLogAspect 接口【userList】总耗时(毫秒)：65
2025-07-03 00:10:47.771 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:10:51.784 - com.tig.im.filter.AuthorizationFilter POST 请求：/console/config
2025-07-03 00:10:51.795 - com.tig.im.SysApiLogAspect 请求参数： /console/config?access_token=51fa3b039f6846a48a35a4ba1cc2c138&secret=69631609ee5cb37c6d722107225c6ee0&time=*************&account=1000&
2025-07-03 00:10:51.796 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:10:51.796 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getConfig】方法
2025-07-03 00:10:51.810 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【getConfig】方法，应答参数：{"currentTime":*************,"data":{"XMPPTimeout":180,"allowAddFirend":1,"allowConference":1,"allowCreateRoom":1,"allowInviteFriend":1,"allowSendCard":1,"allowSpeakCourse":1,"allowUploadFile":1,"androidVersion":0,"audioLen":"20","chatRecordTimeOut":-1,"distance":20,"exchangeVipScore":"0","fileValidTime":-1,"firstShareScoreRate":"0","giftRatio":0.5,"helpUrl":"","id":10000,"invisibleList":[],"iosPushServer":"apns","iosVersion":0,"isAttritionNotice":1,"isAuthApi":1,"isAutoAddressBook":0,"isEncrypt":0,"isFriendsVerify":1,"isKeepalive":1,"isKeyWord":0,"isLook":1,"isMsgSendTime":0,"isMultiLogin":1,"isNeedVerify":0,"isOpenCluster":0,"isOpenGoogleFCM":0,"isOpenOSStatus":0,"isOpenReceipt":1,"isOpenSMSCode":1,"isOpenVoip":0,"isQuestionOpen":1,"isSaveMsg":1,"isSaveMucMsg":1,"isSaveRequestLogs":0,"isStrongNotice":1,"isTelephoneLogin":1,"isTyping":0,"isUrlWhite":0,"isUseGoogleMap":0,"isUserIdLogin":1,"isUserSignRedPacket":1,"isVibration":0,"language":"zh","maxSignRedPacket":"0.01","maxUserSize":1000,"minSignRedPacket":"0.01","nameSearch":1,"nicknameSearchUser":0,"outTimeDestroy":-1,"phoneSearch":1,"regeditPhoneOrName":1,"registerInviteCode":0,"roamingTime":-1,"sMSType":"aliyun","secondShareScoreRate":"0","shareUrl":"","showLastLoginTime":-1,"showMember":1,"showRead":0,"showTelephone":-1,"softUrl":"","tabBarConfigList":{"tabBarNum":0,"tabBarStatus":0},"telephoneSearchUser":1,"thirdShareScoreRate":"0","videoLen":"20","xMPPTimeout":180},"resultCode":1}
2025-07-03 00:10:51.811 - com.tig.im.SysApiLogAspect 接口【getConfig】总耗时(毫秒)：13
2025-07-03 00:10:51.811 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:11:20.413 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/updateUser
2025-07-03 00:11:20.459 - com.tig.im.SysApiLogAspect 请求参数： /console/updateUser?birthday=**********&sex=0&telephone=***********&secret=df1f2a97dc53ce8f66d653c25ec2f79d&serInviteCode=&registerType=0&userId=0&access_token=51fa3b039f6846a48a35a4ba1cc2c138&password=qwer4321&areaCode=86&nickname=haru88&time=**********406&userType=0&vip=6&account=1000&
2025-07-03 00:11:20.459 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:11:20.460 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【saveUserMsg】方法
2025-07-03 00:11:21.082 - cn.tig.im.service.impl.UserManagerImpl  config defaultTelephones : null
2025-07-03 00:11:21.100 - cn.tig.im.service.impl.AddressBookManagerImpl 注册时修改数据：telephone:86***********   toUserId:********    nickName:haru88   registerTime:**********
2025-07-03 00:11:21.113 - cn.tig.im.service.impl.AddressBookManagerImpl 推送使用的电话号码：86***********
2025-07-03 00:11:21.143 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【saveUserMsg】方法，应答参数：{"currentTime":*************,"resultCode":1}
2025-07-03 00:11:21.145 - com.tig.im.SysApiLogAspect 接口【saveUserMsg】总耗时(毫秒)：683
2025-07-03 00:11:21.145 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:11:21.173 - com.tig.im.filter.AuthorizationFilter GET 请求：/console/userList
2025-07-03 00:11:21.186 - com.tig.im.SysApiLogAspect 请求参数： /console/userList?access_token=51fa3b039f6846a48a35a4ba1cc2c138&limit=15&secret=38485412d12847484f8f46ae36baf1e7&time=*************&page=1&account=1000&
2025-07-03 00:11:21.186 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-03 00:11:21.187 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法
2025-07-03 00:11:21.294 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.AdminController】类的【userList】方法，应答参数：{"currentTime":*************,"total":0,"totalPassAilpay":0,"data":[{"account":"1000","active":0,"anonymous":0,"areaCode":"86","areaId":0,"attCount":0,"balance":0,"birthday":**********,"carrier":"电信","checkRealNameAuthStatus":0,"cityId":0,"countryId":0,"createTime":**********,"description":"","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":0,"lng":0},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"loginLog":{"isFirstLogin":1,"latitude":0,"loginTime":**********,"longitude":0,"offlineTime":0},"modifyTime":**********,"msgNum":0,"name":"","nickname":"haru88","num":0,"offlineNoPushMsg":0,"onlinestate":0,"password":"","payPassword":"","phone":"***********","phoneToLocation":"浙江-宁波市","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"yfjtkwrfzjvcnqq1","score":"0.00","serInviteCode":"4Uvs","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":0,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"86***********","totalConsume":0,"totalRecharge":0,"userAccountKey":"a9b7ba70783b617e9998dc4dd82eb3c5","userId":********,"userKey":"057b91fdd1d9aa0aa483c01632b7317c","userType":0,"vip":0},{"active":0,"anonymous":0,"areaCode":"","areaId":0,"attCount":0,"balance":0,"birthday":1100,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"1100","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"1100","nickname":"系统通知","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"6m0nv7obbpr016by","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":1100,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861100","totalConsume":0,"totalRecharge":0,"userId":1100,"userKey":"1e6e0a04d20f50967c64dac2d639a577","userType":1,"vip":0},{"active":0,"anonymous":0,"attCount":0,"balance":0,"checkRealNameAuthStatus":0,"createTime":**********,"fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"msgNum":0,"nickname":"1000","num":0,"offlineNoPushMsg":0,"onlinestate":0,"password":"","payPassword":"","phone":"1000","redPacketVip":0,"registerType":0,"score":"0.00","setAccountCount":0,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"861000","totalConsume":0,"totalRecharge":0,"userId":1000,"userKey":"a9b7ba70783b617e9998dc4dd82eb3c5","userType":1,"vip":0},{"active":0,"anonymous":0,"areaCode":"86","areaId":0,"attCount":0,"balance":0,"birthday":10000,"checkRealNameAuthStatus":0,"cityId":400300,"countryId":0,"createTime":**********,"description":"10000","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":10,"lng":10},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"name":"10000","nickname":"客服公众号","num":0,"offlineNoPushMsg":0,"onlinestate":1,"password":"","payPassword":"","phone":"10000","provinceId":0,"redPacketVip":0,"registerType":0,"salt":"qtsx7ae11dbyn5oz","score":"0.00","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":10000,"showLastLoginTime":0,"signCount":"0","signMoneyCount":"0.00","status":1,"telephone":"8610000","totalConsume":0,"totalRecharge":0,"userId":10000,"userKey":"b7a782741f667201b54880c925faec4b","userType":2,"vip":0}],"totalPassBank":0,"totalDone":0,"resultCode":1,"count":4,"totalCreate":0}
2025-07-03 00:11:21.295 - com.tig.im.SysApiLogAspect 接口【userList】总耗时(毫秒)：102
2025-07-03 00:11:21.296 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:14:31.361 - com.tig.im.filter.AuthorizationFilter HEAD 请求：/config
2025-07-03 00:14:31.373 - com.tig.im.SysApiLogAspect 请求参数： /config
2025-07-03 00:14:31.373 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent curl/8.11.0 
2025-07-03 00:14:31.373 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法
2025-07-03 00:14:31.374 - controller ==Client-IP===>  ************  ===Address==>  CN 
2025-07-03 00:14:31.805 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法，应答参数：{"currentTime":*************,"data":{"XMPPDomain":"im.server.com","XMPPHost":"im.server.com","XMPPTimeout":180,"address":"CN","aliLoginStatus":2,"aliPayStatus":2,"aliWithdrawStatus":2,"androidVersion":0,"audioLen":"20","chatRecordTimeOut":-1,"cusServerUrl":"","displayRedPacket":1,"distance":20,"exchangeVipScore":"0","fileValidTime":-1,"firstShareScoreRate":"0","helpUrl":"","hideSearchByFriends":1,"hmPayStatus":2,"hmWithdrawStatus":2,"invisibleList":[],"iosVersion":0,"ipAddress":"************","isAudioStatus":0,"isCommonCreateGroup":0,"isCommonFindFriends":0,"isDelAfterReading":1,"isDiscoverStatus":1,"isEnableCusServer":1,"isNodesStatus":0,"isOpenCluster":0,"isOpenDHRecharge":0,"isOpenGoogleFCM":0,"isOpenOSStatus":0,"isOpenPositionService":1,"isOpenReadReceipt":1,"isOpenReceipt":1,"isOpenRegister":1,"isOpenSMSCode":1,"isOpenTelnum":1,"isOpenTwoBarCode":1,"isQestionOpen":1,"isTabBarStatus":1,"isUserSignRedPacket":1,"isWeiBaoStatus":0,"isWithdrawToAdmin":0,"jiGuangStatus":2,"macVersion":0,"maxSendRedPagesAmount":500,"minTransferAmount":0,"minWithdrawToAdmin":0,"nicknameSearchUser":0,"pcVersion":0,"popularAPP":"{\"lifeCircle\":1,\"videoMeeting\":1,\"liveVideo\":1,\"shortVideo\":1,\"peopleNearby\":1,\"scan\":1}","qqLoginStatus":2,"regeditPhoneOrName":1,"registerInviteCode":0,"secondShareScoreRate":"0","shareUrl":"","showContactsUser":0,"softUrl":"","tabBarConfigList":{"tabBarNum":0,"tabBarStatus":0},"thirdShareScoreRate":"0","tlPayStatus":2,"tlWithdrawStatus":2,"transferRate":0,"uploadMaxSize":20,"videoLen":"20","wechatH5LoginStatus":2,"wechatLoginStatus":2,"wechatPayStatus":2,"wechatWithdrawStatus":2,"weiBaoMaxRedPacketAmount":0,"weiBaoMaxTransferAmount":0,"weiBaoMinTransferAmount":0,"weiBaoTransferRate":0,"weiPayStatus":2,"weiWithdrawStatus":2,"xMPPDomain":"im.server.com","xMPPHost":"im.server.com","xMPPTimeout":180,"xmppPingTime":72,"yunPayStatus":2,"yunWithdrawStatus":2},"resultCode":1}
2025-07-03 00:14:31.805 - com.tig.im.SysApiLogAspect 接口【getConfig】总耗时(毫秒)：430
2025-07-03 00:14:31.805 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:15:00.000 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:15:00
2025-07-03 00:15:00.008 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:15:00.024 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:15:00
2025-07-03 00:20:00.010 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:20:00
2025-07-03 00:20:00.025 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:20:00.036 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:20:00
2025-07-03 00:25:00.017 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:25:00
2025-07-03 00:25:00.018 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:25:00
2025-07-03 00:25:00.026 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:29:46.814 - com.tig.im.filter.AuthorizationFilter GET 请求：/clipboard.min.js
2025-07-03 00:29:47.435 - com.tig.im.filter.AuthorizationFilter POST 请求：/config
2025-07-03 00:29:47.452 - com.tig.im.SysApiLogAspect 请求参数： /config
2025-07-03 00:29:47.452 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:29:47.453 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法
2025-07-03 00:29:47.453 - controller ==Client-IP===>  ************  ===Address==>  CN 
2025-07-03 00:29:47.594 - com.tig.im.filter.AuthorizationFilter GET 请求：/256X256.ico
2025-07-03 00:29:47.594 - com.tig.im.filter.AuthorizationFilter 不包含请求令牌
2025-07-03 00:29:47.892 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法，应答参数：{"currentTime":1751473787892,"data":{"XMPPDomain":"im.server.com","XMPPHost":"im.server.com","XMPPTimeout":180,"address":"CN","aliLoginStatus":2,"aliPayStatus":2,"aliWithdrawStatus":2,"androidVersion":0,"audioLen":"20","chatRecordTimeOut":-1,"cusServerUrl":"","displayRedPacket":1,"distance":20,"exchangeVipScore":"0","fileValidTime":-1,"firstShareScoreRate":"0","helpUrl":"","hideSearchByFriends":1,"hmPayStatus":2,"hmWithdrawStatus":2,"invisibleList":[],"iosVersion":0,"ipAddress":"************","isAudioStatus":0,"isCommonCreateGroup":0,"isCommonFindFriends":0,"isDelAfterReading":1,"isDiscoverStatus":1,"isEnableCusServer":1,"isNodesStatus":0,"isOpenCluster":0,"isOpenDHRecharge":0,"isOpenGoogleFCM":0,"isOpenOSStatus":0,"isOpenPositionService":1,"isOpenReadReceipt":1,"isOpenReceipt":1,"isOpenRegister":1,"isOpenSMSCode":1,"isOpenTelnum":1,"isOpenTwoBarCode":1,"isQestionOpen":1,"isTabBarStatus":1,"isUserSignRedPacket":1,"isWeiBaoStatus":0,"isWithdrawToAdmin":0,"jiGuangStatus":2,"macVersion":0,"maxSendRedPagesAmount":500,"minTransferAmount":0,"minWithdrawToAdmin":0,"nicknameSearchUser":0,"pcVersion":0,"popularAPP":"{\"lifeCircle\":1,\"videoMeeting\":1,\"liveVideo\":1,\"shortVideo\":1,\"peopleNearby\":1,\"scan\":1}","qqLoginStatus":2,"regeditPhoneOrName":1,"registerInviteCode":0,"secondShareScoreRate":"0","shareUrl":"","showContactsUser":0,"softUrl":"","tabBarConfigList":{"tabBarNum":0,"tabBarStatus":0},"thirdShareScoreRate":"0","tlPayStatus":2,"tlWithdrawStatus":2,"transferRate":0,"uploadMaxSize":20,"videoLen":"20","wechatH5LoginStatus":2,"wechatLoginStatus":2,"wechatPayStatus":2,"wechatWithdrawStatus":2,"weiBaoMaxRedPacketAmount":0,"weiBaoMaxTransferAmount":0,"weiBaoMinTransferAmount":0,"weiBaoTransferRate":0,"weiPayStatus":2,"weiWithdrawStatus":2,"xMPPDomain":"im.server.com","xMPPHost":"im.server.com","xMPPTimeout":180,"xmppPingTime":72,"yunPayStatus":2,"yunWithdrawStatus":2},"resultCode":1}
2025-07-03 00:29:47.894 - com.tig.im.SysApiLogAspect 接口【getConfig】总耗时(毫秒)：439
2025-07-03 00:29:47.894 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:29:57.839 - com.tig.im.filter.AuthorizationFilter POST 请求：/user/login
2025-07-03 00:29:57.854 - com.tig.im.SysApiLogAspect 请求参数： /user/login?access_token=&areaCode=86&password=e113ccd32d3b88da2a373fc47b7e28ef&appBrand=web&serial=eb3a6450f37dddff8567196936009eca&loginIp=&telephone=653dbb14838ec396395e6e3c07e50837&time=1751473798&secret=a3db532bc34a38ada4f5c6904e69bffb&
2025-07-03 00:29:57.855 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:29:57.855 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法
2025-07-03 00:29:57.907 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法，应答参数：{"currentTime":1751473797907,"resultCode":0,"resultMsg":"帐号不存在, 请注册!"}
2025-07-03 00:29:57.907 - com.tig.im.SysApiLogAspect 接口【login】总耗时(毫秒)：51
2025-07-03 00:29:57.907 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:00.017 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:30:00
2025-07-03 00:30:00.022 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:30:00
2025-07-03 00:30:00.027 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:30:00.524 - com.tig.im.filter.AuthorizationFilter POST 请求：/user/login
2025-07-03 00:30:00.540 - com.tig.im.SysApiLogAspect 请求参数： /user/login?access_token=&areaCode=86&password=e113ccd32d3b88da2a373fc47b7e28ef&appBrand=web&serial=eb3a6450f37dddff8567196936009eca&loginIp=&telephone=653dbb14838ec396395e6e3c07e50837&time=1751473800&secret=7182af8d5907d88d99697857aadb7e40&
2025-07-03 00:30:00.549 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:00.549 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法
2025-07-03 00:30:00.583 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法，应答参数：{"currentTime":1751473800583,"resultCode":0,"resultMsg":"帐号不存在, 请注册!"}
2025-07-03 00:30:00.584 - com.tig.im.SysApiLogAspect 接口【login】总耗时(毫秒)：34
2025-07-03 00:30:00.585 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:16.407 - com.tig.im.filter.AuthorizationFilter POST 请求：/user/login
2025-07-03 00:30:16.415 - com.tig.im.SysApiLogAspect 请求参数： /user/login?access_token=&areaCode=86&password=e113ccd32d3b88da2a373fc47b7e28ef&appBrand=web&serial=eb3a6450f37dddff8567196936009eca&loginIp=&telephone=057b91fdd1d9aa0aa483c01632b7317c&time=**********&secret=942abe29b6f27a885cced15bd6769e49&
2025-07-03 00:30:16.416 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:16.416 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法
2025-07-03 00:30:16.548 - cn.tig.im.service.impl.UserManagerImpl login user userid: ******** , userKey: 057b91fdd1d9aa0aa483c01632b7317c, access_token: 0fe39c38b9c64ef0b31d56c42b9b9741
2025-07-03 00:30:16.678 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法，应答参数：{"currentTime":**********678,"data":{"birthday":**********,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"friendCount":1,"salt":"yfjtkwrfzjvcnqq1","offlineNoPushMsg":0,"sex":0,"hasRealNameAuth":0,"multipleDevices":1,"serInviteCode":"4Uvs","login":{"isFirstLogin":1,"latitude":0,"loginTime":**********,"longitude":0,"offlineTime":0},"userId":********,"myInviteCode":"","access_token":"0fe39c38b9c64ef0b31d56c42b9b9741","nickname":"haru88","isupdate":1,"expires_in":3024000,"payPassword":0},"resultCode":1}
2025-07-03 00:30:16.679 - com.tig.im.SysApiLogAspect 接口【login】总耗时(毫秒)：262
2025-07-03 00:30:16.680 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:16.716 - com.tig.im.filter.AuthorizationFilter POST 请求：/user/get
2025-07-03 00:30:16.746 - com.tig.im.SysApiLogAspect 请求参数： /user/get?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&time=**********&secret=561c1476204e2a643f379b57aef6770b&userId=********&
2025-07-03 00:30:16.749 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:16.749 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【getUser】方法
2025-07-03 00:30:16.872 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【getUser】方法，应答参数：{"currentTime":*************,"data":{"account":"1000","active":0,"anonymous":0,"areaCode":"86","attCount":0,"birthday":**********,"carrier":"电信","checkRealNameAuthStatus":0,"countryId":0,"createTime":**********,"description":"","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":0,"lng":0},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"modifyTime":**********,"msgNum":0,"myInviteCode":"","name":"","nickname":"haru88","num":0,"offlineNoPushMsg":0,"onlinestate":0,"payPassword":"0","phone":"***********","phoneToLocation":"浙江-宁波市","redPacketVip":0,"registerType":0,"role":[],"salt":"yfjtkwrfzjvcnqq1","score":"0.00","serInviteCode":"4Uvs","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":0,"showLastLoginTime":**********,"status":1,"telephone":"86***********","totalConsume":0,"totalRecharge":0,"userAccountKey":"a9b7ba70783b617e9998dc4dd82eb3c5","userId":********,"userKey":"057b91fdd1d9aa0aa483c01632b7317c","userType":0,"vip":0},"resultCode":1}
2025-07-03 00:30:16.873 - com.tig.im.SysApiLogAspect 接口【getUser】总耗时(毫秒)：122
2025-07-03 00:30:16.873 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:16.895 - com.tig.im.filter.AuthorizationFilter POST 请求：/friends/page
2025-07-03 00:30:16.897 - com.tig.im.filter.AuthorizationFilter POST 请求：/room/list/his
2025-07-03 00:30:16.920 - com.tig.im.SysApiLogAspect 请求参数： /friends/page?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageIndex=0&pageSize=10000&time=**********&secret=561c1476204e2a643f379b57aef6770b&userId=********&status=2&
2025-07-03 00:30:16.920 - com.tig.im.SysApiLogAspect 请求参数： /room/list/his?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageSize=10000&time=**********&secret=561c1476204e2a643f379b57aef6770b&pageIndex=0&
2025-07-03 00:30:16.921 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:16.921 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【getFriendsPage】方法
2025-07-03 00:30:16.921 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:16.923 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.RoomController】类的【historyList】方法
2025-07-03 00:30:16.960 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.RoomController】类的【historyList】方法，应答参数：{"currentTime":**********960,"resultCode":1}
2025-07-03 00:30:16.960 - com.tig.im.SysApiLogAspect 接口【historyList】总耗时(毫秒)：36
2025-07-03 00:30:16.961 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:16.991 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【getFriendsPage】方法，应答参数：{"currentTime":**********984,"data":{"pageCount":1,"pageData":[{"blacklist":0,"chatRecordTimeOut":-1,"createTime":**********,"fromAddType":0,"isBeenBlack":0,"lastTalkTime":0,"modifyTime":0,"msgNum":0,"offlineNoPushMsg":0,"status":2,"toFriendsRole":[2],"toNickname":"客服公众号","toUserId":10000,"toUserType":2,"userId":********,"vip":0}],"pageIndex":0,"pageSize":10000,"start":0,"total":1},"resultCode":1}
2025-07-03 00:30:16.992 - com.tig.im.SysApiLogAspect 接口【getFriendsPage】总耗时(毫秒)：63
2025-07-03 00:30:16.992 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:17.018 - com.tig.im.filter.AuthorizationFilter POST 请求：/getCurrentTime
2025-07-03 00:30:17.034 - com.tig.im.SysApiLogAspect 请求参数： /getCurrentTime?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&time=**********&secret=561c1476204e2a643f379b57aef6770b&
2025-07-03 00:30:17.034 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:17.034 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getCurrentTime】方法
2025-07-03 00:30:17.035 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getCurrentTime】方法，应答参数：{"currentTime":**********035,"data":**********035,"resultCode":1}
2025-07-03 00:30:17.035 - com.tig.im.SysApiLogAspect 接口【getCurrentTime】总耗时(毫秒)：0
2025-07-03 00:30:17.036 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:17.057 - com.tig.im.filter.AuthorizationFilter POST 请求：/friends/newFriendListWeb
2025-07-03 00:30:17.057 - com.tig.im.filter.AuthorizationFilter POST 请求：/tigase/getLastChatList
2025-07-03 00:30:17.073 - com.tig.im.SysApiLogAspect 请求参数： /tigase/getLastChatList?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageSize=200&startTime=0&endTime=0&time=**********&secret=561c1476204e2a643f379b57aef6770b&
2025-07-03 00:30:17.074 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:17.074 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.TigaseController】类的【getLastChatList】方法
2025-07-03 00:30:17.074 - com.tig.im.SysApiLogAspect 请求参数： /friends/newFriendListWeb?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageSize=10000&time=**********&secret=561c1476204e2a643f379b57aef6770b&userId=********&pageIndex=0&
2025-07-03 00:30:17.076 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:17.076 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【newFriendListWeb】方法
2025-07-03 00:30:17.113 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【newFriendListWeb】方法，应答参数：{"currentTime":**********113,"data":{"pageCount":0,"pageData":[],"pageIndex":0,"pageSize":10000,"start":0,"total":0},"resultCode":1}
2025-07-03 00:30:17.113 - com.tig.im.SysApiLogAspect 接口【newFriendListWeb】总耗时(毫秒)：37
2025-07-03 00:30:17.113 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:17.134 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.TigaseController】类的【getLastChatList】方法，应答参数：{"currentTime":**********133,"data":[],"resultCode":1}
2025-07-03 00:30:17.134 - com.tig.im.SysApiLogAspect 接口【getLastChatList】总耗时(毫秒)：59
2025-07-03 00:30:17.135 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:36.242 - com.tig.im.filter.AuthorizationFilter GET 请求：/clipboard.min.js
2025-07-03 00:30:36.588 - com.tig.im.filter.AuthorizationFilter POST 请求：/config
2025-07-03 00:30:36.603 - com.tig.im.SysApiLogAspect 请求参数： /config
2025-07-03 00:30:36.603 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:36.603 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法
2025-07-03 00:30:36.604 - controller ==Client-IP===>  ************  ===Address==>  CN 
2025-07-03 00:30:37.103 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法，应答参数：{"currentTime":1751473837102,"data":{"XMPPDomain":"im.server.com","XMPPHost":"im.server.com","XMPPTimeout":180,"address":"CN","aliLoginStatus":2,"aliPayStatus":2,"aliWithdrawStatus":2,"androidVersion":0,"audioLen":"20","chatRecordTimeOut":-1,"cusServerUrl":"","displayRedPacket":1,"distance":20,"exchangeVipScore":"0","fileValidTime":-1,"firstShareScoreRate":"0","helpUrl":"","hideSearchByFriends":1,"hmPayStatus":2,"hmWithdrawStatus":2,"invisibleList":[],"iosVersion":0,"ipAddress":"************","isAudioStatus":0,"isCommonCreateGroup":0,"isCommonFindFriends":0,"isDelAfterReading":1,"isDiscoverStatus":1,"isEnableCusServer":1,"isNodesStatus":0,"isOpenCluster":0,"isOpenDHRecharge":0,"isOpenGoogleFCM":0,"isOpenOSStatus":0,"isOpenPositionService":1,"isOpenReadReceipt":1,"isOpenReceipt":1,"isOpenRegister":1,"isOpenSMSCode":1,"isOpenTelnum":1,"isOpenTwoBarCode":1,"isQestionOpen":1,"isTabBarStatus":1,"isUserSignRedPacket":1,"isWeiBaoStatus":0,"isWithdrawToAdmin":0,"jiGuangStatus":2,"macVersion":0,"maxSendRedPagesAmount":500,"minTransferAmount":0,"minWithdrawToAdmin":0,"nicknameSearchUser":0,"pcVersion":0,"popularAPP":"{\"lifeCircle\":1,\"videoMeeting\":1,\"liveVideo\":1,\"shortVideo\":1,\"peopleNearby\":1,\"scan\":1}","qqLoginStatus":2,"regeditPhoneOrName":1,"registerInviteCode":0,"secondShareScoreRate":"0","shareUrl":"","showContactsUser":0,"softUrl":"","tabBarConfigList":{"tabBarNum":0,"tabBarStatus":0},"thirdShareScoreRate":"0","tlPayStatus":2,"tlWithdrawStatus":2,"transferRate":0,"uploadMaxSize":20,"videoLen":"20","wechatH5LoginStatus":2,"wechatLoginStatus":2,"wechatPayStatus":2,"wechatWithdrawStatus":2,"weiBaoMaxRedPacketAmount":0,"weiBaoMaxTransferAmount":0,"weiBaoMinTransferAmount":0,"weiBaoTransferRate":0,"weiPayStatus":2,"weiWithdrawStatus":2,"xMPPDomain":"im.server.com","xMPPHost":"im.server.com","xMPPTimeout":180,"xmppPingTime":72,"yunPayStatus":2,"yunWithdrawStatus":2},"resultCode":1}
2025-07-03 00:30:37.104 - com.tig.im.SysApiLogAspect 接口【getConfig】总耗时(毫秒)：498
2025-07-03 00:30:37.104 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:43.425 - com.tig.im.filter.AuthorizationFilter POST 请求：/user/login
2025-07-03 00:30:43.439 - com.tig.im.SysApiLogAspect 请求参数： /user/login?access_token=&areaCode=86&password=e113ccd32d3b88da2a373fc47b7e28ef&appBrand=web&serial=eb3a6450f37dddff8567196936009eca&loginIp=&telephone=057b91fdd1d9aa0aa483c01632b7317c&time=**********&secret=3f65845fb02b60e71ee95c39a297a52e&
2025-07-03 00:30:43.440 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:43.440 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法
2025-07-03 00:30:43.667 - cn.tig.im.service.impl.UserManagerImpl login user userid: ******** , userKey: 057b91fdd1d9aa0aa483c01632b7317c, access_token: 0fe39c38b9c64ef0b31d56c42b9b9741
2025-07-03 00:30:43.831 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法，应答参数：{"currentTime":**********831,"data":{"birthday":**********,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"friendCount":1,"salt":"yfjtkwrfzjvcnqq1","offlineNoPushMsg":0,"sex":0,"hasRealNameAuth":0,"multipleDevices":1,"serInviteCode":"4Uvs","login":{"appBrand":"web","isFirstLogin":0,"latitude":0,"loginTime":**********,"longitude":0,"offlineTime":0,"serial":"eb3a6450f37dddff8567196936009eca"},"userId":********,"myInviteCode":"","access_token":"0fe39c38b9c64ef0b31d56c42b9b9741","nickname":"haru88","isupdate":0,"expires_in":3024000,"payPassword":0},"resultCode":1}
2025-07-03 00:30:43.831 - com.tig.im.SysApiLogAspect 接口【login】总耗时(毫秒)：390
2025-07-03 00:30:43.832 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:43.863 - com.tig.im.filter.AuthorizationFilter POST 请求：/user/get
2025-07-03 00:30:43.880 - com.tig.im.SysApiLogAspect 请求参数： /user/get?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&time=**********&secret=d6206ef6bcaea7ac090e8591f493bd78&userId=********&
2025-07-03 00:30:43.881 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:43.881 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【getUser】方法
2025-07-03 00:30:44.017 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【getUser】方法，应答参数：{"currentTime":**********017,"data":{"account":"1000","active":0,"anonymous":0,"areaCode":"86","attCount":0,"birthday":**********,"carrier":"电信","checkRealNameAuthStatus":0,"countryId":0,"createTime":**********,"description":"","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":0,"lng":0},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"loginIp":"************","modifyTime":**********,"msgNum":0,"myInviteCode":"","name":"","nickname":"haru88","num":0,"offlineNoPushMsg":0,"onlinestate":0,"payPassword":"0","phone":"***********","phoneToLocation":"浙江-宁波市","redPacketVip":0,"registerType":0,"role":[],"salt":"yfjtkwrfzjvcnqq1","score":"0.00","serInviteCode":"4Uvs","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":0,"showLastLoginTime":**********,"status":1,"telephone":"86***********","totalConsume":0,"totalRecharge":0,"userAccountKey":"a9b7ba70783b617e9998dc4dd82eb3c5","userId":********,"userKey":"057b91fdd1d9aa0aa483c01632b7317c","userType":0,"vip":0},"resultCode":1}
2025-07-03 00:30:44.017 - com.tig.im.SysApiLogAspect 接口【getUser】总耗时(毫秒)：136
2025-07-03 00:30:44.017 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:44.066 - com.tig.im.filter.AuthorizationFilter POST 请求：/friends/page
2025-07-03 00:30:44.067 - com.tig.im.filter.AuthorizationFilter POST 请求：/room/list/his
2025-07-03 00:30:44.079 - com.tig.im.SysApiLogAspect 请求参数： /room/list/his?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageSize=10000&time=**********&secret=d6206ef6bcaea7ac090e8591f493bd78&pageIndex=0&
2025-07-03 00:30:44.082 - com.tig.im.SysApiLogAspect 请求参数： /friends/page?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageIndex=0&pageSize=10000&time=**********&secret=d6206ef6bcaea7ac090e8591f493bd78&userId=********&status=2&
2025-07-03 00:30:44.082 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:44.083 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.RoomController】类的【historyList】方法
2025-07-03 00:30:44.084 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:44.084 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【getFriendsPage】方法
2025-07-03 00:30:44.114 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.RoomController】类的【historyList】方法，应答参数：{"currentTime":**********114,"resultCode":1}
2025-07-03 00:30:44.117 - com.tig.im.SysApiLogAspect 接口【historyList】总耗时(毫秒)：31
2025-07-03 00:30:44.117 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:44.144 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【getFriendsPage】方法，应答参数：{"currentTime":**********144,"data":{"pageCount":1,"pageData":[{"blacklist":0,"chatRecordTimeOut":-1,"createTime":**********,"fromAddType":0,"isBeenBlack":0,"lastTalkTime":0,"modifyTime":0,"msgNum":0,"offlineNoPushMsg":0,"status":2,"toFriendsRole":[2],"toNickname":"客服公众号","toUserId":10000,"toUserType":2,"userId":********,"vip":0}],"pageIndex":0,"pageSize":10000,"start":0,"total":1},"resultCode":1}
2025-07-03 00:30:44.145 - com.tig.im.SysApiLogAspect 接口【getFriendsPage】总耗时(毫秒)：60
2025-07-03 00:30:44.145 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:44.162 - com.tig.im.filter.AuthorizationFilter POST 请求：/getCurrentTime
2025-07-03 00:30:44.178 - com.tig.im.SysApiLogAspect 请求参数： /getCurrentTime?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&time=**********&secret=d6206ef6bcaea7ac090e8591f493bd78&
2025-07-03 00:30:44.180 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:44.180 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getCurrentTime】方法
2025-07-03 00:30:44.180 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getCurrentTime】方法，应答参数：{"currentTime":**********180,"data":**********180,"resultCode":1}
2025-07-03 00:30:44.180 - com.tig.im.SysApiLogAspect 接口【getCurrentTime】总耗时(毫秒)：0
2025-07-03 00:30:44.181 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:44.195 - com.tig.im.filter.AuthorizationFilter POST 请求：/friends/newFriendListWeb
2025-07-03 00:30:44.196 - com.tig.im.filter.AuthorizationFilter POST 请求：/tigase/getLastChatList
2025-07-03 00:30:44.213 - com.tig.im.SysApiLogAspect 请求参数： /tigase/getLastChatList?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageSize=200&startTime=0&endTime=0&time=**********&secret=d6206ef6bcaea7ac090e8591f493bd78&
2025-07-03 00:30:44.214 - com.tig.im.SysApiLogAspect 请求参数： /friends/newFriendListWeb?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageSize=10000&time=**********&secret=d6206ef6bcaea7ac090e8591f493bd78&userId=********&pageIndex=0&
2025-07-03 00:30:44.215 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:44.215 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:30:44.215 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.TigaseController】类的【getLastChatList】方法
2025-07-03 00:30:44.215 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【newFriendListWeb】方法
2025-07-03 00:30:44.249 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【newFriendListWeb】方法，应答参数：{"currentTime":**********249,"data":{"pageCount":0,"pageData":[],"pageIndex":0,"pageSize":10000,"start":0,"total":0},"resultCode":1}
2025-07-03 00:30:44.252 - com.tig.im.SysApiLogAspect 接口【newFriendListWeb】总耗时(毫秒)：33
2025-07-03 00:30:44.252 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:30:44.264 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.TigaseController】类的【getLastChatList】方法，应答参数：{"currentTime":**********264,"data":[],"resultCode":1}
2025-07-03 00:30:44.270 - com.tig.im.SysApiLogAspect 接口【getLastChatList】总耗时(毫秒)：49
2025-07-03 00:30:44.271 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:33:29.526 - com.tig.im.filter.AuthorizationFilter GET 请求：/clipboard.min.js
2025-07-03 00:33:30.635 - com.tig.im.filter.AuthorizationFilter POST 请求：/config
2025-07-03 00:33:30.648 - com.tig.im.SysApiLogAspect 请求参数： /config
2025-07-03 00:33:30.648 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:33:30.649 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法
2025-07-03 00:33:30.649 - controller ==Client-IP===>  ************  ===Address==>  CN 
2025-07-03 00:33:31.109 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法，应答参数：{"currentTime":1751474011109,"data":{"XMPPDomain":"im.server.com","XMPPHost":"im.server.com","XMPPTimeout":180,"address":"CN","aliLoginStatus":2,"aliPayStatus":2,"aliWithdrawStatus":2,"androidVersion":0,"audioLen":"20","chatRecordTimeOut":-1,"cusServerUrl":"","displayRedPacket":1,"distance":20,"exchangeVipScore":"0","fileValidTime":-1,"firstShareScoreRate":"0","helpUrl":"","hideSearchByFriends":1,"hmPayStatus":2,"hmWithdrawStatus":2,"invisibleList":[],"iosVersion":0,"ipAddress":"************","isAudioStatus":0,"isCommonCreateGroup":0,"isCommonFindFriends":0,"isDelAfterReading":1,"isDiscoverStatus":1,"isEnableCusServer":1,"isNodesStatus":0,"isOpenCluster":0,"isOpenDHRecharge":0,"isOpenGoogleFCM":0,"isOpenOSStatus":0,"isOpenPositionService":1,"isOpenReadReceipt":1,"isOpenReceipt":1,"isOpenRegister":1,"isOpenSMSCode":1,"isOpenTelnum":1,"isOpenTwoBarCode":1,"isQestionOpen":1,"isTabBarStatus":1,"isUserSignRedPacket":1,"isWeiBaoStatus":0,"isWithdrawToAdmin":0,"jiGuangStatus":2,"macVersion":0,"maxSendRedPagesAmount":500,"minTransferAmount":0,"minWithdrawToAdmin":0,"nicknameSearchUser":0,"pcVersion":0,"popularAPP":"{\"lifeCircle\":1,\"videoMeeting\":1,\"liveVideo\":1,\"shortVideo\":1,\"peopleNearby\":1,\"scan\":1}","qqLoginStatus":2,"regeditPhoneOrName":1,"registerInviteCode":0,"secondShareScoreRate":"0","shareUrl":"","showContactsUser":0,"softUrl":"","tabBarConfigList":{"tabBarNum":0,"tabBarStatus":0},"thirdShareScoreRate":"0","tlPayStatus":2,"tlWithdrawStatus":2,"transferRate":0,"uploadMaxSize":20,"videoLen":"20","wechatH5LoginStatus":2,"wechatLoginStatus":2,"wechatPayStatus":2,"wechatWithdrawStatus":2,"weiBaoMaxRedPacketAmount":0,"weiBaoMaxTransferAmount":0,"weiBaoMinTransferAmount":0,"weiBaoTransferRate":0,"weiPayStatus":2,"weiWithdrawStatus":2,"xMPPDomain":"im.server.com","xMPPHost":"im.server.com","xMPPTimeout":180,"xmppPingTime":72,"yunPayStatus":2,"yunWithdrawStatus":2},"resultCode":1}
2025-07-03 00:33:31.110 - com.tig.im.SysApiLogAspect 接口【getConfig】总耗时(毫秒)：460
2025-07-03 00:33:31.110 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:33:41.262 - com.tig.im.filter.AuthorizationFilter GET 请求：/clipboard.min.js
2025-07-03 00:33:42.237 - com.tig.im.filter.AuthorizationFilter POST 请求：/config
2025-07-03 00:33:42.255 - com.tig.im.SysApiLogAspect 请求参数： /config
2025-07-03 00:33:42.255 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:33:42.255 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法
2025-07-03 00:33:42.255 - controller ==Client-IP===>  ************  ===Address==>  CN 
2025-07-03 00:33:42.752 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getConfig】方法，应答参数：{"currentTime":1751474022752,"data":{"XMPPDomain":"im.server.com","XMPPHost":"im.server.com","XMPPTimeout":180,"address":"CN","aliLoginStatus":2,"aliPayStatus":2,"aliWithdrawStatus":2,"androidVersion":0,"audioLen":"20","chatRecordTimeOut":-1,"cusServerUrl":"","displayRedPacket":1,"distance":20,"exchangeVipScore":"0","fileValidTime":-1,"firstShareScoreRate":"0","helpUrl":"","hideSearchByFriends":1,"hmPayStatus":2,"hmWithdrawStatus":2,"invisibleList":[],"iosVersion":0,"ipAddress":"************","isAudioStatus":0,"isCommonCreateGroup":0,"isCommonFindFriends":0,"isDelAfterReading":1,"isDiscoverStatus":1,"isEnableCusServer":1,"isNodesStatus":0,"isOpenCluster":0,"isOpenDHRecharge":0,"isOpenGoogleFCM":0,"isOpenOSStatus":0,"isOpenPositionService":1,"isOpenReadReceipt":1,"isOpenReceipt":1,"isOpenRegister":1,"isOpenSMSCode":1,"isOpenTelnum":1,"isOpenTwoBarCode":1,"isQestionOpen":1,"isTabBarStatus":1,"isUserSignRedPacket":1,"isWeiBaoStatus":0,"isWithdrawToAdmin":0,"jiGuangStatus":2,"macVersion":0,"maxSendRedPagesAmount":500,"minTransferAmount":0,"minWithdrawToAdmin":0,"nicknameSearchUser":0,"pcVersion":0,"popularAPP":"{\"lifeCircle\":1,\"videoMeeting\":1,\"liveVideo\":1,\"shortVideo\":1,\"peopleNearby\":1,\"scan\":1}","qqLoginStatus":2,"regeditPhoneOrName":1,"registerInviteCode":0,"secondShareScoreRate":"0","shareUrl":"","showContactsUser":0,"softUrl":"","tabBarConfigList":{"tabBarNum":0,"tabBarStatus":0},"thirdShareScoreRate":"0","tlPayStatus":2,"tlWithdrawStatus":2,"transferRate":0,"uploadMaxSize":20,"videoLen":"20","wechatH5LoginStatus":2,"wechatLoginStatus":2,"wechatPayStatus":2,"wechatWithdrawStatus":2,"weiBaoMaxRedPacketAmount":0,"weiBaoMaxTransferAmount":0,"weiBaoMinTransferAmount":0,"weiBaoTransferRate":0,"weiPayStatus":2,"weiWithdrawStatus":2,"xMPPDomain":"im.server.com","xMPPHost":"im.server.com","xMPPTimeout":180,"xmppPingTime":72,"yunPayStatus":2,"yunWithdrawStatus":2},"resultCode":1}
2025-07-03 00:33:42.753 - com.tig.im.SysApiLogAspect 接口【getConfig】总耗时(毫秒)：497
2025-07-03 00:33:42.754 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:33:47.049 - com.tig.im.filter.AuthorizationFilter POST 请求：/user/login
2025-07-03 00:33:47.063 - com.tig.im.SysApiLogAspect 请求参数： /user/login?access_token=&areaCode=86&password=e113ccd32d3b88da2a373fc47b7e28ef&appBrand=web&serial=eb3a6450f37dddff8567196936009eca&loginIp=&telephone=057b91fdd1d9aa0aa483c01632b7317c&time=**********&secret=024fd7cc55de72a95000981e40b2a088&
2025-07-03 00:33:47.064 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:33:47.064 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法
2025-07-03 00:33:47.267 - cn.tig.im.service.impl.UserManagerImpl login user userid: ******** , userKey: 057b91fdd1d9aa0aa483c01632b7317c, access_token: 0fe39c38b9c64ef0b31d56c42b9b9741
2025-07-03 00:33:47.462 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【login】方法，应答参数：{"currentTime":**********461,"data":{"birthday":**********,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"friendCount":1,"salt":"yfjtkwrfzjvcnqq1","offlineNoPushMsg":0,"sex":0,"hasRealNameAuth":0,"multipleDevices":1,"serInviteCode":"4Uvs","login":{"appBrand":"web","isFirstLogin":0,"latitude":0,"loginTime":**********,"longitude":0,"offlineTime":0,"serial":"eb3a6450f37dddff8567196936009eca"},"userId":********,"myInviteCode":"","access_token":"0fe39c38b9c64ef0b31d56c42b9b9741","nickname":"haru88","isupdate":0,"expires_in":3024000,"payPassword":0},"resultCode":1}
2025-07-03 00:33:47.464 - com.tig.im.SysApiLogAspect 接口【login】总耗时(毫秒)：396
2025-07-03 00:33:47.464 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:33:47.506 - com.tig.im.filter.AuthorizationFilter POST 请求：/user/get
2025-07-03 00:33:47.524 - com.tig.im.SysApiLogAspect 请求参数： /user/get?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&time=**********&secret=036789e6c895f4200c96dfc56655f94f&userId=********&
2025-07-03 00:33:47.525 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:33:47.525 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【getUser】方法
2025-07-03 00:33:47.611 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.UserController】类的【getUser】方法，应答参数：{"currentTime":*************,"data":{"account":"1000","active":0,"anonymous":0,"areaCode":"86","attCount":0,"birthday":**********,"carrier":"电信","checkRealNameAuthStatus":0,"countryId":0,"createTime":**********,"description":"","fansCount":0,"friendsCount":0,"hasRealNameAuth":0,"idcard":"","idcardUrl":"","isAddFirend":1,"isAuth":0,"isCreateRoom":1,"isForbidDevice":0,"isForbidIp":0,"isPasuse":0,"lasterrTime":0,"level":0,"loc":{"lat":0,"lng":0},"lockStatus":2,"lockTime":0,"loginFailCount":0,"loginFailTotal":0,"loginIp":"************","modifyTime":**********,"msgNum":0,"myInviteCode":"","name":"","nickname":"haru88","num":0,"offlineNoPushMsg":0,"onlinestate":0,"payPassword":"0","phone":"***********","phoneToLocation":"浙江-宁波市","redPacketVip":0,"registerType":0,"role":[],"salt":"yfjtkwrfzjvcnqq1","score":"0.00","serInviteCode":"4Uvs","setAccountCount":0,"settings":{"allowAtt":1,"allowGreet":1,"chatRecordTimeOut":"-1.0","chatSyncTimeLen":-1,"closeTelephoneFind":1,"friendFromList":"1,2,3,4,5","friendsVerify":1,"isEncrypt":0,"isKeepalive":1,"isTyping":0,"isUseGoogleMap":0,"isVibration":0,"multipleDevices":1,"nameSearch":1,"openService":0,"phoneSearch":1,"showLastLoginTime":-1,"showTelephone":-1},"sex":0,"showLastLoginTime":**********,"status":1,"telephone":"86***********","totalConsume":0,"totalRecharge":0,"userAccountKey":"a9b7ba70783b617e9998dc4dd82eb3c5","userId":********,"userKey":"057b91fdd1d9aa0aa483c01632b7317c","userType":0,"vip":0},"resultCode":1}
2025-07-03 00:33:47.612 - com.tig.im.SysApiLogAspect 接口【getUser】总耗时(毫秒)：86
2025-07-03 00:33:47.612 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:33:47.639 - com.tig.im.filter.AuthorizationFilter POST 请求：/room/list/his
2025-07-03 00:33:47.639 - com.tig.im.filter.AuthorizationFilter POST 请求：/friends/page
2025-07-03 00:33:47.653 - com.tig.im.SysApiLogAspect 请求参数： /friends/page?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageIndex=0&pageSize=10000&time=**********&secret=718d6c4f6c664da732c45e370e04100e&userId=********&status=2&
2025-07-03 00:33:47.653 - com.tig.im.SysApiLogAspect 请求参数： /room/list/his?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageSize=10000&time=**********&secret=718d6c4f6c664da732c45e370e04100e&pageIndex=0&
2025-07-03 00:33:47.654 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:33:47.654 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.RoomController】类的【historyList】方法
2025-07-03 00:33:47.653 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:33:47.656 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【getFriendsPage】方法
2025-07-03 00:33:47.669 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.RoomController】类的【historyList】方法，应答参数：{"currentTime":**********669,"resultCode":1}
2025-07-03 00:33:47.670 - com.tig.im.SysApiLogAspect 接口【historyList】总耗时(毫秒)：15
2025-07-03 00:33:47.670 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:33:47.681 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【getFriendsPage】方法，应答参数：{"currentTime":**********681,"data":{"pageCount":1,"pageData":[{"blacklist":0,"chatRecordTimeOut":-1,"createTime":**********,"fromAddType":0,"isBeenBlack":0,"lastTalkTime":0,"modifyTime":0,"msgNum":0,"offlineNoPushMsg":0,"status":2,"toFriendsRole":[2],"toNickname":"客服公众号","toUserId":10000,"toUserType":2,"userId":********,"vip":0}],"pageIndex":0,"pageSize":10000,"start":0,"total":1},"resultCode":1}
2025-07-03 00:33:47.701 - com.tig.im.SysApiLogAspect 接口【getFriendsPage】总耗时(毫秒)：25
2025-07-03 00:33:47.702 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:33:47.716 - com.tig.im.filter.AuthorizationFilter POST 请求：/getCurrentTime
2025-07-03 00:33:47.723 - com.tig.im.SysApiLogAspect 请求参数： /getCurrentTime?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&time=**********&secret=718d6c4f6c664da732c45e370e04100e&
2025-07-03 00:33:47.723 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:33:47.723 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getCurrentTime】方法
2025-07-03 00:33:47.724 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.BasicController】类的【getCurrentTime】方法，应答参数：{"currentTime":**********723,"data":**********723,"resultCode":1}
2025-07-03 00:33:47.724 - com.tig.im.SysApiLogAspect 接口【getCurrentTime】总耗时(毫秒)：0
2025-07-03 00:33:47.724 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:33:47.750 - com.tig.im.filter.AuthorizationFilter POST 请求：/friends/newFriendListWeb
2025-07-03 00:33:47.750 - com.tig.im.filter.AuthorizationFilter POST 请求：/tigase/getLastChatList
2025-07-03 00:33:47.761 - com.tig.im.SysApiLogAspect 请求参数： /tigase/getLastChatList?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageSize=200&startTime=0&endTime=0&time=**********&secret=718d6c4f6c664da732c45e370e04100e&
2025-07-03 00:33:47.761 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:33:47.761 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.TigaseController】类的【getLastChatList】方法
2025-07-03 00:33:47.766 - com.tig.im.SysApiLogAspect 请求参数： /friends/newFriendListWeb?access_token=0fe39c38b9c64ef0b31d56c42b9b9741&pageSize=10000&time=**********&secret=718d6c4f6c664da732c45e370e04100e&userId=********&pageIndex=0&
2025-07-03 00:33:47.767 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0 
2025-07-03 00:33:47.767 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【newFriendListWeb】方法
2025-07-03 00:33:47.780 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.FriendsController】类的【newFriendListWeb】方法，应答参数：{"currentTime":**********780,"data":{"pageCount":0,"pageData":[],"pageIndex":0,"pageSize":10000,"start":0,"total":0},"resultCode":1}
2025-07-03 00:33:47.780 - com.tig.im.SysApiLogAspect 接口【newFriendListWeb】总耗时(毫秒)：13
2025-07-03 00:33:47.781 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:33:47.801 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.TigaseController】类的【getLastChatList】方法，应答参数：{"currentTime":**********801,"data":[],"resultCode":1}
2025-07-03 00:33:47.810 - com.tig.im.SysApiLogAspect 接口【getLastChatList】总耗时(毫秒)：40
2025-07-03 00:33:47.810 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-03 00:35:00.007 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:35:00
2025-07-03 00:35:00.020 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:35:00.034 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:35:00
2025-07-03 00:40:00.016 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:40:00
2025-07-03 00:40:00.018 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:40:00
2025-07-03 00:40:00.024 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:45:00.012 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:45:00
2025-07-03 00:45:00.020 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:45:00.040 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:45:00
2025-07-03 00:50:00.011 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:50:00
2025-07-03 00:50:00.024 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:50:00.039 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:50:00
2025-07-03 00:55:00.007 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-03 12:55:00
2025-07-03 00:55:00.014 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-03 00:55:00.027 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-03 12:55:00
2025-07-03 00:55:34.115 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-03 00:55:34.130 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-03 00:55:34.131 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-03 00:55:34.132 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-03 00:55:34.132 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-03 00:56:16.536 - com.tig.im.Application Starting Application on WIN-90Q87RKIEF7 with PID 16200 (D:\code\liaotian3\IMjava\后端文件代码\imserver-parent\imserver-api\target\classes started by Shakher in D:\code\liaotian3\IMjava\后端文件代码\imserver-parent)
2025-07-03 00:56:16.538 - com.tig.im.Application No active profile set, falling back to default profiles: default
2025-07-03 00:56:18.507 - org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8092"]
2025-07-03 00:56:18.511 - org.apache.catalina.core.StandardService Starting service Tomcat
2025-07-03 00:56:18.511 - org.apache.catalina.core.StandardEngine Starting Servlet Engine: Apache Tomcat/8.5.13
2025-07-03 00:56:18.519 - org.apache.catalina.core.AprLifecycleListener The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: D:\develop\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\ShadowBot;C:\Program Files\jdk\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Ruby34-x64\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\libnvvp;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;D:\Program Files\nodejs\;D:\Program Files\nodejs\node_global\node_modules;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin;D:\apache-maven-3.9.10\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3.2\bin;D:\Program Files\nodejs\node_global;C:\Users\<USER>\AppData\Local\Pro;C:\Program Files\cursor\resources\app\bin;C:\Program Files\ShadowBot;C:\Users\<USER>\.pychange\current;C:\Users\<USER>\.pychange\current\Scripts;.
2025-07-03 00:56:18.692 - org.apache.jasper.servlet.TldScanner At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-03 00:56:18.700 - o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
2025-07-03 00:56:18.913 - cn.tig.commons.autoconfigure.CommAutoConfiguration appConfig  ---->>>{"apiKey":"123456","buildTime":"2019-06-12","distance":20,"isBeta":0,"isDebug":1,"languages":[{"key":"zh","name":"中文 ","value":"简体中文"},{"key":"en","name":"英文","value":"English"},{"key":"big5","name":"繁体","value":"繁体中文"}],"openRedPacketBackTask":1,"openTask":1,"qqzengPath":"","uploadDomain":"http://*************:8088","wxChatUrl":""}
2025-07-03 00:56:19.084 - cn.tig.commons.autoconfigure.CommAutoConfiguration redisConfig  ---->>>{"address":"redis://*************:6739","connectTimeout":10000,"connectionMinimumIdleSize":32,"connectionPoolSize":64,"database":0,"isCluster":0,"password":"2hXPJc7YtNErss28","pingConnectionInterval":500,"pingTimeout":10000,"port":0,"timeout":10000}
2025-07-03 00:56:19.117 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson Single start 
2025-07-03 00:56:19.589 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson create end 
2025-07-03 00:56:19.607 - cn.tig.commons.autoconfigure.CommAutoConfiguration mongoConfig  ---->>>{"cluster":0,"connectTimeout":20000,"dbName":"imapi","maxWaitTime":20000,"password":"qwer4321","roomDbName":"imRoom","socketTimeout":20000,"uri":"**********************************************","username":"mongodb"}
2025-07-03 00:56:19.615 - cn.tig.commons.autoconfigure.CommAutoConfiguration xmppConfig  ---->>>{"dbName":"tigase","dbPassword":"qwer4321","dbUri":"**********************************************","dbUsername":"mongodb","host":"***********","password":"998","passwordEncryptStatus":true,"port":5222,"serverConfig":[{"host":"*************","port":5222,"serverName":"*************","tigaseUserInfo":"configAdmin100001"}],"serverName":"***********","username":"998"}
2025-07-03 00:56:21.590 - cn.tig.commons.autoconfigure.CommAutoConfiguration mqConfig  ---->>>{"batchMaxSize":20,"isConsumerUserStatus":1,"nameAddr":"http://***********:9876","threadMax":64,"threadMin":32}
2025-07-03 00:56:21.928 - cn.tig.im.utils.ConstantUtil  ConstantUtil----init--- Start
2025-07-03 00:56:21.929 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- init
2025-07-03 00:56:21.943 - cn.tig.im.utils.TigBeanUtils TigBeanUtils ===> init end  ===> localSpringBeanManager > LocalSpringBeanManager
2025-07-03 00:56:21.957 - cn.tig.rocketmq.UserStatusConsumer  MQ config nameAddr ===> http://***********:9876
2025-07-03 00:56:21.989 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- End
2025-07-03 00:56:22.630 - cn.tig.commons.autoconfigure.CommAutoConfiguration inviteConfig  ---->>>{"passCardIsOpen":false}
2025-07-03 00:56:22.632 - cn.tig.commons.autoconfigure.CommAutoConfiguration apiAccessConfig  ---->>>{"accessIsSet":true}
2025-07-03 00:56:22.635 - cn.tig.commons.autoconfigure.CommAutoConfiguration obsConfig  ---->>>{"rSAPrivateKey":"","rSAPublicKey":""}
2025-07-03 00:56:22.637 - cn.tig.commons.autoconfigure.CommAutoConfiguration TlPayConfig  ---->>>{"signCerPath":"","signPkPath":""}
2025-07-03 00:56:22.638 - cn.tig.commons.autoconfigure.CommAutoConfiguration SysPayConfig  ---->>>{"tlPayIsOpen":false}
2025-07-03 00:56:22.640 - cn.tig.commons.autoconfigure.CommAutoConfiguration QuartzConfig  ---->>>{"filePath":"D:\\tig-im\\config\\quartz\\quartz.properties"}
2025-07-03 00:56:22.643 - cn.tig.commons.autoconfigure.CommAutoConfiguration RoomScanConfig  ---->>>{"androidSchema":"schema://realmName","iphoneSchema":"schema://name"}
2025-07-03 00:56:22.643 - cn.tig.commons.autoconfigure.CommAutoConfiguration DHConfig  ---->>>null
2025-07-03 00:56:23.445 - org.quartz.impl.StdSchedulerFactory Using default implementation for ThreadExecutor
2025-07-03 00:56:23.471 - org.quartz.core.SchedulerSignalerImpl Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 00:56:23.471 - org.quartz.core.QuartzScheduler Quartz Scheduler v.2.2.3 created.
2025-07-03 00:56:23.472 - org.quartz.simpl.RAMJobStore RAMJobStore initialized.
2025-07-03 00:56:23.472 - org.quartz.core.QuartzScheduler Scheduler meta-data: Quartz Scheduler (v2.2.3) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-03 00:56:23.472 - org.quartz.impl.StdSchedulerFactory Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-03 00:56:23.472 - org.quartz.impl.StdSchedulerFactory Quartz scheduler version: 2.2.3
2025-07-03 00:56:23.472 - org.quartz.core.QuartzScheduler JobFactory set to: org.springframework.boot.autoconfigure.quartz.AutowireCapableBeanJobFactory@7de28ab
2025-07-03 00:56:23.539 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-03 00:56:23.570 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-03 00:56:23.574 - org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-8092"]
2025-07-03 00:56:23.577 - org.apache.tomcat.util.net.NioSelectorPool Using a shared selector for servlet write/read
2025-07-03 00:56:23.641 - com.tig.im.Application Started Application in 7.459 seconds (JVM running for 18.167)
2025-07-03 00:56:23.814 - cn.tig.commons.utils.XMPPClientUtil 998已经注册了!请放心连接
2025-07-03 00:56:23.814 - cn.tig.im.utils.InitializationData 
>>>>>>>>>>>>>>> 默认API系统消息tigase账号数据初始化完成  <<<<<<<<<<<<<
2025-07-03 00:56:24.257 - com.tig.im.Application 启动成功  当前版本编译时间  =====>>>>>> 2025-07-03 00:56:24
2025-07-03 00:56:42.068 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-03 00:56:42.069 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-03 00:56:42.069 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-03 00:56:42.069 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-03 00:56:42.070 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
