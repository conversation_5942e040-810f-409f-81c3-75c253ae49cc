2025-07-02 22:58:07.810 - com.tig.im.Application Starting Application on WIN-90Q87RKIEF7 with PID 45700 (D:\code\liaotian3\IMjava\后端文件代码\imserver-parent\imserver-api\target\classes started by <PERSON>hakher in D:\code\liaotian3\IMjava\后端文件代码\imserver-parent)
2025-07-02 22:58:07.814 - com.tig.im.Application No active profile set, falling back to default profiles: default
2025-07-02 22:58:09.093 - com.tig.im.Application 启动报错=== Cannot load configuration class: com.tig.im.Application
2025-07-02 22:59:35.388 - com.tig.im.Application Starting Application on WIN-90Q87RKIEF7 with PID 2772 (D:\code\liaotian3\IMjava\后端文件代码\imserver-parent\imserver-api\target\classes started by <PERSON>hakher in D:\code\liaotian3\IMjava\后端文件代码\imserver-parent)
2025-07-02 22:59:35.394 - com.tig.im.Application No active profile set, falling back to default profiles: default
2025-07-02 22:59:37.470 - org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8092"]
2025-07-02 22:59:37.479 - org.apache.catalina.core.StandardService Starting service Tomcat
2025-07-02 22:59:37.479 - org.apache.catalina.core.StandardEngine Starting Servlet Engine: Apache Tomcat/8.5.13
2025-07-02 22:59:37.486 - org.apache.catalina.core.AprLifecycleListener The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: D:\develop\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\ShadowBot;C:\Program Files\jdk\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Ruby34-x64\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\libnvvp;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;D:\Program Files\nodejs\;D:\Program Files\nodejs\node_global\node_modules;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin;D:\apache-maven-3.9.10\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3.2\bin;D:\Program Files\nodejs\node_global;C:\Users\<USER>\AppData\Local\Pro;C:\Program Files\cursor\resources\app\bin;C:\Program Files\ShadowBot;C:\Users\<USER>\.pychange\current;C:\Users\<USER>\.pychange\current\Scripts;.
2025-07-02 22:59:37.648 - org.apache.jasper.servlet.TldScanner At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-02 22:59:37.658 - o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
2025-07-02 22:59:37.909 - cn.tig.commons.autoconfigure.CommAutoConfiguration appConfig  ---->>>{"apiKey":"123456","buildTime":"2019-06-12","distance":20,"isBeta":0,"isDebug":1,"languages":[{"key":"zh","name":"中文 ","value":"简体中文"},{"key":"en","name":"英文","value":"English"},{"key":"big5","name":"繁体","value":"繁体中文"}],"openRedPacketBackTask":1,"openTask":1,"qqzengPath":"","uploadDomain":"http://*************:8088","wxChatUrl":""}
2025-07-02 22:59:38.068 - cn.tig.commons.autoconfigure.CommAutoConfiguration redisConfig  ---->>>{"address":"redis://*************:6739","connectTimeout":10000,"connectionMinimumIdleSize":32,"connectionPoolSize":64,"database":0,"isCluster":0,"password":"2hXPJc7YtNErss28","pingConnectionInterval":500,"pingTimeout":10000,"port":0,"timeout":10000}
2025-07-02 22:59:38.102 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson Single start 
2025-07-02 22:59:38.706 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson create end 
2025-07-02 22:59:38.723 - cn.tig.commons.autoconfigure.CommAutoConfiguration mongoConfig  ---->>>{"cluster":0,"connectTimeout":20000,"dbName":"imapi","maxWaitTime":20000,"password":"qwer4321","roomDbName":"imRoom","socketTimeout":20000,"uri":"**********************************************","username":"mongodb"}
2025-07-02 22:59:38.729 - cn.tig.commons.autoconfigure.CommAutoConfiguration xmppConfig  ---->>>{"dbName":"tigase","dbPassword":"qwer4321","dbUri":"**********************************************","dbUsername":"mongodb","host":"***********","password":"998","passwordEncryptStatus":true,"port":5222,"serverConfig":[{"host":"*************","port":5222,"serverName":"*************","tigaseUserInfo":"configAdmin100001"}],"serverName":"***********","username":"998"}
2025-07-02 22:59:40.501 - cn.tig.commons.autoconfigure.CommAutoConfiguration mqConfig  ---->>>{"batchMaxSize":20,"isConsumerUserStatus":1,"nameAddr":"http://***********:9876","threadMax":64,"threadMin":32}
2025-07-02 22:59:40.852 - cn.tig.im.utils.ConstantUtil  ConstantUtil----init--- Start
2025-07-02 22:59:40.852 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- init
2025-07-02 22:59:40.863 - cn.tig.im.utils.TigBeanUtils TigBeanUtils ===> init end  ===> localSpringBeanManager > LocalSpringBeanManager
2025-07-02 22:59:40.872 - cn.tig.rocketmq.UserStatusConsumer  MQ config nameAddr ===> http://***********:9876
2025-07-02 22:59:40.898 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- End
2025-07-02 22:59:41.434 - cn.tig.commons.autoconfigure.CommAutoConfiguration inviteConfig  ---->>>{"passCardIsOpen":false}
2025-07-02 22:59:41.436 - cn.tig.commons.autoconfigure.CommAutoConfiguration apiAccessConfig  ---->>>{"accessIsSet":true}
2025-07-02 22:59:41.437 - cn.tig.commons.autoconfigure.CommAutoConfiguration obsConfig  ---->>>{"rSAPrivateKey":"","rSAPublicKey":""}
2025-07-02 22:59:41.439 - cn.tig.commons.autoconfigure.CommAutoConfiguration TlPayConfig  ---->>>{"signCerPath":"","signPkPath":""}
2025-07-02 22:59:41.441 - cn.tig.commons.autoconfigure.CommAutoConfiguration SysPayConfig  ---->>>{"tlPayIsOpen":false}
2025-07-02 22:59:41.443 - cn.tig.commons.autoconfigure.CommAutoConfiguration QuartzConfig  ---->>>{"filePath":"D:\\tig-im\\config\\quartz\\quartz.properties"}
2025-07-02 22:59:41.445 - cn.tig.commons.autoconfigure.CommAutoConfiguration RoomScanConfig  ---->>>{"androidSchema":"schema://realmName","iphoneSchema":"schema://name"}
2025-07-02 22:59:41.445 - cn.tig.commons.autoconfigure.CommAutoConfiguration DHConfig  ---->>>null
2025-07-02 22:59:42.201 - org.quartz.impl.StdSchedulerFactory Using default implementation for ThreadExecutor
2025-07-02 22:59:42.247 - org.quartz.core.SchedulerSignalerImpl Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-02 22:59:42.247 - org.quartz.core.QuartzScheduler Quartz Scheduler v.2.2.3 created.
2025-07-02 22:59:42.254 - org.quartz.simpl.RAMJobStore RAMJobStore initialized.
2025-07-02 22:59:42.256 - org.quartz.core.QuartzScheduler Scheduler meta-data: Quartz Scheduler (v2.2.3) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-02 22:59:42.256 - org.quartz.impl.StdSchedulerFactory Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-02 22:59:42.256 - org.quartz.impl.StdSchedulerFactory Quartz scheduler version: 2.2.3
2025-07-02 22:59:42.256 - org.quartz.core.QuartzScheduler JobFactory set to: org.springframework.boot.autoconfigure.quartz.AutowireCapableBeanJobFactory@70dd66e1
2025-07-02 22:59:42.322 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-02 22:59:42.339 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-02 22:59:42.343 - org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-8092"]
2025-07-02 22:59:42.346 - org.apache.tomcat.util.net.NioSelectorPool Using a shared selector for servlet write/read
2025-07-02 22:59:42.418 - com.tig.im.Application Started Application in 7.416 seconds (JVM running for 15.566)
2025-07-02 22:59:42.911 - cn.tig.im.utils.InitializationData 
>>>>>>>>>>>>>>> 默认管理员数据初始化完成  <<<<<<<<<<<<<
2025-07-02 22:59:42.995 - cn.tig.im.utils.InitializationData 
>>>>>>>>>>>>>>> 隐藏管理员账号数据初始化完成  <<<<<<<<<<<<<
2025-07-02 22:59:43.116 - cn.tig.im.utils.InitializationData 
>>>>>>>>>>>>>>> 默认系统通知数据初始化完成  <<<<<<<<<<<<<
2025-07-02 22:59:43.164 - cn.tig.commons.utils.XMPPClientUtil   注册到 Tigase  ***********,998,9ab0d88431732957a618d4a469a0d4c3
2025-07-02 22:59:43.164 - cn.tig.commons.utils.XMPPClientUtil   注册到 Tigase  ***********,998,9ab0d88431732957a618d4a469a0d4c3
2025-07-02 22:59:43.165 - cn.tig.im.utils.InitializationData 
>>>>>>>>>>>>>>> 默认API系统消息tigase账号数据初始化完成  <<<<<<<<<<<<<
2025-07-02 22:59:43.598 - cn.tig.im.utils.InitializationData 
>>>>>>>>>>>>>>> 异常信息数据初始化完成  <<<<<<<<<<<<<
2025-07-02 22:59:45.003 - com.tig.im.Application 启动成功  当前版本编译时间  =====>>>>>> 2025-07-02 22:59:45
2025-07-02 23:00:00.002 - cn.tig.im.scheduleds.CommTask =======红包退回和转账退回定时任务开启中=======
2025-07-02 23:00:00.018 - cn.tig.im.scheduleds.CommTask 红包超时未领取的数量 ======>>>>> 0
2025-07-02 23:00:00.019 - cn.tig.im.scheduleds.CommTask 刷新红包成功,耗时16毫秒,执行时间2025-07-02 11:00:00
2025-07-02 23:00:00.035 - cn.tig.im.scheduleds.CommTask 转账超时未领取的数量 ======>>>>> 0
2025-07-02 23:00:00.035 - cn.tig.im.scheduleds.CommTask 刷新转账成功,耗时33毫秒
2025-07-02 23:00:00.036 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-02 11:00:00
2025-07-02 23:00:00.052 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-02 23:00:00.053 - cn.tig.im.scheduleds.CommTask 当前时间:2025-07-02 11:00:00
2025-07-02 23:00:00.108 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-02 11:00:00
2025-07-02 23:00:06.150 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-02 23:00:06.153 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-02 23:00:06.153 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-02 23:00:06.153 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-02 23:00:06.154 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-02 23:16:21.987 - com.tig.im.Application Starting Application on WIN-90Q87RKIEF7 with PID 14328 (D:\code\liaotian3\IMjava\后端文件代码\imserver-parent\imserver-api\target\classes started by Shakher in D:\code\liaotian3\IMjava\后端文件代码\imserver-parent)
2025-07-02 23:16:21.993 - com.tig.im.Application No active profile set, falling back to default profiles: default
2025-07-02 23:16:24.101 - org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8092"]
2025-07-02 23:16:24.107 - org.apache.catalina.core.StandardService Starting service Tomcat
2025-07-02 23:16:24.107 - org.apache.catalina.core.StandardEngine Starting Servlet Engine: Apache Tomcat/8.5.13
2025-07-02 23:16:24.113 - org.apache.catalina.core.AprLifecycleListener The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: D:\develop\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\ShadowBot;C:\Program Files\jdk\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Ruby34-x64\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\libnvvp;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;D:\Program Files\nodejs\;D:\Program Files\nodejs\node_global\node_modules;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin;D:\apache-maven-3.9.10\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3.2\bin;D:\Program Files\nodejs\node_global;C:\Users\<USER>\AppData\Local\Pro;C:\Program Files\cursor\resources\app\bin;C:\Program Files\ShadowBot;C:\Users\<USER>\.pychange\current;C:\Users\<USER>\.pychange\current\Scripts;.
2025-07-02 23:16:24.280 - org.apache.jasper.servlet.TldScanner At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-02 23:16:24.289 - o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
2025-07-02 23:16:24.487 - cn.tig.commons.autoconfigure.CommAutoConfiguration appConfig  ---->>>{"apiKey":"123456","buildTime":"2019-06-12","distance":20,"isBeta":0,"isDebug":1,"languages":[{"key":"zh","name":"中文 ","value":"简体中文"},{"key":"en","name":"英文","value":"English"},{"key":"big5","name":"繁体","value":"繁体中文"}],"openRedPacketBackTask":1,"openTask":1,"qqzengPath":"","uploadDomain":"http://*************:8088","wxChatUrl":""}
2025-07-02 23:16:24.631 - cn.tig.commons.autoconfigure.CommAutoConfiguration redisConfig  ---->>>{"address":"redis://*************:6739","connectTimeout":10000,"connectionMinimumIdleSize":32,"connectionPoolSize":64,"database":0,"isCluster":0,"password":"2hXPJc7YtNErss28","pingConnectionInterval":500,"pingTimeout":10000,"port":0,"timeout":10000}
2025-07-02 23:16:24.663 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson Single start 
2025-07-02 23:16:25.120 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson create end 
2025-07-02 23:16:25.135 - cn.tig.commons.autoconfigure.CommAutoConfiguration mongoConfig  ---->>>{"cluster":0,"connectTimeout":20000,"dbName":"imapi","maxWaitTime":20000,"password":"qwer4321","roomDbName":"imRoom","socketTimeout":20000,"uri":"**********************************************","username":"mongodb"}
2025-07-02 23:16:25.143 - cn.tig.commons.autoconfigure.CommAutoConfiguration xmppConfig  ---->>>{"dbName":"tigase","dbPassword":"qwer4321","dbUri":"**********************************************","dbUsername":"mongodb","host":"***********","password":"998","passwordEncryptStatus":true,"port":5222,"serverConfig":[{"host":"*************","port":5222,"serverName":"*************","tigaseUserInfo":"configAdmin100001"}],"serverName":"***********","username":"998"}
2025-07-02 23:16:26.985 - cn.tig.commons.autoconfigure.CommAutoConfiguration mqConfig  ---->>>{"batchMaxSize":20,"isConsumerUserStatus":1,"nameAddr":"http://***********:9876","threadMax":64,"threadMin":32}
2025-07-02 23:16:27.285 - cn.tig.im.utils.ConstantUtil  ConstantUtil----init--- Start
2025-07-02 23:16:27.285 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- init
2025-07-02 23:16:27.298 - cn.tig.im.utils.TigBeanUtils TigBeanUtils ===> init end  ===> localSpringBeanManager > LocalSpringBeanManager
2025-07-02 23:16:27.312 - cn.tig.rocketmq.UserStatusConsumer  MQ config nameAddr ===> http://***********:9876
2025-07-02 23:16:27.343 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- End
2025-07-02 23:16:27.953 - cn.tig.commons.autoconfigure.CommAutoConfiguration inviteConfig  ---->>>{"passCardIsOpen":false}
2025-07-02 23:16:27.955 - cn.tig.commons.autoconfigure.CommAutoConfiguration apiAccessConfig  ---->>>{"accessIsSet":true}
2025-07-02 23:16:27.958 - cn.tig.commons.autoconfigure.CommAutoConfiguration obsConfig  ---->>>{"rSAPrivateKey":"","rSAPublicKey":""}
2025-07-02 23:16:27.960 - cn.tig.commons.autoconfigure.CommAutoConfiguration TlPayConfig  ---->>>{"signCerPath":"","signPkPath":""}
2025-07-02 23:16:27.962 - cn.tig.commons.autoconfigure.CommAutoConfiguration SysPayConfig  ---->>>{"tlPayIsOpen":false}
2025-07-02 23:16:27.964 - cn.tig.commons.autoconfigure.CommAutoConfiguration QuartzConfig  ---->>>{"filePath":"D:\\tig-im\\config\\quartz\\quartz.properties"}
2025-07-02 23:16:27.966 - cn.tig.commons.autoconfigure.CommAutoConfiguration RoomScanConfig  ---->>>{"androidSchema":"schema://realmName","iphoneSchema":"schema://name"}
2025-07-02 23:16:27.967 - cn.tig.commons.autoconfigure.CommAutoConfiguration DHConfig  ---->>>null
2025-07-02 23:16:28.949 - org.quartz.impl.StdSchedulerFactory Using default implementation for ThreadExecutor
2025-07-02 23:16:29.003 - org.quartz.core.SchedulerSignalerImpl Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-02 23:16:29.003 - org.quartz.core.QuartzScheduler Quartz Scheduler v.2.2.3 created.
2025-07-02 23:16:29.010 - org.quartz.simpl.RAMJobStore RAMJobStore initialized.
2025-07-02 23:16:29.013 - org.quartz.core.QuartzScheduler Scheduler meta-data: Quartz Scheduler (v2.2.3) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-02 23:16:29.013 - org.quartz.impl.StdSchedulerFactory Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-02 23:16:29.013 - org.quartz.impl.StdSchedulerFactory Quartz scheduler version: 2.2.3
2025-07-02 23:16:29.013 - org.quartz.core.QuartzScheduler JobFactory set to: org.springframework.boot.autoconfigure.quartz.AutowireCapableBeanJobFactory@2a79e241
2025-07-02 23:16:29.091 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-02 23:16:29.113 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-02 23:16:29.117 - org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-8092"]
2025-07-02 23:16:29.121 - org.apache.tomcat.util.net.NioSelectorPool Using a shared selector for servlet write/read
2025-07-02 23:16:29.181 - com.tig.im.Application Started Application in 7.596 seconds (JVM running for 15.784)
2025-07-02 23:16:29.332 - cn.tig.commons.utils.XMPPClientUtil 998已经注册了!请放心连接
2025-07-02 23:16:29.332 - cn.tig.im.utils.InitializationData 
>>>>>>>>>>>>>>> 默认API系统消息tigase账号数据初始化完成  <<<<<<<<<<<<<
2025-07-02 23:16:29.775 - com.tig.im.Application 启动成功  当前版本编译时间  =====>>>>>> 2025-07-02 23:16:29
2025-07-02 23:20:00.000 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-02 11:20:00
2025-07-02 23:20:00.014 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-02 23:20:00.064 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-02 11:20:00
2025-07-02 23:25:00.027 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-02 11:25:00
2025-07-02 23:25:00.028 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-02 11:25:00
2025-07-02 23:25:00.037 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-02 23:29:01.547 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-02 23:29:01.557 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-02 23:29:01.559 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-02 23:29:01.559 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-02 23:29:01.559 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-02 23:41:11.584 - com.tig.im.Application Starting Application on WIN-90Q87RKIEF7 with PID 41320 (D:\code\liaotian3\IMjava\后端文件代码\imserver-parent\imserver-api\target\classes started by Shakher in D:\code\liaotian3\IMjava\后端文件代码\imserver-parent)
2025-07-02 23:41:11.589 - com.tig.im.Application No active profile set, falling back to default profiles: default
2025-07-02 23:41:13.778 - org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8092"]
2025-07-02 23:41:13.784 - org.apache.catalina.core.StandardService Starting service Tomcat
2025-07-02 23:41:13.784 - org.apache.catalina.core.StandardEngine Starting Servlet Engine: Apache Tomcat/8.5.13
2025-07-02 23:41:13.794 - org.apache.catalina.core.AprLifecycleListener The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: D:\develop\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\ShadowBot;C:\Program Files\jdk\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Ruby34-x64\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\libnvvp;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;D:\Program Files\nodejs\;D:\Program Files\nodejs\node_global\node_modules;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin;D:\apache-maven-3.9.10\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3.2\bin;D:\Program Files\nodejs\node_global;C:\Users\<USER>\AppData\Local\Pro;C:\Program Files\cursor\resources\app\bin;C:\Program Files\ShadowBot;C:\Users\<USER>\.pychange\current;C:\Users\<USER>\.pychange\current\Scripts;.
2025-07-02 23:41:14.043 - org.apache.jasper.servlet.TldScanner At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-02 23:41:14.061 - o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
2025-07-02 23:41:14.270 - cn.tig.commons.autoconfigure.CommAutoConfiguration appConfig  ---->>>{"apiKey":"123456","buildTime":"2019-06-12","distance":20,"isBeta":0,"isDebug":1,"languages":[{"key":"zh","name":"中文 ","value":"简体中文"},{"key":"en","name":"英文","value":"English"},{"key":"big5","name":"繁体","value":"繁体中文"}],"openRedPacketBackTask":1,"openTask":1,"qqzengPath":"","uploadDomain":"http://*************:8088","wxChatUrl":""}
2025-07-02 23:41:14.425 - cn.tig.commons.autoconfigure.CommAutoConfiguration redisConfig  ---->>>{"address":"redis://*************:6739","connectTimeout":10000,"connectionMinimumIdleSize":32,"connectionPoolSize":64,"database":0,"isCluster":0,"password":"2hXPJc7YtNErss28","pingConnectionInterval":500,"pingTimeout":10000,"port":0,"timeout":10000}
2025-07-02 23:41:14.459 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson Single start 
2025-07-02 23:41:15.040 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson create end 
2025-07-02 23:41:15.053 - cn.tig.commons.autoconfigure.CommAutoConfiguration mongoConfig  ---->>>{"cluster":0,"connectTimeout":20000,"dbName":"imapi","maxWaitTime":20000,"password":"qwer4321","roomDbName":"imRoom","socketTimeout":20000,"uri":"**********************************************","username":"mongodb"}
2025-07-02 23:41:15.058 - cn.tig.commons.autoconfigure.CommAutoConfiguration xmppConfig  ---->>>{"dbName":"tigase","dbPassword":"qwer4321","dbUri":"**********************************************","dbUsername":"mongodb","host":"***********","password":"998","passwordEncryptStatus":true,"port":5222,"serverConfig":[{"host":"*************","port":5222,"serverName":"*************","tigaseUserInfo":"configAdmin100001"}],"serverName":"***********","username":"998"}
2025-07-02 23:41:16.435 - cn.tig.commons.autoconfigure.CommAutoConfiguration mqConfig  ---->>>{"batchMaxSize":20,"isConsumerUserStatus":1,"nameAddr":"http://***********:9876","threadMax":64,"threadMin":32}
2025-07-02 23:41:16.715 - cn.tig.im.utils.ConstantUtil  ConstantUtil----init--- Start
2025-07-02 23:41:16.715 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- init
2025-07-02 23:41:16.729 - cn.tig.im.utils.TigBeanUtils TigBeanUtils ===> init end  ===> localSpringBeanManager > LocalSpringBeanManager
2025-07-02 23:41:16.743 - cn.tig.rocketmq.UserStatusConsumer  MQ config nameAddr ===> http://***********:9876
2025-07-02 23:41:16.764 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- End
2025-07-02 23:41:17.315 - cn.tig.commons.autoconfigure.CommAutoConfiguration inviteConfig  ---->>>{"passCardIsOpen":false}
2025-07-02 23:41:17.317 - cn.tig.commons.autoconfigure.CommAutoConfiguration apiAccessConfig  ---->>>{"accessIsSet":true}
2025-07-02 23:41:17.318 - cn.tig.commons.autoconfigure.CommAutoConfiguration obsConfig  ---->>>{"rSAPrivateKey":"","rSAPublicKey":""}
2025-07-02 23:41:17.321 - cn.tig.commons.autoconfigure.CommAutoConfiguration TlPayConfig  ---->>>{"signCerPath":"","signPkPath":""}
2025-07-02 23:41:17.322 - cn.tig.commons.autoconfigure.CommAutoConfiguration SysPayConfig  ---->>>{"tlPayIsOpen":false}
2025-07-02 23:41:17.324 - cn.tig.commons.autoconfigure.CommAutoConfiguration QuartzConfig  ---->>>{"filePath":"D:\\tig-im\\config\\quartz\\quartz.properties"}
2025-07-02 23:41:17.326 - cn.tig.commons.autoconfigure.CommAutoConfiguration RoomScanConfig  ---->>>{"androidSchema":"schema://realmName","iphoneSchema":"schema://name"}
2025-07-02 23:41:17.327 - cn.tig.commons.autoconfigure.CommAutoConfiguration DHConfig  ---->>>null
2025-07-02 23:41:18.146 - org.quartz.impl.StdSchedulerFactory Using default implementation for ThreadExecutor
2025-07-02 23:41:18.177 - org.quartz.core.SchedulerSignalerImpl Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-02 23:41:18.177 - org.quartz.core.QuartzScheduler Quartz Scheduler v.2.2.3 created.
2025-07-02 23:41:18.178 - org.quartz.simpl.RAMJobStore RAMJobStore initialized.
2025-07-02 23:41:18.178 - org.quartz.core.QuartzScheduler Scheduler meta-data: Quartz Scheduler (v2.2.3) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-02 23:41:18.179 - org.quartz.impl.StdSchedulerFactory Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-02 23:41:18.179 - org.quartz.impl.StdSchedulerFactory Quartz scheduler version: 2.2.3
2025-07-02 23:41:18.179 - org.quartz.core.QuartzScheduler JobFactory set to: org.springframework.boot.autoconfigure.quartz.AutowireCapableBeanJobFactory@5dc743cd
2025-07-02 23:41:18.248 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-02 23:41:18.269 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-02 23:41:18.272 - org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-8092"]
2025-07-02 23:41:18.276 - org.apache.tomcat.util.net.NioSelectorPool Using a shared selector for servlet write/read
2025-07-02 23:41:18.330 - com.tig.im.Application Started Application in 7.146 seconds (JVM running for 16.436)
2025-07-02 23:41:18.531 - cn.tig.commons.utils.XMPPClientUtil 998已经注册了!请放心连接
2025-07-02 23:41:18.531 - cn.tig.im.utils.InitializationData 
>>>>>>>>>>>>>>> 默认API系统消息tigase账号数据初始化完成  <<<<<<<<<<<<<
2025-07-02 23:41:18.950 - com.tig.im.Application 启动成功  当前版本编译时间  =====>>>>>> 2025-07-02 23:41:18
2025-07-02 23:42:32.691 - o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-07-02 23:42:32.706 - com.tig.im.filter.AuthorizationFilter HEAD 请求：/
2025-07-02 23:42:32.708 - com.tig.im.filter.AuthorizationFilter 不包含请求令牌
2025-07-02 23:45:00.009 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-02 11:45:00
2025-07-02 23:45:00.025 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-02 23:45:00.056 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-02 11:45:00
2025-07-02 23:47:03.917 - com.tig.im.filter.AuthorizationFilter GET 请求：/
2025-07-02 23:47:03.917 - com.tig.im.filter.AuthorizationFilter 不包含请求令牌
2025-07-02 23:47:19.302 - com.tig.im.filter.AuthorizationFilter GET 请求：/
2025-07-02 23:47:19.302 - com.tig.im.filter.AuthorizationFilter 不包含请求令牌
2025-07-02 23:47:20.325 - com.tig.im.filter.AuthorizationFilter GET 请求：/
2025-07-02 23:47:20.325 - com.tig.im.filter.AuthorizationFilter 不包含请求令牌
2025-07-02 23:47:21.032 - com.tig.im.filter.AuthorizationFilter GET 请求：/
2025-07-02 23:47:21.033 - com.tig.im.filter.AuthorizationFilter 不包含请求令牌
2025-07-02 23:50:00.023 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-02 11:50:00
2025-07-02 23:50:00.023 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-02 11:50:00
2025-07-02 23:50:00.031 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-02 23:50:28.595 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/login.html
2025-07-02 23:50:42.307 - com.tig.im.filter.AuthorizationFilter GET 请求：/open/login
2025-07-02 23:50:42.309 - com.tig.im.SysApiLogAspect 请求参数： /open/login
2025-07-02 23:50:42.310 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-02 23:50:42.310 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.OpenAdminController】类的【openLogin】方法
2025-07-02 23:50:42.314 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.OpenAdminController】类的【openLogin】方法，应答参数：null
2025-07-02 23:50:42.316 - com.tig.im.SysApiLogAspect 接口【openLogin】总耗时(毫秒)：4
2025-07-02 23:50:42.316 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-02 23:50:42.334 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/landingPage/publicOpenLogin.html
2025-07-02 23:50:44.654 - com.tig.im.filter.AuthorizationFilter GET 请求：/open/login
2025-07-02 23:50:44.654 - com.tig.im.SysApiLogAspect 请求参数： /open/login
2025-07-02 23:50:44.654 - com.tig.im.SysApiLogAspect 客户端ip [************]  User-Agent Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.7.408 
2025-07-02 23:50:44.656 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.OpenAdminController】类的【openLogin】方法
2025-07-02 23:50:44.656 - com.tig.im.SysApiLogAspect 【com.tig.im.controller.OpenAdminController】类的【openLogin】方法，应答参数：null
2025-07-02 23:50:44.656 - com.tig.im.SysApiLogAspect 接口【openLogin】总耗时(毫秒)：0
2025-07-02 23:50:44.656 - com.tig.im.SysApiLogAspect ********************************************   
2025-07-02 23:50:44.689 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/landingPage/publicOpenLogin.html
2025-07-02 23:50:51.780 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/login.html
2025-07-02 23:51:15.042 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/login.html
2025-07-02 23:52:10.696 - com.tig.im.filter.AuthorizationFilter GET 请求：/pages/console/login.html
2025-07-02 23:53:11.317 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-02 23:53:11.319 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-02 23:53:11.319 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-02 23:53:11.319 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-02 23:53:11.320 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-02 23:53:27.004 - com.tig.im.Application Starting Application on WIN-90Q87RKIEF7 with PID 45436 (D:\code\liaotian3\IMjava\后端文件代码\imserver-parent\imserver-api\target\classes started by Shakher in D:\code\liaotian3\IMjava\后端文件代码\imserver-parent)
2025-07-02 23:53:27.011 - com.tig.im.Application No active profile set, falling back to default profiles: default
2025-07-02 23:53:29.070 - org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8092"]
2025-07-02 23:53:29.076 - org.apache.catalina.core.StandardService Starting service Tomcat
2025-07-02 23:53:29.076 - org.apache.catalina.core.StandardEngine Starting Servlet Engine: Apache Tomcat/8.5.13
2025-07-02 23:53:29.084 - org.apache.catalina.core.AprLifecycleListener The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: D:\develop\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\ShadowBot;C:\Program Files\jdk\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Ruby34-x64\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\libnvvp;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;D:\Program Files\nodejs\;D:\Program Files\nodejs\node_global\node_modules;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin;D:\apache-maven-3.9.10\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3.2\bin;D:\Program Files\nodejs\node_global;C:\Users\<USER>\AppData\Local\Pro;C:\Program Files\cursor\resources\app\bin;C:\Program Files\ShadowBot;C:\Users\<USER>\.pychange\current;C:\Users\<USER>\.pychange\current\Scripts;.
2025-07-02 23:53:29.245 - org.apache.jasper.servlet.TldScanner At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-02 23:53:29.256 - o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
2025-07-02 23:53:29.506 - cn.tig.commons.autoconfigure.CommAutoConfiguration appConfig  ---->>>{"apiKey":"123456","buildTime":"2019-06-12","distance":20,"isBeta":0,"isDebug":1,"languages":[{"key":"zh","name":"中文 ","value":"简体中文"},{"key":"en","name":"英文","value":"English"},{"key":"big5","name":"繁体","value":"繁体中文"}],"openRedPacketBackTask":1,"openTask":1,"qqzengPath":"","uploadDomain":"http://*************:8088","wxChatUrl":""}
2025-07-02 23:53:29.655 - cn.tig.commons.autoconfigure.CommAutoConfiguration redisConfig  ---->>>{"address":"redis://*************:6739","connectTimeout":10000,"connectionMinimumIdleSize":32,"connectionPoolSize":64,"database":0,"isCluster":0,"password":"2hXPJc7YtNErss28","pingConnectionInterval":500,"pingTimeout":10000,"port":0,"timeout":10000}
2025-07-02 23:53:29.680 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson Single start 
2025-07-02 23:53:30.277 - c.t.commons.autoconfigure.KRedisAutoConfiguration redisson create end 
2025-07-02 23:53:30.294 - cn.tig.commons.autoconfigure.CommAutoConfiguration mongoConfig  ---->>>{"cluster":0,"connectTimeout":20000,"dbName":"imapi","maxWaitTime":20000,"password":"qwer4321","roomDbName":"imRoom","socketTimeout":20000,"uri":"**********************************************","username":"mongodb"}
2025-07-02 23:53:30.301 - cn.tig.commons.autoconfigure.CommAutoConfiguration xmppConfig  ---->>>{"dbName":"tigase","dbPassword":"qwer4321","dbUri":"**********************************************","dbUsername":"mongodb","host":"***********","password":"998","passwordEncryptStatus":true,"port":5222,"serverConfig":[{"host":"*************","port":5222,"serverName":"*************","tigaseUserInfo":"configAdmin100001"}],"serverName":"***********","username":"998"}
2025-07-02 23:53:32.784 - cn.tig.commons.autoconfigure.CommAutoConfiguration mqConfig  ---->>>{"batchMaxSize":20,"isConsumerUserStatus":1,"nameAddr":"http://***********:9876","threadMax":64,"threadMin":32}
2025-07-02 23:53:33.065 - cn.tig.im.utils.ConstantUtil  ConstantUtil----init--- Start
2025-07-02 23:53:33.065 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- init
2025-07-02 23:53:33.078 - cn.tig.im.utils.TigBeanUtils TigBeanUtils ===> init end  ===> localSpringBeanManager > LocalSpringBeanManager
2025-07-02 23:53:33.093 - cn.tig.rocketmq.UserStatusConsumer  MQ config nameAddr ===> http://***********:9876
2025-07-02 23:53:33.125 - cn.tig.im.utils.ConstantUtil  ConstantUtil----MsgMap--- End
2025-07-02 23:53:33.753 - cn.tig.commons.autoconfigure.CommAutoConfiguration inviteConfig  ---->>>{"passCardIsOpen":false}
2025-07-02 23:53:33.756 - cn.tig.commons.autoconfigure.CommAutoConfiguration apiAccessConfig  ---->>>{"accessIsSet":true}
2025-07-02 23:53:33.758 - cn.tig.commons.autoconfigure.CommAutoConfiguration obsConfig  ---->>>{"rSAPrivateKey":"","rSAPublicKey":""}
2025-07-02 23:53:33.761 - cn.tig.commons.autoconfigure.CommAutoConfiguration TlPayConfig  ---->>>{"signCerPath":"","signPkPath":""}
2025-07-02 23:53:33.764 - cn.tig.commons.autoconfigure.CommAutoConfiguration SysPayConfig  ---->>>{"tlPayIsOpen":false}
2025-07-02 23:53:33.765 - cn.tig.commons.autoconfigure.CommAutoConfiguration QuartzConfig  ---->>>{"filePath":"D:\\tig-im\\config\\quartz\\quartz.properties"}
2025-07-02 23:53:33.767 - cn.tig.commons.autoconfigure.CommAutoConfiguration RoomScanConfig  ---->>>{"androidSchema":"schema://realmName","iphoneSchema":"schema://name"}
2025-07-02 23:53:33.768 - cn.tig.commons.autoconfigure.CommAutoConfiguration DHConfig  ---->>>null
2025-07-02 23:53:34.603 - org.quartz.impl.StdSchedulerFactory Using default implementation for ThreadExecutor
2025-07-02 23:53:34.638 - org.quartz.core.SchedulerSignalerImpl Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-02 23:53:34.638 - org.quartz.core.QuartzScheduler Quartz Scheduler v.2.2.3 created.
2025-07-02 23:53:34.641 - org.quartz.simpl.RAMJobStore RAMJobStore initialized.
2025-07-02 23:53:34.642 - org.quartz.core.QuartzScheduler Scheduler meta-data: Quartz Scheduler (v2.2.3) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-02 23:53:34.643 - org.quartz.impl.StdSchedulerFactory Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-02 23:53:34.643 - org.quartz.impl.StdSchedulerFactory Quartz scheduler version: 2.2.3
2025-07-02 23:53:34.643 - org.quartz.core.QuartzScheduler JobFactory set to: org.springframework.boot.autoconfigure.quartz.AutowireCapableBeanJobFactory@6edaefe8
2025-07-02 23:53:34.711 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-02 23:53:34.730 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-02 23:53:34.734 - org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-8092"]
2025-07-02 23:53:34.737 - org.apache.tomcat.util.net.NioSelectorPool Using a shared selector for servlet write/read
2025-07-02 23:53:34.794 - com.tig.im.Application Started Application in 8.168 seconds (JVM running for 16.198)
2025-07-02 23:53:34.954 - cn.tig.commons.utils.XMPPClientUtil 998已经注册了!请放心连接
2025-07-02 23:53:34.954 - cn.tig.im.utils.InitializationData 
>>>>>>>>>>>>>>> 默认API系统消息tigase账号数据初始化完成  <<<<<<<<<<<<<
2025-07-02 23:53:35.507 - com.tig.im.Application 启动成功  当前版本编译时间  =====>>>>>> 2025-07-02 23:53:35
2025-07-02 23:54:55.362 - o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-07-02 23:54:55.383 - com.tig.im.filter.AuthorizationFilter HEAD 请求：/pages/console/login.html
2025-07-02 23:55:00.001 - cn.tig.im.scheduleds.CommTask queryExpireVip 当前时间:2025-07-02 11:55:00
2025-07-02 23:55:00.018 - cn.tig.im.scheduleds.CommTask =========查询过期vip========  0
2025-07-02 23:55:00.061 - cn.tig.im.scheduleds.CommTask 刷新用户状态统计======>>>>>2,执行时间2025-07-02 11:55:00
2025-07-02 23:55:10.491 - cn.tig.im.scheduleds.CommTask =======定时任务开启中=======
2025-07-02 23:55:10.492 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-02 23:55:10.492 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-02 23:55:10.492 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-02 23:55:10.492 - org.quartz.core.QuartzScheduler Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
