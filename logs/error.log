===2025-07-02 22:58:09.092 ERROR org.springframework.boot.SpringApplication Line:845 - Application run failed
java.lang.IllegalStateException: Cannot load configuration class: com.tig.im.Application
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.enhanceConfigurationClasses(ConfigurationClassPostProcessor.java:414)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanFactory(ConfigurationClassPostProcessor.java:254)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:282)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:126)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:694)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:532)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:398)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:330)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1258)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1246)
	at com.tig.im.Application.main(Application.java:73)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.maven.AbstractRunMojo$LaunchRunner.run(AbstractRunMojo.java:496)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.ExceptionInInitializerError: null
	at org.springframework.context.annotation.ConfigurationClassEnhancer.newEnhancer(ConfigurationClassEnhancer.java:122)
	at org.springframework.context.annotation.ConfigurationClassEnhancer.enhance(ConfigurationClassEnhancer.java:110)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.enhanceConfigurationClasses(ConfigurationClassPostProcessor.java:403)
	... 16 common frames omitted
Caused by: org.springframework.cglib.core.CodeGenerationException: java.lang.reflect.InaccessibleObjectException-->Unable to make protected final java.lang.Class java.lang.ClassLoader.defineClass(java.lang.String,byte[],int,int,java.security.ProtectionDomain) throws java.lang.ClassFormatError accessible: module java.base does not "opens java.lang" to unnamed module @79c7556e
	at org.springframework.cglib.core.ReflectUtils.defineClass(ReflectUtils.java:464)
	at org.springframework.cglib.core.AbstractClassGenerator.generate(AbstractClassGenerator.java:336)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData$3.apply(AbstractClassGenerator.java:93)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData$3.apply(AbstractClassGenerator.java:91)
	at org.springframework.cglib.core.internal.LoadingCache$2.call(LoadingCache.java:54)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:61)
	at org.springframework.cglib.core.internal.LoadingCache.get(LoadingCache.java:34)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData.get(AbstractClassGenerator.java:116)
	at org.springframework.cglib.core.AbstractClassGenerator.create(AbstractClassGenerator.java:291)
	at org.springframework.cglib.core.KeyFactory$Generator.create(KeyFactory.java:221)
	at org.springframework.cglib.core.KeyFactory.create(KeyFactory.java:174)
	at org.springframework.cglib.core.KeyFactory.create(KeyFactory.java:153)
	at org.springframework.cglib.proxy.Enhancer.<clinit>(Enhancer.java:73)
	... 19 common frames omitted
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make protected final java.lang.Class java.lang.ClassLoader.defineClass(java.lang.String,byte[],int,int,java.security.ProtectionDomain) throws java.lang.ClassFormatError accessible: module java.base does not "opens java.lang" to unnamed module @79c7556e
	at java.base/java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:391)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:367)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:315)
	at java.base/java.lang.reflect.Method.checkCanSetAccessible(Method.java:203)
	at java.base/java.lang.reflect.Method.setAccessible(Method.java:197)
	at org.springframework.cglib.core.ReflectUtils$1.run(ReflectUtils.java:61)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at org.springframework.cglib.core.ReflectUtils.<clinit>(ReflectUtils.java:52)
	at org.springframework.cglib.core.KeyFactory$Generator.generateClass(KeyFactory.java:243)
	at org.springframework.cglib.core.DefaultGeneratorStrategy.generate(DefaultGeneratorStrategy.java:25)
	at org.springframework.cglib.core.AbstractClassGenerator.generate(AbstractClassGenerator.java:329)
	... 31 common frames omitted
===2025-07-02 23:22:58.621 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0xa4130492, L:/192.168.1.16:64225 ! R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.623 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x863ce81c, L:/192.168.1.16:64236 ! R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.624 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x6df9b291, L:/192.168.1.16:64213 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.625 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x246227f9, L:/192.168.1.16:64227 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.625 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x6c1e8b4a, L:/192.168.1.16:64206 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.627 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x8f3481c7, L:/192.168.1.16:64226 ! R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.627 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x1e7cf2cd, L:/192.168.1.16:64217 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.628 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x6b3e3d1c, L:/192.168.1.16:64231 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.629 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0xbd1dcdc2, L:/192.168.1.16:64208 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.629 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x28f4b0a5, L:/192.168.1.16:64232 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.630 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x2a66ec14, L:/192.168.1.16:64220 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.630 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x499dfc7e, L:/192.168.1.16:64216 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.630 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x6b06e6ca, L:/192.168.1.16:64224 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.631 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x06fd4e8b, L:/192.168.1.16:64235 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.631 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x455a251e, L:/192.168.1.16:64205 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.632 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0xcef8fb6e, L:/192.168.1.16:64228 ! R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.632 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x243bf677, L:/192.168.1.16:64221 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:22:58.633 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0xde4fb31c, L:/192.168.1.16:64233 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:26:59.507 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0xcfa7843d, L:/192.168.1.16:49614 ! R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:26:59.612 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0xa5459898, L:/192.168.1.16:49619 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:26:59.618 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0xcc2e0580, L:/192.168.1.16:49626 ! R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:26:59.719 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0xc4faa72b, L:/192.168.1.16:49623 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:26:59.812 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0xebd12f5c, L:/192.168.1.16:64212 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:26:59.827 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x359d34d7, L:/192.168.1.16:64209 ! R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:26:59.828 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x92158193, L:/192.168.1.16:64214 ! R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
===2025-07-02 23:26:59.829 ERROR org.redisson.client.handler.PingConnectionHandler Line:79  - Unable to send PING command over channel: [id: 0x681197ed, L:/192.168.1.16:49616 - R:/110.42.40.185:6739]
io.netty.util.concurrent.DefaultPromise$LeanCancellationException: null
	at io.netty.util.concurrent.DefaultPromise.cancel(...)(Unknown Source)
