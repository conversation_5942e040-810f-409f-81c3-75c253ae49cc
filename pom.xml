<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.tig</groupId>
	<artifactId>imserver-parent</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>imserver-parent</name>
	<description>imserver-parent</description>
	<properties>
		<tomcat.version>8.5.32</tomcat.version> 
		<spring-boot.version>2.0.4.RELEASE</spring-boot.version>
        
        <commons-codec.version>1.11</commons-codec.version>
       <dom4j.version>1.6.1</dom4j.version>
        <freemarker.version>2.3.28</freemarker.version>
        <git-commit-id-plugin.version>2.2.4</git-commit-id-plugin.version>
       
        <gson.version>2.8.5</gson.version>
       
        <httpasyncclient.version>4.1.3</httpasyncclient.version>
        <httpclient.version>4.5.6</httpclient.version>
        <httpcore.version>4.4.10</httpcore.version>
       
       
        <jdom2.version>2.0.6</jdom2.version>
        <jedis.version>2.9.0</jedis.version>
        
        <jstl.version>1.2</jstl.version>
        <jtds.version>1.3.1</jtds.version>
        <junit.version>4.12</junit.version>
        <junit-jupiter.version>5.1.1</junit-jupiter.version>
        <junit-platform.version>1.1.0</junit-platform.version>
        <kafka.version>1.0.2</kafka.version>
        <kotlin.version>1.2.51</kotlin.version>
        <lettuce.version>5.0.4.RELEASE</lettuce.version>
        <liquibase.version>3.5.5</liquibase.version>
        <log4j2.version>2.10.0</log4j2.version>
        <logback.version>1.2.3</logback.version>
        <lombok.version>1.16.22</lombok.version>
       
        <xml-apis.version>1.4.01</xml-apis.version>
        <maven-clean-plugin.version>3.0.0</maven-clean-plugin.version>
        <maven-compiler-plugin.version>3.7.0</maven-compiler-plugin.version>
        <maven-dependency-plugin.version>3.0.2</maven-dependency-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-eclipse-plugin.version>2.10</maven-eclipse-plugin.version>
        <maven-enforcer-plugin.version>3.0.0-M2</maven-enforcer-plugin.version>
        <maven-failsafe-plugin.version>2.21.0</maven-failsafe-plugin.version>
        <maven-help-plugin.version>2.2</maven-help-plugin.version>
        <maven-install-plugin.version>2.5.2</maven-install-plugin.version>
        <maven-invoker-plugin.version>3.1.0</maven-invoker-plugin.version>
        <maven-jar-plugin.version>3.0.2</maven-jar-plugin.version>
        <maven-javadoc-plugin.version>3.0.1</maven-javadoc-plugin.version>
        <maven-resources-plugin.version>3.0.2</maven-resources-plugin.version>
        <maven-shade-plugin.version>2.4.3</maven-shade-plugin.version>
        <maven-site-plugin.version>3.6</maven-site-plugin.version>
        <maven-source-plugin.version>3.0.1</maven-source-plugin.version>
        <maven-surefire-plugin.version>2.21.0</maven-surefire-plugin.version>
        <maven-war-plugin.version>3.1.0</maven-war-plugin.version>
        <micrometer.version>1.0.6</micrometer.version>
       <mongodb.version>3.6.4</mongodb.version>
        <mongo-driver-reactivestreams.version>1.7.1</mongo-driver-reactivestreams.version>
        <nekohtml.version>1.9.22</nekohtml.version>
        <netty.version>4.1.27.Final</netty.version>
        <nio-multipart-parser.version>1.1.0</nio-multipart-parser.version>
        <quartz.version>2.3.0</quartz.version>
        <querydsl.version>4.1.4</querydsl.version>
        <rabbit-amqp-client.version>5.1.2</rabbit-amqp-client.version>
        <reactive-streams.version>1.0.2</reactive-streams.version>
        <reactor-bom.version>Bismuth-SR10</reactor-bom.version>
       
        <servlet-api.version>3.1.0</servlet-api.version>
        <simple-json.version>1.1.1</simple-json.version>
        <slf4j.version>1.7.25</slf4j.version>
        <snakeyaml.version>1.19</snakeyaml.version>
        <spring.version>5.0.8.RELEASE</spring.version>
        <spring-amqp.version>2.0.5.RELEASE</spring-amqp.version>
        <spring-batch.version>4.0.1.RELEASE</spring-batch.version>
        <spring-cloud-connectors.version>2.0.2.RELEASE</spring-cloud-connectors.version>
        <spring-data-releasetrain.version>Kay-SR9</spring-data-releasetrain.version>
        <spring-hateoas.version>0.25.0.RELEASE</spring-hateoas.version>
        <spring-integration.version>5.0.7.RELEASE</spring-integration.version>
        <spring-kafka.version>2.1.8.RELEASE</spring-kafka.version>
        <spring-ldap.version>2.3.2.RELEASE</spring-ldap.version>
        <spring-plugin.version>1.2.0.RELEASE</spring-plugin.version>
        <spring-restdocs.version>2.0.2.RELEASE</spring-restdocs.version>
        <spring-retry.version>1.2.2.RELEASE</spring-retry.version>
        <spring-security.version>5.0.7.RELEASE</spring-security.version>
        <spring-session-bom.version>Apple-SR4</spring-session-bom.version>
        <spring-ws.version>3.0.3.RELEASE</spring-ws.version>
        <statsd-client.version>3.1.0</statsd-client.version>
        <sun-mail.version>1.6.1</sun-mail.version>
        <thymeleaf.version>3.0.9.RELEASE</thymeleaf.version>
        <thymeleaf-extras-data-attribute.version>2.0.1</thymeleaf-extras-data-attribute.version>
        <thymeleaf-extras-java8time.version>3.0.1.RELEASE</thymeleaf-extras-java8time.version>
        <thymeleaf-extras-springsecurity4.version>3.0.2.RELEASE</thymeleaf-extras-springsecurity4.version>
        <thymeleaf-layout-dialect.version>2.3.0</thymeleaf-layout-dialect.version>
        <paypal-sdk.version>1.0.4</paypal-sdk.version>
    </properties>
	<dependencyManagement>
		<dependencies>
			<!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-dependencies -->
			<dependency>
			    <groupId>org.springframework.boot</groupId>
			    <artifactId>spring-boot-dependencies</artifactId>
			    <version>2.0.4.RELEASE</version>
			</dependency>


		</dependencies>
	</dependencyManagement>

	<!-- Add typical dependencies for a web application -->
	<dependencies>
			<!--内置Tomcat版本导致的 The valid characters are defined in RFC 7230 and RFC 3986  -->
			
		<dependency>
		   <groupId>org.springframework.boot</groupId>
		   <artifactId>spring-boot-starter-web</artifactId>
		   <version>${spring-boot.version}</version>
		  <exclusions>
		     
		      <!-- <exclusion>
		         <groupId>org.springframework.boot</groupId>
		         <artifactId>spring-boot-starter-tomcat</artifactId>
		      </exclusion> -->
		   </exclusions>
		   
		</dependency>
        <dependency>
            <groupId>com.paypal.sdk</groupId>
            <artifactId>checkout-sdk</artifactId>
            <version>${paypal-sdk.version}</version>
        </dependency>

		<dependency>
		   <groupId>org.springframework.boot</groupId>
		   <artifactId>spring-boot-starter-web</artifactId>
		   <version>${spring-boot.version}</version>
		 </dependency>
        <dependency>
            <groupId>com.devskiller.friendly-id</groupId>
            <artifactId>friendly-id</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>


    </dependencies>

	<!-- Add Spring repositories -->
	<!-- (you don't need this if you are using a .RELEASE version) -->
	<repositories>
<!--        <repository>-->
<!--            <id>nexus</id>-->
<!--            <url>http://10.10.10.83:8081/repository/maven-public</url>-->
<!--        </repository>-->
<!--		<repository>-->
<!--			<id>spring-snapshots</id>-->
<!--			<url>https://repo.spring.io/snapshot</url>-->
<!--			<snapshots>-->
<!--				<enabled>true</enabled>-->
<!--			</snapshots>-->
<!--		</repository>-->
<!--		<repository>-->
<!--			<id>spring-milestones</id>-->
<!--			<url>https://repo.spring.io/milestone</url>-->
<!--		</repository>-->
        <!--<repository>-->
            <!--<id>spring-plugin</id>-->
            <!--<url>https://repo.spring.io/plugins-release/</url>-->
        <!--</repository>-->
        <!--<repository>-->
            <!--<id>aliyun</id>-->
            <!--<url>https://maven.aliyun.com/nexus/content/repositories/releases/</url>-->
        <!--</repository>-->
<!--        <snapshotRepository>-->
<!--            <id>archiva.snapshots</id>-->
<!--            <name>Internal Snapshot Repository</name>-->
<!--            <url>https://nexus.d.xiaomi.net/nexus/content/repositories/snapshots/-->
<!--            </url>-->
<!--        </snapshotRepository>-->
	</repositories>

	<!--<pluginRepositories>
		<pluginRepository>
			<id>spring-snapshots</id>
			<url>http://repo.spring.io/snapshot</url>
		</pluginRepository>
		<pluginRepository>
			<id>spring-milestones</id>
			<url>http://repo.spring.io/milestone</url>
		</pluginRepository>
	</pluginRepositories>-->

<!--    <distributionManagement>-->
<!--        <snapshotRepository>-->
<!--            <id>maven-snapshots</id>-->
<!--            <name>maven snapshots resp</name>-->
<!--            <url>http://10.10.10.83:8081/repository/maven-snapshots/</url>-->
<!--        </snapshotRepository>-->
<!--        <repository>-->
<!--            <id>maven-releases</id>-->
<!--            <name>maven release resp</name>-->
<!--            <url>http://10.10.10.83:8081/repository/maven-releases/</url>-->
<!--        </repository>-->
<!--    </distributionManagement>-->

	<modules>
		<module>imserver-service</module>
		<module>imserver-api</module>
    </modules>

</project>